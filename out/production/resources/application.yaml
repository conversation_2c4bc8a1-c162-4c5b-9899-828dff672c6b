spring:
  data:
    mongodb:
      uri: mongodb://localhost:27017/backend-db
      database: backend-db

  security:
    oauth2:
      client:
        registration:
          github:
            client-id: Ov23lixPz71P8RhDPcl
            client-secret: c42045754a7bbc187f94814984f93f8e5ddf315d
            scope: user:email
        provider:
          github:
            authorization-uri: https://github.com/login/oauth/authorize
            token-uri: https://github.com/login/oauth/access_token
            user-info-uri: https://api.github.com/user
