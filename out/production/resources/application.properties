spring.data.mongodb.uri=mongodb://localhost:27017/backend-db
spring.data.mongodb.database=backend-db
#
#server.port=8080
#spring.jackson.serialization.INDENT_OUTPUT=true
#spring.main.allow-bean-definition-overriding=true
#
spring.security.oauth2.resourceserver.jwt.issuer-uri=http://localhost:8080
logging.level.org.example=TRACE
logging.level.org.springframework.security=TRACE

spring.security.oauth2.client.registration.google.client-id=VOTRE_CLIENT_ID
spring.security.oauth2.client.registration.google.client-secret=VOTRE_CLIENT_SECRET
spring.security.oauth2.client.registration.google.scope=profile,email
spring.security.oauth2.client.registration.google.redirect-uri=http://localhost:8080/login
spring.security.oauth2.client.provider.google.authorization-uri=https://accounts.google.com/o/oauth2/auth
spring.security.oauth2.client.provider.google.token-uri=https://oauth2.googleapis.com/token
spring.security.oauth2.client.provider.google.user-info-uri=https://www.googleapis.com/oauth2/v3/userinfo


server.port=8081

