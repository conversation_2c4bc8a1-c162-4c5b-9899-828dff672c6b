    plugins {
        id 'java'
        id 'org.springframework.boot' version '3.4.2'
        id 'io.spring.dependency-management' version '1.1.7'
    }

    group = 'org.example'
    version = '1.0-SNAPSHOT'

    repositories {
        mavenCentral()
        google()
        maven { url 'https://repo1.maven.org/maven2/' }
    }

    java {
        toolchain {
            languageVersion = JavaLanguageVersion.of(21)
        }
    }

    dependencies {
        implementation 'org.springframework.boot:spring-boot-starter-webflux' // ✅ important pour Mono
        implementation 'org.springframework.boot:spring-boot-starter-data-mongodb'
        implementation 'org.springframework.boot:spring-boot-starter-validation'
        implementation 'org.springframework.boot:spring-boot-starter-security'
        implementation 'org.springframework.boot:spring-boot-starter-oauth2-client'
        implementation 'org.springframework.boot:spring-boot-starter-oauth2-resource-server'
        implementation 'org.springframework.boot:spring-boot-starter-mail'

        implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui:2.6.0'
        implementation 'io.springfox:springfox-boot-starter:3.0.0'
        implementation 'io.jsonwebtoken:jjwt-api:0.12.3'
        implementation 'io.jsonwebtoken:jjwt-impl:0.12.3'
        implementation 'io.jsonwebtoken:jjwt-jackson:0.12.3'

        implementation 'com.fasterxml.jackson.core:jackson-databind'
        implementation 'com.h2database:h2'
        implementation 'org.hibernate:hibernate-core:6.6.5.Final'

        implementation 'com.google.apis:google-api-services-oauth2:v2-rev157-1.25.0'
        implementation 'com.google.api-client:google-api-client:1.34.1'
        implementation 'com.google.http-client:google-http-client-gson:1.42.3'
        implementation 'com.google.http-client:google-http-client-jackson2:1.39.2'

        implementation 'com.stripe:stripe-java:24.8.0'

        implementation 'org.springframework.boot:spring-boot-starter-mail'
        implementation 'org.apache.pdfbox:pdfbox:2.0.27'

        testImplementation 'org.springframework.boot:spring-boot-starter-test'
        testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
        implementation 'jakarta.servlet:jakarta.servlet-api:6.0.0'
        implementation 'org.springframework.boot:spring-boot-starter-web'         // MVC (Servlet)
        implementation 'org.springframework.boot:spring-boot-starter-webflux'     // WebFlux (réactif)
        implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
        implementation 'org.springframework.boot:spring-boot-starter-data-mongodb'

    }
    tasks.named('test') {
        useJUnitPlatform()
    }