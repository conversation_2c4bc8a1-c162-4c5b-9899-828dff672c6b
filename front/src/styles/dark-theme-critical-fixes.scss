/**
 * Styles ultra-spécifiques pour corriger les éléments qui ne s'appliquent pas correctement au mode sombre
 * Ce fichier cible spécifiquement les blocs dans la section d'information de profil,
 * l'arrière-plan de la page de profil et la partie sous le header
 */

/* Correction pour l'arrière-plan de la page de profil */
.dark-theme {
  .profile-container {
    background-color: var(--background-color) !important;
    color: var(--text-primary) !important;
  }

  /* Correction pour le header de profil */
  .profile-header {
    background: linear-gradient(135deg, #0a192f 0%, #112a5e 100%) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
  }

  .header-background {
    background: linear-gradient(135deg, #0a192f 0%, #112a5e 100%) !important;
    
    &::before {
      opacity: 0.05 !important;
    }
    
    &::after {
      background: linear-gradient(to bottom, rgba(0,0,0,0) 0%, rgba(0,0,0,0.2) 100%) !important;
    }
  }

  /* Correction pour la partie sous le header */
  .profile-content {
    background-color: var(--background-color) !important;
    color: var(--text-primary) !important;
  }

  .left-column,
  .right-column {
    background-color: var(--background-color) !important;
    color: var(--text-primary) !important;
  }

  /* Correction pour les blocs dans la section d'information de profil */
  .info-card {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
    box-shadow: var(--shadow-sm) !important;
    
    &::before {
      background-color: var(--accent-color) !important;
    }
    
    &:hover {
      box-shadow: var(--shadow-md) !important;
      background-color: var(--background-card) !important;
    }
    
    mat-card-header {
      background-color: var(--background-card) !important;
      color: var(--text-primary) !important;
      border-color: var(--border-color) !important;
    }
    
    mat-card-title {
      color: var(--text-primary) !important;
    }
    
    mat-card-subtitle {
      color: var(--text-secondary) !important;
    }
    
    mat-card-content {
      background-color: var(--background-card) !important;
      color: var(--text-secondary) !important;
      
      p {
        color: var(--text-secondary) !important;
        
        mat-icon {
          color: var(--primary-color) !important;
        }
      }
    }
    
    mat-card-actions {
      background-color: var(--background-card) !important;
      border-color: var(--border-color) !important;
    }
  }

  /* Correction ultra-spécifique pour les blocs d'items dans la colonne droite */
  .right-column .info-card {
    border-color: var(--border-color) !important;
    
    mat-card-content {
      .item-block {
        background-color: var(--background-alt) !important;
        color: var(--text-primary) !important;
        border-color: var(--border-color) !important;
        
        &:hover {
          background-color: var(--background-alt) !important;
          box-shadow: var(--shadow-sm) !important;
        }
        
        .item-header {
          color: var(--text-primary) !important;
          
          .item-title {
            color: var(--text-primary) !important;
            
            h4 {
              color: var(--text-primary) !important;
            }
            
            span {
              color: var(--text-secondary) !important;
            }
          }
          
          .item-actions {
            color: var(--text-primary) !important;
            
            button {
              color: var(--text-primary) !important;
              
              mat-icon {
                color: var(--primary-color) !important;
              }
            }
          }
        }
        
        .item-content {
          color: var(--text-secondary) !important;
        }
        
        .item-footer {
          color: var(--text-secondary) !important;
          border-color: var(--border-color) !important;
        }
      }
    }
  }

  /* Correction pour les cartes bio */
  .bio-card {
    background: linear-gradient(135deg, var(--background-card) 0%, var(--background-alt) 100%) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
    box-shadow: var(--shadow-sm) !important;
    
    &::before {
      background: linear-gradient(90deg, var(--primary-color), var(--accent-color)) !important;
    }
    
    &:hover {
      box-shadow: var(--shadow-md) !important;
    }
    
    .bio-header {
      border-color: var(--border-color) !important;
      
      .bio-title {
        h2 {
          color: var(--text-primary) !important;
        }
        
        mat-icon {
          color: var(--primary-color) !important;
        }
      }
      
      .bio-actions {
        button {
          color: var(--text-primary) !important;
          
          &:hover {
            background-color: rgba(var(--primary-color-rgb), 0.1) !important;
          }
          
          mat-icon {
            color: var(--text-secondary) !important;
          }
        }
        
        .edit-button:hover mat-icon {
          color: var(--primary-color) !important;
        }
      }
    }
    
    .bio-content {
      p {
        color: var(--text-secondary) !important;
      }
      
      .empty-bio {
        color: var(--text-muted) !important;
        
        mat-icon {
          color: var(--text-muted) !important;
        }
        
        span {
          color: var(--text-muted) !important;
        }
        
        button {
          color: var(--primary-color) !important;
          border-color: var(--primary-color) !important;
        }
      }
    }
  }

  /* Correction pour les cartes CV */
  .cv-card {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
    
    &.has-cv {
      border-left-color: var(--primary-color) !important;
    }
    
    .cv-content {
      .cv-preview {
        .cv-icon {
          background-color: rgba(var(--primary-color-rgb), 0.1) !important;
          
          mat-icon {
            color: var(--primary-color) !important;
          }
        }
        
        .cv-details {
          .cv-name {
            color: var(--text-primary) !important;
          }
          
          .cv-date {
            color: var(--text-secondary) !important;
          }
        }
      }
    }
    
    .no-cv-content {
      .no-cv-icon {
        background-color: var(--background-alt) !important;
        
        mat-icon {
          color: var(--text-muted) !important;
        }
      }
      
      .no-cv-message {
        p {
          color: var(--text-secondary) !important;
        }
        
        .add-cv-btn {
          background-color: var(--primary-color) !important;
          color: var(--primary-contrast) !important;
          
          &::after {
            background-color: var(--accent-color) !important;
          }
        }
      }
    }
  }

  /* Correction pour les éléments de formulaire dans la page de profil */
  .edit-profile-container {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
    box-shadow: var(--shadow-sm) !important;
    
    .modal-header {
      h2 {
        color: var(--text-primary) !important;
      }
      
      .close-button {
        color: var(--text-secondary) !important;
        
        &:hover {
          color: var(--text-primary) !important;
        }
      }
    }
    
    .loading-container {
      color: var(--text-primary) !important;
    }
    
    mat-form-field {
      color: var(--text-primary) !important;
      
      .mat-form-field-label {
        color: var(--text-secondary) !important;
      }
      
      .mat-form-field-underline {
        background-color: var(--border-color) !important;
      }
      
      .mat-form-field-ripple {
        background-color: var(--primary-color) !important;
      }
      
      .mat-form-field-hint {
        color: var(--text-muted) !important;
      }
      
      .mat-error {
        color: var(--error-color) !important;
      }
    }
    
    input, textarea, select {
      background-color: var(--background-alt) !important;
      color: var(--text-primary) !important;
      border-color: var(--border-color) !important;
    }
    
    button {
      &.primary-button {
        background-color: var(--primary-color) !important;
        color: var(--primary-contrast) !important;
      }
      
      &.secondary-button {
        background-color: var(--background-alt) !important;
        color: var(--text-primary) !important;
        border-color: var(--border-color) !important;
      }
    }
  }

  /* Correction pour les éléments de bio dans la page de profil */
  .bio-capsule {
    background-color: var(--background-alt) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
    
    .bio-preview {
      .bio-label {
        span {
          color: var(--primary-color) !important;
          background-color: rgba(var(--primary-color-rgb), 0.1) !important;
        }
      }
      
      .bio-text-container {
        .bio-text {
          color: var(--text-secondary) !important;
        }
        
        .bio-edit-hint {
          mat-icon {
            color: var(--primary-color) !important;
          }
        }
      }
      
      .bio-empty {
        .empty-message {
          .primary-message {
            color: var(--text-primary) !important;
          }
          
          .secondary-message {
            color: var(--text-secondary) !important;
          }
        }
        
        mat-icon {
          color: var(--primary-color) !important;
        }
      }
    }
    
    &.editing {
      background-color: var(--background-card) !important;
      
      .bio-edit-form {
        mat-form-field {
          background-color: var(--background-alt) !important;
          color: var(--text-primary) !important;
          
          textarea {
            background-color: var(--background-alt) !important;
            color: var(--text-primary) !important;
          }
        }
        
        .bio-edit-actions {
          button {
            &.save-btn {
              background-color: var(--primary-color) !important;
              color: var(--primary-contrast) !important;
            }
            
            &.cancel-btn {
              background-color: var(--background-alt) !important;
              color: var(--text-primary) !important;
              border-color: var(--border-color) !important;
            }
          }
        }
      }
    }
  }

  /* Correction pour les éléments de contact dans la page de profil */
  .contact-info {
    background-color: var(--background-alt) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
    
    .contact-item {
      color: var(--text-secondary) !important;
      
      mat-icon {
        color: var(--primary-color) !important;
      }
      
      span {
        color: var(--text-secondary) !important;
      }
    }
  }

  /* Correction pour les éléments d'action dans la page de profil */
  .profile-actions {
    button {
      &.edit-profile-btn {
        background-color: var(--primary-color) !important;
        color: var(--primary-contrast) !important;
      }
      
      &.cv-btn {
        background-color: var(--background-alt) !important;
        color: var(--text-primary) !important;
        border-color: var(--border-color) !important;
      }
      
      &.complete-profile-btn {
        background-color: var(--background-alt) !important;
        color: var(--text-primary) !important;
        border-color: var(--border-color) !important;
      }
      
      mat-icon {
        color: inherit !important;
      }
    }
  }

  /* Correction pour les éléments de grille dans la page de profil */
  mat-grid-list {
    background-color: var(--background-color) !important;
    
    mat-grid-tile {
      background-color: var(--background-color) !important;
    }
  }

  /* Correction pour les éléments de carte dans la page de profil */
  mat-card {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
    box-shadow: var(--shadow-sm) !important;
    
    mat-card-header {
      background-color: var(--background-card) !important;
      color: var(--text-primary) !important;
      border-color: var(--border-color) !important;
    }
    
    mat-card-title {
      color: var(--text-primary) !important;
      
      mat-icon {
        color: var(--primary-color) !important;
      }
    }
    
    mat-card-subtitle {
      color: var(--text-secondary) !important;
    }
    
    mat-card-content {
      background-color: var(--background-card) !important;
      color: var(--text-secondary) !important;
    }
    
    mat-card-actions {
      background-color: var(--background-card) !important;
      border-color: var(--border-color) !important;
    }
  }

  /* Correction pour les éléments de carte avec la classe highlight-card */
  .highlight-card {
    background-color: rgba(var(--primary-color-rgb), 0.05) !important;
    border-color: var(--primary-color) !important;
    
    mat-card-title {
      color: var(--primary-color) !important;
    }
    
    mat-card-content {
      color: var(--text-primary) !important;
    }
    
    .add-now-btn {
      background-color: var(--primary-color) !important;
      color: var(--primary-contrast) !important;
    }
  }
}

/* Correction pour les éléments avec des styles en ligne spécifiques */
.dark-theme {
  [style*="background-color: #f4f4f4"],
  [style*="background-color:#f4f4f4"],
  [style*="background: #f4f4f4"],
  [style*="background:#f4f4f4"] {
    background-color: var(--background-color) !important;
    background: var(--background-color) !important;
  }

  [style*="background-color: #ffffff"],
  [style*="background-color:#ffffff"],
  [style*="background: #ffffff"],
  [style*="background:#ffffff"],
  [style*="background-color: #fff"],
  [style*="background-color:#fff"],
  [style*="background: #fff"],
  [style*="background:#fff"] {
    background-color: var(--background-card) !important;
    background: var(--background-card) !important;
  }

  [style*="background-color: #f8f9fa"],
  [style*="background-color:#f8f9fa"],
  [style*="background: #f8f9fa"],
  [style*="background:#f8f9fa"] {
    background-color: var(--background-alt) !important;
    background: var(--background-alt) !important;
  }

  [style*="color: #333"],
  [style*="color:#333"],
  [style*="color: #1a3c34"],
  [style*="color:#1a3c34"] {
    color: var(--text-primary) !important;
  }

  [style*="color: #444"],
  [style*="color:#444"],
  [style*="color: #666"],
  [style*="color:#666"] {
    color: var(--text-secondary) !important;
  }

  [style*="color: #888"],
  [style*="color:#888"],
  [style*="color: #999"],
  [style*="color:#999"],
  [style*="color: #ccc"],
  [style*="color:#ccc"] {
    color: var(--text-muted) !important;
  }

  [style*="border-color: #e0e0e0"],
  [style*="border-color:#e0e0e0"],
  [style*="border: 1px solid #e0e0e0"],
  [style*="border:1px solid #e0e0e0"],
  [style*="border-color: #e8ecef"],
  [style*="border-color:#e8ecef"],
  [style*="border: 1px solid #e8ecef"],
  [style*="border:1px solid #e8ecef"] {
    border-color: var(--border-color) !important;
  }

  [style*="box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05)"],
  [style*="box-shadow:0 2px 10px rgba(0,0,0,0.05)"] {
    box-shadow: var(--shadow-sm) !important;
  }

  [style*="box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1)"],
  [style*="box-shadow:0 8px 25px rgba(0,0,0,0.1)"] {
    box-shadow: var(--shadow-md) !important;
  }
}

/* Correction pour les éléments avec des classes spécifiques */
.dark-theme {
  .bg-white,
  .bg-light,
  .bg-gray-100,
  .bg-gray-200,
  .bg-gray-300 {
    background-color: var(--background-card) !important;
  }

  .text-dark,
  .text-black,
  .text-gray-800,
  .text-gray-900 {
    color: var(--text-primary) !important;
  }

  .text-muted,
  .text-gray-500,
  .text-gray-600,
  .text-gray-700 {
    color: var(--text-secondary) !important;
  }

  .border,
  .border-light,
  .border-gray-100,
  .border-gray-200,
  .border-gray-300 {
    border-color: var(--border-color) !important;
  }
}
