/**
 * Styles ultra-spécifiques pour les cartes de certification et de lien en mode sombre
 * Ce fichier cible spécifiquement les cartes de certification et de lien
 */

/* Styles pour les cartes de certification et de lien en mode sombre */
.dark-theme {
  /* Styles pour les cartes de certification */
  .certification-card,
  mat-card.certification-card,
  .mat-card.certification-card,
  .mat-mdc-card.certification-card,
  [class*="certification-card"],
  [class*="mat-card"].certification-card,
  [class*="mat-mdc-card"].certification-card,
  [class*="certification"] mat-card,
  [class*="certification"] .mat-card,
  [class*="certification"] .mat-mdc-card,
  [class*="certification"] [class*="mat-card"],
  [class*="certification"] [class*="mat-mdc-card"],
  .certification-item,
  .certification-block,
  .certification-container,
  .certification-section,
  .certification-wrapper,
  .certification-box,
  .certification-area,
  [class*="certification-item"],
  [class*="certification-block"],
  [class*="certification-container"],
  [class*="certification-section"],
  [class*="certification-wrapper"],
  [class*="certification-box"],
  [class*="certification-area"] {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
    box-shadow: var(--shadow-sm) !important;
    
    &:hover {
      box-shadow: var(--shadow-md) !important;
    }
    
    /* En-tête de carte */
    mat-card-header,
    .mat-card-header,
    .mat-mdc-card-header,
    [class*="mat-card-header"],
    [class*="mat-mdc-card-header"],
    .certification-header,
    [class*="certification-header"],
    .header,
    [class*="header"] {
      background-color: var(--background-alt) !important;
      color: var(--text-primary) !important;
      border-color: var(--border-color) !important;
      
      mat-card-title,
      .mat-card-title,
      .mat-mdc-card-title,
      [class*="mat-card-title"],
      [class*="mat-mdc-card-title"],
      .certification-title,
      [class*="certification-title"],
      .title,
      [class*="title"],
      h1, h2, h3, h4, h5, h6 {
        color: var(--text-primary) !important;
        
        mat-icon,
        .mat-icon,
        [class*="mat-icon"],
        .certification-icon,
        [class*="certification-icon"],
        .icon,
        [class*="icon"] {
          color: var(--primary-color) !important;
        }
      }
      
      mat-card-subtitle,
      .mat-card-subtitle,
      .mat-mdc-card-subtitle,
      [class*="mat-card-subtitle"],
      [class*="mat-mdc-card-subtitle"],
      .certification-subtitle,
      [class*="certification-subtitle"],
      .subtitle,
      [class*="subtitle"] {
        color: var(--text-secondary) !important;
      }
    }
    
    /* Contenu de carte */
    mat-card-content,
    .mat-card-content,
    .mat-mdc-card-content,
    [class*="mat-card-content"],
    [class*="mat-mdc-card-content"],
    .certification-content,
    [class*="certification-content"],
    .content,
    [class*="content"] {
      background-color: var(--background-card) !important;
      color: var(--text-secondary) !important;
      
      p, span, div, label {
        color: var(--text-secondary) !important;
        
        mat-icon,
        .mat-icon,
        [class*="mat-icon"],
        .certification-icon,
        [class*="certification-icon"],
        .icon,
        [class*="icon"] {
          color: var(--primary-color) !important;
        }
      }
      
      /* Éléments de bloc */
      .certification-item,
      .certification-block,
      .item,
      .block,
      [class*="certification-item"],
      [class*="certification-block"],
      [class*="item"],
      [class*="block"] {
        background-color: var(--background-alt) !important;
        color: var(--text-primary) !important;
        border-color: var(--border-color) !important;
        
        &:hover {
          background-color: rgba(var(--primary-color-rgb), 0.05) !important;
          box-shadow: var(--shadow-md) !important;
        }
        
        /* En-tête de bloc */
        .certification-item-header,
        .certification-block-header,
        .item-header,
        .block-header,
        [class*="certification-item-header"],
        [class*="certification-block-header"],
        [class*="item-header"],
        [class*="block-header"],
        .header,
        [class*="header"] {
          color: var(--text-primary) !important;
          
          .certification-item-title,
          .certification-block-title,
          .item-title,
          .block-title,
          [class*="certification-item-title"],
          [class*="certification-block-title"],
          [class*="item-title"],
          [class*="block-title"],
          .title,
          [class*="title"],
          h1, h2, h3, h4, h5, h6 {
            color: var(--text-primary) !important;
            
            mat-icon,
            .mat-icon,
            [class*="mat-icon"],
            .certification-icon,
            [class*="certification-icon"],
            .icon,
            [class*="icon"] {
              color: var(--primary-color) !important;
            }
          }
          
          .certification-item-subtitle,
          .certification-block-subtitle,
          .item-subtitle,
          .block-subtitle,
          [class*="certification-item-subtitle"],
          [class*="certification-block-subtitle"],
          [class*="item-subtitle"],
          [class*="block-subtitle"],
          .subtitle,
          [class*="subtitle"] {
            color: var(--text-secondary) !important;
          }
        }
        
        /* Contenu de bloc */
        .certification-item-content,
        .certification-block-content,
        .item-content,
        .block-content,
        [class*="certification-item-content"],
        [class*="certification-block-content"],
        [class*="item-content"],
        [class*="block-content"],
        .content,
        [class*="content"] {
          color: var(--text-secondary) !important;
          
          p, span, div, label {
            color: var(--text-secondary) !important;
            
            mat-icon,
            .mat-icon,
            [class*="mat-icon"],
            .certification-icon,
            [class*="certification-icon"],
            .icon,
            [class*="icon"] {
              color: var(--primary-color) !important;
            }
          }
        }
        
        /* Pied de bloc */
        .certification-item-footer,
        .certification-block-footer,
        .item-footer,
        .block-footer,
        [class*="certification-item-footer"],
        [class*="certification-block-footer"],
        [class*="item-footer"],
        [class*="block-footer"],
        .footer,
        [class*="footer"] {
          color: var(--text-secondary) !important;
          border-color: var(--border-color) !important;
          
          .certification-item-date,
          .certification-block-date,
          .item-date,
          .block-date,
          [class*="certification-item-date"],
          [class*="certification-block-date"],
          [class*="item-date"],
          [class*="block-date"],
          .date,
          [class*="date"] {
            color: var(--text-muted) !important;
          }
        }
      }
    }
    
    /* Actions de carte */
    mat-card-actions,
    .mat-card-actions,
    .mat-mdc-card-actions,
    [class*="mat-card-actions"],
    [class*="mat-mdc-card-actions"],
    .certification-actions,
    [class*="certification-actions"],
    .actions,
    [class*="actions"] {
      background-color: var(--background-card) !important;
      border-color: var(--border-color) !important;
      
      button {
        color: var(--primary-color) !important;
        
        &:hover {
          background-color: rgba(var(--primary-color-rgb), 0.1) !important;
        }
        
        mat-icon,
        .mat-icon,
        [class*="mat-icon"],
        .certification-icon,
        [class*="certification-icon"],
        .icon,
        [class*="icon"] {
          color: var(--primary-color) !important;
        }
      }
    }
    
    /* Pied de carte */
    mat-card-footer,
    .mat-card-footer,
    .mat-mdc-card-footer,
    [class*="mat-card-footer"],
    [class*="mat-mdc-card-footer"],
    .certification-footer,
    [class*="certification-footer"],
    .footer,
    [class*="footer"] {
      background-color: var(--background-card) !important;
      border-color: var(--border-color) !important;
    }
  }

  /* Styles pour les cartes de lien */
  .link-card,
  mat-card.link-card,
  .mat-card.link-card,
  .mat-mdc-card.link-card,
  [class*="link-card"],
  [class*="mat-card"].link-card,
  [class*="mat-mdc-card"].link-card,
  [class*="link"] mat-card,
  [class*="link"] .mat-card,
  [class*="link"] .mat-mdc-card,
  [class*="link"] [class*="mat-card"],
  [class*="link"] [class*="mat-mdc-card"],
  .link-item,
  .link-block,
  .link-container,
  .link-section,
  .link-wrapper,
  .link-box,
  .link-area,
  [class*="link-item"],
  [class*="link-block"],
  [class*="link-container"],
  [class*="link-section"],
  [class*="link-wrapper"],
  [class*="link-box"],
  [class*="link-area"] {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
    box-shadow: var(--shadow-sm) !important;
    
    &:hover {
      box-shadow: var(--shadow-md) !important;
    }
    
    /* En-tête de carte */
    mat-card-header,
    .mat-card-header,
    .mat-mdc-card-header,
    [class*="mat-card-header"],
    [class*="mat-mdc-card-header"],
    .link-header,
    [class*="link-header"],
    .header,
    [class*="header"] {
      background-color: var(--background-alt) !important;
      color: var(--text-primary) !important;
      border-color: var(--border-color) !important;
      
      mat-card-title,
      .mat-card-title,
      .mat-mdc-card-title,
      [class*="mat-card-title"],
      [class*="mat-mdc-card-title"],
      .link-title,
      [class*="link-title"],
      .title,
      [class*="title"],
      h1, h2, h3, h4, h5, h6 {
        color: var(--text-primary) !important;
        
        mat-icon,
        .mat-icon,
        [class*="mat-icon"],
        .link-icon,
        [class*="link-icon"],
        .icon,
        [class*="icon"] {
          color: var(--primary-color) !important;
        }
      }
      
      mat-card-subtitle,
      .mat-card-subtitle,
      .mat-mdc-card-subtitle,
      [class*="mat-card-subtitle"],
      [class*="mat-mdc-card-subtitle"],
      .link-subtitle,
      [class*="link-subtitle"],
      .subtitle,
      [class*="subtitle"] {
        color: var(--text-secondary) !important;
      }
    }
    
    /* Contenu de carte */
    mat-card-content,
    .mat-card-content,
    .mat-mdc-card-content,
    [class*="mat-card-content"],
    [class*="mat-mdc-card-content"],
    .link-content,
    [class*="link-content"],
    .content,
    [class*="content"] {
      background-color: var(--background-card) !important;
      color: var(--text-secondary) !important;
      
      p, span, div, label {
        color: var(--text-secondary) !important;
        
        mat-icon,
        .mat-icon,
        [class*="mat-icon"],
        .link-icon,
        [class*="link-icon"],
        .icon,
        [class*="icon"] {
          color: var(--primary-color) !important;
        }
      }
      
      /* Éléments de bloc */
      .link-item,
      .link-block,
      .item,
      .block,
      [class*="link-item"],
      [class*="link-block"],
      [class*="item"],
      [class*="block"] {
        background-color: var(--background-alt) !important;
        color: var(--text-primary) !important;
        border-color: var(--border-color) !important;
        
        &:hover {
          background-color: rgba(var(--primary-color-rgb), 0.05) !important;
          box-shadow: var(--shadow-md) !important;
        }
        
        /* En-tête de bloc */
        .link-item-header,
        .link-block-header,
        .item-header,
        .block-header,
        [class*="link-item-header"],
        [class*="link-block-header"],
        [class*="item-header"],
        [class*="block-header"],
        .header,
        [class*="header"] {
          color: var(--text-primary) !important;
          
          .link-item-title,
          .link-block-title,
          .item-title,
          .block-title,
          [class*="link-item-title"],
          [class*="link-block-title"],
          [class*="item-title"],
          [class*="block-title"],
          .title,
          [class*="title"],
          h1, h2, h3, h4, h5, h6 {
            color: var(--text-primary) !important;
            
            mat-icon,
            .mat-icon,
            [class*="mat-icon"],
            .link-icon,
            [class*="link-icon"],
            .icon,
            [class*="icon"] {
              color: var(--primary-color) !important;
            }
          }
          
          .link-item-subtitle,
          .link-block-subtitle,
          .item-subtitle,
          .block-subtitle,
          [class*="link-item-subtitle"],
          [class*="link-block-subtitle"],
          [class*="item-subtitle"],
          [class*="block-subtitle"],
          .subtitle,
          [class*="subtitle"] {
            color: var(--text-secondary) !important;
          }
        }
        
        /* Contenu de bloc */
        .link-item-content,
        .link-block-content,
        .item-content,
        .block-content,
        [class*="link-item-content"],
        [class*="link-block-content"],
        [class*="item-content"],
        [class*="block-content"],
        .content,
        [class*="content"] {
          color: var(--text-secondary) !important;
          
          p, span, div, label {
            color: var(--text-secondary) !important;
            
            mat-icon,
            .mat-icon,
            [class*="mat-icon"],
            .link-icon,
            [class*="link-icon"],
            .icon,
            [class*="icon"] {
              color: var(--primary-color) !important;
            }
          }
        }
        
        /* Pied de bloc */
        .link-item-footer,
        .link-block-footer,
        .item-footer,
        .block-footer,
        [class*="link-item-footer"],
        [class*="link-block-footer"],
        [class*="item-footer"],
        [class*="block-footer"],
        .footer,
        [class*="footer"] {
          color: var(--text-secondary) !important;
          border-color: var(--border-color) !important;
          
          .link-item-date,
          .link-block-date,
          .item-date,
          .block-date,
          [class*="link-item-date"],
          [class*="link-block-date"],
          [class*="item-date"],
          [class*="block-date"],
          .date,
          [class*="date"] {
            color: var(--text-muted) !important;
          }
        }
      }
    }
    
    /* Actions de carte */
    mat-card-actions,
    .mat-card-actions,
    .mat-mdc-card-actions,
    [class*="mat-card-actions"],
    [class*="mat-mdc-card-actions"],
    .link-actions,
    [class*="link-actions"],
    .actions,
    [class*="actions"] {
      background-color: var(--background-card) !important;
      border-color: var(--border-color) !important;
      
      button {
        color: var(--primary-color) !important;
        
        &:hover {
          background-color: rgba(var(--primary-color-rgb), 0.1) !important;
        }
        
        mat-icon,
        .mat-icon,
        [class*="mat-icon"],
        .link-icon,
        [class*="link-icon"],
        .icon,
        [class*="icon"] {
          color: var(--primary-color) !important;
        }
      }
    }
    
    /* Pied de carte */
    mat-card-footer,
    .mat-card-footer,
    .mat-mdc-card-footer,
    [class*="mat-card-footer"],
    [class*="mat-mdc-card-footer"],
    .link-footer,
    [class*="link-footer"],
    .footer,
    [class*="footer"] {
      background-color: var(--background-card) !important;
      border-color: var(--border-color) !important;
    }
  }

  /* Styles pour les éléments avec des styles en ligne */
  .certification-card,
  [class*="certification-card"],
  [class*="certification"],
  .link-card,
  [class*="link-card"],
  [class*="link"] {
    &[style*="background-color: #fff"],
    &[style*="background-color:#fff"],
    &[style*="background-color: white"],
    &[style*="background-color:#white"],
    &[style*="background-color: rgb(255, 255, 255)"],
    &[style*="background-color:rgb(255,255,255)"],
    &[style*="background-color: rgba(255, 255, 255"],
    &[style*="background-color:rgba(255,255,255"],
    &[style*="background: #fff"],
    &[style*="background:#fff"],
    &[style*="background: white"],
    &[style*="background:#white"],
    &[style*="background: rgb(255, 255, 255)"],
    &[style*="background:rgb(255,255,255)"],
    &[style*="background: rgba(255, 255, 255"],
    &[style*="background:rgba(255,255,255"],
    &[style*="background-color: #f8f9fa"],
    &[style*="background-color:#f8f9fa"],
    &[style*="background: #f8f9fa"],
    &[style*="background:#f8f9fa"],
    &[style*="background-color: #f4f4f4"],
    &[style*="background-color:#f4f4f4"],
    &[style*="background: #f4f4f4"],
    &[style*="background:#f4f4f4"],
    &[style*="background-color: #f5f5f5"],
    &[style*="background-color:#f5f5f5"],
    &[style*="background: #f5f5f5"],
    &[style*="background:#f5f5f5"],
    &[style*="background-color: #f0f0f0"],
    &[style*="background-color:#f0f0f0"],
    &[style*="background: #f0f0f0"],
    &[style*="background:#f0f0f0"],
    &[style*="background-color: #eeeeee"],
    &[style*="background-color:#eeeeee"],
    &[style*="background: #eeeeee"],
    &[style*="background:#eeeeee"],
    &[style*="background-color: #e9ecef"],
    &[style*="background-color:#e9ecef"],
    &[style*="background: #e9ecef"],
    &[style*="background:#e9ecef"] {
      background-color: var(--background-card) !important;
      background: var(--background-card) !important;
    }
    
    &[style*="color: #000"],
    &[style*="color:#000"],
    &[style*="color: black"],
    &[style*="color:#black"],
    &[style*="color: rgb(0, 0, 0)"],
    &[style*="color:rgb(0,0,0)"],
    &[style*="color: rgba(0, 0, 0"],
    &[style*="color:rgba(0,0,0"],
    &[style*="color: #111111"],
    &[style*="color:#111111"],
    &[style*="color: #222222"],
    &[style*="color:#222222"],
    &[style*="color: #333333"],
    &[style*="color:#333333"],
    &[style*="color: #444444"],
    &[style*="color:#444444"],
    &[style*="color: #1a2a44"],
    &[style*="color:#1a2a44"],
    &[style*="color: #001660"],
    &[style*="color:#001660"] {
      color: var(--text-primary) !important;
    }
    
    &[style*="color: #555555"],
    &[style*="color:#555555"],
    &[style*="color: #666666"],
    &[style*="color:#666666"],
    &[style*="color: #777777"],
    &[style*="color:#777777"],
    &[style*="color: #888888"],
    &[style*="color:#888888"],
    &[style*="color: #4b5563"],
    &[style*="color:#4b5563"] {
      color: var(--text-secondary) !important;
    }
    
    &[style*="color: #999999"],
    &[style*="color:#999999"],
    &[style*="color: #aaaaaa"],
    &[style*="color:#aaaaaa"],
    &[style*="color: #bbbbbb"],
    &[style*="color:#bbbbbb"],
    &[style*="color: #cccccc"],
    &[style*="color:#cccccc"],
    &[style*="color: gray"],
    &[style*="color:gray"],
    &[style*="color: grey"],
    &[style*="color:grey"] {
      color: var(--text-muted) !important;
    }
    
    &[style*="border-color: #ccc"],
    &[style*="border-color:#ccc"],
    &[style*="border-color: #ddd"],
    &[style*="border-color:#ddd"],
    &[style*="border-color: #eee"],
    &[style*="border-color:#eee"],
    &[style*="border: 1px solid #ccc"],
    &[style*="border:1px solid #ccc"],
    &[style*="border: 1px solid #ddd"],
    &[style*="border:1px solid #ddd"],
    &[style*="border: 1px solid #eee"],
    &[style*="border:1px solid #eee"],
    &[style*="border-color: #e0e0e0"],
    &[style*="border-color:#e0e0e0"],
    &[style*="border: 1px solid #e0e0e0"],
    &[style*="border:1px solid #e0e0e0"],
    &[style*="border-color: #e8ecef"],
    &[style*="border-color:#e8ecef"],
    &[style*="border: 1px solid #e8ecef"],
    &[style*="border:1px solid #e8ecef"] {
      border-color: var(--border-color) !important;
    }
  }
}
