/**
 * Styles spécifiques pour les éléments avec des styles en ligne dans la page de profil
 * Ce fichier cible spécifiquement les éléments avec des styles en ligne dans la page de profil
 */

/* Styles pour les éléments avec des styles en ligne dans la page de profil */
.dark-theme {
  /* Sélecteurs pour les éléments avec des styles en ligne spécifiques à la page de profil */
  .profile-container,
  .profile-header,
  .profile-content,
  .left-column,
  .right-column,
  .info-card,
  .bio-card,
  .cv-card,
  .item-block,
  .contact-item,
  .bio-capsule,
  .profile-tabs,
  .profile-main-info,
  .user-main-details,
  .profile-actions,
  .avatar-section,
  .avatar-container,
  .avatar-wrapper,
  .avatar-icon,
  .avatar-overlay,
  .user-identity,
  .user-name-section,
  .user-title,
  .badges-container,
  .company-badge,
  .experience-badge,
  .location-badge,
  .bio-actions-container,
  .bio-short,
  .actions,
  .edit-profile-btn,
  .contact-btn,
  .share-btn,
  .cv-btn,
  .complete-profile-btn,
  .add-btn,
  .add-now-btn,
  .view-cv-btn,
  .delete-cv-btn,
  .edit-bio-btn,
  .save-btn,
  .cancel-btn,
  .item-header,
  .item-title,
  .item-subtitle,
  .item-actions,
  .item-content,
  .item-footer,
  .item-date,
  .empty-message,
  .empty-bio,
  .bio-content,
  .bio-edit-form,
  .bio-edit-actions,
  .cv-content,
  .cv-preview,
  .cv-icon,
  .cv-details,
  .cv-name,
  .cv-date,
  .cv-actions,
  .no-cv-content,
  .no-cv-icon,
  .no-cv-message,
  .info-row,
  .highlight-card,
  .mat-card,
  .mat-card-header,
  .mat-card-title,
  .mat-card-subtitle,
  .mat-card-content,
  .mat-card-actions,
  .mat-card-footer,
  .mat-form-field,
  .mat-form-field-label,
  .mat-form-field-underline,
  .mat-form-field-ripple,
  .mat-form-field-hint,
  .mat-error,
  .mat-button,
  .mat-raised-button,
  .mat-stroked-button,
  .mat-flat-button,
  .mat-icon-button,
  .mat-fab,
  .mat-mini-fab,
  .mat-icon,
  .mat-badge,
  .mat-chip,
  .mat-divider,
  .mat-list,
  .mat-list-item,
  .mat-table,
  .mat-header-cell,
  .mat-cell,
  .mat-row,
  .mat-paginator,
  .mat-sort-header-arrow,
  .mat-expansion-panel,
  .mat-expansion-panel-header,
  .mat-expansion-panel-header-title,
  .mat-expansion-panel-header-description,
  .mat-expansion-indicator,
  .mat-step-header,
  .mat-step-icon,
  .mat-step-label,
  .mat-tab-label,
  .mat-tab-body-content,
  .mat-toolbar,
  .mat-drawer,
  .mat-drawer-container,
  .mat-bottom-sheet-container,
  .mat-autocomplete-panel,
  .mat-tree,
  .mat-tree-node,
  .mat-grid-list,
  .mat-grid-tile,
  .mat-button-toggle-group,
  .mat-button-toggle,
  .mat-button-toggle-checked {
    /* Appliquer les styles du mode sombre */
    &[style*="background-color: #fff"],
    &[style*="background-color:#fff"],
    &[style*="background-color: white"],
    &[style*="background-color:#white"],
    &[style*="background-color: rgb(255, 255, 255)"],
    &[style*="background-color:rgb(255,255,255)"],
    &[style*="background-color: rgba(255, 255, 255"],
    &[style*="background-color:rgba(255,255,255"],
    &[style*="background: #fff"],
    &[style*="background:#fff"],
    &[style*="background: white"],
    &[style*="background:#white"],
    &[style*="background: rgb(255, 255, 255)"],
    &[style*="background:rgb(255,255,255)"],
    &[style*="background: rgba(255, 255, 255"],
    &[style*="background:rgba(255,255,255"],
    &[style*="background-color: #f8f9fa"],
    &[style*="background-color:#f8f9fa"],
    &[style*="background: #f8f9fa"],
    &[style*="background:#f8f9fa"],
    &[style*="background-color: #f4f4f4"],
    &[style*="background-color:#f4f4f4"],
    &[style*="background: #f4f4f4"],
    &[style*="background:#f4f4f4"],
    &[style*="background-color: #f5f5f5"],
    &[style*="background-color:#f5f5f5"],
    &[style*="background: #f5f5f5"],
    &[style*="background:#f5f5f5"],
    &[style*="background-color: #f0f0f0"],
    &[style*="background-color:#f0f0f0"],
    &[style*="background: #f0f0f0"],
    &[style*="background:#f0f0f0"],
    &[style*="background-color: #eeeeee"],
    &[style*="background-color:#eeeeee"],
    &[style*="background: #eeeeee"],
    &[style*="background:#eeeeee"],
    &[style*="background-color: #e9ecef"],
    &[style*="background-color:#e9ecef"],
    &[style*="background: #e9ecef"],
    &[style*="background:#e9ecef"] {
      background-color: var(--background-card) !important;
      background: var(--background-card) !important;
    }

    &[style*="color: #000"],
    &[style*="color:#000"],
    &[style*="color: black"],
    &[style*="color:#black"],
    &[style*="color: rgb(0, 0, 0)"],
    &[style*="color:rgb(0,0,0)"],
    &[style*="color: rgba(0, 0, 0"],
    &[style*="color:rgba(0,0,0"],
    &[style*="color: #111111"],
    &[style*="color:#111111"],
    &[style*="color: #222222"],
    &[style*="color:#222222"],
    &[style*="color: #333333"],
    &[style*="color:#333333"],
    &[style*="color: #444444"],
    &[style*="color:#444444"],
    &[style*="color: #1a2a44"],
    &[style*="color:#1a2a44"],
    &[style*="color: #001660"],
    &[style*="color:#001660"] {
      color: var(--text-primary) !important;
    }

    &[style*="color: #555555"],
    &[style*="color:#555555"],
    &[style*="color: #666666"],
    &[style*="color:#666666"],
    &[style*="color: #777777"],
    &[style*="color:#777777"],
    &[style*="color: #888888"],
    &[style*="color:#888888"],
    &[style*="color: #4b5563"],
    &[style*="color:#4b5563"] {
      color: var(--text-secondary) !important;
    }

    &[style*="color: #999999"],
    &[style*="color:#999999"],
    &[style*="color: #aaaaaa"],
    &[style*="color:#aaaaaa"],
    &[style*="color: #bbbbbb"],
    &[style*="color:#bbbbbb"],
    &[style*="color: #cccccc"],
    &[style*="color:#cccccc"],
    &[style*="color: gray"],
    &[style*="color:gray"],
    &[style*="color: grey"],
    &[style*="color:grey"] {
      color: var(--text-muted) !important;
    }

    &[style*="border-color: #ccc"],
    &[style*="border-color:#ccc"],
    &[style*="border-color: #ddd"],
    &[style*="border-color:#ddd"],
    &[style*="border-color: #eee"],
    &[style*="border-color:#eee"],
    &[style*="border: 1px solid #ccc"],
    &[style*="border:1px solid #ccc"],
    &[style*="border: 1px solid #ddd"],
    &[style*="border:1px solid #ddd"],
    &[style*="border: 1px solid #eee"],
    &[style*="border:1px solid #eee"],
    &[style*="border-color: #e0e0e0"],
    &[style*="border-color:#e0e0e0"],
    &[style*="border: 1px solid #e0e0e0"],
    &[style*="border:1px solid #e0e0e0"],
    &[style*="border-color: #e8ecef"],
    &[style*="border-color:#e8ecef"],
    &[style*="border: 1px solid #e8ecef"],
    &[style*="border:1px solid #e8ecef"] {
      border-color: var(--border-color) !important;
    }

    &[style*="box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05)"],
    &[style*="box-shadow:0 2px 10px rgba(0,0,0,0.05)"],
    &[style*="box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1)"],
    &[style*="box-shadow:0 2px 5px rgba(0,0,0,0.1)"] {
      box-shadow: var(--shadow-sm) !important;
    }

    &[style*="box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1)"],
    &[style*="box-shadow:0 8px 25px rgba(0,0,0,0.1)"],
    &[style*="box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2)"],
    &[style*="box-shadow:0 4px 12px rgba(0,0,0,0.2)"] {
      box-shadow: var(--shadow-md) !important;
    }
  }

  /* Sélecteurs pour les éléments avec des classes spécifiques à la page de profil */
  .profile-container,
  .profile-header,
  .profile-content,
  .left-column,
  .right-column {
    .bg-white,
    .bg-light,
    .bg-gray-100,
    .bg-gray-200,
    .bg-gray-300 {
      background-color: var(--background-card) !important;
    }

    .text-dark,
    .text-black,
    .text-gray-800,
    .text-gray-900 {
      color: var(--text-primary) !important;
    }

    .text-muted,
    .text-gray-500,
    .text-gray-600,
    .text-gray-700 {
      color: var(--text-secondary) !important;
    }

    .border,
    .border-light,
    .border-gray-100,
    .border-gray-200,
    .border-gray-300 {
      border-color: var(--border-color) !important;
    }
  }

  /* Sélecteurs pour les éléments avec des attributs spécifiques à la page de profil */
  .profile-container,
  .profile-header,
  .profile-content,
  .left-column,
  .right-column {
    [class*="mat-elevation-z"] {
      background-color: var(--background-card) !important;
      color: var(--text-primary) !important;
    }
  }
}
