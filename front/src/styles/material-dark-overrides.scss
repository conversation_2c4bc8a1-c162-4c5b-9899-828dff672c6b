/**
 * Styles spécifiques pour les composants Angular Material en mode sombre
 * Ces styles remplacent les styles par défaut de Angular Material
 */

.dark-theme {
  // Styles pour les composants Angular Material spécifiques
  
  // Inputs et form fields
  .mat-mdc-form-field-focus-overlay {
    background-color: rgba(66, 153, 225, 0.1) !important;
  }
  
  .mat-mdc-form-field-infix {
    color: var(--text-primary) !important;
  }
  
  .mat-mdc-select-value-text {
    color: var(--text-primary) !important;
  }
  
  .mat-mdc-select-arrow {
    color: var(--text-secondary) !important;
  }
  
  .mat-mdc-select-panel {
    background-color: var(--background-card) !important;
  }
  
  .mat-mdc-option {
    color: var(--text-primary) !important;
  }
  
  .mat-mdc-option:hover:not(.mat-option-disabled) {
    background-color: var(--background-alt) !important;
  }
  
  // Buttons
  .mat-mdc-button {
    color: var(--text-primary) !important;
  }
  
  .mat-mdc-raised-button.mat-primary {
    background-color: var(--primary-color) !important;
    color: var(--primary-contrast) !important;
  }
  
  .mat-mdc-raised-button.mat-accent {
    background-color: var(--accent-color) !important;
    color: var(--accent-contrast) !important;
  }
  
  .mat-mdc-stroked-button {
    border-color: var(--border-color) !important;
    color: var(--text-primary) !important;
  }
  
  // Cards
  .mat-mdc-card {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
  }
  
  .mat-mdc-card-title {
    color: var(--text-primary) !important;
  }
  
  .mat-mdc-card-subtitle {
    color: var(--text-secondary) !important;
  }
  
  .mat-mdc-card-content {
    color: var(--text-secondary) !important;
  }
  
  // Dialogs
  .mat-mdc-dialog-container {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
  }
  
  .mat-mdc-dialog-title {
    color: var(--text-primary) !important;
  }
  
  .mat-mdc-dialog-content {
    color: var(--text-secondary) !important;
  }
  
  // Tabs
  .mat-mdc-tab-header {
    background-color: var(--background-alt) !important;
  }
  
  .mat-mdc-tab-label-content {
    color: var(--text-primary) !important;
  }
  
  .mat-mdc-tab-group.mat-primary .mat-mdc-tab.mat-mdc-tab-selected .mat-mdc-tab-label-content {
    color: var(--primary-color) !important;
  }
  
  .mat-mdc-tab-body-content {
    color: var(--text-primary) !important;
  }
  
  // Menus
  .mat-mdc-menu-panel {
    background-color: var(--background-card) !important;
  }
  
  .mat-mdc-menu-item {
    color: var(--text-primary) !important;
  }
  
  .mat-mdc-menu-item:hover:not([disabled]) {
    background-color: var(--background-alt) !important;
  }
  
  // Toolbars
  .mat-toolbar {
    background-color: var(--background-alt) !important;
    color: var(--text-primary) !important;
  }
  
  // Sidenav
  .mat-drawer {
    background-color: var(--background-alt) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
  }
  
  .mat-drawer-container {
    background-color: var(--background-color) !important;
    color: var(--text-primary) !important;
  }
  
  // Lists
  .mat-mdc-list-item {
    color: var(--text-primary) !important;
  }
  
  .mat-mdc-list-item-title {
    color: var(--text-primary) !important;
  }
  
  .mat-mdc-list-item-line {
    color: var(--text-secondary) !important;
  }
  
  // Dividers
  .mat-divider {
    border-top-color: var(--border-color) !important;
  }
  
  // Checkboxes
  .mat-mdc-checkbox-frame {
    border-color: var(--border-color) !important;
  }
  
  .mat-mdc-checkbox-checked .mat-mdc-checkbox-background {
    background-color: var(--primary-color) !important;
  }
  
  // Radio buttons
  .mat-mdc-radio-button .mat-radio-outer-circle {
    border-color: var(--border-color) !important;
  }
  
  .mat-mdc-radio-checked .mat-radio-outer-circle {
    border-color: var(--primary-color) !important;
  }
  
  .mat-mdc-radio-inner-circle {
    background-color: var(--primary-color) !important;
  }
  
  // Sliders
  .mat-mdc-slider-track-background {
    background-color: var(--border-color) !important;
  }
  
  .mat-mdc-slider-track-fill {
    background-color: var(--primary-color) !important;
  }
  
  .mat-mdc-slider-thumb {
    background-color: var(--primary-color) !important;
  }
  
  // Progress bars
  .mat-mdc-progress-bar-buffer {
    background-color: var(--background-alt) !important;
  }
  
  .mat-mdc-progress-bar-fill::after {
    background-color: var(--primary-color) !important;
  }
  
  // Spinners
  .mat-mdc-progress-spinner circle {
    stroke: var(--primary-color) !important;
  }
  
  // Tooltips
  .mat-mdc-tooltip {
    background-color: var(--background-alt) !important;
    color: var(--text-primary) !important;
  }
  
  // Snackbars
  .mat-mdc-snack-bar-container {
    background-color: var(--background-alt) !important;
    color: var(--text-primary) !important;
  }
  
  // Chips
  .mat-mdc-chip {
    background-color: var(--background-alt) !important;
    color: var(--text-primary) !important;
  }
  
  // Tables
  .mat-mdc-table {
    background-color: var(--background-card) !important;
  }
  
  .mat-mdc-header-cell {
    color: var(--text-primary) !important;
    background-color: var(--background-alt) !important;
  }
  
  .mat-mdc-cell {
    color: var(--text-secondary) !important;
  }
  
  .mat-mdc-row:hover {
    background-color: var(--background-alt) !important;
  }
  
  // Paginator
  .mat-mdc-paginator {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
  }
  
  // Sort header
  .mat-sort-header-arrow {
    color: var(--text-primary) !important;
  }
  
  // Expansion panels
  .mat-expansion-panel {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
  }
  
  .mat-expansion-panel-header {
    background-color: var(--background-card) !important;
  }
  
  .mat-expansion-panel-header-title {
    color: var(--text-primary) !important;
  }
  
  .mat-expansion-panel-header-description {
    color: var(--text-secondary) !important;
  }
  
  .mat-expansion-indicator::after {
    color: var(--text-primary) !important;
  }
}
