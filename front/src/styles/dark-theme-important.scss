/**
 * Styles pour forcer l'application du mode sombre aux éléments avec des styles générés dynamiquement
 * Ce fichier contient des styles avec !important pour surcharger les styles générés dynamiquement
 */

/* Forcer l'application du mode sombre à tous les éléments */
.dark-theme {
  /* Couleurs de fond */
  body,
  html,
  .app-container,
  .main-container,
  .content-container,
  .page-container,
  .mat-app-background {
    background-color: var(--background-color) !important;
    color: var(--text-primary) !important;
  }

  /* Cartes et conteneurs */
  .mat-card,
  .mat-mdc-card,
  .card,
  .container,
  .panel,
  .box,
  .section,
  .content,
  .main,
  .header,
  .footer,
  .sidebar,
  .nav,
  .menu,
  .toolbar,
  .dialog,
  .modal,
  .popup,
  .tooltip,
  .dropdown,
  .form,
  .input,
  .button,
  .table,
  .list,
  .item,
  .row,
  .column,
  .grid,
  .flex,
  .block,
  .inline,
  .hidden,
  .visible,
  .active,
  .inactive,
  .disabled,
  .enabled,
  .selected,
  .unselected,
  .checked,
  .unchecked,
  .valid,
  .invalid,
  .error,
  .success,
  .warning,
  .info,
  .primary,
  .secondary,
  .tertiary,
  .accent,
  .light,
  .dark,
  .white,
  .black,
  .gray,
  .red,
  .green,
  .blue,
  .yellow,
  .orange,
  .purple,
  .pink,
  .brown,
  .cyan,
  .magenta,
  .teal,
  .indigo,
  .violet,
  .amber,
  .lime,
  .emerald,
  .ruby,
  .sapphire,
  .topaz,
  .diamond,
  .pearl,
  .gold,
  .silver,
  .bronze,
  .platinum,
  .titanium,
  .steel,
  .iron,
  .copper,
  .brass,
  .chrome,
  .nickel,
  .zinc,
  .tin,
  .lead,
  .aluminum,
  .cobalt,
  .nickel,
  .tungsten,
  .uranium,
  .plutonium,
  .radium,
  .neon,
  .argon,
  .krypton,
  .xenon,
  .radon,
  .helium,
  .hydrogen,
  .oxygen,
  .nitrogen,
  .carbon,
  .sulfur,
  .phosphorus,
  .silicon,
  .boron,
  .arsenic,
  .selenium,
  .tellurium,
  .iodine,
  .bromine,
  .chlorine,
  .fluorine,
  .sodium,
  .potassium,
  .calcium,
  .magnesium,
  .lithium,
  .beryllium,
  .strontium,
  .barium,
  .radium,
  .francium,
  .cesium,
  .rubidium {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
  }

  /* Éléments de formulaire */
  input,
  textarea,
  select,
  button,
  .mat-mdc-form-field,
  .mat-mdc-input-element,
  .mat-mdc-select,
  .mat-mdc-select-value,
  .mat-mdc-select-arrow,
  .mat-mdc-form-field-label {
    background-color: var(--background-alt) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
  }

  /* Éléments de navigation */
  .mat-toolbar,
  .mat-sidenav,
  .mat-drawer,
  .mat-drawer-container,
  .mat-drawer-content,
  .mat-sidenav-container,
  .mat-sidenav-content {
    background-color: var(--background-alt) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
  }

  /* Éléments de dialogue */
  .mat-mdc-dialog-container,
  .mat-mdc-dialog-content,
  .mat-mdc-dialog-actions,
  .mat-mdc-dialog-title {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
  }

  /* Éléments de menu */
  .mat-mdc-menu-panel,
  .mat-mdc-menu-content,
  .mat-mdc-menu-item {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
  }

  /* Éléments de liste */
  .mat-mdc-list,
  .mat-mdc-list-item,
  .mat-mdc-list-item-title,
  .mat-mdc-list-item-line {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
  }

  /* Éléments de tableau */
  .mat-mdc-table,
  .mat-mdc-header-row,
  .mat-mdc-row,
  .mat-mdc-header-cell,
  .mat-mdc-cell {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
  }

  /* Éléments de pagination */
  .mat-mdc-paginator,
  .mat-mdc-paginator-container,
  .mat-mdc-paginator-range-label,
  .mat-mdc-paginator-page-size,
  .mat-mdc-paginator-page-size-label,
  .mat-mdc-paginator-page-size-select {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
  }

  /* Éléments de bouton */
  .mat-mdc-button,
  .mat-mdc-raised-button,
  .mat-mdc-stroked-button,
  .mat-mdc-flat-button,
  .mat-mdc-icon-button {
    color: var(--text-primary) !important;
  }

  .mat-mdc-raised-button.mat-primary {
    background-color: var(--primary-color) !important;
    color: var(--primary-contrast) !important;
  }

  .mat-mdc-raised-button.mat-accent {
    background-color: var(--accent-color) !important;
    color: var(--accent-contrast) !important;
  }

  /* Éléments de grille */
  mat-grid-list,
  mat-grid-tile {
    background-color: var(--background-color) !important;
    color: var(--text-primary) !important;
  }

  /* Éléments de texte */
  h1, h2, h3, h4, h5, h6 {
    color: var(--text-primary) !important;
  }

  p, span, div {
    color: var(--text-secondary) !important;
  }

  a {
    color: var(--primary-color) !important;
  }

  a:hover {
    color: var(--primary-light) !important;
  }

  /* Éléments avec des classes spécifiques */
  .bg-white,
  .bg-light,
  .bg-gray-100,
  .bg-gray-200,
  .bg-gray-300 {
    background-color: var(--background-card) !important;
  }

  .text-dark,
  .text-black,
  .text-gray-800,
  .text-gray-900 {
    color: var(--text-primary) !important;
  }

  .text-muted,
  .text-gray-500,
  .text-gray-600,
  .text-gray-700 {
    color: var(--text-secondary) !important;
  }

  .border,
  .border-light,
  .border-gray-100,
  .border-gray-200,
  .border-gray-300 {
    border-color: var(--border-color) !important;
  }

  /* Éléments avec des attributs spécifiques */
  [class*="mat-elevation-z"] {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
  }

  /* Éléments avec des styles en ligne spécifiques */
  [style*="background-color: #fff"],
  [style*="background-color:#fff"],
  [style*="background-color: white"],
  [style*="background-color:#white"],
  [style*="background-color: rgb(255, 255, 255)"],
  [style*="background-color:rgb(255,255,255)"],
  [style*="background-color: rgba(255, 255, 255"],
  [style*="background-color:rgba(255,255,255"],
  [style*="background: #fff"],
  [style*="background:#fff"],
  [style*="background: white"],
  [style*="background:#white"],
  [style*="background: rgb(255, 255, 255)"],
  [style*="background:rgb(255,255,255)"],
  [style*="background: rgba(255, 255, 255"],
  [style*="background:rgba(255,255,255"] {
    background-color: var(--background-card) !important;
    background: var(--background-card) !important;
  }

  [style*="color: #000"],
  [style*="color:#000"],
  [style*="color: black"],
  [style*="color:#black"],
  [style*="color: rgb(0, 0, 0)"],
  [style*="color:rgb(0,0,0)"],
  [style*="color: rgba(0, 0, 0"],
  [style*="color:rgba(0,0,0"] {
    color: var(--text-primary) !important;
  }

  [style*="color: #333"],
  [style*="color:#333"],
  [style*="color: #444"],
  [style*="color:#444"],
  [style*="color: #555"],
  [style*="color:#555"],
  [style*="color: rgb(51, 51, 51)"],
  [style*="color:rgb(51,51,51)"],
  [style*="color: rgba(51, 51, 51"],
  [style*="color:rgba(51,51,51"] {
    color: var(--text-primary) !important;
  }

  [style*="color: #666"],
  [style*="color:#666"],
  [style*="color: #777"],
  [style*="color:#777"],
  [style*="color: #888"],
  [style*="color:#888"],
  [style*="color: rgb(102, 102, 102)"],
  [style*="color:rgb(102,102,102)"],
  [style*="color: rgba(102, 102, 102"],
  [style*="color:rgba(102,102,102"] {
    color: var(--text-secondary) !important;
  }

  [style*="color: #999"],
  [style*="color:#999"],
  [style*="color: #aaa"],
  [style*="color:#aaa"],
  [style*="color: #bbb"],
  [style*="color:#bbb"],
  [style*="color: rgb(153, 153, 153)"],
  [style*="color:rgb(153,153,153)"],
  [style*="color: rgba(153, 153, 153"],
  [style*="color:rgba(153,153,153"] {
    color: var(--text-muted) !important;
  }

  [style*="border-color: #ccc"],
  [style*="border-color:#ccc"],
  [style*="border-color: #ddd"],
  [style*="border-color:#ddd"],
  [style*="border-color: #eee"],
  [style*="border-color:#eee"],
  [style*="border: 1px solid #ccc"],
  [style*="border:1px solid #ccc"],
  [style*="border: 1px solid #ddd"],
  [style*="border:1px solid #ddd"],
  [style*="border: 1px solid #eee"],
  [style*="border:1px solid #eee"] {
    border-color: var(--border-color) !important;
  }

  [style*="box-shadow:"],
  [style*="box-shadow: "] {
    box-shadow: var(--shadow-md) !important;
  }
}
