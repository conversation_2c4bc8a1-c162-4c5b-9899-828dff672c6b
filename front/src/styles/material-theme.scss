// Styles pour les composants Material Design en mode sombre

// Cartes
.mat-mdc-card {
  background-color: var(--background-card);
  color: var(--text-primary);
  border-color: var(--border-color);
  box-shadow: var(--shadow-sm);
}

// Boutons
.mat-mdc-button, 
.mat-mdc-raised-button, 
.mat-mdc-outlined-button,
.mat-mdc-icon-button {
  color: var(--text-primary);
}

.mat-mdc-raised-button.mat-primary {
  background-color: var(--primary-color);
  color: var(--primary-contrast);
}

.mat-mdc-raised-button.mat-accent {
  background-color: var(--accent-color);
  color: var(--accent-contrast);
}

// Formulaires
.mat-mdc-form-field {
  color: var(--text-primary);
}

.mat-mdc-form-field-label {
  color: var(--text-secondary);
}

.mat-mdc-input-element {
  color: var(--text-primary);
}

.mat-mdc-form-field-underline {
  background-color: var(--border-color);
}

// Menus
.mat-mdc-menu-panel {
  background-color: var(--background-card);
}

.mat-mdc-menu-item {
  color: var(--text-primary);
}

// Dialogues
.mat-mdc-dialog-container {
  background-color: var(--background-card);
  color: var(--text-primary);
}

// Tableaux
.mat-mdc-table {
  background-color: var(--background-card);
}

.mat-mdc-header-cell, .mat-mdc-cell {
  color: var(--text-primary);
}

.mat-mdc-row:hover {
  background-color: var(--background-alt);
}

// Onglets
.mat-mdc-tab-header {
  background-color: var(--background-alt);
}

.mat-mdc-tab-label {
  color: var(--text-primary);
}

.mat-mdc-tab-group.mat-primary .mat-mdc-tab.mat-mdc-tab-selected .mat-mdc-tab-label-content {
  color: var(--primary-color);
}

// Barres de progression
.mat-mdc-progress-bar {
  background-color: var(--background-alt);
}

.mat-mdc-progress-bar-fill::after {
  background-color: var(--primary-color);
}

// Sélecteurs
.mat-mdc-select-value {
  color: var(--text-primary);
}

.mat-mdc-select-arrow {
  color: var(--text-secondary);
}

// Checkboxes et radio buttons
.mat-mdc-checkbox-frame {
  border-color: var(--border-color);
}

.mat-mdc-checkbox-checked .mat-mdc-checkbox-background {
  background-color: var(--primary-color);
}

.mat-mdc-radio-button .mat-mdc-radio-outer-circle {
  border-color: var(--border-color);
}

.mat-mdc-radio-button.mat-mdc-radio-checked .mat-mdc-radio-outer-circle {
  border-color: var(--primary-color);
}

.mat-mdc-radio-button .mat-mdc-radio-inner-circle {
  background-color: var(--primary-color);
}

// Toolbar
.mat-toolbar {
  background-color: var(--background-alt);
  color: var(--text-primary);
}

// Sidenav
.mat-sidenav {
  background-color: var(--background-alt);
  color: var(--text-primary);
  border-color: var(--border-color);
}

.mat-sidenav-content {
  background-color: var(--background-color);
  color: var(--text-primary);
}

// Divider
.mat-divider {
  border-top-color: var(--border-color);
}

// Badges
.mat-badge-content {
  background-color: var(--accent-color);
  color: var(--accent-contrast);
}

// Tooltips
.mat-tooltip {
  background-color: var(--background-alt);
  color: var(--text-primary);
}

// Snackbar
.mat-snack-bar-container {
  background-color: var(--background-alt);
  color: var(--text-primary);
}

// Chips
.mat-chip {
  background-color: var(--background-alt);
  color: var(--text-primary);
}

// Sliders
.mat-slider-track-background {
  background-color: var(--border-color);
}

.mat-slider-track-fill {
  background-color: var(--primary-color);
}

.mat-slider-thumb {
  background-color: var(--primary-color);
}

// Expansion panels
.mat-expansion-panel {
  background-color: var(--background-card);
  color: var(--text-primary);
}

.mat-expansion-panel-header {
  background-color: var(--background-card);
}

.mat-expansion-panel-header-title,
.mat-expansion-panel-header-description {
  color: var(--text-primary);
}

// Stepper
.mat-step-header .mat-step-icon {
  background-color: var(--primary-color);
  color: var(--primary-contrast);
}

.mat-step-header .mat-step-label {
  color: var(--text-primary);
}

// Datepicker
.mat-calendar {
  background-color: var(--background-card);
  color: var(--text-primary);
}

.mat-calendar-body-cell-content {
  color: var(--text-primary);
}

.mat-calendar-body-selected {
  background-color: var(--primary-color);
  color: var(--primary-contrast);
}

// Autocomplete
.mat-autocomplete-panel {
  background-color: var(--background-card);
  color: var(--text-primary);
}

.mat-option {
  color: var(--text-primary);
}

.mat-option:hover:not(.mat-option-disabled) {
  background-color: var(--background-alt);
}

// Paginator
.mat-paginator {
  background-color: var(--background-card);
  color: var(--text-primary);
}

// Sort header
.mat-sort-header-arrow {
  color: var(--text-primary);
}

// Transitions
.mat-mdc-card,
.mat-mdc-button,
.mat-mdc-raised-button,
.mat-mdc-outlined-button,
.mat-mdc-icon-button,
.mat-mdc-form-field,
.mat-mdc-input-element,
.mat-mdc-menu-panel,
.mat-mdc-menu-item,
.mat-mdc-dialog-container,
.mat-mdc-table,
.mat-mdc-header-cell,
.mat-mdc-cell,
.mat-mdc-row,
.mat-mdc-tab-header,
.mat-mdc-tab-label,
.mat-toolbar,
.mat-sidenav,
.mat-sidenav-content,
.mat-divider,
.mat-expansion-panel,
.mat-expansion-panel-header {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}
