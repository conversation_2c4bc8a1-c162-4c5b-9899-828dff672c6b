/**
 * Styles pour forcer l'application du mode sombre aux éléments récalcitrants
 * Ce fichier contient des sélecteurs très spécifiques pour cibler les éléments
 * qui ne changent pas de thème avec les sélecteurs génériques
 */

/* Force l'application du mode sombre à tous les éléments du body */
body.dark-theme {
  background-color: var(--background-color) !important;
  color: var(--text-primary) !important;
}

/* Force l'application du mode sombre aux éléments avec des styles en ligne spécifiques */
.dark-theme {
  /* Éléments avec fond blanc */
  [style*="background-color: #fff"],
  [style*="background-color:#fff"],
  [style*="background-color: white"],
  [style*="background-color:#white"],
  [style*="background-color: rgb(255, 255, 255)"],
  [style*="background-color:rgb(255,255,255)"],
  [style*="background-color: rgba(255, 255, 255"],
  [style*="background-color:rgba(255,255,255"],
  [style*="background: #fff"],
  [style*="background:#fff"],
  [style*="background: white"],
  [style*="background:#white"],
  [style*="background: rgb(255, 255, 255)"],
  [style*="background:rgb(255,255,255)"],
  [style*="background: rgba(255, 255, 255"],
  [style*="background:rgba(255,255,255"] {
    background-color: var(--background-card) !important;
    background: var(--background-card) !important;
  }

  /* Éléments avec texte noir */
  [style*="color: #000"],
  [style*="color:#000"],
  [style*="color: black"],
  [style*="color:#black"],
  [style*="color: rgb(0, 0, 0)"],
  [style*="color:rgb(0,0,0)"],
  [style*="color: rgba(0, 0, 0"],
  [style*="color:rgba(0,0,0"] {
    color: var(--text-primary) !important;
  }

  /* Éléments avec texte gris foncé */
  [style*="color: #333"],
  [style*="color:#333"],
  [style*="color: #444"],
  [style*="color:#444"],
  [style*="color: #555"],
  [style*="color:#555"],
  [style*="color: rgb(51, 51, 51)"],
  [style*="color:rgb(51,51,51)"],
  [style*="color: rgba(51, 51, 51"],
  [style*="color:rgba(51,51,51"] {
    color: var(--text-primary) !important;
  }

  /* Éléments avec texte gris moyen */
  [style*="color: #666"],
  [style*="color:#666"],
  [style*="color: #777"],
  [style*="color:#777"],
  [style*="color: #888"],
  [style*="color:#888"],
  [style*="color: rgb(102, 102, 102)"],
  [style*="color:rgb(102,102,102)"],
  [style*="color: rgba(102, 102, 102"],
  [style*="color:rgba(102,102,102"] {
    color: var(--text-secondary) !important;
  }

  /* Éléments avec texte gris clair */
  [style*="color: #999"],
  [style*="color:#999"],
  [style*="color: #aaa"],
  [style*="color:#aaa"],
  [style*="color: #bbb"],
  [style*="color:#bbb"],
  [style*="color: rgb(153, 153, 153)"],
  [style*="color:rgb(153,153,153)"],
  [style*="color: rgba(153, 153, 153"],
  [style*="color:rgba(153,153,153"] {
    color: var(--text-muted) !important;
  }

  /* Éléments avec bordures grises */
  [style*="border-color: #ccc"],
  [style*="border-color:#ccc"],
  [style*="border-color: #ddd"],
  [style*="border-color:#ddd"],
  [style*="border-color: #eee"],
  [style*="border-color:#eee"],
  [style*="border: 1px solid #ccc"],
  [style*="border:1px solid #ccc"],
  [style*="border: 1px solid #ddd"],
  [style*="border:1px solid #ddd"],
  [style*="border: 1px solid #eee"],
  [style*="border:1px solid #eee"] {
    border-color: var(--border-color) !important;
  }

  /* Éléments avec ombres */
  [style*="box-shadow:"],
  [style*="box-shadow: "] {
    box-shadow: var(--shadow-md) !important;
  }
}

/* Force l'application du mode sombre aux composants Angular Material spécifiques */
.dark-theme {
  /* Dialogs */
  .mat-dialog-container,
  .cdk-overlay-container .cdk-overlay-pane,
  .cdk-overlay-container .cdk-global-overlay-wrapper {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
  }

  /* Overlays */
  .cdk-overlay-backdrop.cdk-overlay-backdrop-showing {
    opacity: 0.85 !important;
  }

  .cdk-overlay-dark-backdrop {
    background: rgba(0, 0, 0, 0.6) !important;
  }

  /* Menus */
  .mat-menu-panel,
  .mat-menu-content,
  .cdk-overlay-connected-position-bounding-box {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
  }

  /* Tooltips */
  .mat-tooltip {
    background-color: var(--background-alt) !important;
    color: var(--text-primary) !important;
  }

  /* Snackbars */
  .mat-snack-bar-container {
    background-color: var(--background-alt) !important;
    color: var(--text-primary) !important;
  }

  /* Autocomplete */
  .mat-autocomplete-panel {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
  }

  /* Select */
  .mat-select-panel {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
  }

  /* Datepicker */
  .mat-datepicker-content {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
  }

  /* Bottom sheet */
  .mat-bottom-sheet-container {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
  }
}

/* Force l'application du mode sombre aux composants générés dynamiquement */
.dark-theme {
  /* Éléments générés par Angular */
  [class*="_ngcontent-"],
  [class*="_nghost-"] {
    color: inherit !important;
    background-color: inherit !important;
  }

  /* Éléments générés par des bibliothèques tierces */
  [class*="ng-"],
  [class*="ngx-"],
  [class*="angular-"] {
    color: inherit !important;
    background-color: inherit !important;
  }

  /* Éléments générés par des frameworks CSS */
  [class*="bootstrap-"],
  [class*="mui-"],
  [class*="tailwind-"] {
    color: inherit !important;
    background-color: inherit !important;
  }
}

/* Force l'application du mode sombre aux iframes et aux éléments embarqués */
.dark-theme {
  iframe,
  embed,
  object {
    filter: invert(90%) hue-rotate(180deg) !important;
  }

  /* Exceptions pour les vidéos et les images */
  iframe[src*="youtube.com"],
  iframe[src*="vimeo.com"],
  iframe[src*="dailymotion.com"],
  iframe[src*=".mp4"],
  iframe[src*=".webm"],
  iframe[src*=".ogg"],
  iframe[src*=".jpg"],
  iframe[src*=".jpeg"],
  iframe[src*=".png"],
  iframe[src*=".gif"],
  iframe[src*=".svg"] {
    filter: none !important;
  }
}

/* Force l'application du mode sombre aux éléments avec des classes spécifiques */
.dark-theme {
  .card,
  .panel,
  .container,
  .wrapper,
  .box,
  .section,
  .content,
  .main,
  .header,
  .footer,
  .sidebar,
  .nav,
  .menu,
  .toolbar,
  .dialog,
  .modal,
  .popup,
  .tooltip,
  .dropdown,
  .form,
  .input,
  .button,
  .table,
  .list,
  .item,
  .row,
  .column,
  .grid,
  .flex,
  .block,
  .inline,
  .hidden,
  .visible,
  .active,
  .inactive,
  .disabled,
  .enabled,
  .selected,
  .unselected,
  .checked,
  .unchecked,
  .valid,
  .invalid,
  .error,
  .success,
  .warning,
  .info,
  .primary,
  .secondary,
  .tertiary,
  .accent,
  .light,
  .dark,
  .white,
  .black,
  .gray,
  .red,
  .green,
  .blue,
  .yellow,
  .orange,
  .purple,
  .pink,
  .brown,
  .cyan,
  .magenta,
  .teal,
  .indigo,
  .violet,
  .amber,
  .lime,
  .emerald,
  .ruby,
  .sapphire,
  .topaz,
  .diamond,
  .pearl,
  .gold,
  .silver,
  .bronze,
  .platinum,
  .titanium,
  .steel,
  .iron,
  .copper,
  .brass,
  .chrome,
  .nickel,
  .zinc,
  .tin,
  .lead,
  .aluminum,
  .cobalt,
  .nickel,
  .tungsten,
  .uranium,
  .plutonium,
  .radium,
  .neon,
  .argon,
  .krypton,
  .xenon,
  .radon,
  .helium,
  .hydrogen,
  .oxygen,
  .nitrogen,
  .carbon,
  .sulfur,
  .phosphorus,
  .silicon,
  .boron,
  .arsenic,
  .selenium,
  .tellurium,
  .iodine,
  .bromine,
  .chlorine,
  .fluorine,
  .sodium,
  .potassium,
  .calcium,
  .magnesium,
  .lithium,
  .beryllium,
  .strontium,
  .barium,
  .radium,
  .francium,
  .cesium,
  .rubidium {
    color: inherit !important;
    background-color: inherit !important;
  }
}
