/**
 * Styles ultra-spécifiques pour le mode sombre de la page de profil
 * Ce fichier cible spécifiquement tous les éléments de la page de profil
 */

/* Styles généraux pour la page de profil en mode sombre */
.dark-theme {
  /* Conteneur principal */
  .profile-container {
    background-color: var(--background-color) !important;
    color: var(--text-primary) !important;
  }

  /* Header de profil */
  .profile-header {
    background: linear-gradient(135deg, #0a192f 0%, #112a5e 100%) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2) !important;

    .header-background {
      background: linear-gradient(135deg, #0a192f 0%, #112a5e 100%) !important;
      
      &::before {
        opacity: 0.05 !important;
        mix-blend-mode: luminosity !important;
      }
      
      &::after {
        background: linear-gradient(to bottom, rgba(0,0,0,0) 0%, rgba(0,0,0,0.2) 100%) !important;
      }
    }

    .profile-main-info {
      color: var(--text-primary) !important;

      .avatar-section {
        .avatar-container {
          .avatar-icon {
            border-color: var(--border-color) !important;
          }

          .avatar-overlay {
            background-color: rgba(0, 0, 0, 0.5) !important;
            
            mat-icon {
              color: var(--text-primary) !important;
            }
          }
        }
      }

      .user-main-details {
        h1 {
          color: var(--text-primary) !important;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
        }

        .user-title {
          color: var(--text-secondary) !important;
        }

        .profile-actions {
          .edit-profile-btn {
            background-color: var(--primary-color) !important;
            color: var(--primary-contrast) !important;
            
            mat-icon {
              color: var(--primary-contrast) !important;
            }
          }

          .cv-btn, .complete-profile-btn {
            background-color: rgba(255, 255, 255, 0.1) !important;
            color: var(--text-primary) !important;
            border-color: rgba(255, 255, 255, 0.2) !important;
            
            &::after {
              background-color: var(--accent-color) !important;
            }
            
            &:hover {
              background-color: rgba(255, 255, 255, 0.15) !important;
              box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3) !important;
            }
            
            mat-icon {
              color: var(--text-primary) !important;
            }
          }
        }
      }
    }

    .profile-tabs {
      background-color: var(--background-card) !important;
      border-color: var(--border-color) !important;

      .contact-info {
        .contact-item {
          background-color: var(--background-alt) !important;
          color: var(--text-secondary) !important;
          box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1) !important;
          
          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2) !important;
          }
          
          mat-icon {
            color: var(--primary-color) !important;
            
            &::after {
              background-color: var(--accent-color) !important;
            }
          }
          
          span {
            color: var(--text-secondary) !important;
          }
        }
      }

      .bio-capsule {
        background-color: var(--background-alt) !important;
        
        &.editing {
          background-color: var(--background-card) !important;
        }
        
        .bio-preview {
          .bio-label {
            span {
              color: var(--primary-color) !important;
              background-color: rgba(var(--primary-color-rgb), 0.1) !important;
            }
          }
          
          .bio-text-container {
            .bio-text {
              color: var(--text-secondary) !important;
            }
            
            .bio-edit-hint {
              mat-icon {
                color: var(--primary-color) !important;
              }
            }
          }
          
          .bio-empty {
            .empty-message {
              .primary-message {
                color: var(--text-primary) !important;
              }
              
              .secondary-message {
                color: var(--text-secondary) !important;
              }
            }
            
            mat-icon {
              color: var(--primary-color) !important;
            }
          }
        }
        
        .bio-edit-form {
          mat-form-field {
            background-color: var(--background-alt) !important;
            color: var(--text-primary) !important;
            
            textarea {
              background-color: var(--background-alt) !important;
              color: var(--text-primary) !important;
            }
          }
          
          .bio-edit-actions {
            button {
              &.save-btn {
                background-color: var(--primary-color) !important;
                color: var(--primary-contrast) !important;
              }
              
              &.cancel-btn {
                background-color: var(--background-alt) !important;
                color: var(--text-primary) !important;
                border-color: var(--border-color) !important;
              }
            }
          }
        }
      }
    }
  }

  /* Section de contenu */
  .profile-content {
    background-color: var(--background-color) !important;
    color: var(--text-primary) !important;
  }

  /* Colonnes */
  .left-column, .right-column {
    background-color: var(--background-color) !important;
    color: var(--text-primary) !important;
    padding: 20px !important;
  }

  /* Cartes d'information */
  .info-card {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
    
    &::before {
      background-color: var(--accent-color) !important;
    }
    
    &:hover {
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2) !important;
      transform: translateY(-2px);
    }
    
    mat-card-header {
      background-color: var(--background-alt) !important;
      border-color: var(--border-color) !important;
      
      mat-card-title {
        color: var(--text-primary) !important;
        
        .card-icon {
          color: var(--primary-color) !important;
          background-color: rgba(var(--primary-color-rgb), 0.1) !important;
          
          &::after {
            background-color: var(--accent-color) !important;
          }
        }
      }
    }
    
    mat-card-content {
      background-color: var(--background-card) !important;
      color: var(--text-secondary) !important;
      
      p {
        color: var(--text-secondary) !important;
        
        mat-icon {
          color: var(--primary-color) !important;
        }
      }
      
      .item-block {
        background-color: var(--background-alt) !important;
        color: var(--text-primary) !important;
        border-color: var(--border-color) !important;
        
        &:hover {
          background-color: rgba(var(--primary-color-rgb), 0.05) !important;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
        }
        
        .item-header {
          .item-title {
            h4 {
              color: var(--text-primary) !important;
            }
            
            span, .item-subtitle {
              color: var(--text-secondary) !important;
            }
          }
          
          .item-actions {
            .action-btn {
              color: var(--text-primary) !important;
              
              mat-icon {
                color: var(--primary-color) !important;
              }
              
              &:hover {
                background-color: rgba(var(--primary-color-rgb), 0.1) !important;
              }
            }
          }
        }
        
        .item-content {
          color: var(--text-secondary) !important;
          
          p {
            color: var(--text-secondary) !important;
          }
        }
        
        .item-footer {
          border-color: var(--border-color) !important;
          
          .item-date {
            color: var(--text-muted) !important;
          }
        }
      }
      
      .add-btn {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark)) !important;
        color: var(--primary-contrast) !important;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2) !important;
        border-left-color: var(--accent-color) !important;
        
        mat-icon {
          color: var(--primary-contrast) !important;
        }
        
        &:hover {
          transform: scale(1.05);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
        }
      }
      
      .add-now-btn {
        background-color: var(--primary-color) !important;
        color: var(--primary-contrast) !important;
      }
      
      .empty-message {
        color: var(--text-muted) !important;
        
        mat-icon {
          color: var(--text-muted) !important;
        }
      }
    }
  }

  /* Carte mise en évidence */
  .highlight-card {
    background-color: rgba(var(--primary-color-rgb), 0.05) !important;
    border-color: var(--primary-color) !important;
    
    mat-card-title {
      color: var(--primary-color) !important;
    }
    
    mat-card-content {
      color: var(--text-primary) !important;
    }
  }

  /* Carte bio */
  .bio-card {
    border-left-color: var(--accent-color) !important;
    
    .bio-content {
      p {
        color: var(--text-secondary) !important;
      }
      
      .empty-bio {
        color: var(--text-muted) !important;
      }
      
      .edit-bio-btn {
        color: var(--accent-color) !important;
        
        &:hover {
          background-color: rgba(var(--accent-color-rgb), 0.1) !important;
        }
        
        mat-icon {
          color: var(--accent-color) !important;
        }
      }
    }
  }

  /* Carte CV */
  .cv-card {
    &.has-cv {
      border-left-color: var(--primary-color) !important;
    }
    
    .cv-content {
      .cv-preview {
        .cv-icon {
          background-color: rgba(var(--primary-color-rgb), 0.1) !important;
          
          mat-icon {
            color: var(--primary-color) !important;
          }
        }
        
        .cv-details {
          .cv-name {
            color: var(--text-primary) !important;
          }
          
          .cv-date {
            color: var(--text-secondary) !important;
          }
        }
      }
      
      .cv-actions {
        .view-cv-btn {
          color: var(--primary-color) !important;
          border-color: var(--primary-color) !important;
          
          &:hover {
            background-color: rgba(var(--primary-color-rgb), 0.1) !important;
          }
        }
        
        .delete-cv-btn {
          color: var(--error-color) !important;
          
          &:hover {
            background-color: rgba(var(--error-color-rgb), 0.1) !important;
          }
        }
      }
    }
    
    .no-cv-content {
      .no-cv-icon {
        background-color: var(--background-alt) !important;
        
        mat-icon {
          color: var(--text-muted) !important;
        }
      }
      
      .no-cv-message {
        p {
          color: var(--text-secondary) !important;
        }
        
        .add-cv-btn {
          background-color: var(--primary-color) !important;
          color: var(--primary-contrast) !important;
          
          &::after {
            background-color: var(--accent-color) !important;
          }
        }
      }
    }
  }

  /* Message de succès pour l'ajout de CV */
  .cv-success-message {
    background-color: var(--success-color) !important;
    color: white !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
  }

  /* Grille */
  mat-grid-list {
    background-color: var(--background-color) !important;
  }

  mat-grid-tile {
    background-color: var(--background-color) !important;
  }

  /* Ajustement pour les cartes en haut */
  .left-column .info-card:first-child,
  .right-column .info-card:first-child {
    margin-top: -350px !important;
  }

  /* Ligne d'information */
  .info-row {
    span {
      color: var(--text-secondary) !important;
    }
    
    button {
      color: var(--primary-color) !important;
    }
  }

  /* Styles pour les éléments de formulaire */
  mat-form-field {
    .mat-form-field-label {
      color: var(--text-secondary) !important;
    }
    
    .mat-form-field-underline {
      background-color: var(--border-color) !important;
    }
    
    .mat-form-field-ripple {
      background-color: var(--primary-color) !important;
    }
    
    .mat-form-field-hint {
      color: var(--text-muted) !important;
    }
    
    .mat-error {
      color: var(--error-color) !important;
    }
  }

  input, textarea, select {
    background-color: var(--background-alt) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
  }

  /* Styles pour les éléments de dialogue */
  .mat-dialog-container {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
  }

  /* Styles pour les éléments de snackbar */
  .mat-snack-bar-container {
    background-color: var(--background-alt) !important;
    color: var(--text-primary) !important;
  }

  /* Styles pour les éléments de tooltip */
  .mat-tooltip {
    background-color: var(--background-alt) !important;
    color: var(--text-primary) !important;
  }

  /* Styles pour les éléments de menu */
  .mat-menu-panel {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
  }

  .mat-menu-item {
    color: var(--text-primary) !important;
    
    &:hover {
      background-color: var(--background-alt) !important;
    }
  }

  /* Styles pour les éléments de sélection */
  .mat-select-panel {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
  }

  .mat-option {
    color: var(--text-primary) !important;
    
    &:hover {
      background-color: var(--background-alt) !important;
    }
  }

  /* Styles pour les éléments de datepicker */
  .mat-datepicker-content {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
  }

  /* Styles pour les éléments de checkbox */
  .mat-checkbox-frame {
    border-color: var(--border-color) !important;
  }

  .mat-checkbox-checked .mat-checkbox-background {
    background-color: var(--primary-color) !important;
  }

  /* Styles pour les éléments de radio */
  .mat-radio-outer-circle {
    border-color: var(--border-color) !important;
  }

  .mat-radio-checked .mat-radio-outer-circle {
    border-color: var(--primary-color) !important;
  }

  .mat-radio-inner-circle {
    background-color: var(--primary-color) !important;
  }

  /* Styles pour les éléments de slider */
  .mat-slider-track-background {
    background-color: var(--border-color) !important;
  }

  .mat-slider-track-fill {
    background-color: var(--primary-color) !important;
  }

  .mat-slider-thumb {
    background-color: var(--primary-color) !important;
  }

  /* Styles pour les éléments de progress bar */
  .mat-progress-bar-buffer {
    background-color: var(--background-alt) !important;
  }

  .mat-progress-bar-fill::after {
    background-color: var(--primary-color) !important;
  }

  /* Styles pour les éléments de spinner */
  .mat-progress-spinner circle {
    stroke: var(--primary-color) !important;
  }

  /* Styles pour les éléments de bouton */
  button {
    &.mat-primary {
      background-color: var(--primary-color) !important;
      color: var(--primary-contrast) !important;
    }
    
    &.mat-accent {
      background-color: var(--accent-color) !important;
      color: var(--accent-contrast) !important;
    }
    
    &.mat-warn {
      background-color: var(--error-color) !important;
      color: white !important;
    }
  }

  /* Styles pour les éléments de lien */
  a {
    color: var(--primary-color) !important;
    
    &:hover {
      color: var(--primary-light) !important;
    }
  }

  /* Styles pour les éléments de texte */
  h1, h2, h3, h4, h5, h6 {
    color: var(--text-primary) !important;
  }

  p, span, div {
    color: var(--text-secondary) !important;
  }

  /* Styles pour les éléments d'icône */
  mat-icon {
    color: var(--text-primary) !important;
  }

  /* Styles pour les éléments de badge */
  .mat-badge-content {
    background-color: var(--primary-color) !important;
    color: var(--primary-contrast) !important;
  }

  /* Styles pour les éléments de chip */
  .mat-chip {
    background-color: var(--background-alt) !important;
    color: var(--text-primary) !important;
  }

  /* Styles pour les éléments de divider */
  .mat-divider {
    border-color: var(--border-color) !important;
  }

  /* Styles pour les éléments de liste */
  .mat-list-item {
    color: var(--text-primary) !important;
  }

  /* Styles pour les éléments de table */
  .mat-table {
    background-color: var(--background-card) !important;
  }

  .mat-header-cell {
    color: var(--text-primary) !important;
  }

  .mat-cell {
    color: var(--text-secondary) !important;
  }

  .mat-row:hover {
    background-color: var(--background-alt) !important;
  }

  /* Styles pour les éléments de pagination */
  .mat-paginator {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
  }

  /* Styles pour les éléments de sort header */
  .mat-sort-header-arrow {
    color: var(--text-primary) !important;
  }

  /* Styles pour les éléments d'expansion panel */
  .mat-expansion-panel {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
  }

  .mat-expansion-panel-header {
    background-color: var(--background-card) !important;
  }

  .mat-expansion-panel-header-title {
    color: var(--text-primary) !important;
  }

  .mat-expansion-panel-header-description {
    color: var(--text-secondary) !important;
  }

  .mat-expansion-indicator::after {
    color: var(--text-primary) !important;
  }

  /* Styles pour les éléments de stepper */
  .mat-step-header .mat-step-icon {
    background-color: var(--primary-color) !important;
    color: var(--primary-contrast) !important;
  }

  .mat-step-header .mat-step-label {
    color: var(--text-primary) !important;
  }

  /* Styles pour les éléments de tabs */
  .mat-tab-label {
    color: var(--text-primary) !important;
  }

  .mat-tab-group.mat-primary .mat-tab-label.mat-tab-label-active {
    color: var(--primary-color) !important;
  }

  .mat-tab-body-content {
    color: var(--text-primary) !important;
  }

  /* Styles pour les éléments de toolbar */
  .mat-toolbar {
    background-color: var(--background-alt) !important;
    color: var(--text-primary) !important;
  }

  /* Styles pour les éléments de sidenav */
  .mat-drawer {
    background-color: var(--background-alt) !important;
    color: var(--text-primary) !important;
  }

  .mat-drawer-container {
    background-color: var(--background-color) !important;
    color: var(--text-primary) !important;
  }

  /* Styles pour les éléments de bottom sheet */
  .mat-bottom-sheet-container {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
  }

  /* Styles pour les éléments de autocomplete */
  .mat-autocomplete-panel {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
  }

  /* Styles pour les éléments de tree */
  .mat-tree {
    background-color: var(--background-card) !important;
  }

  .mat-tree-node {
    color: var(--text-primary) !important;
  }

  /* Styles pour les éléments de grid list */
  .mat-grid-tile {
    background-color: var(--background-color) !important;
  }

  /* Styles pour les éléments de card */
  .mat-card {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
  }

  .mat-card-title {
    color: var(--text-primary) !important;
  }

  .mat-card-subtitle {
    color: var(--text-secondary) !important;
  }

  .mat-card-content {
    color: var(--text-secondary) !important;
  }

  .mat-card-actions {
    background-color: var(--background-card) !important;
  }

  .mat-card-footer {
    background-color: var(--background-card) !important;
  }

  /* Styles pour les éléments de list */
  .mat-list {
    background-color: var(--background-card) !important;
  }

  .mat-list-item {
    color: var(--text-primary) !important;
  }

  .mat-list-item-content {
    color: var(--text-primary) !important;
  }

  /* Styles pour les éléments de divider */
  .mat-divider {
    border-color: var(--border-color) !important;
  }

  /* Styles pour les éléments de button toggle */
  .mat-button-toggle-group {
    background-color: var(--background-alt) !important;
  }

  .mat-button-toggle {
    color: var(--text-primary) !important;
  }

  .mat-button-toggle-checked {
    background-color: var(--primary-color) !important;
    color: var(--primary-contrast) !important;
  }

  /* Styles pour les éléments de button */
  .mat-button,
  .mat-raised-button,
  .mat-stroked-button,
  .mat-flat-button,
  .mat-icon-button,
  .mat-fab,
  .mat-mini-fab {
    color: var(--text-primary) !important;
  }

  .mat-raised-button.mat-primary,
  .mat-flat-button.mat-primary,
  .mat-fab.mat-primary,
  .mat-mini-fab.mat-primary {
    background-color: var(--primary-color) !important;
    color: var(--primary-contrast) !important;
  }

  .mat-raised-button.mat-accent,
  .mat-flat-button.mat-accent,
  .mat-fab.mat-accent,
  .mat-mini-fab.mat-accent {
    background-color: var(--accent-color) !important;
    color: var(--accent-contrast) !important;
  }

  .mat-raised-button.mat-warn,
  .mat-flat-button.mat-warn,
  .mat-fab.mat-warn,
  .mat-mini-fab.mat-warn {
    background-color: var(--error-color) !important;
    color: white !important;
  }

  .mat-stroked-button.mat-primary {
    color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
  }

  .mat-stroked-button.mat-accent {
    color: var(--accent-color) !important;
    border-color: var(--accent-color) !important;
  }

  .mat-stroked-button.mat-warn {
    color: var(--error-color) !important;
    border-color: var(--error-color) !important;
  }
}
