/**
 * Styles ultra-spécifiques pour les boutons et phrases d'ajout en mode sombre
 * Ce fichier cible spécifiquement les boutons et phrases d'ajout dans les cartes
 */

/* Styles pour les boutons et phrases d'ajout en mode sombre */
.dark-theme {
  /* Sélecteurs pour les boutons d'ajout */
  .add-btn,
  .add-now-btn,
  .add-cv-btn,
  .add-item-btn,
  .add-experience-btn,
  .add-education-btn,
  .add-certification-btn,
  .add-skill-btn,
  .add-language-btn,
  .add-link-btn,
  [class*="add-btn"],
  [class*="add-now-btn"],
  [class*="add-cv-btn"],
  [class*="add-item-btn"],
  [class*="add-experience-btn"],
  [class*="add-education-btn"],
  [class*="add-certification-btn"],
  [class*="add-skill-btn"],
  [class*="add-language-btn"],
  [class*="add-link-btn"],
  button[class*="add"],
  a[class*="add"] {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark)) !important;
    color: var(--primary-contrast) !important;
    box-shadow: var(--shadow-sm) !important;
    border-left-color: var(--accent-color) !important;
    
    &::before,
    &::after {
      background-color: var(--accent-color) !important;
    }
    
    &:hover {
      box-shadow: var(--shadow-md) !important;
      transform: translateY(-2px);
    }
    
    mat-icon,
    .mat-icon,
    [class*="mat-icon"],
    i,
    .fa,
    .fas,
    .far,
    .fab,
    [class*="fa-"] {
      color: var(--primary-contrast) !important;
    }
  }

  /* Sélecteurs pour les phrases d'ajout */
  .add-text,
  .add-message,
  .add-prompt,
  .add-label,
  .add-description,
  .empty-message,
  .empty-text,
  .empty-prompt,
  .empty-label,
  .empty-description,
  [class*="add-text"],
  [class*="add-message"],
  [class*="add-prompt"],
  [class*="add-label"],
  [class*="add-description"],
  [class*="empty-message"],
  [class*="empty-text"],
  [class*="empty-prompt"],
  [class*="empty-label"],
  [class*="empty-description"],
  p[class*="add"],
  span[class*="add"],
  div[class*="add"],
  p[class*="empty"],
  span[class*="empty"],
  div[class*="empty"] {
    color: var(--text-secondary) !important;
    
    strong,
    b,
    em,
    i {
      color: var(--text-primary) !important;
    }
    
    mat-icon,
    .mat-icon,
    [class*="mat-icon"],
    i,
    .fa,
    .fas,
    .far,
    .fab,
    [class*="fa-"] {
      color: var(--text-muted) !important;
    }
    
    a,
    .link,
    [class*="link"] {
      color: var(--primary-color) !important;
      
      &:hover {
        color: var(--primary-light) !important;
        text-decoration: underline;
      }
    }
  }

  /* Sélecteurs pour les conteneurs d'ajout */
  .add-container,
  .add-section,
  .add-wrapper,
  .add-box,
  .add-area,
  .empty-container,
  .empty-section,
  .empty-wrapper,
  .empty-box,
  .empty-area,
  [class*="add-container"],
  [class*="add-section"],
  [class*="add-wrapper"],
  [class*="add-box"],
  [class*="add-area"],
  [class*="empty-container"],
  [class*="empty-section"],
  [class*="empty-wrapper"],
  [class*="empty-box"],
  [class*="empty-area"],
  div[class*="add"],
  section[class*="add"],
  div[class*="empty"],
  section[class*="empty"] {
    background-color: var(--background-alt) !important;
    color: var(--text-secondary) !important;
    border-color: var(--border-color) !important;
    box-shadow: var(--shadow-sm) !important;
    
    &:hover {
      background-color: rgba(var(--primary-color-rgb), 0.05) !important;
      box-shadow: var(--shadow-md) !important;
    }
  }

  /* Styles spécifiques pour les boutons d'ajout dans les cartes */
  .info-card,
  mat-card,
  .mat-card,
  .mat-mdc-card,
  [class*="info-card"],
  [class*="mat-card"],
  [class*="mat-mdc-card"] {
    .add-btn,
    .add-now-btn,
    .add-cv-btn,
    .add-item-btn,
    .add-experience-btn,
    .add-education-btn,
    .add-certification-btn,
    .add-skill-btn,
    .add-language-btn,
    .add-link-btn,
    [class*="add-btn"],
    [class*="add-now-btn"],
    [class*="add-cv-btn"],
    [class*="add-item-btn"],
    [class*="add-experience-btn"],
    [class*="add-education-btn"],
    [class*="add-certification-btn"],
    [class*="add-skill-btn"],
    [class*="add-language-btn"],
    [class*="add-link-btn"],
    button[class*="add"],
    a[class*="add"] {
      background: linear-gradient(135deg, var(--primary-color), var(--primary-dark)) !important;
      color: var(--primary-contrast) !important;
      box-shadow: var(--shadow-sm) !important;
      border-left-color: var(--accent-color) !important;
      
      &::before,
      &::after {
        background-color: var(--accent-color) !important;
      }
      
      &:hover {
        box-shadow: var(--shadow-md) !important;
        transform: translateY(-2px);
      }
      
      mat-icon,
      .mat-icon,
      [class*="mat-icon"],
      i,
      .fa,
      .fas,
      .far,
      .fab,
      [class*="fa-"] {
        color: var(--primary-contrast) !important;
      }
    }
    
    .add-text,
    .add-message,
    .add-prompt,
    .add-label,
    .add-description,
    .empty-message,
    .empty-text,
    .empty-prompt,
    .empty-label,
    .empty-description,
    [class*="add-text"],
    [class*="add-message"],
    [class*="add-prompt"],
    [class*="add-label"],
    [class*="add-description"],
    [class*="empty-message"],
    [class*="empty-text"],
    [class*="empty-prompt"],
    [class*="empty-label"],
    [class*="empty-description"],
    p[class*="add"],
    span[class*="add"],
    div[class*="add"],
    p[class*="empty"],
    span[class*="empty"],
    div[class*="empty"] {
      color: var(--text-secondary) !important;
      
      strong,
      b,
      em,
      i {
        color: var(--text-primary) !important;
      }
      
      mat-icon,
      .mat-icon,
      [class*="mat-icon"],
      i,
      .fa,
      .fas,
      .far,
      .fab,
      [class*="fa-"] {
        color: var(--text-muted) !important;
      }
      
      a,
      .link,
      [class*="link"] {
        color: var(--primary-color) !important;
        
        &:hover {
          color: var(--primary-light) !important;
          text-decoration: underline;
        }
      }
    }
    
    .add-container,
    .add-section,
    .add-wrapper,
    .add-box,
    .add-area,
    .empty-container,
    .empty-section,
    .empty-wrapper,
    .empty-box,
    .empty-area,
    [class*="add-container"],
    [class*="add-section"],
    [class*="add-wrapper"],
    [class*="add-box"],
    [class*="add-area"],
    [class*="empty-container"],
    [class*="empty-section"],
    [class*="empty-wrapper"],
    [class*="empty-box"],
    [class*="empty-area"],
    div[class*="add"],
    section[class*="add"],
    div[class*="empty"],
    section[class*="empty"] {
      background-color: var(--background-alt) !important;
      color: var(--text-secondary) !important;
      border-color: var(--border-color) !important;
      box-shadow: var(--shadow-sm) !important;
      
      &:hover {
        background-color: rgba(var(--primary-color-rgb), 0.05) !important;
        box-shadow: var(--shadow-md) !important;
      }
    }
  }

  /* Styles spécifiques pour les boutons d'ajout dans les cartes de certification */
  .certification-card,
  [class*="certification-card"],
  [class*="certification"] {
    .add-btn,
    .add-now-btn,
    .add-certification-btn,
    [class*="add-btn"],
    [class*="add-now-btn"],
    [class*="add-certification-btn"],
    button[class*="add"],
    a[class*="add"] {
      background: linear-gradient(135deg, var(--primary-color), var(--primary-dark)) !important;
      color: var(--primary-contrast) !important;
    }
    
    .add-text,
    .add-message,
    .add-prompt,
    .add-label,
    .add-description,
    .empty-message,
    .empty-text,
    .empty-prompt,
    .empty-label,
    .empty-description,
    [class*="add-text"],
    [class*="add-message"],
    [class*="add-prompt"],
    [class*="add-label"],
    [class*="add-description"],
    [class*="empty-message"],
    [class*="empty-text"],
    [class*="empty-prompt"],
    [class*="empty-label"],
    [class*="empty-description"],
    p[class*="add"],
    span[class*="add"],
    div[class*="add"],
    p[class*="empty"],
    span[class*="empty"],
    div[class*="empty"] {
      color: var(--text-secondary) !important;
    }
  }

  /* Styles spécifiques pour les boutons d'ajout dans les cartes d'expérience */
  .experience-card,
  [class*="experience-card"],
  [class*="experience"] {
    .add-btn,
    .add-now-btn,
    .add-experience-btn,
    [class*="add-btn"],
    [class*="add-now-btn"],
    [class*="add-experience-btn"],
    button[class*="add"],
    a[class*="add"] {
      background: linear-gradient(135deg, var(--primary-color), var(--primary-dark)) !important;
      color: var(--primary-contrast) !important;
    }
    
    .add-text,
    .add-message,
    .add-prompt,
    .add-label,
    .add-description,
    .empty-message,
    .empty-text,
    .empty-prompt,
    .empty-label,
    .empty-description,
    [class*="add-text"],
    [class*="add-message"],
    [class*="add-prompt"],
    [class*="add-label"],
    [class*="add-description"],
    [class*="empty-message"],
    [class*="empty-text"],
    [class*="empty-prompt"],
    [class*="empty-label"],
    [class*="empty-description"],
    p[class*="add"],
    span[class*="add"],
    div[class*="add"],
    p[class*="empty"],
    span[class*="empty"],
    div[class*="empty"] {
      color: var(--text-secondary) !important;
    }
  }

  /* Styles spécifiques pour les boutons d'ajout dans les cartes d'éducation */
  .education-card,
  [class*="education-card"],
  [class*="education"] {
    .add-btn,
    .add-now-btn,
    .add-education-btn,
    [class*="add-btn"],
    [class*="add-now-btn"],
    [class*="add-education-btn"],
    button[class*="add"],
    a[class*="add"] {
      background: linear-gradient(135deg, var(--primary-color), var(--primary-dark)) !important;
      color: var(--primary-contrast) !important;
    }
    
    .add-text,
    .add-message,
    .add-prompt,
    .add-label,
    .add-description,
    .empty-message,
    .empty-text,
    .empty-prompt,
    .empty-label,
    .empty-description,
    [class*="add-text"],
    [class*="add-message"],
    [class*="add-prompt"],
    [class*="add-label"],
    [class*="add-description"],
    [class*="empty-message"],
    [class*="empty-text"],
    [class*="empty-prompt"],
    [class*="empty-label"],
    [class*="empty-description"],
    p[class*="add"],
    span[class*="add"],
    div[class*="add"],
    p[class*="empty"],
    span[class*="empty"],
    div[class*="empty"] {
      color: var(--text-secondary) !important;
    }
  }

  /* Styles spécifiques pour les boutons d'ajout dans les cartes de compétence */
  .skill-card,
  [class*="skill-card"],
  [class*="skill"] {
    .add-btn,
    .add-now-btn,
    .add-skill-btn,
    [class*="add-btn"],
    [class*="add-now-btn"],
    [class*="add-skill-btn"],
    button[class*="add"],
    a[class*="add"] {
      background: linear-gradient(135deg, var(--primary-color), var(--primary-dark)) !important;
      color: var(--primary-contrast) !important;
    }
    
    .add-text,
    .add-message,
    .add-prompt,
    .add-label,
    .add-description,
    .empty-message,
    .empty-text,
    .empty-prompt,
    .empty-label,
    .empty-description,
    [class*="add-text"],
    [class*="add-message"],
    [class*="add-prompt"],
    [class*="add-label"],
    [class*="add-description"],
    [class*="empty-message"],
    [class*="empty-text"],
    [class*="empty-prompt"],
    [class*="empty-label"],
    [class*="empty-description"],
    p[class*="add"],
    span[class*="add"],
    div[class*="add"],
    p[class*="empty"],
    span[class*="empty"],
    div[class*="empty"] {
      color: var(--text-secondary) !important;
    }
  }

  /* Styles spécifiques pour les boutons d'ajout dans les cartes de lien */
  .link-card,
  [class*="link-card"],
  [class*="link"] {
    .add-btn,
    .add-now-btn,
    .add-link-btn,
    [class*="add-btn"],
    [class*="add-now-btn"],
    [class*="add-link-btn"],
    button[class*="add"],
    a[class*="add"] {
      background: linear-gradient(135deg, var(--primary-color), var(--primary-dark)) !important;
      color: var(--primary-contrast) !important;
    }
    
    .add-text,
    .add-message,
    .add-prompt,
    .add-label,
    .add-description,
    .empty-message,
    .empty-text,
    .empty-prompt,
    .empty-label,
    .empty-description,
    [class*="add-text"],
    [class*="add-message"],
    [class*="add-prompt"],
    [class*="add-label"],
    [class*="add-description"],
    [class*="empty-message"],
    [class*="empty-text"],
    [class*="empty-prompt"],
    [class*="empty-label"],
    [class*="empty-description"],
    p[class*="add"],
    span[class*="add"],
    div[class*="add"],
    p[class*="empty"],
    span[class*="empty"],
    div[class*="empty"] {
      color: var(--text-secondary) !important;
    }
  }

  /* Styles spécifiques pour les boutons d'ajout dans les cartes de langue */
  .language-card,
  [class*="language-card"],
  [class*="language"] {
    .add-btn,
    .add-now-btn,
    .add-language-btn,
    [class*="add-btn"],
    [class*="add-now-btn"],
    [class*="add-language-btn"],
    button[class*="add"],
    a[class*="add"] {
      background: linear-gradient(135deg, var(--primary-color), var(--primary-dark)) !important;
      color: var(--primary-contrast) !important;
    }
    
    .add-text,
    .add-message,
    .add-prompt,
    .add-label,
    .add-description,
    .empty-message,
    .empty-text,
    .empty-prompt,
    .empty-label,
    .empty-description,
    [class*="add-text"],
    [class*="add-message"],
    [class*="add-prompt"],
    [class*="add-label"],
    [class*="add-description"],
    [class*="empty-message"],
    [class*="empty-text"],
    [class*="empty-prompt"],
    [class*="empty-label"],
    [class*="empty-description"],
    p[class*="add"],
    span[class*="add"],
    div[class*="add"],
    p[class*="empty"],
    span[class*="empty"],
    div[class*="empty"] {
      color: var(--text-secondary) !important;
    }
  }

  /* Styles pour les éléments avec des styles en ligne */
  .add-btn,
  .add-now-btn,
  .add-cv-btn,
  .add-item-btn,
  .add-experience-btn,
  .add-education-btn,
  .add-certification-btn,
  .add-skill-btn,
  .add-language-btn,
  .add-link-btn,
  [class*="add-btn"],
  [class*="add-now-btn"],
  [class*="add-cv-btn"],
  [class*="add-item-btn"],
  [class*="add-experience-btn"],
  [class*="add-education-btn"],
  [class*="add-certification-btn"],
  [class*="add-skill-btn"],
  [class*="add-language-btn"],
  [class*="add-link-btn"],
  button[class*="add"],
  a[class*="add"],
  .add-text,
  .add-message,
  .add-prompt,
  .add-label,
  .add-description,
  .empty-message,
  .empty-text,
  .empty-prompt,
  .empty-label,
  .empty-description,
  [class*="add-text"],
  [class*="add-message"],
  [class*="add-prompt"],
  [class*="add-label"],
  [class*="add-description"],
  [class*="empty-message"],
  [class*="empty-text"],
  [class*="empty-prompt"],
  [class*="empty-label"],
  [class*="empty-description"],
  p[class*="add"],
  span[class*="add"],
  div[class*="add"],
  p[class*="empty"],
  span[class*="empty"],
  div[class*="empty"] {
    &[style*="background-color: #fff"],
    &[style*="background-color:#fff"],
    &[style*="background-color: white"],
    &[style*="background-color:#white"],
    &[style*="background-color: rgb(255, 255, 255)"],
    &[style*="background-color:rgb(255,255,255)"],
    &[style*="background-color: rgba(255, 255, 255"],
    &[style*="background-color:rgba(255,255,255"],
    &[style*="background: #fff"],
    &[style*="background:#fff"],
    &[style*="background: white"],
    &[style*="background:#white"],
    &[style*="background: rgb(255, 255, 255)"],
    &[style*="background:rgb(255,255,255)"],
    &[style*="background: rgba(255, 255, 255"],
    &[style*="background:rgba(255,255,255"],
    &[style*="background-color: #f8f9fa"],
    &[style*="background-color:#f8f9fa"],
    &[style*="background: #f8f9fa"],
    &[style*="background:#f8f9fa"],
    &[style*="background-color: #f4f4f4"],
    &[style*="background-color:#f4f4f4"],
    &[style*="background: #f4f4f4"],
    &[style*="background:#f4f4f4"],
    &[style*="background-color: #f5f5f5"],
    &[style*="background-color:#f5f5f5"],
    &[style*="background: #f5f5f5"],
    &[style*="background:#f5f5f5"],
    &[style*="background-color: #f0f0f0"],
    &[style*="background-color:#f0f0f0"],
    &[style*="background: #f0f0f0"],
    &[style*="background:#f0f0f0"],
    &[style*="background-color: #eeeeee"],
    &[style*="background-color:#eeeeee"],
    &[style*="background: #eeeeee"],
    &[style*="background:#eeeeee"],
    &[style*="background-color: #e9ecef"],
    &[style*="background-color:#e9ecef"],
    &[style*="background: #e9ecef"],
    &[style*="background:#e9ecef"] {
      background-color: var(--background-alt) !important;
      background: var(--background-alt) !important;
    }
    
    &[style*="color: #000"],
    &[style*="color:#000"],
    &[style*="color: black"],
    &[style*="color:#black"],
    &[style*="color: rgb(0, 0, 0)"],
    &[style*="color:rgb(0,0,0)"],
    &[style*="color: rgba(0, 0, 0"],
    &[style*="color:rgba(0,0,0"],
    &[style*="color: #111111"],
    &[style*="color:#111111"],
    &[style*="color: #222222"],
    &[style*="color:#222222"],
    &[style*="color: #333333"],
    &[style*="color:#333333"],
    &[style*="color: #444444"],
    &[style*="color:#444444"],
    &[style*="color: #1a2a44"],
    &[style*="color:#1a2a44"],
    &[style*="color: #001660"],
    &[style*="color:#001660"] {
      color: var(--text-primary) !important;
    }
    
    &[style*="color: #555555"],
    &[style*="color:#555555"],
    &[style*="color: #666666"],
    &[style*="color:#666666"],
    &[style*="color: #777777"],
    &[style*="color:#777777"],
    &[style*="color: #888888"],
    &[style*="color:#888888"],
    &[style*="color: #4b5563"],
    &[style*="color:#4b5563"] {
      color: var(--text-secondary) !important;
    }
    
    &[style*="color: #999999"],
    &[style*="color:#999999"],
    &[style*="color: #aaaaaa"],
    &[style*="color:#aaaaaa"],
    &[style*="color: #bbbbbb"],
    &[style*="color:#bbbbbb"],
    &[style*="color: #cccccc"],
    &[style*="color:#cccccc"],
    &[style*="color: gray"],
    &[style*="color:gray"],
    &[style*="color: grey"],
    &[style*="color:grey"] {
      color: var(--text-muted) !important;
    }
  }
}
