/**
 * Styles spécifiques pour les pages principales en mode sombre
 * Ce fichier contient des styles spécifiques pour les pages d'accueil et de profil
 */

/* Styles pour la page d'accueil */
.dark-theme {
  /* Section héro */
  .hero-section,
  .hero-container,
  #hero,
  .hero {
    background-color: var(--background-color) !important;
    color: var(--text-primary) !important;
  }

  .hero-title,
  .hero-subtitle {
    color: var(--text-primary) !important;
  }

  .hero-description {
    color: var(--text-secondary) !important;
  }

  .hero-image {
    filter: brightness(0.8) !important;
  }

  /* Section à propos */
  .about-section,
  .about-container {
    background-color: var(--background-alt) !important;
    color: var(--text-primary) !important;
  }

  .about-title {
    color: var(--text-primary) !important;
  }

  .about-description {
    color: var(--text-secondary) !important;
  }

  /* Section services */
  .services-section,
  .services-container {
    background-color: var(--background-color) !important;
    color: var(--text-primary) !important;
  }

  .services-title {
    color: var(--text-primary) !important;
  }

  .services-description {
    color: var(--text-secondary) !important;
  }

  .service-card {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
    box-shadow: var(--shadow-sm) !important;
  }

  .service-title {
    color: var(--text-primary) !important;
  }

  .service-description {
    color: var(--text-secondary) !important;
  }

  .service-icon {
    color: var(--primary-color) !important;
  }

  /* Section prix */
  .pricing-section,
  .pricing-container {
    background-color: var(--background-alt) !important;
    color: var(--text-primary) !important;
  }

  .pricing-title {
    color: var(--text-primary) !important;
  }

  .pricing-description {
    color: var(--text-secondary) !important;
  }

  .pricing-card {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
    box-shadow: var(--shadow-sm) !important;
  }

  .pricing-card.popular {
    border-color: var(--primary-color) !important;
  }

  .pricing-card-header {
    border-color: var(--border-color) !important;
  }

  .pricing-card-title {
    color: var(--text-primary) !important;
  }

  .pricing-card-price {
    color: var(--primary-color) !important;
  }

  .pricing-card-description {
    color: var(--text-secondary) !important;
  }

  .pricing-card-features {
    color: var(--text-secondary) !important;
  }

  .pricing-card-feature {
    color: var(--text-secondary) !important;
  }

  .pricing-card-feature-icon {
    color: var(--primary-color) !important;
  }

  /* Section contact */
  .contact-section,
  .contact-container {
    background-color: var(--background-color) !important;
    color: var(--text-primary) !important;
  }

  .contact-title {
    color: var(--text-primary) !important;
  }

  .contact-description {
    color: var(--text-secondary) !important;
  }

  .contact-form {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
    box-shadow: var(--shadow-sm) !important;
  }

  .contact-form-title {
    color: var(--text-primary) !important;
  }

  .contact-form-description {
    color: var(--text-secondary) !important;
  }

  .contact-form-input {
    background-color: var(--background-alt) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
  }

  .contact-form-input:focus {
    border-color: var(--primary-color) !important;
  }

  .contact-form-button {
    background-color: var(--primary-color) !important;
    color: var(--primary-contrast) !important;
  }

  /* Section témoignages */
  .testimonials-section,
  .testimonials-container {
    background-color: var(--background-alt) !important;
    color: var(--text-primary) !important;
  }

  .testimonials-title {
    color: var(--text-primary) !important;
  }

  .testimonials-description {
    color: var(--text-secondary) !important;
  }

  .testimonial-card {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
    box-shadow: var(--shadow-sm) !important;
  }

  .testimonial-text {
    color: var(--text-secondary) !important;
  }

  .testimonial-author {
    color: var(--text-primary) !important;
  }

  .testimonial-author-title {
    color: var(--text-secondary) !important;
  }

  .testimonial-author-avatar {
    border-color: var(--border-color) !important;
  }

  /* Section partenaires */
  .partners-section,
  .partners-container {
    background-color: var(--background-color) !important;
    color: var(--text-primary) !important;
  }

  .partners-title {
    color: var(--text-primary) !important;
  }

  .partners-description {
    color: var(--text-secondary) !important;
  }

  .partner-logo {
    filter: brightness(0.8) grayscale(0.5) !important;
  }

  /* Section newsletter */
  .newsletter-section,
  .newsletter-container {
    background-color: var(--background-alt) !important;
    color: var(--text-primary) !important;
  }

  .newsletter-title {
    color: var(--text-primary) !important;
  }

  .newsletter-description {
    color: var(--text-secondary) !important;
  }

  .newsletter-form {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
  }

  .newsletter-form-input {
    background-color: var(--background-alt) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
  }

  .newsletter-form-button {
    background-color: var(--primary-color) !important;
    color: var(--primary-contrast) !important;
  }

  /* Section footer */
  .footer,
  .footer-container {
    background-color: var(--background-alt) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
  }

  .footer-logo {
    filter: brightness(0.8) !important;
  }

  .footer-title {
    color: var(--text-primary) !important;
  }

  .footer-description {
    color: var(--text-secondary) !important;
  }

  .footer-link {
    color: var(--text-secondary) !important;
  }

  .footer-link:hover {
    color: var(--primary-color) !important;
  }

  .footer-social-icon {
    color: var(--text-secondary) !important;
  }

  .footer-social-icon:hover {
    color: var(--primary-color) !important;
  }

  .footer-copyright {
    color: var(--text-secondary) !important;
    border-color: var(--border-color) !important;
  }
}

/* Styles pour la page de profil */
.dark-theme {
  /* Header de profil */
  .profile-header,
  .profile-header-container {
    background: linear-gradient(135deg, #0a192f 0%, #112a5e 100%) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
  }

  .profile-header-overlay {
    background-color: rgba(0, 0, 0, 0.3) !important;
  }

  .profile-header-pattern {
    opacity: 0.05 !important;
  }

  .profile-header-gradient {
    background: linear-gradient(to top, rgba(0, 0, 0, 0.3), transparent) !important;
  }

  .profile-avatar-wrapper {
    border-color: var(--border-color) !important;
    background-color: var(--background-alt) !important;
  }

  .profile-avatar-overlay {
    background-color: rgba(0, 0, 0, 0.5) !important;
  }

  .profile-avatar-icon {
    color: var(--text-primary) !important;
  }

  .profile-name {
    color: var(--text-primary) !important;
  }

  .profile-title {
    color: var(--text-secondary) !important;
    border-color: var(--accent-color) !important;
  }

  .profile-badge {
    background-color: var(--background-alt) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
  }

  .profile-badge-icon {
    color: var(--primary-color) !important;
  }

  .profile-bio {
    background-color: rgba(255, 255, 255, 0.1) !important;
    color: var(--text-secondary) !important;
    border-color: var(--accent-color) !important;
  }

  .profile-bio-icon {
    background-color: var(--accent-color) !important;
    color: var(--accent-contrast) !important;
  }

  .profile-contact-info {
    color: var(--text-secondary) !important;
  }

  .profile-contact-item {
    background-color: rgba(255, 255, 255, 0.1) !important;
    color: var(--text-secondary) !important;
  }

  .profile-contact-icon {
    color: var(--primary-color) !important;
  }

  .profile-action-button {
    background-color: var(--primary-color) !important;
    color: var(--primary-contrast) !important;
  }

  .profile-action-button.secondary {
    background-color: rgba(255, 255, 255, 0.1) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
  }

  /* Corps de profil */
  .profile-body,
  .profile-body-container {
    background-color: var(--background-color) !important;
    color: var(--text-primary) !important;
  }

  .profile-section {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
    box-shadow: var(--shadow-sm) !important;
  }

  .profile-section-header {
    border-color: var(--border-color) !important;
  }

  .profile-section-title {
    color: var(--text-primary) !important;
  }

  .profile-section-subtitle {
    color: var(--text-secondary) !important;
  }

  .profile-section-content {
    color: var(--text-secondary) !important;
  }

  .profile-section-footer {
    border-color: var(--border-color) !important;
  }

  .profile-section-action {
    color: var(--primary-color) !important;
  }

  /* Sections spécifiques */
  .profile-about-section {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
  }

  .profile-experience-section {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
  }

  .profile-education-section {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
  }

  .profile-skills-section {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
  }

  .profile-projects-section {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
  }

  .profile-certifications-section {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
  }

  .profile-languages-section {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
  }

  .profile-interests-section {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
  }

  /* Éléments de timeline */
  .timeline-item {
    border-color: var(--border-color) !important;
  }

  .timeline-item::before {
    background-color: var(--primary-color) !important;
    border-color: var(--border-color) !important;
  }

  .timeline-item-header {
    color: var(--text-primary) !important;
  }

  .timeline-item-subheader {
    color: var(--text-secondary) !important;
  }

  .timeline-item-date {
    color: var(--text-muted) !important;
  }

  .timeline-item-content {
    color: var(--text-secondary) !important;
  }

  /* Badges de compétences */
  .skill-badge {
    background-color: var(--background-alt) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
  }

  .skill-badge:hover {
    background-color: rgba(var(--primary-color-rgb), 0.1) !important;
    color: var(--primary-color) !important;
  }

  /* Cartes de projet */
  .project-card {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
    box-shadow: var(--shadow-sm) !important;
  }

  .project-card-header {
    border-color: var(--border-color) !important;
  }

  .project-card-title {
    color: var(--text-primary) !important;
  }

  .project-card-subtitle {
    color: var(--text-secondary) !important;
  }

  .project-card-content {
    color: var(--text-secondary) !important;
  }

  .project-card-footer {
    border-color: var(--border-color) !important;
  }

  .project-card-action {
    color: var(--primary-color) !important;
  }

  /* Barre latérale de profil */
  .profile-sidebar {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
    box-shadow: var(--shadow-sm) !important;
  }

  .profile-sidebar-header {
    border-color: var(--border-color) !important;
  }

  .profile-sidebar-title {
    color: var(--text-primary) !important;
  }

  .profile-sidebar-content {
    color: var(--text-secondary) !important;
  }

  .profile-sidebar-footer {
    border-color: var(--border-color) !important;
  }

  .profile-sidebar-action {
    color: var(--primary-color) !important;
  }
}
