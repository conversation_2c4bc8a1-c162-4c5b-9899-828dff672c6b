/**
 * Styles spécifiques pour corriger les éléments qui ne s'appliquent pas correctement au mode sombre
 * Ce fichier contient des sélecteurs très spécifiques pour cibler les éléments problématiques
 */

/* Correction pour les cartes d'information sur la page d'accueil */
.dark-theme {
  /* Cartes sur la page d'accueil */
  .info-card,
  .post-card,
  .company-card,
  .workspace-card,
  .pricing-card,
  .profile-card,
  mat-card,
  .mat-mdc-card {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
    box-shadow: var(--shadow-sm) !important;
  }

  /* Titres des cartes */
  .info-card mat-card-title,
  .post-card mat-card-title,
  .company-card mat-card-title,
  .workspace-card mat-card-title,
  .pricing-card mat-card-title,
  .profile-card mat-card-title,
  mat-card mat-card-title,
  .mat-mdc-card-title {
    color: var(--text-primary) !important;
  }

  /* Contenu des cartes */
  .info-card mat-card-content,
  .post-card mat-card-content,
  .company-card mat-card-content,
  .workspace-card mat-card-content,
  .pricing-card mat-card-content,
  .profile-card mat-card-content,
  mat-card mat-card-content,
  .mat-mdc-card-content {
    color: var(--text-secondary) !important;
  }

  /* Correction pour l'arrière-plan de la page de profil */
  .profile-header,
  .profile-cover,
  .profile-avatar-container,
  .profile-avatar,
  .profile-main-info,
  .profile-body {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
  }

  /* Correction pour les éléments d'information */
  .info-item,
  .info-text {
    color: var(--text-secondary) !important;
    border-color: var(--border-color) !important;
  }

  .info-item mat-icon {
    color: var(--primary-color) !important;
  }

  .info-item:hover {
    background-color: rgba(var(--primary-color-rgb), 0.1) !important;
    color: var(--primary-color) !important;
  }

  /* Correction pour les cartes d'entreprise */
  .company-card {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
    box-shadow: var(--shadow-sm) !important;
  }

  .company-card::before {
    background: linear-gradient(90deg, rgba(0, 0, 0, 0) 0%, rgba(var(--primary-color-rgb), 0.2) 50%, rgba(0, 0, 0, 0) 100%) !important;
  }

  .company-name {
    color: var(--text-primary) !important;
  }

  .company-logo-container {
    background-color: var(--background-alt) !important;
  }

  .company-icon {
    color: var(--primary-color) !important;
  }

  /* Correction pour les éléments de la page d'accueil */
  .carousel-description h2 {
    color: var(--text-primary) !important;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color)) !important;
    -webkit-background-clip: text !important;
    background-clip: text !important;
  }

  .carousel-description p {
    color: var(--text-secondary) !important;
  }

  .section-title {
    color: var(--text-primary) !important;
  }

  .about-description p {
    color: var(--text-secondary) !important;
  }

  /* Correction pour les éléments de la page d'accueil des posts */
  .post-cards-container {
    background-color: var(--background-color) !important;
  }

  .post-card {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
  }

  .post-card-header {
    border-color: var(--border-color) !important;
  }

  .avatar-icon {
    background-color: var(--primary-color) !important;
    border-color: var(--border-color) !important;
  }

  .workspace-name {
    color: var(--primary-color) !important;
  }

  .description {
    color: var(--text-secondary) !important;
  }

  .info {
    color: var(--text-muted) !important;
  }

  .info mat-icon {
    color: var(--primary-color) !important;
  }

  /* Correction pour les éléments avec des styles en ligne spécifiques */
  [style*="background-color: #F8F9FA"],
  [style*="background-color:#F8F9FA"],
  [style*="background: #F8F9FA"],
  [style*="background:#F8F9FA"] {
    background-color: var(--background-card) !important;
    background: var(--background-card) !important;
  }

  [style*="color: #1A2A44"],
  [style*="color:#1A2A44"],
  [style*="color: #001660"],
  [style*="color:#001660"] {
    color: var(--text-primary) !important;
  }

  [style*="color: #4B5563"],
  [style*="color:#4B5563"] {
    color: var(--text-secondary) !important;
  }

  [style*="border-color: #e9ecef"],
  [style*="border-color:#e9ecef"],
  [style*="border: 1px solid #e9ecef"],
  [style*="border:1px solid #e9ecef"] {
    border-color: var(--border-color) !important;
  }

  /* Correction pour les éléments avec des classes spécifiques */
  .bg-white,
  .bg-light,
  .bg-gray-100,
  .bg-gray-200 {
    background-color: var(--background-card) !important;
  }

  .text-dark,
  .text-black,
  .text-gray-800,
  .text-gray-900 {
    color: var(--text-primary) !important;
  }

  .text-muted,
  .text-gray-500,
  .text-gray-600,
  .text-gray-700 {
    color: var(--text-secondary) !important;
  }

  .border,
  .border-light,
  .border-gray-100,
  .border-gray-200 {
    border-color: var(--border-color) !important;
  }

  /* Correction pour les éléments avec des attributs spécifiques */
  [class*="mat-elevation-z"] {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
  }

  /* Correction pour les éléments avec des styles générés dynamiquement */
  .mat-mdc-card,
  .mat-mdc-card-content,
  .mat-mdc-card-title,
  .mat-mdc-card-subtitle,
  .mat-mdc-card-actions,
  .mat-mdc-card-header {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
  }

  /* Correction pour les éléments de la page d'accueil */
  #hero,
  .hero,
  .about-section,
  .workspaces-section,
  .services-section,
  .pricing-section,
  .contact-section,
  .footer {
    background-color: var(--background-color) !important;
    color: var(--text-primary) !important;
  }

  /* Correction pour les éléments de formulaire */
  input,
  textarea,
  select,
  .mat-mdc-form-field,
  .mat-mdc-input-element,
  .mat-mdc-select,
  .mat-mdc-select-value,
  .mat-mdc-select-arrow,
  .mat-mdc-form-field-label {
    background-color: var(--background-alt) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
  }

  /* Correction pour les éléments de navigation */
  .mat-toolbar,
  .mat-sidenav,
  .mat-drawer,
  .mat-drawer-container,
  .mat-drawer-content,
  .mat-sidenav-container,
  .mat-sidenav-content {
    background-color: var(--background-alt) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
  }

  /* Correction pour les éléments de dialogue */
  .mat-mdc-dialog-container,
  .mat-mdc-dialog-content,
  .mat-mdc-dialog-actions,
  .mat-mdc-dialog-title {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
  }

  /* Correction pour les éléments de menu */
  .mat-mdc-menu-panel,
  .mat-mdc-menu-content,
  .mat-mdc-menu-item {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
  }

  /* Correction pour les éléments de liste */
  .mat-mdc-list,
  .mat-mdc-list-item,
  .mat-mdc-list-item-title,
  .mat-mdc-list-item-line {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
  }

  /* Correction pour les éléments de tableau */
  .mat-mdc-table,
  .mat-mdc-header-row,
  .mat-mdc-row,
  .mat-mdc-header-cell,
  .mat-mdc-cell {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
  }

  /* Correction pour les éléments de pagination */
  .mat-mdc-paginator,
  .mat-mdc-paginator-container,
  .mat-mdc-paginator-range-label,
  .mat-mdc-paginator-page-size,
  .mat-mdc-paginator-page-size-label,
  .mat-mdc-paginator-page-size-select {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
  }

  /* Correction pour les éléments de bouton */
  .mat-mdc-button,
  .mat-mdc-raised-button,
  .mat-mdc-stroked-button,
  .mat-mdc-flat-button,
  .mat-mdc-icon-button {
    color: var(--text-primary) !important;
  }

  .mat-mdc-raised-button.mat-primary {
    background-color: var(--primary-color) !important;
    color: var(--primary-contrast) !important;
  }

  .mat-mdc-raised-button.mat-accent {
    background-color: var(--accent-color) !important;
    color: var(--accent-contrast) !important;
  }

  /* Correction pour les éléments de grille */
  mat-grid-list,
  mat-grid-tile {
    background-color: var(--background-color) !important;
    color: var(--text-primary) !important;
  }

  /* Correction pour les éléments de fond */
  body,
  html,
  .app-container,
  .main-container,
  .content-container,
  .page-container {
    background-color: var(--background-color) !important;
    color: var(--text-primary) !important;
  }
}
