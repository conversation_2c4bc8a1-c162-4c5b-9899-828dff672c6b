/**
 * Styles ultra-spécifiques pour les mat-card et info-row en mode sombre
 * Ce fichier cible spécifiquement les mat-card dans les cartes d'information du profil
 * et les div de classe info-row
 */

/* Styles pour les mat-card et info-row en mode sombre */
.dark-theme {
  /* Styles pour les mat-card dans les cartes d'information */
  .info-card mat-card,
  .info-card .mat-card,
  .info-card .mat-mdc-card,
  .info-card [class*="mat-card"],
  .info-card [class*="mat-mdc-card"],
  mat-card.info-card,
  .mat-card.info-card,
  .mat-mdc-card.info-card,
  [class*="mat-card"].info-card,
  [class*="mat-mdc-card"].info-card,
  mat-card mat-card,
  .mat-card .mat-card,
  .mat-mdc-card .mat-mdc-card,
  [class*="mat-card"] [class*="mat-card"],
  [class*="mat-mdc-card"] [class*="mat-mdc-card"],
  .item-block mat-card,
  .item-block .mat-card,
  .item-block .mat-mdc-card,
  .item-block [class*="mat-card"],
  .item-block [class*="mat-mdc-card"],
  [class*="item-block"] mat-card,
  [class*="item-block"] .mat-card,
  [class*="item-block"] .mat-mdc-card,
  [class*="item-block"] [class*="mat-card"],
  [class*="item-block"] [class*="mat-mdc-card"] {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
    box-shadow: var(--shadow-sm) !important;
    
    &:hover {
      box-shadow: var(--shadow-md) !important;
    }
    
    /* En-tête de carte */
    mat-card-header,
    .mat-card-header,
    .mat-mdc-card-header,
    [class*="mat-card-header"],
    [class*="mat-mdc-card-header"] {
      background-color: var(--background-alt) !important;
      color: var(--text-primary) !important;
      border-color: var(--border-color) !important;
      
      mat-card-title,
      .mat-card-title,
      .mat-mdc-card-title,
      [class*="mat-card-title"],
      [class*="mat-mdc-card-title"] {
        color: var(--text-primary) !important;
        
        mat-icon,
        .mat-icon,
        [class*="mat-icon"] {
          color: var(--primary-color) !important;
        }
      }
      
      mat-card-subtitle,
      .mat-card-subtitle,
      .mat-mdc-card-subtitle,
      [class*="mat-card-subtitle"],
      [class*="mat-mdc-card-subtitle"] {
        color: var(--text-secondary) !important;
      }
    }
    
    /* Contenu de carte */
    mat-card-content,
    .mat-card-content,
    .mat-mdc-card-content,
    [class*="mat-card-content"],
    [class*="mat-mdc-card-content"] {
      background-color: var(--background-card) !important;
      color: var(--text-secondary) !important;
      
      p {
        color: var(--text-secondary) !important;
        
        mat-icon,
        .mat-icon,
        [class*="mat-icon"] {
          color: var(--primary-color) !important;
        }
      }
    }
    
    /* Actions de carte */
    mat-card-actions,
    .mat-card-actions,
    .mat-mdc-card-actions,
    [class*="mat-card-actions"],
    [class*="mat-mdc-card-actions"] {
      background-color: var(--background-card) !important;
      border-color: var(--border-color) !important;
      
      button {
        color: var(--primary-color) !important;
        
        &:hover {
          background-color: rgba(var(--primary-color-rgb), 0.1) !important;
        }
        
        mat-icon,
        .mat-icon,
        [class*="mat-icon"] {
          color: var(--primary-color) !important;
        }
      }
    }
    
    /* Pied de carte */
    mat-card-footer,
    .mat-card-footer,
    .mat-mdc-card-footer,
    [class*="mat-card-footer"],
    [class*="mat-mdc-card-footer"] {
      background-color: var(--background-card) !important;
      border-color: var(--border-color) !important;
    }
  }

  /* Styles pour les info-row */
  .info-row,
  [class*="info-row"] {
    background-color: var(--background-alt) !important;
    color: var(--text-secondary) !important;
    border-color: var(--border-color) !important;
    
    &:hover {
      background-color: rgba(var(--primary-color-rgb), 0.05) !important;
    }
    
    /* Éléments de texte */
    span,
    p,
    div,
    label,
    h1, h2, h3, h4, h5, h6 {
      color: var(--text-secondary) !important;
    }
    
    /* Éléments forts */
    strong,
    b,
    em,
    i {
      color: var(--text-primary) !important;
    }
    
    /* Liens */
    a,
    .link,
    [class*="link"] {
      color: var(--primary-color) !important;
      
      &:hover {
        color: var(--primary-light) !important;
        text-decoration: underline;
      }
    }
    
    /* Icônes */
    mat-icon,
    .mat-icon,
    [class*="mat-icon"],
    i,
    .fa,
    .fas,
    .far,
    .fab,
    [class*="fa-"] {
      color: var(--primary-color) !important;
    }
    
    /* Boutons */
    button {
      color: var(--primary-color) !important;
      
      &:hover {
        background-color: rgba(var(--primary-color-rgb), 0.1) !important;
      }
      
      mat-icon,
      .mat-icon,
      [class*="mat-icon"],
      i,
      .fa,
      .fas,
      .far,
      .fab,
      [class*="fa-"] {
        color: var(--primary-color) !important;
      }
    }
  }

  /* Styles pour les mat-card dans les cartes spécifiques */
  .certification-card,
  .experience-card,
  .education-card,
  .skill-card,
  .link-card,
  .language-card,
  [class*="certification-card"],
  [class*="experience-card"],
  [class*="education-card"],
  [class*="skill-card"],
  [class*="link-card"],
  [class*="language-card"],
  [class*="certification"],
  [class*="experience"],
  [class*="education"],
  [class*="skill"],
  [class*="link"],
  [class*="language"] {
    mat-card,
    .mat-card,
    .mat-mdc-card,
    [class*="mat-card"],
    [class*="mat-mdc-card"] {
      background-color: var(--background-card) !important;
      color: var(--text-primary) !important;
      border-color: var(--border-color) !important;
      box-shadow: var(--shadow-sm) !important;
      
      &:hover {
        box-shadow: var(--shadow-md) !important;
      }
      
      /* En-tête de carte */
      mat-card-header,
      .mat-card-header,
      .mat-mdc-card-header,
      [class*="mat-card-header"],
      [class*="mat-mdc-card-header"] {
        background-color: var(--background-alt) !important;
        color: var(--text-primary) !important;
        border-color: var(--border-color) !important;
        
        mat-card-title,
        .mat-card-title,
        .mat-mdc-card-title,
        [class*="mat-card-title"],
        [class*="mat-mdc-card-title"] {
          color: var(--text-primary) !important;
          
          mat-icon,
          .mat-icon,
          [class*="mat-icon"] {
            color: var(--primary-color) !important;
          }
        }
        
        mat-card-subtitle,
        .mat-card-subtitle,
        .mat-mdc-card-subtitle,
        [class*="mat-card-subtitle"],
        [class*="mat-mdc-card-subtitle"] {
          color: var(--text-secondary) !important;
        }
      }
      
      /* Contenu de carte */
      mat-card-content,
      .mat-card-content,
      .mat-mdc-card-content,
      [class*="mat-card-content"],
      [class*="mat-mdc-card-content"] {
        background-color: var(--background-card) !important;
        color: var(--text-secondary) !important;
        
        p {
          color: var(--text-secondary) !important;
          
          mat-icon,
          .mat-icon,
          [class*="mat-icon"] {
            color: var(--primary-color) !important;
          }
        }
      }
      
      /* Actions de carte */
      mat-card-actions,
      .mat-card-actions,
      .mat-mdc-card-actions,
      [class*="mat-card-actions"],
      [class*="mat-mdc-card-actions"] {
        background-color: var(--background-card) !important;
        border-color: var(--border-color) !important;
        
        button {
          color: var(--primary-color) !important;
          
          &:hover {
            background-color: rgba(var(--primary-color-rgb), 0.1) !important;
          }
          
          mat-icon,
          .mat-icon,
          [class*="mat-icon"] {
            color: var(--primary-color) !important;
          }
        }
      }
      
      /* Pied de carte */
      mat-card-footer,
      .mat-card-footer,
      .mat-mdc-card-footer,
      [class*="mat-card-footer"],
      [class*="mat-mdc-card-footer"] {
        background-color: var(--background-card) !important;
        border-color: var(--border-color) !important;
      }
    }
    
    /* Styles pour les info-row dans les cartes spécifiques */
    .info-row,
    [class*="info-row"] {
      background-color: var(--background-alt) !important;
      color: var(--text-secondary) !important;
      border-color: var(--border-color) !important;
      
      &:hover {
        background-color: rgba(var(--primary-color-rgb), 0.05) !important;
      }
      
      /* Éléments de texte */
      span,
      p,
      div,
      label,
      h1, h2, h3, h4, h5, h6 {
        color: var(--text-secondary) !important;
      }
      
      /* Éléments forts */
      strong,
      b,
      em,
      i {
        color: var(--text-primary) !important;
      }
      
      /* Liens */
      a,
      .link,
      [class*="link"] {
        color: var(--primary-color) !important;
        
        &:hover {
          color: var(--primary-light) !important;
          text-decoration: underline;
        }
      }
      
      /* Icônes */
      mat-icon,
      .mat-icon,
      [class*="mat-icon"],
      i,
      .fa,
      .fas,
      .far,
      .fab,
      [class*="fa-"] {
        color: var(--primary-color) !important;
      }
      
      /* Boutons */
      button {
        color: var(--primary-color) !important;
        
        &:hover {
          background-color: rgba(var(--primary-color-rgb), 0.1) !important;
        }
        
        mat-icon,
        .mat-icon,
        [class*="mat-icon"],
        i,
        .fa,
        .fas,
        .far,
        .fab,
        [class*="fa-"] {
          color: var(--primary-color) !important;
        }
      }
    }
  }

  /* Styles pour les éléments avec des styles en ligne */
  mat-card,
  .mat-card,
  .mat-mdc-card,
  [class*="mat-card"],
  [class*="mat-mdc-card"],
  .info-row,
  [class*="info-row"] {
    &[style*="background-color: #fff"],
    &[style*="background-color:#fff"],
    &[style*="background-color: white"],
    &[style*="background-color:#white"],
    &[style*="background-color: rgb(255, 255, 255)"],
    &[style*="background-color:rgb(255,255,255)"],
    &[style*="background-color: rgba(255, 255, 255"],
    &[style*="background-color:rgba(255,255,255"],
    &[style*="background: #fff"],
    &[style*="background:#fff"],
    &[style*="background: white"],
    &[style*="background:#white"],
    &[style*="background: rgb(255, 255, 255)"],
    &[style*="background:rgb(255,255,255)"],
    &[style*="background: rgba(255, 255, 255"],
    &[style*="background:rgba(255,255,255"],
    &[style*="background-color: #f8f9fa"],
    &[style*="background-color:#f8f9fa"],
    &[style*="background: #f8f9fa"],
    &[style*="background:#f8f9fa"],
    &[style*="background-color: #f4f4f4"],
    &[style*="background-color:#f4f4f4"],
    &[style*="background: #f4f4f4"],
    &[style*="background:#f4f4f4"],
    &[style*="background-color: #f5f5f5"],
    &[style*="background-color:#f5f5f5"],
    &[style*="background: #f5f5f5"],
    &[style*="background:#f5f5f5"],
    &[style*="background-color: #f0f0f0"],
    &[style*="background-color:#f0f0f0"],
    &[style*="background: #f0f0f0"],
    &[style*="background:#f0f0f0"],
    &[style*="background-color: #eeeeee"],
    &[style*="background-color:#eeeeee"],
    &[style*="background: #eeeeee"],
    &[style*="background:#eeeeee"],
    &[style*="background-color: #e9ecef"],
    &[style*="background-color:#e9ecef"],
    &[style*="background: #e9ecef"],
    &[style*="background:#e9ecef"] {
      background-color: var(--background-card) !important;
      background: var(--background-card) !important;
    }
    
    &[style*="color: #000"],
    &[style*="color:#000"],
    &[style*="color: black"],
    &[style*="color:#black"],
    &[style*="color: rgb(0, 0, 0)"],
    &[style*="color:rgb(0,0,0)"],
    &[style*="color: rgba(0, 0, 0"],
    &[style*="color:rgba(0,0,0"],
    &[style*="color: #111111"],
    &[style*="color:#111111"],
    &[style*="color: #222222"],
    &[style*="color:#222222"],
    &[style*="color: #333333"],
    &[style*="color:#333333"],
    &[style*="color: #444444"],
    &[style*="color:#444444"],
    &[style*="color: #1a2a44"],
    &[style*="color:#1a2a44"],
    &[style*="color: #001660"],
    &[style*="color:#001660"] {
      color: var(--text-primary) !important;
    }
    
    &[style*="color: #555555"],
    &[style*="color:#555555"],
    &[style*="color: #666666"],
    &[style*="color:#666666"],
    &[style*="color: #777777"],
    &[style*="color:#777777"],
    &[style*="color: #888888"],
    &[style*="color:#888888"],
    &[style*="color: #4b5563"],
    &[style*="color:#4b5563"] {
      color: var(--text-secondary) !important;
    }
    
    &[style*="color: #999999"],
    &[style*="color:#999999"],
    &[style*="color: #aaaaaa"],
    &[style*="color:#aaaaaa"],
    &[style*="color: #bbbbbb"],
    &[style*="color:#bbbbbb"],
    &[style*="color: #cccccc"],
    &[style*="color:#cccccc"],
    &[style*="color: gray"],
    &[style*="color:gray"],
    &[style*="color: grey"],
    &[style*="color:grey"] {
      color: var(--text-muted) !important;
    }
    
    &[style*="border-color: #ccc"],
    &[style*="border-color:#ccc"],
    &[style*="border-color: #ddd"],
    &[style*="border-color:#ddd"],
    &[style*="border-color: #eee"],
    &[style*="border-color:#eee"],
    &[style*="border: 1px solid #ccc"],
    &[style*="border:1px solid #ccc"],
    &[style*="border: 1px solid #ddd"],
    &[style*="border:1px solid #ddd"],
    &[style*="border: 1px solid #eee"],
    &[style*="border:1px solid #eee"],
    &[style*="border-color: #e0e0e0"],
    &[style*="border-color:#e0e0e0"],
    &[style*="border: 1px solid #e0e0e0"],
    &[style*="border:1px solid #e0e0e0"],
    &[style*="border-color: #e8ecef"],
    &[style*="border-color:#e8ecef"],
    &[style*="border: 1px solid #e8ecef"],
    &[style*="border:1px solid #e8ecef"] {
      border-color: var(--border-color) !important;
    }
  }
}
