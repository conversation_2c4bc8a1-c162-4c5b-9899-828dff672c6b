/**
 * Styles ultra-spécifiques pour les noms dans les cartes de certification, expérience, éducation, compétence et lien
 * Ce fichier cible spécifiquement les noms dans ces cartes
 */

/* Styles pour les noms dans les cartes en mode sombre */
.dark-theme {
  /* Styles généraux pour les noms dans les cartes */
  .item-title h4,
  .item-title .title,
  .item-title .name,
  .item-title .heading,
  .item-title .label,
  .item-title .text,
  .item-title span,
  .item-title strong,
  .item-title b,
  .item-title em,
  .item-title i,
  .item-title a,
  .item-title .link,
  .item-name,
  .item-title-text,
  .item-heading,
  .item-label,
  .item-text,
  .title-text,
  .name-text,
  .heading-text,
  .label-text,
  .text-primary,
  .text-title,
  .text-name,
  .text-heading,
  .text-label,
  [class*="item-title"] h4,
  [class*="item-title"] .title,
  [class*="item-title"] .name,
  [class*="item-title"] .heading,
  [class*="item-title"] .label,
  [class*="item-title"] .text,
  [class*="item-title"] span,
  [class*="item-title"] strong,
  [class*="item-title"] b,
  [class*="item-title"] em,
  [class*="item-title"] i,
  [class*="item-title"] a,
  [class*="item-title"] .link,
  [class*="item-name"],
  [class*="item-title-text"],
  [class*="item-heading"],
  [class*="item-label"],
  [class*="item-text"],
  [class*="title-text"],
  [class*="name-text"],
  [class*="heading-text"],
  [class*="label-text"],
  [class*="text-primary"],
  [class*="text-title"],
  [class*="text-name"],
  [class*="text-heading"],
  [class*="text-label"] {
    color: var(--text-primary) !important;
    
    &:hover {
      color: var(--primary-color) !important;
    }
    
    a, .link, [class*="link"] {
      color: var(--primary-color) !important;
      
      &:hover {
        color: var(--primary-light) !important;
        text-decoration: underline;
      }
    }
  }

  /* Styles spécifiques pour les noms dans les cartes de certification */
  .certification-card,
  [class*="certification-card"],
  [class*="certification"] {
    .item-title h4,
    .item-title .title,
    .item-title .name,
    .item-title .heading,
    .item-title .label,
    .item-title .text,
    .item-title span,
    .item-title strong,
    .item-title b,
    .item-title em,
    .item-title i,
    .item-title a,
    .item-title .link,
    .item-name,
    .item-title-text,
    .item-heading,
    .item-label,
    .item-text,
    .title-text,
    .name-text,
    .heading-text,
    .label-text,
    .text-primary,
    .text-title,
    .text-name,
    .text-heading,
    .text-label,
    [class*="item-title"] h4,
    [class*="item-title"] .title,
    [class*="item-title"] .name,
    [class*="item-title"] .heading,
    [class*="item-title"] .label,
    [class*="item-title"] .text,
    [class*="item-title"] span,
    [class*="item-title"] strong,
    [class*="item-title"] b,
    [class*="item-title"] em,
    [class*="item-title"] i,
    [class*="item-title"] a,
    [class*="item-title"] .link,
    [class*="item-name"],
    [class*="item-title-text"],
    [class*="item-heading"],
    [class*="item-label"],
    [class*="item-text"],
    [class*="title-text"],
    [class*="name-text"],
    [class*="heading-text"],
    [class*="label-text"],
    [class*="text-primary"],
    [class*="text-title"],
    [class*="text-name"],
    [class*="text-heading"],
    [class*="text-label"],
    .certification-name,
    .certification-title,
    .certification-heading,
    .certification-label,
    .certification-text,
    [class*="certification-name"],
    [class*="certification-title"],
    [class*="certification-heading"],
    [class*="certification-label"],
    [class*="certification-text"] {
      color: var(--text-primary) !important;
      
      &:hover {
        color: var(--primary-color) !important;
      }
      
      a, .link, [class*="link"] {
        color: var(--primary-color) !important;
        
        &:hover {
          color: var(--primary-light) !important;
          text-decoration: underline;
        }
      }
    }
  }

  /* Styles spécifiques pour les noms dans les cartes d'expérience */
  .experience-card,
  [class*="experience-card"],
  [class*="experience"] {
    .item-title h4,
    .item-title .title,
    .item-title .name,
    .item-title .heading,
    .item-title .label,
    .item-title .text,
    .item-title span,
    .item-title strong,
    .item-title b,
    .item-title em,
    .item-title i,
    .item-title a,
    .item-title .link,
    .item-name,
    .item-title-text,
    .item-heading,
    .item-label,
    .item-text,
    .title-text,
    .name-text,
    .heading-text,
    .label-text,
    .text-primary,
    .text-title,
    .text-name,
    .text-heading,
    .text-label,
    [class*="item-title"] h4,
    [class*="item-title"] .title,
    [class*="item-title"] .name,
    [class*="item-title"] .heading,
    [class*="item-title"] .label,
    [class*="item-title"] .text,
    [class*="item-title"] span,
    [class*="item-title"] strong,
    [class*="item-title"] b,
    [class*="item-title"] em,
    [class*="item-title"] i,
    [class*="item-title"] a,
    [class*="item-title"] .link,
    [class*="item-name"],
    [class*="item-title-text"],
    [class*="item-heading"],
    [class*="item-label"],
    [class*="item-text"],
    [class*="title-text"],
    [class*="name-text"],
    [class*="heading-text"],
    [class*="label-text"],
    [class*="text-primary"],
    [class*="text-title"],
    [class*="text-name"],
    [class*="text-heading"],
    [class*="text-label"],
    .experience-name,
    .experience-title,
    .experience-heading,
    .experience-label,
    .experience-text,
    .company-name,
    .company-title,
    .company-heading,
    .company-label,
    .company-text,
    .job-name,
    .job-title,
    .job-heading,
    .job-label,
    .job-text,
    .position-name,
    .position-title,
    .position-heading,
    .position-label,
    .position-text,
    [class*="experience-name"],
    [class*="experience-title"],
    [class*="experience-heading"],
    [class*="experience-label"],
    [class*="experience-text"],
    [class*="company-name"],
    [class*="company-title"],
    [class*="company-heading"],
    [class*="company-label"],
    [class*="company-text"],
    [class*="job-name"],
    [class*="job-title"],
    [class*="job-heading"],
    [class*="job-label"],
    [class*="job-text"],
    [class*="position-name"],
    [class*="position-title"],
    [class*="position-heading"],
    [class*="position-label"],
    [class*="position-text"] {
      color: var(--text-primary) !important;
      
      &:hover {
        color: var(--primary-color) !important;
      }
      
      a, .link, [class*="link"] {
        color: var(--primary-color) !important;
        
        &:hover {
          color: var(--primary-light) !important;
          text-decoration: underline;
        }
      }
    }
  }

  /* Styles spécifiques pour les noms dans les cartes d'éducation */
  .education-card,
  [class*="education-card"],
  [class*="education"] {
    .item-title h4,
    .item-title .title,
    .item-title .name,
    .item-title .heading,
    .item-title .label,
    .item-title .text,
    .item-title span,
    .item-title strong,
    .item-title b,
    .item-title em,
    .item-title i,
    .item-title a,
    .item-title .link,
    .item-name,
    .item-title-text,
    .item-heading,
    .item-label,
    .item-text,
    .title-text,
    .name-text,
    .heading-text,
    .label-text,
    .text-primary,
    .text-title,
    .text-name,
    .text-heading,
    .text-label,
    [class*="item-title"] h4,
    [class*="item-title"] .title,
    [class*="item-title"] .name,
    [class*="item-title"] .heading,
    [class*="item-title"] .label,
    [class*="item-title"] .text,
    [class*="item-title"] span,
    [class*="item-title"] strong,
    [class*="item-title"] b,
    [class*="item-title"] em,
    [class*="item-title"] i,
    [class*="item-title"] a,
    [class*="item-title"] .link,
    [class*="item-name"],
    [class*="item-title-text"],
    [class*="item-heading"],
    [class*="item-label"],
    [class*="item-text"],
    [class*="title-text"],
    [class*="name-text"],
    [class*="heading-text"],
    [class*="label-text"],
    [class*="text-primary"],
    [class*="text-title"],
    [class*="text-name"],
    [class*="text-heading"],
    [class*="text-label"],
    .education-name,
    .education-title,
    .education-heading,
    .education-label,
    .education-text,
    .school-name,
    .school-title,
    .school-heading,
    .school-label,
    .school-text,
    .university-name,
    .university-title,
    .university-heading,
    .university-label,
    .university-text,
    .degree-name,
    .degree-title,
    .degree-heading,
    .degree-label,
    .degree-text,
    [class*="education-name"],
    [class*="education-title"],
    [class*="education-heading"],
    [class*="education-label"],
    [class*="education-text"],
    [class*="school-name"],
    [class*="school-title"],
    [class*="school-heading"],
    [class*="school-label"],
    [class*="school-text"],
    [class*="university-name"],
    [class*="university-title"],
    [class*="university-heading"],
    [class*="university-label"],
    [class*="university-text"],
    [class*="degree-name"],
    [class*="degree-title"],
    [class*="degree-heading"],
    [class*="degree-label"],
    [class*="degree-text"] {
      color: var(--text-primary) !important;
      
      &:hover {
        color: var(--primary-color) !important;
      }
      
      a, .link, [class*="link"] {
        color: var(--primary-color) !important;
        
        &:hover {
          color: var(--primary-light) !important;
          text-decoration: underline;
        }
      }
    }
  }

  /* Styles spécifiques pour les noms dans les cartes de compétence */
  .skill-card,
  [class*="skill-card"],
  [class*="skill"] {
    .item-title h4,
    .item-title .title,
    .item-title .name,
    .item-title .heading,
    .item-title .label,
    .item-title .text,
    .item-title span,
    .item-title strong,
    .item-title b,
    .item-title em,
    .item-title i,
    .item-title a,
    .item-title .link,
    .item-name,
    .item-title-text,
    .item-heading,
    .item-label,
    .item-text,
    .title-text,
    .name-text,
    .heading-text,
    .label-text,
    .text-primary,
    .text-title,
    .text-name,
    .text-heading,
    .text-label,
    [class*="item-title"] h4,
    [class*="item-title"] .title,
    [class*="item-title"] .name,
    [class*="item-title"] .heading,
    [class*="item-title"] .label,
    [class*="item-title"] .text,
    [class*="item-title"] span,
    [class*="item-title"] strong,
    [class*="item-title"] b,
    [class*="item-title"] em,
    [class*="item-title"] i,
    [class*="item-title"] a,
    [class*="item-title"] .link,
    [class*="item-name"],
    [class*="item-title-text"],
    [class*="item-heading"],
    [class*="item-label"],
    [class*="item-text"],
    [class*="title-text"],
    [class*="name-text"],
    [class*="heading-text"],
    [class*="label-text"],
    [class*="text-primary"],
    [class*="text-title"],
    [class*="text-name"],
    [class*="text-heading"],
    [class*="text-label"],
    .skill-name,
    .skill-title,
    .skill-heading,
    .skill-label,
    .skill-text,
    [class*="skill-name"],
    [class*="skill-title"],
    [class*="skill-heading"],
    [class*="skill-label"],
    [class*="skill-text"] {
      color: var(--text-primary) !important;
      
      &:hover {
        color: var(--primary-color) !important;
      }
      
      a, .link, [class*="link"] {
        color: var(--primary-color) !important;
        
        &:hover {
          color: var(--primary-light) !important;
          text-decoration: underline;
        }
      }
    }
  }

  /* Styles spécifiques pour les noms dans les cartes de lien */
  .link-card,
  [class*="link-card"],
  [class*="link"] {
    .item-title h4,
    .item-title .title,
    .item-title .name,
    .item-title .heading,
    .item-title .label,
    .item-title .text,
    .item-title span,
    .item-title strong,
    .item-title b,
    .item-title em,
    .item-title i,
    .item-title a,
    .item-title .link,
    .item-name,
    .item-title-text,
    .item-heading,
    .item-label,
    .item-text,
    .title-text,
    .name-text,
    .heading-text,
    .label-text,
    .text-primary,
    .text-title,
    .text-name,
    .text-heading,
    .text-label,
    [class*="item-title"] h4,
    [class*="item-title"] .title,
    [class*="item-title"] .name,
    [class*="item-title"] .heading,
    [class*="item-title"] .label,
    [class*="item-title"] .text,
    [class*="item-title"] span,
    [class*="item-title"] strong,
    [class*="item-title"] b,
    [class*="item-title"] em,
    [class*="item-title"] i,
    [class*="item-title"] a,
    [class*="item-title"] .link,
    [class*="item-name"],
    [class*="item-title-text"],
    [class*="item-heading"],
    [class*="item-label"],
    [class*="item-text"],
    [class*="title-text"],
    [class*="name-text"],
    [class*="heading-text"],
    [class*="label-text"],
    [class*="text-primary"],
    [class*="text-title"],
    [class*="text-name"],
    [class*="text-heading"],
    [class*="text-label"],
    .link-name,
    .link-title,
    .link-heading,
    .link-label,
    .link-text,
    .website-name,
    .website-title,
    .website-heading,
    .website-label,
    .website-text,
    .url-name,
    .url-title,
    .url-heading,
    .url-label,
    .url-text,
    [class*="link-name"],
    [class*="link-title"],
    [class*="link-heading"],
    [class*="link-label"],
    [class*="link-text"],
    [class*="website-name"],
    [class*="website-title"],
    [class*="website-heading"],
    [class*="website-label"],
    [class*="website-text"],
    [class*="url-name"],
    [class*="url-title"],
    [class*="url-heading"],
    [class*="url-label"],
    [class*="url-text"] {
      color: var(--text-primary) !important;
      
      &:hover {
        color: var(--primary-color) !important;
      }
      
      a, .link, [class*="link"] {
        color: var(--primary-color) !important;
        
        &:hover {
          color: var(--primary-light) !important;
          text-decoration: underline;
        }
      }
    }
  }

  /* Styles pour les éléments avec des styles en ligne */
  .item-title h4,
  .item-title .title,
  .item-title .name,
  .item-title .heading,
  .item-title .label,
  .item-title .text,
  .item-title span,
  .item-title strong,
  .item-title b,
  .item-title em,
  .item-title i,
  .item-title a,
  .item-title .link,
  .item-name,
  .item-title-text,
  .item-heading,
  .item-label,
  .item-text,
  .title-text,
  .name-text,
  .heading-text,
  .label-text,
  .text-primary,
  .text-title,
  .text-name,
  .text-heading,
  .text-label,
  [class*="item-title"] h4,
  [class*="item-title"] .title,
  [class*="item-title"] .name,
  [class*="item-title"] .heading,
  [class*="item-title"] .label,
  [class*="item-title"] .text,
  [class*="item-title"] span,
  [class*="item-title"] strong,
  [class*="item-title"] b,
  [class*="item-title"] em,
  [class*="item-title"] i,
  [class*="item-title"] a,
  [class*="item-title"] .link,
  [class*="item-name"],
  [class*="item-title-text"],
  [class*="item-heading"],
  [class*="item-label"],
  [class*="item-text"],
  [class*="title-text"],
  [class*="name-text"],
  [class*="heading-text"],
  [class*="label-text"],
  [class*="text-primary"],
  [class*="text-title"],
  [class*="text-name"],
  [class*="text-heading"],
  [class*="text-label"] {
    &[style*="color: #000"],
    &[style*="color:#000"],
    &[style*="color: black"],
    &[style*="color:#black"],
    &[style*="color: rgb(0, 0, 0)"],
    &[style*="color:rgb(0,0,0)"],
    &[style*="color: rgba(0, 0, 0"],
    &[style*="color:rgba(0,0,0"],
    &[style*="color: #111111"],
    &[style*="color:#111111"],
    &[style*="color: #222222"],
    &[style*="color:#222222"],
    &[style*="color: #333333"],
    &[style*="color:#333333"],
    &[style*="color: #444444"],
    &[style*="color:#444444"],
    &[style*="color: #1a2a44"],
    &[style*="color:#1a2a44"],
    &[style*="color: #001660"],
    &[style*="color:#001660"] {
      color: var(--text-primary) !important;
    }
  }
}
