/**
 * Styles spécifiques pour le mode sombre
 * Ce fichier contient les styles qui s'appliquent uniquement en mode sombre
 * pour les composants personnalisés de l'application
 */

/* Appliquer le mode sombre à tous les éléments */
.dark-theme * {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

/* Force l'application du mode sombre à tous les éléments */
.dark-theme {
  color-scheme: dark !important;
}

/* Force l'application du mode sombre aux éléments avec des styles en ligne */
.dark-theme [style*="color:"],
.dark-theme [style*="background-color:"],
.dark-theme [style*="background:"],
.dark-theme [style*="border-color:"] {
  transition: all 0.3s ease !important;
}

/* Force l'application du mode sombre aux éléments générés dynamiquement */
.dark-theme [class*="mat-"],
.dark-theme [class*="cdk-"],
.dark-theme [class*="mdc-"] {
  color: var(--text-primary) !important;
  background-color: transparent !important;
}

/* Styles généraux pour les composants personnalisés en mode sombre */
.dark-theme {
  /* Headers et barres de navigation */
  .app-bar,
  .header-container,
  .mat-toolbar {
    background-color: var(--background-alt) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
    box-shadow: var(--shadow-sm) !important;
  }

  /* Sidenav */
  .sidenav,
  .sidebar {
    background: linear-gradient(180deg, #0a1a2f 0%, #0d1520 100%) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
  }

  .active-workspace-container {
    background-color: rgba(66, 153, 225, 0.15) !important;
  }

  .active-workspace-container:hover {
    background-color: rgba(66, 153, 225, 0.25) !important;
  }

  .workspace-name {
    color: var(--text-primary) !important;
  }

  /* Cartes et conteneurs */
  .post-card,
  .result-card,
  .info-card,
  .mat-card,
  .card {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
    box-shadow: var(--shadow-sm) !important;
  }

  .post-card:hover,
  .result-card:hover,
  .info-card:hover,
  .mat-card:hover,
  .card:hover {
    box-shadow: var(--shadow-md) !important;
  }

  .post-card-header,
  .card-header {
    border-color: var(--border-color) !important;
  }

  .post-card-content,
  .card-content {
    color: var(--text-secondary) !important;
  }

  /* Formulaires */
  .search-bar,
  mat-form-field,
  input,
  select,
  textarea {
    background-color: var(--background-alt) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
  }

  .search-bar:hover,
  mat-form-field:hover {
    border-color: var(--primary-color) !important;
    box-shadow: 0 2px 6px rgba(66, 153, 225, 0.2) !important;
  }

  .search-icon {
    color: var(--text-muted) !important;
  }

  .search-bar:hover .search-icon {
    color: var(--primary-color) !important;
  }

  /* Boutons */
  button[mat-button],
  button[mat-flat-button],
  button[mat-stroked-button],
  button[mat-icon-button] {
    color: var(--text-primary) !important;
  }

  button[mat-raised-button].mat-primary {
    background-color: var(--primary-color) !important;
    color: var(--primary-contrast) !important;
  }

  button[mat-raised-button].mat-accent {
    background-color: var(--accent-color) !important;
    color: var(--accent-contrast) !important;
  }

  /* Textes et typographie */
  h1, h2, h3, h4, h5, h6 {
    color: var(--text-primary) !important;
  }

  p, span, div {
    color: var(--text-secondary) !important;
  }

  .mat-icon {
    color: var(--text-primary) !important;
  }

  /* Liens */
  a {
    color: var(--primary-color) !important;
  }

  a:hover {
    color: var(--primary-light) !important;
  }

  /* Badges et étiquettes */
  .badge,
  .tag,
  .chip {
    background-color: var(--background-alt) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
  }

  /* Dialogues et modales */
  .mat-dialog-container,
  .modal,
  .confirm-dialog-container {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
  }

  .dialog-title {
    color: var(--text-primary) !important;
  }

  .dialog-content {
    color: var(--text-secondary) !important;
  }

  /* Tableaux */
  table {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
  }

  th {
    background-color: var(--background-alt) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
  }

  td {
    color: var(--text-secondary) !important;
    border-color: var(--border-color) !important;
  }

  tr:hover {
    background-color: var(--background-alt) !important;
  }

  /* Listes */
  .mat-list-item,
  .list-item {
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
  }

  .mat-list-item:hover,
  .list-item:hover {
    background-color: var(--background-alt) !important;
  }

  /* Onglets */
  .mat-tab-label {
    color: var(--text-primary) !important;
  }

  .mat-tab-group.mat-primary .mat-tab-label.mat-tab-label-active {
    color: var(--primary-color) !important;
  }

  /* Dividers */
  .mat-divider,
  hr {
    border-color: var(--border-color) !important;
  }

  /* Avatars et images */
  .avatar-icon {
    color: var(--text-muted) !important;
  }

  /* Styles spécifiques pour les composants personnalisés */

  /* Profil recruteur */
  .profile-header {
    background: linear-gradient(135deg, #0a192f 0%, #112a5e 100%) !important;
  }

  /* Styles spécifiques pour les composants avec des couleurs codées en dur */
  .result-card,
  .post-card,
  .mat-card,
  .card {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
  }

  /* Styles pour les composants qui utilisent des couleurs spécifiques */
  [style*="background-color: #FFFFFF"],
  [style*="background-color:#FFFFFF"],
  [style*="background: #FFFFFF"],
  [style*="background:#FFFFFF"] {
    background-color: var(--background-card) !important;
    background: var(--background-card) !important;
  }

  [style*="color: #333"],
  [style*="color:#333"],
  [style*="color: #1C2526"],
  [style*="color:#1C2526"] {
    color: var(--text-primary) !important;
  }

  [style*="color: #666"],
  [style*="color:#666"],
  [style*="color: #6B7280"],
  [style*="color:#6B7280"] {
    color: var(--text-secondary) !important;
  }

  [style*="border-color: #E5E7EB"],
  [style*="border-color:#E5E7EB"],
  [style*="border: 1px solid #E5E7EB"],
  [style*="border:1px solid #E5E7EB"] {
    border-color: var(--border-color) !important;
  }

  /* Styles spécifiques pour les composants de la page settings */
  .sidebar h2,
  .header h2 {
    color: var(--primary-color) !important;
  }

  /* Accueil posts */
  .main-container {
    background-color: var(--background-color) !important;
  }

  /* Styles spécifiques pour les composants de post */
  .post-title {
    color: var(--text-primary) !important;
  }

  .post-company {
    color: var(--primary-color) !important;
  }

  .post-location,
  .post-date,
  .post-salary {
    color: var(--text-secondary) !important;
  }

  .post-description {
    color: var(--text-secondary) !important;
  }

  .post-tags .tag {
    background-color: rgba(66, 153, 225, 0.15) !important;
    color: var(--primary-color) !important;
  }

  /* Styles spécifiques pour les composants de profil */
  .profile-section {
    background-color: var(--background-card) !important;
    border-color: var(--border-color) !important;
  }

  .profile-section h3 {
    color: var(--text-primary) !important;
    border-bottom-color: var(--border-color) !important;
  }

  .profile-section .section-content {
    color: var(--text-secondary) !important;
  }

  /* Styles spécifiques pour les composants de notification */
  .notification-item {
    background-color: var(--background-card) !important;
    border-color: var(--border-color) !important;
  }

  .notification-item:hover {
    background-color: var(--background-alt) !important;
  }

  .notification-title {
    color: var(--text-primary) !important;
  }

  .notification-content {
    color: var(--text-secondary) !important;
  }

  .notification-time {
    color: var(--text-muted) !important;
  }

  .notification-unread {
    background-color: rgba(66, 153, 225, 0.1) !important;
  }

  /* Styles spécifiques pour les composants de workspace */
  .workspace-card {
    background-color: var(--background-card) !important;
    border-color: var(--border-color) !important;
  }

  .workspace-name {
    color: var(--text-primary) !important;
  }

  .workspace-description {
    color: var(--text-secondary) !important;
  }

  .workspace-members {
    color: var(--text-muted) !important;
  }

  /* Styles spécifiques pour les composants de formulaire */
  .form-container {
    background-color: var(--background-card) !important;
    border-color: var(--border-color) !important;
  }

  .form-title {
    color: var(--text-primary) !important;
    border-bottom-color: var(--border-color) !important;
  }

  .form-subtitle {
    color: var(--text-secondary) !important;
  }

  .form-group label {
    color: var(--text-primary) !important;
  }

  .form-control {
    background-color: var(--background-alt) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
  }

  .form-control:focus {
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 2px rgba(66, 153, 225, 0.2) !important;
  }

  .form-error {
    color: var(--error-color) !important;
  }

  .form-success {
    color: var(--success-color) !important;
  }

  /* Styles spécifiques pour les composants de login et signup */
  .login-container,
  .signup-container,
  .auth-container {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
  }

  .login-form,
  .signup-form,
  .auth-form {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
  }

  .login-title,
  .signup-title,
  .auth-title {
    color: var(--text-primary) !important;
  }

  .login-subtitle,
  .signup-subtitle,
  .auth-subtitle {
    color: var(--text-secondary) !important;
  }

  .login-input,
  .signup-input,
  .auth-input {
    background-color: var(--background-alt) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
  }

  .login-button,
  .signup-button,
  .auth-button {
    background-color: var(--primary-color) !important;
    color: var(--primary-contrast) !important;
  }

  .login-link,
  .signup-link,
  .auth-link {
    color: var(--primary-color) !important;
  }

  /* Styles spécifiques pour les composants de dashboard */
  .dashboard-container {
    background-color: var(--background-color) !important;
    color: var(--text-primary) !important;
  }

  .dashboard-card {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
  }

  .dashboard-title {
    color: var(--text-primary) !important;
  }

  .dashboard-subtitle {
    color: var(--text-secondary) !important;
  }

  .dashboard-value {
    color: var(--primary-color) !important;
  }

  .dashboard-chart {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
  }

  /* Styles spécifiques pour les composants de modal et popup */
  .modal-container,
  .popup-container,
  .dialog-container {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
  }

  .modal-header,
  .popup-header,
  .dialog-header {
    background-color: var(--background-alt) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
  }

  .modal-title,
  .popup-title,
  .dialog-title {
    color: var(--text-primary) !important;
  }

  .modal-body,
  .popup-body,
  .dialog-body {
    background-color: var(--background-card) !important;
    color: var(--text-secondary) !important;
  }

  .modal-footer,
  .popup-footer,
  .dialog-footer {
    background-color: var(--background-alt) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
  }

  /* Styles spécifiques pour les composants de pagination */
  .pagination {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
  }

  .pagination-item {
    background-color: var(--background-alt) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
  }

  .pagination-item.active {
    background-color: var(--primary-color) !important;
    color: var(--primary-contrast) !important;
  }

  /* Styles spécifiques pour les composants de recherche */
  .search-container {
    background-color: var(--background-alt) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
  }

  .search-input {
    background-color: var(--background-alt) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
  }

  .search-button {
    background-color: var(--primary-color) !important;
    color: var(--primary-contrast) !important;
  }

  .search-results {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
  }

  /* Styles spécifiques pour les composants de filtre */
  .filter-container {
    background-color: var(--background-alt) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
  }

  .filter-title {
    color: var(--text-primary) !important;
  }

  .filter-option {
    color: var(--text-secondary) !important;
  }

  .filter-checkbox {
    border-color: var(--border-color) !important;
  }

  .filter-checkbox:checked {
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
  }

  .filter-radio {
    border-color: var(--border-color) !important;
  }

  .filter-radio:checked {
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
  }

  .filter-select {
    background-color: var(--background-alt) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
  }

  .filter-button {
    background-color: var(--primary-color) !important;
    color: var(--primary-contrast) !important;
  }

  /* Styles spécifiques pour les composants de toast et notification */
  .toast-container,
  .notification-container {
    background-color: var(--background-alt) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
  }

  .toast-success,
  .notification-success {
    background-color: var(--success-color) !important;
    color: white !important;
  }

  .toast-error,
  .notification-error {
    background-color: var(--error-color) !important;
    color: white !important;
  }

  .toast-warning,
  .notification-warning {
    background-color: var(--warning-color) !important;
    color: white !important;
  }

  .toast-info,
  .notification-info {
    background-color: var(--info-color) !important;
    color: white !important;
  }

  /* Settings */
  .sidebar {
    background-color: var(--background-alt) !important;
  }

  .sidebar ul li {
    color: var(--text-primary) !important;
  }

  .sidebar ul li.active {
    background-color: rgba(66, 153, 225, 0.2) !important;
  }

  .content {
    background-color: var(--background-color) !important;
  }

  /* Archive */
  .posts-title {
    color: var(--text-primary) !important;
  }

  /* Confirm dialog */
  .confirm-button {
    background-color: var(--primary-color) !important;
    color: var(--primary-contrast) !important;
  }

  .confirm-button:hover {
    background-color: var(--primary-dark) !important;
    box-shadow: 0 2px 8px rgba(66, 153, 225, 0.3) !important;
  }

  .cancel-button {
    color: var(--text-muted) !important;
  }

  /* Workspace display */
  .workspace-logo {
    background-color: var(--primary-dark) !important;
  }

  /* Animations et transitions */
  .theme-transition {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease !important;
  }
}
