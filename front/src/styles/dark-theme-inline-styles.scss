/**
 * Styles pour forcer l'application du mode sombre aux éléments avec des styles en ligne spécifiques
 * Ce fichier contient des sélecteurs très spécifiques pour cibler les éléments
 * avec des styles en ligne qui ne changent pas de thème avec les sélecteurs génériques
 */

/* Fonction pour générer des sélecteurs pour les styles en ligne */
@function generate-inline-style-selectors($property, $values, $replacement) {
  $selectors: ();
  
  @each $value in $values {
    $selectors: append($selectors, '[style*="#{$property}: #{$value}"]', comma);
    $selectors: append($selectors, '[style*="#{$property}:#{$value}"]', comma);
    $selectors: append($selectors, '[style*="#{$property}: #{$value};"]', comma);
    $selectors: append($selectors, '[style*="#{$property}:#{$value};"]', comma);
  }
  
  @return $selectors;
}

/* Liste des couleurs de fond blanches */
$white-backgrounds: (
  '#fff',
  '#ffffff',
  'white',
  'rgb(255, 255, 255)',
  'rgba(255, 255, 255, 1)',
  '#f8f9fa',
  '#f5f5f5',
  '#f0f0f0',
  '#eeeeee',
  '#e9ecef'
);

/* Liste des couleurs de texte noires */
$black-texts: (
  '#000',
  '#000000',
  'black',
  'rgb(0, 0, 0)',
  'rgba(0, 0, 0, 1)',
  '#111111',
  '#222222',
  '#333333',
  '#444444'
);

/* Liste des couleurs de texte grises */
$gray-texts: (
  '#555555',
  '#666666',
  '#777777',
  '#888888',
  '#999999',
  '#aaaaaa',
  'gray',
  'grey',
  'rgb(128, 128, 128)',
  'rgba(128, 128, 128, 1)'
);

/* Liste des couleurs de bordure claires */
$light-borders: (
  '#ccc',
  '#cccccc',
  '#ddd',
  '#dddddd',
  '#eee',
  '#eeeeee',
  'rgb(204, 204, 204)',
  'rgba(204, 204, 204, 1)',
  'rgb(221, 221, 221)',
  'rgba(221, 221, 221, 1)',
  'rgb(238, 238, 238)',
  'rgba(238, 238, 238, 1)'
);

/* Générer les sélecteurs pour les styles en ligne */
$white-background-selectors: generate-inline-style-selectors('background-color', $white-backgrounds, 'var(--background-card)');
$white-background-shorthand-selectors: generate-inline-style-selectors('background', $white-backgrounds, 'var(--background-card)');
$black-text-selectors: generate-inline-style-selectors('color', $black-texts, 'var(--text-primary)');
$gray-text-selectors: generate-inline-style-selectors('color', $gray-texts, 'var(--text-secondary)');
$light-border-selectors: generate-inline-style-selectors('border-color', $light-borders, 'var(--border-color)');
$light-border-top-selectors: generate-inline-style-selectors('border-top-color', $light-borders, 'var(--border-color)');
$light-border-right-selectors: generate-inline-style-selectors('border-right-color', $light-borders, 'var(--border-color)');
$light-border-bottom-selectors: generate-inline-style-selectors('border-bottom-color', $light-borders, 'var(--border-color)');
$light-border-left-selectors: generate-inline-style-selectors('border-left-color', $light-borders, 'var(--border-color)');

/* Appliquer les styles en mode sombre */
.dark-theme {
  /* Remplacer les couleurs de fond blanches */
  #{$white-background-selectors} {
    background-color: var(--background-card) !important;
  }
  
  #{$white-background-shorthand-selectors} {
    background: var(--background-card) !important;
  }
  
  /* Remplacer les couleurs de texte noires */
  #{$black-text-selectors} {
    color: var(--text-primary) !important;
  }
  
  /* Remplacer les couleurs de texte grises */
  #{$gray-text-selectors} {
    color: var(--text-secondary) !important;
  }
  
  /* Remplacer les couleurs de bordure claires */
  #{$light-border-selectors} {
    border-color: var(--border-color) !important;
  }
  
  #{$light-border-top-selectors} {
    border-top-color: var(--border-color) !important;
  }
  
  #{$light-border-right-selectors} {
    border-right-color: var(--border-color) !important;
  }
  
  #{$light-border-bottom-selectors} {
    border-bottom-color: var(--border-color) !important;
  }
  
  #{$light-border-left-selectors} {
    border-left-color: var(--border-color) !important;
  }
  
  /* Styles pour les éléments avec des styles en ligne spécifiques */
  [style*="box-shadow:"],
  [style*="box-shadow: "] {
    box-shadow: var(--shadow-md) !important;
  }
  
  [style*="text-shadow:"],
  [style*="text-shadow: "] {
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3) !important;
  }
  
  /* Styles pour les éléments avec des classes spécifiques */
  .bg-white,
  .bg-light,
  .bg-gray-100,
  .bg-gray-200,
  .bg-gray-300 {
    background-color: var(--background-card) !important;
  }
  
  .text-dark,
  .text-black,
  .text-gray-800,
  .text-gray-900 {
    color: var(--text-primary) !important;
  }
  
  .text-muted,
  .text-gray-500,
  .text-gray-600,
  .text-gray-700 {
    color: var(--text-secondary) !important;
  }
  
  .border,
  .border-light,
  .border-gray-100,
  .border-gray-200,
  .border-gray-300 {
    border-color: var(--border-color) !important;
  }
}
