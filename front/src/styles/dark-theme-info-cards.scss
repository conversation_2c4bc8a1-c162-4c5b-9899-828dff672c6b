/**
 * Styles ultra-spécifiques pour les cartes d'information en mode sombre
 * Ce fichier cible spécifiquement les cartes d'information et leurs éléments imbriqués
 */

/* Styles pour les cartes d'information en mode sombre */
.dark-theme {
  /* Sélecteur de base pour toutes les cartes d'information */
  .info-card,
  mat-card.info-card,
  .mat-card.info-card,
  .mat-mdc-card.info-card,
  [class*="info-card"],
  [class*="mat-card"],
  [class*="mat-mdc-card"] {
    background-color: var(--background-card) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
    box-shadow: var(--shadow-sm) !important;
    
    &::before {
      background-color: var(--accent-color) !important;
    }
    
    &:hover {
      box-shadow: var(--shadow-md) !important;
      background-color: var(--background-card) !important;
    }
    
    /* En-tête de carte */
    mat-card-header,
    .mat-card-header,
    .mat-mdc-card-header,
    [class*="mat-card-header"],
    [class*="mat-mdc-card-header"] {
      background-color: var(--background-alt) !important;
      color: var(--text-primary) !important;
      border-color: var(--border-color) !important;
      
      mat-card-title,
      .mat-card-title,
      .mat-mdc-card-title,
      [class*="mat-card-title"],
      [class*="mat-mdc-card-title"] {
        color: var(--text-primary) !important;
        
        .card-icon,
        [class*="card-icon"] {
          color: var(--primary-color) !important;
          background-color: rgba(var(--primary-color-rgb), 0.1) !important;
          
          &::after {
            background-color: var(--accent-color) !important;
          }
        }
        
        mat-icon,
        .mat-icon,
        [class*="mat-icon"] {
          color: var(--primary-color) !important;
        }
      }
      
      mat-card-subtitle,
      .mat-card-subtitle,
      .mat-mdc-card-subtitle,
      [class*="mat-card-subtitle"],
      [class*="mat-mdc-card-subtitle"] {
        color: var(--text-secondary) !important;
      }
    }
    
    /* Contenu de carte */
    mat-card-content,
    .mat-card-content,
    .mat-mdc-card-content,
    [class*="mat-card-content"],
    [class*="mat-mdc-card-content"] {
      background-color: var(--background-card) !important;
      color: var(--text-secondary) !important;
      
      p {
        color: var(--text-secondary) !important;
        
        mat-icon,
        .mat-icon,
        [class*="mat-icon"] {
          color: var(--primary-color) !important;
        }
        
        strong {
          color: var(--text-primary) !important;
        }
      }
      
      /* Blocs d'éléments imbriqués */
      .item-block,
      [class*="item-block"] {
        background-color: var(--background-alt) !important;
        color: var(--text-primary) !important;
        border-color: var(--border-color) !important;
        box-shadow: var(--shadow-sm) !important;
        
        &:hover {
          background-color: rgba(var(--primary-color-rgb), 0.05) !important;
          box-shadow: var(--shadow-md) !important;
        }
        
        /* En-tête de bloc */
        .item-header,
        [class*="item-header"] {
          color: var(--text-primary) !important;
          
          .item-title,
          [class*="item-title"] {
            color: var(--text-primary) !important;
            
            h4 {
              color: var(--text-primary) !important;
            }
            
            span,
            .item-subtitle,
            [class*="item-subtitle"] {
              color: var(--text-secondary) !important;
            }
            
            a,
            .link-title,
            [class*="link-title"] {
              color: var(--primary-color) !important;
              
              &:hover {
                color: var(--primary-light) !important;
              }
            }
          }
          
          .item-actions,
          [class*="item-actions"] {
            color: var(--text-primary) !important;
            
            button,
            .action-btn,
            [class*="action-btn"] {
              color: var(--text-primary) !important;
              
              &:hover {
                background-color: rgba(var(--primary-color-rgb), 0.1) !important;
              }
              
              mat-icon,
              .mat-icon,
              [class*="mat-icon"] {
                color: var(--primary-color) !important;
              }
            }
          }
        }
        
        /* Contenu de bloc */
        .item-content,
        .item-details,
        [class*="item-content"],
        [class*="item-details"] {
          color: var(--text-secondary) !important;
          
          p {
            color: var(--text-secondary) !important;
          }
        }
        
        /* Pied de bloc */
        .item-footer,
        [class*="item-footer"] {
          color: var(--text-secondary) !important;
          border-color: var(--border-color) !important;
          
          .item-date,
          [class*="item-date"] {
            color: var(--text-muted) !important;
          }
        }
      }
      
      /* Boutons d'ajout */
      .add-btn,
      [class*="add-btn"] {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark)) !important;
        color: var(--primary-contrast) !important;
        box-shadow: var(--shadow-sm) !important;
        border-left-color: var(--accent-color) !important;
        
        mat-icon,
        .mat-icon,
        [class*="mat-icon"] {
          color: var(--primary-contrast) !important;
        }
        
        &:hover {
          box-shadow: var(--shadow-md) !important;
        }
      }
      
      /* Boutons d'ajout maintenant */
      .add-now-btn,
      [class*="add-now-btn"] {
        background-color: var(--primary-color) !important;
        color: var(--primary-contrast) !important;
      }
      
      /* Messages vides */
      .empty-message,
      [class*="empty-message"] {
        color: var(--text-muted) !important;
        
        mat-icon,
        .mat-icon,
        [class*="mat-icon"] {
          color: var(--text-muted) !important;
        }
      }
      
      /* Boutons de voir plus */
      .show-more,
      [class*="show-more"] {
        button {
          color: var(--primary-color) !important;
          
          &:hover {
            background-color: rgba(var(--primary-color-rgb), 0.1) !important;
          }
          
          &:disabled {
            color: var(--text-muted) !important;
          }
        }
      }
      
      /* Éléments de statistiques */
      .stats-grid,
      [class*="stats-grid"] {
        background-color: var(--background-card) !important;
        
        .stat-item,
        [class*="stat-item"] {
          background-color: var(--background-alt) !important;
          color: var(--text-primary) !important;
          border-color: var(--border-color) !important;
          box-shadow: var(--shadow-sm) !important;
          
          h4 {
            color: var(--primary-color) !important;
          }
          
          p {
            color: var(--text-secondary) !important;
          }
          
          .stat-icon,
          [class*="stat-icon"] {
            background-color: rgba(var(--primary-color-rgb), 0.1) !important;
            
            mat-icon,
            .mat-icon,
            [class*="mat-icon"] {
              color: var(--primary-color) !important;
            }
          }
          
          &:hover {
            box-shadow: var(--shadow-md) !important;
            transform: translateY(-2px);
          }
        }
      }
    }
    
    /* Actions de carte */
    mat-card-actions,
    .mat-card-actions,
    .mat-mdc-card-actions,
    [class*="mat-card-actions"],
    [class*="mat-mdc-card-actions"] {
      background-color: var(--background-card) !important;
      border-color: var(--border-color) !important;
      
      button {
        color: var(--primary-color) !important;
        
        &:hover {
          background-color: rgba(var(--primary-color-rgb), 0.1) !important;
        }
        
        mat-icon,
        .mat-icon,
        [class*="mat-icon"] {
          color: var(--primary-color) !important;
        }
      }
    }
    
    /* Pied de carte */
    mat-card-footer,
    .mat-card-footer,
    .mat-mdc-card-footer,
    [class*="mat-card-footer"],
    [class*="mat-mdc-card-footer"] {
      background-color: var(--background-card) !important;
      border-color: var(--border-color) !important;
    }
  }

  /* Styles spécifiques pour les cartes dans la colonne droite */
  .right-column {
    .info-card,
    mat-card.info-card,
    .mat-card.info-card,
    .mat-mdc-card.info-card,
    [class*="info-card"],
    [class*="mat-card"],
    [class*="mat-mdc-card"] {
      border-color: var(--border-color) !important;
      
      mat-card-content,
      .mat-card-content,
      .mat-mdc-card-content,
      [class*="mat-card-content"],
      [class*="mat-mdc-card-content"] {
        .item-block,
        [class*="item-block"] {
          background-color: var(--background-alt) !important;
          color: var(--text-primary) !important;
          border-color: var(--border-color) !important;
          
          &:hover {
            background-color: rgba(var(--primary-color-rgb), 0.05) !important;
            box-shadow: var(--shadow-md) !important;
          }
        }
      }
    }
  }

  /* Styles spécifiques pour les cartes dans la colonne gauche */
  .left-column {
    .info-card,
    mat-card.info-card,
    .mat-card.info-card,
    .mat-mdc-card.info-card,
    [class*="info-card"],
    [class*="mat-card"],
    [class*="mat-mdc-card"] {
      border-color: var(--border-color) !important;
      
      mat-card-content,
      .mat-card-content,
      .mat-mdc-card-content,
      [class*="mat-card-content"],
      [class*="mat-mdc-card-content"] {
        p {
          color: var(--text-secondary) !important;
          
          mat-icon,
          .mat-icon,
          [class*="mat-icon"] {
            color: var(--primary-color) !important;
          }
          
          strong {
            color: var(--text-primary) !important;
          }
        }
      }
    }
  }

  /* Styles spécifiques pour les cartes bio */
  .bio-card,
  [class*="bio-card"] {
    border-left-color: var(--accent-color) !important;
    
    .bio-content,
    [class*="bio-content"] {
      p {
        color: var(--text-secondary) !important;
      }
      
      .empty-bio,
      [class*="empty-bio"] {
        color: var(--text-muted) !important;
      }
      
      .edit-bio-btn,
      [class*="edit-bio-btn"] {
        color: var(--accent-color) !important;
        
        &:hover {
          background-color: rgba(var(--accent-color-rgb), 0.1) !important;
        }
        
        mat-icon,
        .mat-icon,
        [class*="mat-icon"] {
          color: var(--accent-color) !important;
        }
      }
    }
  }

  /* Styles spécifiques pour les cartes CV */
  .cv-card,
  [class*="cv-card"] {
    &.has-cv {
      border-left-color: var(--primary-color) !important;
    }
    
    .cv-content,
    [class*="cv-content"] {
      .cv-preview,
      [class*="cv-preview"] {
        .cv-icon,
        [class*="cv-icon"] {
          background-color: rgba(var(--primary-color-rgb), 0.1) !important;
          
          mat-icon,
          .mat-icon,
          [class*="mat-icon"] {
            color: var(--primary-color) !important;
          }
        }
        
        .cv-details,
        [class*="cv-details"] {
          .cv-name,
          [class*="cv-name"] {
            color: var(--text-primary) !important;
          }
          
          .cv-date,
          [class*="cv-date"] {
            color: var(--text-secondary) !important;
          }
        }
      }
      
      .cv-actions,
      [class*="cv-actions"] {
        .view-cv-btn,
        [class*="view-cv-btn"] {
          color: var(--primary-color) !important;
          border-color: var(--primary-color) !important;
          
          &:hover {
            background-color: rgba(var(--primary-color-rgb), 0.1) !important;
          }
        }
        
        .delete-cv-btn,
        [class*="delete-cv-btn"] {
          color: var(--error-color) !important;
          
          &:hover {
            background-color: rgba(var(--error-color-rgb), 0.1) !important;
          }
        }
      }
    }
    
    .no-cv-content,
    [class*="no-cv-content"] {
      .no-cv-icon,
      [class*="no-cv-icon"] {
        background-color: var(--background-alt) !important;
        
        mat-icon,
        .mat-icon,
        [class*="mat-icon"] {
          color: var(--text-muted) !important;
        }
      }
      
      .no-cv-message,
      [class*="no-cv-message"] {
        p {
          color: var(--text-secondary) !important;
        }
        
        .add-cv-btn,
        [class*="add-cv-btn"] {
          background-color: var(--primary-color) !important;
          color: var(--primary-contrast) !important;
          
          &::after {
            background-color: var(--accent-color) !important;
          }
        }
      }
    }
  }

  /* Styles spécifiques pour les cartes de statistiques */
  .stats-card,
  [class*="stats-card"] {
    .stats-grid,
    [class*="stats-grid"] {
      .stat-item,
      [class*="stat-item"] {
        background-color: var(--background-alt) !important;
        color: var(--text-primary) !important;
        border-color: var(--border-color) !important;
        
        h4 {
          color: var(--primary-color) !important;
        }
        
        p {
          color: var(--text-secondary) !important;
        }
        
        .stat-icon,
        [class*="stat-icon"] {
          background-color: rgba(var(--primary-color-rgb), 0.1) !important;
          
          mat-icon,
          .mat-icon,
          [class*="mat-icon"] {
            color: var(--primary-color) !important;
          }
        }
      }
    }
  }

  /* Styles spécifiques pour les cartes mises en évidence */
  .highlight-card,
  [class*="highlight-card"] {
    background-color: rgba(var(--primary-color-rgb), 0.05) !important;
    border-color: var(--primary-color) !important;
    
    mat-card-title,
    .mat-card-title,
    .mat-mdc-card-title,
    [class*="mat-card-title"],
    [class*="mat-mdc-card-title"] {
      color: var(--primary-color) !important;
    }
    
    mat-card-content,
    .mat-card-content,
    .mat-mdc-card-content,
    [class*="mat-card-content"],
    [class*="mat-mdc-card-content"] {
      color: var(--text-primary) !important;
    }
  }

  /* Styles pour les éléments avec des styles en ligne */
  .info-card,
  mat-card.info-card,
  .mat-card.info-card,
  .mat-mdc-card.info-card,
  [class*="info-card"],
  [class*="mat-card"],
  [class*="mat-mdc-card"],
  .item-block,
  [class*="item-block"],
  .stat-item,
  [class*="stat-item"] {
    &[style*="background-color: #fff"],
    &[style*="background-color:#fff"],
    &[style*="background-color: white"],
    &[style*="background-color:#white"],
    &[style*="background-color: rgb(255, 255, 255)"],
    &[style*="background-color:rgb(255,255,255)"],
    &[style*="background-color: rgba(255, 255, 255"],
    &[style*="background-color:rgba(255,255,255"],
    &[style*="background: #fff"],
    &[style*="background:#fff"],
    &[style*="background: white"],
    &[style*="background:#white"],
    &[style*="background: rgb(255, 255, 255)"],
    &[style*="background:rgb(255,255,255)"],
    &[style*="background: rgba(255, 255, 255"],
    &[style*="background:rgba(255,255,255"],
    &[style*="background-color: #f8f9fa"],
    &[style*="background-color:#f8f9fa"],
    &[style*="background: #f8f9fa"],
    &[style*="background:#f8f9fa"],
    &[style*="background-color: #f4f4f4"],
    &[style*="background-color:#f4f4f4"],
    &[style*="background: #f4f4f4"],
    &[style*="background:#f4f4f4"],
    &[style*="background-color: #f5f5f5"],
    &[style*="background-color:#f5f5f5"],
    &[style*="background: #f5f5f5"],
    &[style*="background:#f5f5f5"],
    &[style*="background-color: #f0f0f0"],
    &[style*="background-color:#f0f0f0"],
    &[style*="background: #f0f0f0"],
    &[style*="background:#f0f0f0"],
    &[style*="background-color: #eeeeee"],
    &[style*="background-color:#eeeeee"],
    &[style*="background: #eeeeee"],
    &[style*="background:#eeeeee"],
    &[style*="background-color: #e9ecef"],
    &[style*="background-color:#e9ecef"],
    &[style*="background: #e9ecef"],
    &[style*="background:#e9ecef"] {
      background-color: var(--background-card) !important;
      background: var(--background-card) !important;
    }
  }
}
