import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';
import { Observable } from 'rxjs';
import { WorkspaceService } from '../services/workspace/workspace.service';
import { OidcSecurityService } from 'angular-auth-oidc-client';
import { UserRoleService } from '../services/user-role/user-role.service';

@Injectable({
  providedIn: 'root'
})
export class RoleGuard implements CanActivate {

  constructor(
    private workspaceService: WorkspaceService,
    private router: Router,
    private oidcSecurityService: OidcSecurityService,
    private userRoleService: UserRoleService
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {
    console.log('RoleGuard - URL actuelle:', state.url);

    // Déterminer si l'utilisateur est un recruteur ou un candidat
    const isRecruiter = this.userRoleService.isUserRecruiter();
    console.log('RoleGuard - Utilisateur est recruteur:', isRecruiter);

    // Vérifier si l'URL actuelle est une page réservée aux recruteurs
    const isRecruiterPage = this.userRoleService.isRecruiterRoute(state.url);

    // Vérifier si l'URL actuelle est une page réservée aux candidats
    const isCandidatePage = this.userRoleService.isCandidateRoute(state.url);

    // Si c'est la page d'accueil principale, forcer le mode candidat
    if (state.url === '/acceuil' || (state.url.startsWith('/acceuil/') && !state.url.startsWith('/acceuilposts'))) {
      console.log('RoleGuard - Accès à la page d\'accueil principale, forçage du mode candidat');
      this.userRoleService.forceUserAsCandidate();
      return true;
    }

    // Vérification spécifique pour la page acceuilposts (réservée aux recruteurs)
    if (state.url === '/acceuilposts' || state.url.startsWith('/acceuilposts')) {
      // Vérifier si l'utilisateur a un workspace actif
      const workspaceId = this.workspaceService.getActiveWorkspaceId() ||
                          this.workspaceService.getSelectedWorkspaceId() ||
                          sessionStorage.getItem('Workspace-Id') ||
                          localStorage.getItem('Workspace-Id');

      if (!workspaceId) {
        console.log('RoleGuard - Accès refusé à acceuilposts : utilisateur sans workspace');
        this.router.navigate(['/acceuil']);
        return false;
      }

      // Forcer le mode recruteur
      this.userRoleService.forceUserAsRecruiter(workspaceId);
      return true;
    }

    // Si c'est une page recruteur et l'utilisateur n'est pas recruteur, vérifier s'il a un workspace
    if (isRecruiterPage && !isRecruiter) {
      console.log('RoleGuard - Accès à une page recruteur en tant que candidat');

      // Vérifier si l'utilisateur a un workspace actif
      const workspaceId = this.workspaceService.getActiveWorkspaceId() ||
                          this.workspaceService.getSelectedWorkspaceId() ||
                          sessionStorage.getItem('Workspace-Id') ||
                          localStorage.getItem('Workspace-Id');

      if (workspaceId) {
        // Si l'utilisateur a un workspace, forcer le mode recruteur et autoriser l'accès
        console.log('RoleGuard - Utilisateur a un workspace actif, forçage du mode recruteur');
        this.userRoleService.forceUserAsRecruiter(workspaceId);
        return true;
      } else {
        // Si l'utilisateur n'a pas de workspace, rediriger vers l'accueil
        console.log('RoleGuard - Utilisateur sans workspace, redirection vers l\'accueil');
        this.router.navigate(['/acceuil']);
        return false;
      }
    }

    // Si c'est une page candidat et l'utilisateur est recruteur, rediriger vers le dashboard
    if (isCandidatePage && isRecruiter) {
      console.log('RoleGuard - Accès à une page candidat en tant que recruteur, redirection vers le dashboard');
      this.router.navigate(['/dashboard']);
      return false;
    }

    // Pour toutes les autres pages (comme le profil), autoriser l'accès
    console.log('RoleGuard - Accès autorisé : cette page est accessible à tous les utilisateurs');
    return true;
  }
}
