import { CanActivateFn } from '@angular/router';
import { inject } from '@angular/core';
import { LoginResponse, OidcSecurityService } from 'angular-auth-oidc-client';
import { map, tap } from 'rxjs';

export const authenticatedGuard: CanActivateFn = (route, state) => {
  const oidcSecurityService = inject(OidcSecurityService);

  return oidcSecurityService.checkAuth().pipe(
    tap((loginResponse: LoginResponse) => {
      console.log('Résultat de checkAuth:', loginResponse);
    }),
    map((loginResponse: LoginResponse) => {
      if (!loginResponse.isAuthenticated) {
        console.warn('Utilisateur non authentifié, redirection en cours...');
        localStorage.setItem('afterLogin', JSON.stringify(route.url));
        oidcSecurityService.authorize();
        return false;
      }

      console.log('Utilisateur authentifié');

      oidcSecurityService.getAccessToken().subscribe(token => {
        console.log('Token JWT récupéré:', token);
      });

      return true;
    })
  );
};
