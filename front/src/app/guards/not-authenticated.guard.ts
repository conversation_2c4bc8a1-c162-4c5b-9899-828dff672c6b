import {CanActivateFn, Router} from '@angular/router';
import {inject} from '@angular/core';
import {UserService} from '../services/user/user.service';
import {LoginResponse, OidcSecurityService} from 'angular-auth-oidc-client';
import {map} from 'rxjs';

export const notAuthenticatedGuard: CanActivateFn = (route, state) => {
  const router = inject(Router);
  const oidcSecurityService = inject(OidcSecurityService);
  return oidcSecurityService.checkAuth().pipe(
    map((loginResponse: LoginResponse) => {
      if (!loginResponse.isAuthenticated) {
        return true;
      }
      // Rediriger vers le profil personnel après la connexion
      router.navigate(['/profile']).then();
      return false;
    })
  );
};
