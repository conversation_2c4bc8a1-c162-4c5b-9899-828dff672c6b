import { Injectable } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Observable } from 'rxjs';
import { ConfirmDialogComponent, ConfirmDialogData } from '../../shared/confirm-dialog/confirm-dialog.component';

@Injectable({
  providedIn: 'root'
})
export class ConfirmationService {
  constructor(private dialog: MatDialog) {}

  /**
   * Ouvre une boîte de dialogue de confirmation et retourne un Observable qui émet true si l'utilisateur confirme, false sinon
   * @param data Les données de configuration de la boîte de dialogue
   * @returns Observable<boolean>
   */
  confirm(data: ConfirmDialogData): Observable<boolean> {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '400px',
      disableClose: true,
      data: data
    });

    return dialogRef.afterClosed();
  }

  /**
   * Méthode de commodité pour confirmer une suppression
   * @param itemName Le nom de l'élément à supprimer
   * @returns Observable<boolean>
   */
  confirmDeletion(itemName: string): Observable<boolean> {
    return this.confirm({
      title: 'Confirmer la suppression',
      message: `Êtes-vous sûr de vouloir supprimer ${itemName} ? Cette action est irréversible.`,
      confirmText: 'Supprimer',
      cancelText: 'Annuler',
      icon: 'delete',
      iconColor: '#ef4444' // Rouge
    });
  }

  /**
   * Méthode de commodité pour confirmer une archive
   * @param itemName Le nom de l'élément à archiver
   * @returns Observable<boolean>
   */
  confirmArchive(itemName: string): Observable<boolean> {
    return this.confirm({
      title: 'Confirmer l\'archivage',
      message: `Êtes-vous sûr de vouloir archiver ${itemName} ?`,
      confirmText: 'Archiver',
      cancelText: 'Annuler',
      icon: 'archive',
      iconColor: '#f59e0b' // Orange
    });
  }

  /**
   * Méthode de commodité pour confirmer une action générique
   * @param action Le nom de l'action à confirmer
   * @param itemName Le nom de l'élément concerné
   * @returns Observable<boolean>
   */
  confirmAction(action: string, itemName: string): Observable<boolean> {
    return this.confirm({
      title: `Confirmer ${action}`,
      message: `Êtes-vous sûr de vouloir ${action.toLowerCase()} ${itemName} ?`,
      confirmText: 'Confirmer',
      cancelText: 'Annuler',
      icon: 'help',
      iconColor: '#4299e1' // Bleu
    });
  }
}
