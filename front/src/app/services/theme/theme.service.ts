import { Injectable, Renderer2, RendererFactory2 } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

/**
 * Service de gestion du thème (clair/sombre) de l'application
 */
@Injectable({
  providedIn: 'root'
})
export class ThemeService {
  private renderer: Renderer2;
  private colorTheme: string = 'light'; // Valeur par défaut

  // BehaviorSubject pour suivre l'état actuel du thème
  private isDarkThemeSubject = new BehaviorSubject<boolean>(false);

  // Observable public pour que les composants puissent s'abonner aux changements de thème
  public isDarkTheme$ = this.isDarkThemeSubject.asObservable();

  constructor(private rendererFactory: RendererFactory2) {
    this.renderer = this.rendererFactory.createRenderer(null, null);

    // Initialiser le thème au démarrage
    this.initTheme();
  }

  /**
   * Initialise le thème en fonction des préférences sauvegardées ou des préférences système
   */
  initTheme(): void {
    // Vérifier si un thème est sauvegardé dans le localStorage
    const savedTheme = localStorage.getItem('theme');

    if (savedTheme) {
      // Utiliser le thème sauvegardé
      this.colorTheme = savedTheme;
    } else {
      // Vérifier les préférences système
      const prefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
      this.colorTheme = prefersDark ? 'dark' : 'light';
    }

    // Appliquer le thème initial
    this.setTheme(this.colorTheme);
  }

  /**
   * Définit le thème actuel et met à jour le DOM
   * @param theme Le thème à appliquer ('light' ou 'dark')
   */
  setTheme(theme: string): void {
    this.colorTheme = theme;

    // Sauvegarder la préférence dans le localStorage
    localStorage.setItem('theme', theme);

    // Mettre à jour le BehaviorSubject
    this.isDarkThemeSubject.next(theme === 'dark');

    // Appliquer la classe au document et au body
    if (theme === 'dark') {
      this.renderer.addClass(document.documentElement, 'dark-theme');
      this.renderer.addClass(document.body, 'dark-theme');
      this.renderer.removeClass(document.documentElement, 'light-theme');
      this.renderer.removeClass(document.body, 'light-theme');
      // Ajouter un attribut data-theme pour les sélecteurs CSS
      this.renderer.setAttribute(document.documentElement, 'data-theme', 'dark');
    } else {
      this.renderer.addClass(document.documentElement, 'light-theme');
      this.renderer.addClass(document.body, 'light-theme');
      this.renderer.removeClass(document.documentElement, 'dark-theme');
      this.renderer.removeClass(document.body, 'dark-theme');
      // Ajouter un attribut data-theme pour les sélecteurs CSS
      this.renderer.setAttribute(document.documentElement, 'data-theme', 'light');
    }
  }

  /**
   * Bascule entre les thèmes clair et sombre
   */
  toggleTheme(): void {
    if (this.colorTheme === 'light') {
      this.setTheme('dark');
    } else {
      this.setTheme('light');
    }
  }

  /**
   * Vérifie si le thème actuel est le thème sombre
   * @returns true si le thème actuel est sombre, false sinon
   */
  isDarkTheme(): boolean {
    return this.colorTheme === 'dark';
  }
}
