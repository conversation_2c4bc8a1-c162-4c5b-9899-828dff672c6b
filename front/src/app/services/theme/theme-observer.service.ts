import { Injectable, Renderer2, RendererFactory2, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { ThemeService } from './theme.service';
import { Subscription } from 'rxjs';

/**
 * Service qui observe les changements dans le DOM et applique le thème sombre
 * aux éléments générés dynamiquement
 */
@Injectable({
  providedIn: 'root'
})
export class ThemeObserverService implements OnDestroy {
  private renderer: Renderer2;
  private observer: MutationObserver | null = null;
  private isDarkTheme = false;
  private subscription: Subscription;

  constructor(
    private rendererFactory: RendererFactory2,
    private themeService: ThemeService
  ) {
    this.renderer = this.rendererFactory.createRenderer(null, null);
    
    // S'abonner aux changements de thème
    this.subscription = this.themeService.isDarkTheme$.subscribe(isDark => {
      this.isDarkTheme = isDark;
      
      if (isDark) {
        this.startObserver();
      } else {
        this.stopObserver();
      }
    });
  }

  /**
   * <PERSON><PERSON><PERSON>re l'observateur de mutations du DOM
   */
  private startObserver(): void {
    if (this.observer) {
      return;
    }

    // Créer un observateur de mutations
    this.observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        // Traiter les nouveaux nœuds ajoutés
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              this.applyDarkThemeToElement(node as Element);
            }
          });
        }
        
        // Traiter les attributs modifiés
        if (mutation.type === 'attributes') {
          if (mutation.attributeName === 'style' || mutation.attributeName === 'class') {
            this.applyDarkThemeToElement(mutation.target as Element);
          }
        }
      });
    });

    // Observer les changements dans le document
    this.observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['style', 'class']
    });
    
    // Appliquer le thème sombre aux éléments existants
    this.applyDarkThemeToElement(document.body);
  }

  /**
   * Arrête l'observateur de mutations du DOM
   */
  private stopObserver(): void {
    if (this.observer) {
      this.observer.disconnect();
      this.observer = null;
    }
  }

  /**
   * Applique le thème sombre à un élément et à ses enfants
   * @param element L'élément auquel appliquer le thème sombre
   */
  private applyDarkThemeToElement(element: Element): void {
    if (!this.isDarkTheme) {
      return;
    }

    // Appliquer le thème sombre aux éléments avec des styles en ligne
    this.applyDarkThemeToInlineStyles(element);
    
    // Appliquer le thème sombre aux enfants
    Array.from(element.children).forEach((child) => {
      this.applyDarkThemeToElement(child);
    });
  }

  /**
   * Applique le thème sombre aux styles en ligne d'un élément
   * @param element L'élément auquel appliquer le thème sombre
   */
  private applyDarkThemeToInlineStyles(element: Element): void {
    const style = (element as HTMLElement).style;
    
    if (!style) {
      return;
    }
    
    // Remplacer les couleurs de fond blanches
    if (this.isWhiteBackground(style.backgroundColor)) {
      style.backgroundColor = 'var(--background-card)';
    }
    
    // Remplacer les couleurs de texte noires
    if (this.isBlackText(style.color)) {
      style.color = 'var(--text-primary)';
    }
    
    // Remplacer les couleurs de bordure claires
    if (this.isLightBorder(style.borderColor)) {
      style.borderColor = 'var(--border-color)';
    }
  }

  /**
   * Vérifie si une couleur de fond est blanche ou très claire
   * @param color La couleur à vérifier
   * @returns true si la couleur est blanche ou très claire, false sinon
   */
  private isWhiteBackground(color: string): boolean {
    return color === '#fff' || 
           color === '#ffffff' || 
           color === 'white' || 
           color === 'rgb(255, 255, 255)' || 
           color === 'rgba(255, 255, 255, 1)' ||
           color === '#f8f9fa' ||
           color === '#f5f5f5' ||
           color === '#f0f0f0' ||
           color === '#eeeeee' ||
           color === '#e9ecef';
  }

  /**
   * Vérifie si une couleur de texte est noire ou très foncée
   * @param color La couleur à vérifier
   * @returns true si la couleur est noire ou très foncée, false sinon
   */
  private isBlackText(color: string): boolean {
    return color === '#000' || 
           color === '#000000' || 
           color === 'black' || 
           color === 'rgb(0, 0, 0)' || 
           color === 'rgba(0, 0, 0, 1)' ||
           color === '#111111' ||
           color === '#222222' ||
           color === '#333333' ||
           color === '#444444';
  }

  /**
   * Vérifie si une couleur de bordure est claire
   * @param color La couleur à vérifier
   * @returns true si la couleur est claire, false sinon
   */
  private isLightBorder(color: string): boolean {
    return color === '#ccc' || 
           color === '#cccccc' || 
           color === '#ddd' || 
           color === '#dddddd' || 
           color === '#eee' || 
           color === '#eeeeee' || 
           color === 'rgb(204, 204, 204)' || 
           color === 'rgba(204, 204, 204, 1)' ||
           color === 'rgb(221, 221, 221)' || 
           color === 'rgba(221, 221, 221, 1)' ||
           color === 'rgb(238, 238, 238)' || 
           color === 'rgba(238, 238, 238, 1)';
  }

  /**
   * Nettoie les ressources lors de la destruction du service
   */
  ngOnDestroy(): void {
    this.stopObserver();
    
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
