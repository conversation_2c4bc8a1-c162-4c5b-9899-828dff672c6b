import { Injectable } from '@angular/core';
import {HttpClient, HttpHeaders} from '@angular/common/http';
import {environment} from '../../../environments/environment';
import {catchError, Observable, switchMap, throwError} from 'rxjs';
import {OidcSecurityService} from 'angular-auth-oidc-client';

@Injectable({
  providedIn: 'root'
})
export class InvitationService {
  apiUrl = environment.apiUrl;
  constructor(
    private http: HttpClient,
    private oidcSecurityService: OidcSecurityService // Add OidcSecurityService for authentication
  ) {}
  sendInvitation(data: any): Observable<any> {
    return this.getAuthHeaders().pipe(
      switchMap(headers => {
        return this.http.post(`${this.apiUrl}/invitations/send`, data, { headers });
      }),
      catchError(error => {
        console.error('Error sending invitation:', error);
        return throwError(() => new Error(error.message || 'Failed to send invitation. Please try again.'));
      })
    );
  }

  loadReceivedInvitations(): Observable<any> {
    return this.getAuthHeaders().pipe(
      switchMap(headers => {
        return this.http.get<any>(`${this.apiUrl}/invitations/me`, { headers });
      }),
      catchError(error => {
        console.error('Error loading received invitations:', error);
        return throwError(() => new Error(error.message || 'Failed to load invitations. Please try again.'));
      })
    );
  }

  acceptInvitation(id: string): Observable<any> {
    return this.getAuthHeaders().pipe(
      switchMap(headers => {
        return this.http.post<any>(`${this.apiUrl}/invitations/${id}/accept`, {}, { headers });
      }),
      catchError(error => {
        console.error('Error accepting invitation:', error);
        return throwError(() => new Error(error.message || 'Failed to accept invitation. Please try again.'));
      })
    );
  }

  rejectInvitation(id: string): Observable<any> {
    return this.getAuthHeaders().pipe(
      switchMap(headers => {
        return this.http.post<any>(`${this.apiUrl}/invitations/${id}/reject`, {}, { headers });
      }),
      catchError(error => {
        console.error('Error rejecting invitation:', error);
        return throwError(() => new Error(error.message || 'Failed to reject invitation. Please try again.'));
      })
    );
  }


  updateStatus(id: string, status: string): Observable<any> {
    return this.getAuthHeaders().pipe(
      switchMap(headers => {
        return this.http.put(`${this.apiUrl}/${id}/status?status=${status}`, null, { headers });
      }),
      catchError(error => {
        console.error('Error updating invitation status:', error);
        return throwError(() => new Error(error.message || 'Failed to update invitation status. Please try again.'));
      })
    );
  }

  deleteInvitation(id: string): Observable<any> {
    return this.getAuthHeaders().pipe(
      switchMap(headers => {
        return this.http.delete(`${this.apiUrl}/${id}`, { headers });
      }),
      catchError(error => {
        console.error('Error deleting invitation:', error);
        return throwError(() => new Error(error.message || 'Failed to delete invitation. Please try again.'));
      })
    );
  }

  private getAuthHeaders(): Observable<HttpHeaders> {
    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap(token => {
        if (!token) {
          throw new Error('No token found. Please log in.');
        }
        return [new HttpHeaders({
          'Authorization': `Bearer ${token}`
        })];
      }),
      catchError(error => {
        console.error('Error retrieving token:', error);
        return throwError(() => new Error('Authentication failed. Please log in.'));
      })
    );
  }

  // Fetch paginated invitations
  getInvitations(page: number, size: number): Observable<any> {
    return this.getAuthHeaders().pipe(
      switchMap(headers => {
        return this.http.get(`${this.apiUrl}/search?page=${page}&size=${size}`, { headers });
      }),
      catchError(error => {
        console.error('Error fetching invitations:', error);
        return throwError(() => new Error(error.message || 'Failed to fetch invitations. Please try again later.'));
      })
    );
  }

  // Fetch invitations sent by the current user
  loadSentInvitations(): Observable<any> {
    return this.getAuthHeaders().pipe(
      switchMap(headers => {
        return this.http.get<any>(`${this.apiUrl}/invitations/sent`, { headers });
      }),
      catchError(error => {
        console.error('Error loading sent invitations:', error);
        return throwError(() => new Error(error.message || 'Failed to load sent invitations. Please try again.'));
      })
    );
  }



  // Resend an invitation
  resendInvitation(id: string): Observable<any> {
    return this.getAuthHeaders().pipe(
      switchMap(headers => {
        return this.http.post<any>(`${this.apiUrl}/invitations/${id}/resend`, {}, { headers });
      }),
      catchError(error => {
        console.error('Error resending invitation:', error);
        return throwError(() => new Error(error.message || 'Failed to resend invitation. Please try again.'));
      })
    );
  }

  // Cancel an invitation
  cancelInvitation(id: string): Observable<any> {
    return this.getAuthHeaders().pipe(
      switchMap(headers => {
        return this.http.post<any>(`${this.apiUrl}/invitations/${id}/cancel`, {}, { headers });
      }),
      catchError(error => {
        console.error('Error cancelling invitation:', error);
        return throwError(() => new Error(error.message || 'Failed to cancel invitation. Please try again.'));
      })
    );
  }
}
