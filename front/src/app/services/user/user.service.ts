import {Injectable} from '@angular/core';
import {HttpClient, HttpErrorResponse, HttpHeaders, HttpEventType} from '@angular/common/http';
import {environment} from '../../../environments/environment';
import {catchError, map, mergeMap, Observable, of, switchMap, throwError} from 'rxjs';
import {Router} from '@angular/router';
import {OidcSecurityService} from 'angular-auth-oidc-client';
import {UserProfile} from '../../models/user-profile.model';

@Injectable({
  providedIn: 'root'
})
export class UserService {
  private apiUrl = `${environment.apiUrl}/users`;
  private authApiUrl = `${environment.apiUrl}/user`;
  private profileApiUrl = `${environment.apiUrl}/user-profiles`;

  constructor(
    private http: HttpClient,
    private router: Router,
    private oidcSecurityService: OidcSecurityService
  ) {
  }

  // ----------------------
  // Authentication
  // ----------------------
  signUp(signUpRequest: any): Observable<any> {
    const cvUpload = new FormData();

    return this.http.post(`${this.authApiUrl}/signup`, signUpRequest).pipe(
      catchError((error: HttpErrorResponse) => {
        if (error.status === 400 && error.error.message === 'Email already in use.') {
          return throwError(() => new Error('EMAIL_EXISTS'));
        }
        return throwError(() => error);
      })
    );
  }

  checkEmail(email: string): Observable<boolean> {
    return this.http.post<boolean>(
      `${this.authApiUrl}/check-email`,
      email,
      {
        headers: new HttpHeaders({'Content-Type': 'application/json'})
      }
    );
  }

  loginWithGitHub(code: string): Observable<any> {
    return this.http.post(`${this.apiUrl}/login-github`, {code}).pipe(
      map((response: any) => {
        localStorage.setItem('token', response.token);
        return response;
      })
    );
  }

  logout(): void {
    localStorage.removeItem('Workspace-Id');
    sessionStorage.removeItem('Workspace-Id');
    this.oidcSecurityService.logoffAndRevokeTokens();
    this.router.navigate(['/']).then();
  }

  // ----------------------
  // User Profile
  // ----------------------
  getUserProfile(): Observable<any> {
    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap(token => {
        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
        return this.http.get<any>(`${this.apiUrl}/me`, {headers});
      })
    );

  }

  updateUserProfile(param: { skills?: string[], bio?: string }): Observable<any> {
    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap(token => {
        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);

        // Si le paramètre contient une bio, utiliser l'endpoint spécifique pour la bio
        if (param.bio !== undefined) {
          console.log('Mise à jour de la bio via endpoint spécifique');

          // Essayer d'abord l'endpoint spécifique pour la bio
          return this.http.patch(`${this.profileApiUrl}/bio`, { bio: param.bio }, {headers}).pipe(
            catchError(error => {
              console.error('Erreur avec l\'endpoint spécifique pour la bio:', error);

              // En cas d'échec, essayer l'endpoint général
              console.log('Tentative avec l\'endpoint général...');
              return this.http.patch(`${this.apiUrl}/me`, { bio: param.bio }, {headers}).pipe(
                catchError(err => {
                  console.error('Erreur avec l\'endpoint général:', err);

                  // En dernier recours, essayer de mettre à jour le profil complet
                  console.log('Tentative de mise à jour du profil complet...');
                  return this.getUserProfile().pipe(
                    switchMap(profile => {
                      if (profile && profile.userProfile) {
                        const updatedProfile = {
                          ...profile.userProfile,
                          bio: param.bio
                        };
                        return this.http.put(`${this.profileApiUrl}/edit`, updatedProfile, {headers});
                      }
                      return throwError(() => new Error('Impossible de récupérer le profil utilisateur'));
                    })
                  );
                })
              );
            })
          );
        }

        // Sinon, utiliser l'endpoint général
        return this.http.patch(`${this.apiUrl}/me`, param, {headers});
      })
    );
  }

  /**
   * Méthode spécifique pour mettre à jour la bio de l'utilisateur
   */
  updateBio(bio: string): Observable<any> {
    console.log('Appel de updateBio avec:', bio);
    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap(token => {
        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
        // Utiliser l'endpoint spécifique pour la bio
        return this.http.patch(`${this.profileApiUrl}/bio`, { bio }, {headers});
      }),
      catchError(error => {
        console.error('Erreur lors de la mise à jour de la bio:', error);
        return throwError(() => error);
      })
    );
  }

  updateFullUserProfile(updatedProfile: Partial<UserProfile>): Observable<UserProfile> {
    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap(token => {
        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
        return this.http.put(`${this.profileApiUrl}/edit`, updatedProfile, {headers});
      })
    );
  }

  uploadPhoto(file: File): Observable<any> {
    const formData = new FormData();
    formData.append('file', file);
    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap(token => {
        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
        return this.http.post(`${this.profileApiUrl}/upload-photo`, formData, {headers});
      })
    );
  }

  uploadWorkspaceLogo(file: File, workspaceId: string): Observable<any> {
    const formData = new FormData();
    formData.append('logo', file);
    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap(token => {
        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
        return this.http.post(`${environment.apiUrl}/files/workspace/${workspaceId}/logo`, formData, {headers});
      })
    );
  }

  uploadCV(file: File): Observable<any> {
    const formData = new FormData();
    formData.append('file', file);

    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap(token => {
        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);

        // Essayer plusieurs endpoints possibles pour assurer la compatibilité
        return this.http.post(`${this.profileApiUrl}/upload-cv`, formData, {
          headers,
          reportProgress: true,
          observe: 'events'
        }).pipe(
          catchError(error => {
            console.log('Trying alternative endpoint for CV upload...');
            // Si le premier endpoint échoue, essayer le deuxième
            return this.http.post(`${this.profileApiUrl}/uploadCV`, formData, {
              headers,
              reportProgress: true,
              observe: 'events'
            });
          }),
          catchError(error => {
            console.log('Trying third endpoint for CV upload...');
            // Si le deuxième endpoint échoue, essayer le troisième
            return this.http.post(`${this.profileApiUrl}/add-cv`, formData, {
              headers,
              reportProgress: true,
              observe: 'events'
            });
          }),
          map(event => {
            if (event.type === HttpEventType.UploadProgress && event.total) {
              const progress = Math.round(100 * event.loaded / event.total);
              return { progress };
            } else if (event.type === HttpEventType.Response) {
              return event.body;
            }
            return null;
          }),
          catchError(error => {
            console.error('Error uploading CV:', error);
            return throwError(() => new Error('Failed to upload CV: ' + (error.error?.message || error.statusText || error.message)));
          })
        );
      })
    );
  }


  // ----------------------
  // Profile Sections
  // ----------------------
  addEducation(education: any): Observable<any> {
    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap(token => {
        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
        return this.http.post(`${this.profileApiUrl}/add-education`, education, {headers});
      })
    );
  }

  editEducation(education: any, index: number): Observable<any> {
    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap(token => {
        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
        return this.http.put(`${this.profileApiUrl}/edit-education/${index}`, education, {headers});
      })
    );
  }

  deleteEducation(index: number): Observable<any> {
    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap(token => {
        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
        return this.http.delete(`${this.profileApiUrl}/delete-education/${index}`, {headers});
      })
    );
  }

  addCertification(certification: any): Observable<any> {
    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap(token => {
        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
        return this.http.post(`${this.profileApiUrl}/add-certification`, certification, {headers});
      })
    );
  }

  editCertification(certification: any, index: number): Observable<any> {
    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap(token => {
        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
        return this.http.put(`${this.profileApiUrl}/edit-certification/${index}`, certification, {headers});
      })
    );
  }

  deleteCertification(index: number): Observable<any> {
    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap(token => {
        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
        return this.http.delete(`${this.profileApiUrl}/delete-certification/${index}`, {headers});
      })
    );
  }

  addExperience(experience: any): Observable<any> {
    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap(token => {
        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
        return this.http.post(`${this.profileApiUrl}/add-experience`, experience, {headers});
      })
    );
  }

  editExperience(experience: any, index: number): Observable<any> {
    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap(token => {
        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
        return this.http.put(`${this.profileApiUrl}/edit-experience/${index}`, experience, {headers});
      })
    );
  }

  deleteExperience(index: number): Observable<any> {
    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap(token => {
        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
        return this.http.delete(`${this.profileApiUrl}/delete-experience/${index}`, {headers});
      })
    );
  }

  addSkill(skill: any): Observable<any> {
    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap(token => {
        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
        return this.http.post(`${this.profileApiUrl}/add-skill`, skill, {headers});
      })
    );
  }

  editSkill(skill: any, index: number): Observable<any> {
    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap(token => {
        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
        return this.http.put(`${this.profileApiUrl}/edit-skill/${index}`, skill, {headers});
      })
    );
  }

  deleteSkill(index: number): Observable<any> {
    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap(token => {
        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
        return this.http.delete(`${this.profileApiUrl}/delete-skill/${index}`, {headers});
      })
    );
  }

  addLink(link: any): Observable<any> {
    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap(token => {
        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
        return this.http.post(`${this.profileApiUrl}/add-link`, link, {headers});
      })
    );
  }

  editLink(link: any, index: number): Observable<any> {
    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap(token => {
        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
        return this.http.put(`${this.profileApiUrl}/edit-link/${index}`, link, {headers});
      })
    );
  }

  deleteLink(index: number): Observable<any> {
    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap(token => {
        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
        return this.http.delete(`${this.profileApiUrl}/delete-link/${index}`, {headers});
      })
    );
  }

  // ----------------------
  // Users & Invitations
  // ----------------------
  loadUsers(): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}`);
  }

  loadUsersForInvitations(search: string): Observable<any> {
    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap(token => {
        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
        // Si search est vide, utilisez l'URL sans le paramètre search
        const url = search ? `${this.apiUrl}/invitation/${search}` : `${this.apiUrl}/invitation/`;
        return this.http.get<any>(url, {headers}).pipe(
          catchError((error: HttpErrorResponse) => {
            console.error('Error loading users for invitation:', error);
            return throwError(() => new Error('Failed to load users for invitation: ' + error.message));
          })
        );
      })
    );
  }

  getAllUsers(): Observable<any[]> {
    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap(token => {
        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
        return this.http.get<any>(`${this.apiUrl}`, {headers}).pipe(
          map(response => response.content || []),
          catchError((error: HttpErrorResponse) => {
            console.error('Error fetching users:', error.message);
            return throwError(() => new Error('Failed to fetch users: ' + error.message));
          })
        );
      })
    );
  }

  updateUser(userId: string, updatedUser: any): Observable<any> {
    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap(token => {
        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
        return this.http.put(`${this.apiUrl}/${userId}`, updatedUser, {headers}).pipe(
          catchError((error: HttpErrorResponse) => {
            console.error('Update user error:', error.message);
            return throwError(() => new Error('Failed to update user: ' + error.message));
          })
        );
      })
    );
  }

  deleteUser(userId: string): Observable<{ deleted: boolean }> {
    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap(token => {
        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
        return this.http.delete<{ deleted: boolean }>(`${this.apiUrl}/${userId}`, {headers}).pipe(
          catchError((error: HttpErrorResponse) => {
            console.error('Delete user error:', error.message);
            return throwError(() => new Error('Failed to delete user: ' + error.message));
          })
        );
      })
    );
  }

  // ----------------------
  // Recruiter & Job Postings
  // ----------------------
  getRecruiterProfile() {
  }

  updateRecruiterProfile(param: { recruiter: any; recruiterProfile: any }) {
  }

  addJobPosting(result: any) {
  }


  changePassword(email: any, payload: { oldPassword: string; newPassword: string }) {
    return this.http.post(`http://localhost:8081/api/users/change-password/${email}`, payload);
  }

  private getAuthHeaders(): Observable<{ headers: { Authorization: string } }> {
    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap(token => {
        if (!token) {
          throw new Error('No token found. Please log in.');
        }
        return of({headers: {Authorization: `Bearer ${token}`}});
      }),
      catchError(err => {
        console.error('Error fetching token:', err);
        return throwError(() => err);
      })
    );
  }

  getUserId(): Observable<string> {
    return this.getUserProfile().pipe(
      map((profile) => {
        console.log("Profil récupéré:", profile);
        return profile.userProfile?._id || profile.userProfile?.userId;
      })
    );
  }

  addCv(file: File, email: string): Observable<any> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('email', email);

    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap(token => {
        if (!token) {
          throw new Error('No access token available. Please log in.');
        }
        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
        return this.http.post(`${this.profileApiUrl}/add-cv`, formData, {
          headers,
          reportProgress: true,
          observe: 'events'
        }).pipe(
          map(event => {
            if (event.type === HttpEventType.UploadProgress && event.total) {
              const progress = Math.round(100 * event.loaded / event.total);
              return { progress };
            } else if (event.type === HttpEventType.Response) {
              return event.body;
            }
            return null;
          }),
          catchError((error: HttpErrorResponse) => {
            console.error('Error uploading CV:', error);
            return throwError(() => {
              const errorMsg = error.error?.message || 'Failed to upload CV';
              return new Error(errorMsg);
            });
          })
        );
      })
    );
  }
}
