import { Injectable } from '@angular/core';
import {HttpClient, HttpHeaders} from '@angular/common/http';
import { Observable } from 'rxjs';
import {ReactionService} from '../reaction/reaction.service';

@Injectable({
  providedIn: 'root'
})
export class PostService {
  private apiUrl = 'http://localhost:8081/api/workspaces';
  post: any = { id: 1 }; // Exemple de post
  userId = 1;

  constructor(private http: HttpClient) {}

  getAllWorkspaces(page: number, size: number): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}?page=${page}&size=${size}`);
  }

  getPostById(postId: string) {

  }
}
