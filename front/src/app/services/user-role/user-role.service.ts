import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { Router, NavigationEnd } from '@angular/router';
import { filter } from 'rxjs/operators';
import { WorkspaceService } from '../workspace/workspace.service';

/**
 * Service dédié à la gestion du rôle de l'utilisateur (candidat ou recruteur)
 * Ce service centralise la logique de détermination du rôle et notifie tous les composants
 * lorsque le rôle change.
 */
@Injectable({
  providedIn: 'root'
})
export class UserRoleService {
  private isRecruiterSubject = new BehaviorSubject<boolean>(false);
  public isRecruiter$ = this.isRecruiterSubject.asObservable();
  private lastKnownRole: 'candidate' | 'recruiter' = 'candidate';
  private candidateOnlyRoutes: string[] = [
    '/acceuil',
    '/job-application',
    '/saved-items'
  ];
  private recruiterOnlyRoutes: string[] = [
    '/dashboard',
    '/posts',
    '/workspace-profile',
    '/recruiter-invitations',
    '/edit-post',
    '/acceuilposts',
    '/recruiter-profile',
    '/archive'
  ];

  constructor(
    private router: Router,
    private workspaceService: WorkspaceService
  ) {
    // Vérifier s'il y a un rôle stocké dans le localStorage
    const storedRole = localStorage.getItem('User-Role');
    if (storedRole === 'recruiter') {
      this.lastKnownRole = 'recruiter';
    } else {
      this.lastKnownRole = 'candidate';
      localStorage.setItem('User-Role', 'candidate');
    }

    // Initialiser le rôle au démarrage
    this.updateUserRole();

    // Écouter les changements de workspace
    this.workspaceService.workspaceChanged$.subscribe(() => {
      this.updateUserRole();
    });

    // Écouter les changements de route
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd)
    ).subscribe((event: any) => {
      this.handleRouteChange(event.url);
    });
  }

  /**
   * Gère les changements de route et met à jour le rôle en conséquence
   * @param url URL actuelle
   */
  private handleRouteChange(url: string): void {
    console.log('UserRoleService - Changement de route détecté:', url);

    // Vérifier si l'URL correspond à une route qui force le mode candidat
    const isCandidateRoute = this.isCandidateRoute(url);
    if (isCandidateRoute) {
      console.log('UserRoleService - Route candidat détectée, forçage du mode candidat');
      this.forceUserAsCandidate();
      return;
    }

    // Vérifier si l'URL correspond à une route qui force le mode recruteur
    const isRecruiterRoute = this.isRecruiterRoute(url);
    if (isRecruiterRoute) {
      // Vérifier s'il y a un workspace actif
      const workspaceId = this.workspaceService.getActiveWorkspaceId() || this.workspaceService.getSelectedWorkspaceId();
      if (workspaceId) {
        console.log('UserRoleService - Route recruteur détectée avec workspace actif, forçage du mode recruteur');
        this.forceUserAsRecruiter(workspaceId);
      } else {
        // Si l'utilisateur tente d'accéder à une page recruteur sans workspace actif,
        // le rediriger vers la page d'accueil
        console.log('UserRoleService - Route recruteur détectée sans workspace actif, redirection vers l\'accueil');
        this.forceUserAsCandidate();
        this.router.navigate(['/acceuil']);
      }
      return;
    }

    // Pour la page de profil, respecter le dernier rôle connu
    if (url === '/profile' || url.startsWith('/profile')) {
      console.log('UserRoleService - Navigation vers le profil détectée, utilisation du dernier rôle connu:', this.lastKnownRole);
      if (this.lastKnownRole === 'candidate') {
        this.forceUserAsCandidate();
      } else {
        // Vérifier s'il y a un workspace actif
        const workspaceId = this.workspaceService.getActiveWorkspaceId() || this.workspaceService.getSelectedWorkspaceId();
        if (workspaceId) {
          this.forceUserAsRecruiter(workspaceId);
        } else {
          // Si pas de workspace, forcer le mode candidat
          this.forceUserAsCandidate();
        }
      }
      return;
    }

    // Pour les autres pages, mettre à jour le rôle normalement
    this.updateUserRole();
  }

  /**
   * Met à jour le rôle de l'utilisateur en fonction du workspace actif et du dernier rôle connu
   */
  private updateUserRole(): void {
    // Vérifier s'il y a un workspace actif
    const workspaceId = this.workspaceService.getActiveWorkspaceId();

    // Vérifier également dans le stockage pour s'assurer de la cohérence
    const sessionWorkspaceId = sessionStorage.getItem('Workspace-Id');
    const localWorkspaceId = localStorage.getItem('Workspace-Id');

    // Récupérer le dernier rôle connu
    const storedRole = localStorage.getItem('User-Role');

    console.log('UserRoleService - Mise à jour du rôle:', {
      activeWorkspaceId: workspaceId,
      sessionStorage: sessionWorkspaceId,
      localStorage: localWorkspaceId,
      lastKnownRole: this.lastKnownRole,
      storedRole: storedRole,
      currentUrl: this.router.url
    });

    // Vérifier si l'URL actuelle correspond à une route qui force le mode candidat
    const currentUrl = this.router.url;
    const isCandidateRoute = this.isCandidateRoute(currentUrl);
    if (isCandidateRoute) {
      console.log('UserRoleService - Sur une page candidat, forçage du mode candidat');
      this.lastKnownRole = 'candidate';
      localStorage.setItem('User-Role', 'candidate');
      this.isRecruiterSubject.next(false);
      return;
    }

    // Vérifier si l'URL actuelle correspond à une route qui force le mode recruteur
    const isRecruiterRoute = this.isRecruiterRoute(currentUrl);
    if (isRecruiterRoute) {
      // Vérifier s'il y a un workspace actif
      if (workspaceId || sessionWorkspaceId || localWorkspaceId) {
        console.log('UserRoleService - Sur une page recruteur avec workspace actif, forçage du mode recruteur');
        this.lastKnownRole = 'recruiter';
        localStorage.setItem('User-Role', 'recruiter');
        this.isRecruiterSubject.next(true);
      } else {
        // Si l'utilisateur tente d'accéder à une page recruteur sans workspace actif,
        // le rediriger vers la page d'accueil
        console.log('UserRoleService - Sur une page recruteur sans workspace actif, redirection vers l\'accueil');
        this.lastKnownRole = 'candidate';
        localStorage.setItem('User-Role', 'candidate');
        this.isRecruiterSubject.next(false);
        this.router.navigate(['/acceuil']);
      }
      return;
    }

    // Si l'utilisateur est sur la page de profil, respecter son dernier rôle connu
    if (currentUrl === '/profile' || currentUrl.startsWith('/profile')) {
      console.log('UserRoleService - Sur la page de profil, utilisation du dernier rôle connu:', this.lastKnownRole);
      if (this.lastKnownRole === 'candidate' || storedRole === 'candidate') {
        this.isRecruiterSubject.next(false);
        return;
      }
    }

    // Si un workspace est actif, l'utilisateur est un recruteur
    const isRecruiter = (
      (!!workspaceId && workspaceId.length > 0) ||
      (!!sessionWorkspaceId && sessionWorkspaceId.length > 0) ||
      (!!localWorkspaceId && localWorkspaceId.length > 0)
    );

    // Si l'utilisateur était précédemment un candidat et qu'il n'a pas explicitement switché,
    // le garder en mode candidat
    if (this.lastKnownRole === 'candidate' && storedRole === 'candidate' && !isRecruiter) {
      console.log('UserRoleService - Utilisateur précédemment candidat, maintien du mode candidat');
      this.isRecruiterSubject.next(false);
      return;
    }

    console.log('UserRoleService - Est recruteur:', isRecruiter);

    // Mettre à jour le BehaviorSubject
    this.isRecruiterSubject.next(isRecruiter);

    // Mettre à jour le dernier rôle connu
    if (isRecruiter) {
      this.lastKnownRole = 'recruiter';
      localStorage.setItem('User-Role', 'recruiter');
    } else {
      this.lastKnownRole = 'candidate';
      localStorage.setItem('User-Role', 'candidate');
    }
  }

  /**
   * Force l'utilisateur en mode candidat
   */
  public forceUserAsCandidate(): void {
    console.log('UserRoleService - Forçage du mode candidat');

    // Mettre à jour le dernier rôle connu
    this.lastKnownRole = 'candidate';
    localStorage.setItem('User-Role', 'candidate');

    // Supprimer l'ID du workspace du stockage
    sessionStorage.removeItem('Workspace-Id');
    localStorage.removeItem('Workspace-Id');

    // Supprimer le workspace actif dans le service
    this.workspaceService.deleteActiveWorkspace();

    // Mettre à jour le rôle
    this.isRecruiterSubject.next(false);
  }

  /**
   * Force l'utilisateur en mode recruteur
   * @param workspaceId ID du workspace à activer
   */
  public forceUserAsRecruiter(workspaceId: string): void {
    console.log('UserRoleService - Forçage du mode recruteur avec workspace:', workspaceId);

    // Mettre à jour le dernier rôle connu
    this.lastKnownRole = 'recruiter';
    localStorage.setItem('User-Role', 'recruiter');

    // Stocker l'ID du workspace dans le stockage
    sessionStorage.setItem('Workspace-Id', workspaceId);
    localStorage.setItem('Workspace-Id', workspaceId);

    // Activer le workspace dans le service
    this.workspaceService.setActiveWorkspace(workspaceId);

    // Mettre à jour le rôle
    this.isRecruiterSubject.next(true);
  }

  /**
   * Vérifie si l'utilisateur est un recruteur
   * @returns true si l'utilisateur est un recruteur, false sinon
   */
  public isUserRecruiter(): boolean {
    return this.isRecruiterSubject.getValue();
  }

  /**
   * Vérifie si l'URL correspond à une route qui force le mode candidat
   * @param url URL à vérifier
   * @returns true si l'URL correspond à une route candidat, false sinon
   */
  public isCandidateRoute(url: string): boolean {
    // Exclure explicitement la route /acceuilposts
    if (url === '/acceuilposts' || url.startsWith('/acceuilposts')) {
      return false;
    }
    return this.candidateOnlyRoutes.some(route => url === route || url.startsWith(route));
  }

  /**
   * Vérifie si l'URL correspond à une route qui force le mode recruteur
   * @param url URL à vérifier
   * @returns true si l'URL correspond à une route recruteur, false sinon
   */
  public isRecruiterRoute(url: string): boolean {
    // Vérification spécifique pour acceuilposts
    if (url === '/acceuilposts' || url.startsWith('/acceuilposts')) {
      return true;
    }
    return this.recruiterOnlyRoutes.some(route => url === route || url.startsWith(route));
  }
}
