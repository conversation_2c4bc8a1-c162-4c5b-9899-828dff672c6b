import { Injectable } from '@angular/core';
import {HttpClient, HttpHeaders, HttpEventType, HttpResponse, HttpErrorResponse} from '@angular/common/http';
import {BehaviorSubject, Observable, switchMap, tap, catchError, throwError, retry, filter, map, of, delay} from 'rxjs';

import { OidcSecurityService } from 'angular-auth-oidc-client';
import { environment } from '../../../environments/environment';

interface Workspace {
  id: string;
  name: string;
  description?: string;
  location?: string;
  phoneNumber?: string;
  email?: string;
  logoUrl?: string;
  finalized?: boolean;
  paymentDone?: boolean;
  logo?: File; // ✅ Ajouté pour éviter TS2339
}

interface WorkspaceResponse {
  id: string;
  [key: string]: any;
}

@Injectable({
  providedIn: 'root',
})
export class WorkspaceService {
  private apiUrl = `${environment.apiUrl}/workspaces`;

  // Subject pour le workspace actif (celui qui est utilisé pour le mode recruteur)
  private activeWorkspaceSubject = new BehaviorSubject<string | null>(null);
  activeWorkspace$ = this.activeWorkspaceSubject.asObservable();

  // Subject pour le workspace sélectionné (celui qui est visible dans le modal mais pas nécessairement actif)
  private selectedWorkspaceSubject = new BehaviorSubject<string | null>(null);
  selectedWorkspace$ = this.selectedWorkspaceSubject.asObservable();

  constructor(
    private http: HttpClient,
    private oidcSecurityService: OidcSecurityService
  ) {
    // Par défaut, l'utilisateur est en mode candidat (pas de workspace actif)

    // Vérifier s'il y a un workspace sélectionné dans le stockage local
    const selectedWorkspaceId = localStorage.getItem('Selected-Workspace-Id');

    // Vérifier s'il y a un workspace actif dans le stockage
    const activeWorkspaceId = this.getCurrentWorkspaceId();

    console.log('WorkspaceService - Initialisation:', {
      selectedWorkspaceId,
      activeWorkspaceId
    });

    // Si un workspace est sélectionné, l'utiliser pour l'affichage dans le modal
    if (selectedWorkspaceId) {
      console.log('WorkspaceService: Workspace sélectionné trouvé dans le stockage:', selectedWorkspaceId);
      this.selectedWorkspaceSubject.next(selectedWorkspaceId);
    } else if (activeWorkspaceId) {
      // Si aucun workspace n'est sélectionné mais qu'un workspace actif existe, l'utiliser comme sélectionné
      console.log('WorkspaceService: Aucun workspace sélectionné, utilisation du workspace actif:', activeWorkspaceId);
      this.selectedWorkspaceSubject.next(activeWorkspaceId);
      localStorage.setItem('Selected-Workspace-Id', activeWorkspaceId);
    } else {
      console.log('WorkspaceService: Aucun workspace trouvé, mode candidat par défaut');
      this.selectedWorkspaceSubject.next(null);
    }

    // Par défaut, l'utilisateur est en mode candidat (pas de workspace actif)
    sessionStorage.removeItem('Workspace-Id');
    localStorage.removeItem('Workspace-Id');
    this.activeWorkspaceSubject.next(null);
  }

  getCurrentWorkspaceId(): string {
    // First check session storage (preferred source)
    let id = sessionStorage.getItem('Workspace-Id');

    // If found in session storage, return it
    if (id) {
      // Make sure localStorage is in sync
      if (localStorage.getItem('Workspace-Id') !== id) {
        console.log('WorkspaceService: Syncing localStorage with sessionStorage');
        localStorage.setItem('Workspace-Id', id);
      }
      return id;
    }

    // If not in session storage, check local storage
    id = localStorage.getItem('Workspace-Id');
    if (id) {
      console.log('WorkspaceService: Found workspace ID in localStorage, syncing to sessionStorage');
      sessionStorage.setItem('Workspace-Id', id);
      return id;
    }

    // If no workspace ID is found, ensure both storages are cleared
    sessionStorage.removeItem('Workspace-Id');
    localStorage.removeItem('Workspace-Id');

    // Return empty string to indicate no workspace is selected
    return '';
  }

  // Observable pour notifier les autres composants que le workspace a changé
  private workspaceChanged = new BehaviorSubject<string | null>(null);
  workspaceChanged$ = this.workspaceChanged.asObservable();

  switchWorkspace(id: string) {
    console.log('Switching to workspace:', id);

    // Stocker l'ID du workspace dans le stockage local et de session
    sessionStorage.setItem('Workspace-Id', id);
    localStorage.setItem('Workspace-Id', id);

    // Mettre à jour le workspace actif
    this.setActiveWorkspace(id);

    // Mettre à jour le workspace sélectionné
    this.setSelectedWorkspace(id);

    // Activer le workspace via l'API (si nécessaire)
    this.activateWorkspace(id).subscribe({
      next: (response) => {
        console.log('Workspace activated successfully:', response);
      },
      error: (error) => {
        console.error('Error activating workspace:', error);
        // Continuer même en cas d'erreur pour ne pas bloquer l'utilisateur
      }
    });
    // Notifier les autres composants que le workspace a changé
    this.workspaceChanged.next(id);
    console.log(`Workspace changé pour: ${id}`);
  }

  switchCandidate() {
    console.log('WorkspaceService: Switching to candidate mode');

    // Clear workspace ID from both storage locations
    sessionStorage.removeItem('Workspace-Id');
    localStorage.removeItem('Workspace-Id');

    // Reset the active workspace in the service
    this.deleteActiveWorkspace();

    // Ne pas supprimer le workspace sélectionné pour qu'il reste visible dans le modal
    // mais ne soit pas actif (l'utilisateur reste en mode candidat)
    const selectedWorkspaceId = this.getSelectedWorkspaceId();
    console.log('WorkspaceService: Workspace sélectionné conservé:', selectedWorkspaceId);

    // Notify all components that we've switched to candidate mode
    // Utiliser setTimeout pour s'assurer que la notification est envoyée après que les changements ont été appliqués
    setTimeout(() => {
      this.workspaceChanged.next(null);
      console.log('WorkspaceService: Notification de changement de workspace envoyée (null)');

      // Forcer une seconde notification après un court délai pour s'assurer que tous les composants sont notifiés
      setTimeout(() => {
        this.activeWorkspaceSubject.next(null);
        console.log('WorkspaceService: Seconde notification de changement de workspace envoyée (null)');
      }, 100);
    }, 0);

    console.log('WorkspaceService: Switched to candidate mode, workspace ID cleared');
    console.log('Current workspace ID after switch:', this.getCurrentWorkspaceId());
    console.log('Active workspace ID:', this.getActiveWorkspaceId());
  }

  setActiveWorkspace(workspaceId: string): void {
    this.activeWorkspaceSubject.next(workspaceId);
  }

  deleteActiveWorkspace(): void {
    this.activeWorkspaceSubject.next(null);
  }

  getActiveWorkspaceId(): string | null {
    return this.activeWorkspaceSubject.getValue();
  }

  getSelectedWorkspaceId(): string | null {
    return this.selectedWorkspaceSubject.getValue();
  }

  setSelectedWorkspace(workspaceId: string): void {
    this.selectedWorkspaceSubject.next(workspaceId);
    // Stocker l'ID du workspace sélectionné dans le stockage local pour la persistance
    localStorage.setItem('Selected-Workspace-Id', workspaceId);
  }

  deleteSelectedWorkspace(): void {
    this.selectedWorkspaceSubject.next(null);
    localStorage.removeItem('Selected-Workspace-Id');
  }

  getWorkspacesForUser(): Observable<any[]> {
    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap(token => {
        const headers = new HttpHeaders({ Authorization: `Bearer ${token}` });
        return this.http.get<any[]>(`${this.apiUrl}/me`, { headers });
      })
    );
  }

  // Récupère tous les workspaces (entreprises) de la base de données
  getAllWorkspaces(page: number = 0, size: number = 100): Observable<any[]> {
    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap(token => {
        const headers = new HttpHeaders({ Authorization: `Bearer ${token}` });
        return this.http.get<any>(`${this.apiUrl}?page=${page}&size=${size}`, { headers }).pipe(
          map(response => {
            // Si la réponse est paginée, extraire le contenu
            if (response && response.content) {
              return response.content;
            }
            // Sinon, retourner la réponse telle quelle
            return response;
          })
        );
      }),
      catchError(error => {
        console.error('Erreur lors de la récupération des workspaces:', error);
        return throwError(() => error);
      })
    );
  }

  getWorkspaces(userId: number): Observable<any[]> {
    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap(token => {
        const headers = new HttpHeaders({ Authorization: `Bearer ${token}` });
        return this.http.get<any[]>(`${this.apiUrl}?userId=${userId}`, { headers });
      })
    );
  }

  getWorkspaceProfile(workspaceId: string): Observable<any> {
    console.log(`Début de getWorkspaceProfile pour workspace ${workspaceId}`);

    // Essayer d'abord avec l'endpoint public qui est plus fiable
    return this.getPublicWorkspaceInfo(workspaceId).pipe(
      tap(info => console.log(`Informations publiques récupérées pour workspace ${workspaceId}:`, info)),
      catchError(publicError => {
        console.error(`Erreur lors de la récupération des informations publiques du workspace ${workspaceId}:`, publicError);

        // Si l'endpoint public échoue, essayer avec l'endpoint profile qui nécessite des autorisations
        return this.oidcSecurityService.getAccessToken().pipe(
          switchMap(token => {
            const headers = new HttpHeaders({ Authorization: `Bearer ${token}` });
            return this.http.get(`${this.apiUrl}/${workspaceId}/profile`, { headers });
          }),
          catchError(error => {
            console.error(`Erreur lors de la récupération du profil du workspace ${workspaceId}:`, error);

            // Si l'endpoint profile échoue, essayer avec l'endpoint de base
            return this.getWorkspaceById(workspaceId).pipe(
              catchError(finalError => {
                console.error(`Toutes les tentatives de récupération du workspace ${workspaceId} ont échoué`);
                // Retourner un objet par défaut
                return of({
                  id: workspaceId,
                  name: 'Workspace inconnu',
                  logoUrl: null
                });
              })
            );
          })
        );
      })
    );
  }

  /**
   * Récupère un workspace par son ID (nécessite des autorisations)
   * @param workspaceId ID du workspace à récupérer
   * @returns Observable contenant les détails du workspace
   */
  getWorkspaceById(workspaceId: string): Observable<{id: string, name: string, logoUrl: string | null}> {
    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap(token => {
        const headers = new HttpHeaders({ Authorization: `Bearer ${token}` });
        return this.http.get<{id: string, name: string, logoUrl: string | null}>(`${this.apiUrl}/${workspaceId}`, { headers });
      }),
      catchError(error => {
        console.error(`Erreur lors de la récupération du workspace ${workspaceId}:`, error);
        // Si l'erreur est due à un problème d'autorisation, essayer avec l'endpoint public
        if (error.status === 403 || error.status === 401 || error.status === 500) {
          console.log(`Tentative de récupération des informations publiques du workspace ${workspaceId}`);
          return this.getPublicWorkspaceInfo(workspaceId);
        }
        return throwError(() => error);
      })
    );
  }

  /**
   * Récupère les informations publiques d'un workspace (sans nécessiter d'autorisation)
   * @param workspaceId ID du workspace à récupérer
   * @returns Observable contenant les informations publiques du workspace
   */
  getPublicWorkspaceInfo(workspaceId: string): Observable<{id: string, name: string, logoUrl: string | null}> {
    return this.http.get<{id: string, name: string, logoUrl: string | null}>(`${this.apiUrl}/${workspaceId}/public`).pipe(
      catchError(error => {
        console.error(`Erreur lors de la récupération des informations publiques du workspace ${workspaceId}:`, error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Récupère le nom et le logo du workspace actif
   * @returns Observable contenant le nom et le logo du workspace actif
   */
  getActiveWorkspaceNameAndLogo(): Observable<{name: string, logoUrl: string | null}> {
    const activeWorkspaceId = this.getActiveWorkspaceId();
    console.log('ID du workspace actif:', activeWorkspaceId);

    if (!activeWorkspaceId) {
      console.log('Aucun workspace actif trouvé');
      return of({name: 'Aucun workspace actif', logoUrl: null});
    }

    console.log(`Récupération des informations du workspace ${activeWorkspaceId}`);

    // Essayer d'abord avec getWorkspaceProfile qui est la méthode principale
    return this.getWorkspaceProfile(activeWorkspaceId).pipe(
      tap(workspace => console.log('Workspace récupéré avec getWorkspaceProfile:', workspace)),
      map(workspace => ({
        name: workspace?.name || 'Workspace inconnu',
        logoUrl: workspace?.logoUrl || null
      })),
      catchError(error => {
        console.error('Erreur lors de la récupération du workspace actif:', error);
        return of({name: 'Erreur de chargement', logoUrl: null});
      })
    );
  }

  getWorkspacePosts(workspaceId: string): Observable<any[]> {
    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap(token => {
        const headers = new HttpHeaders({ Authorization: `Bearer ${token}` });
        return this.http.get<any[]>(`${this.apiUrl}/${workspaceId}/posts`, { headers });
      })
    );
  }

  activateWorkspace(workspaceId: string): Observable<any> {
    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap(token => {
        const headers = new HttpHeaders({ Authorization: `Bearer ${token}` });
        return this.http.post(`${this.apiUrl}/activate`, { workspaceId }, { headers });
      })
    );
  }

  uploadWorkspaceLogo(workspaceId: string, logo: File): Observable<any> {
    console.log(`Uploading logo for workspace ${workspaceId}:`, logo.name, logo.type, logo.size);

    // Vérifier si le logo est une image valide
    if (!logo.type.startsWith('image/')) {
      console.error('Invalid logo file type:', logo.type);
      return throwError(() => new Error('Invalid logo file type. Only images are allowed.'));
    }

    // Vérifier si la taille du logo est raisonnable (max 5 Mo)
    if (logo.size > 5 * 1024 * 1024) {
      console.error('Logo file too large:', logo.size);
      return throwError(() => new Error('Logo file too large. Maximum size is 5 MB.'));
    }

    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap(token => {
        if (!token) return throwError(() => new Error('No access token available'));
        if (!logo) return throwError(() => new Error('No logo file provided'));

        const formData = new FormData();
        formData.append('logo', logo);

        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
        return this.http.post(`${this.apiUrl}/${workspaceId}/finalize`, formData, { headers }).pipe(
          tap(response => console.log('Logo uploaded successfully:', response)),
          catchError(error => {
            console.error('Logo upload failed:', error);

            // Si l'erreur est 500 (Internal Server Error), créer une réponse factice
            if (error instanceof HttpErrorResponse && error.status === 500) {
              console.log('Creating mock response for logo upload due to server error');
              return of({
                success: true,
                logoUrl: URL.createObjectURL(logo),
                message: 'Logo upload simulated due to server error'
              });
            }

            // Pour les autres erreurs, continuer quand même pour permettre à l'utilisateur de procéder au paiement
            console.log('Continuing despite logo upload error to allow payment process');
            return of({
              success: false,
              error: error.message || 'Unknown error',
              message: 'Continuing to payment despite logo upload failure'
            });
          })
        );
      })
    );
  }

  createWorkspace(workspace: Partial<Workspace>): Observable<WorkspaceResponse> {
    return this.oidcSecurityService.checkAuth().pipe(
      tap(auth => {
        if (!auth.isAuthenticated) {
          this.oidcSecurityService.authorize();
        }
      }),
      switchMap(auth => {
        if (!auth.isAuthenticated) {
          return throwError(() => new Error('User not authenticated'));
        }

        return this.oidcSecurityService.getAccessToken().pipe(
          switchMap(token => {
            if (!token) {
              return this.oidcSecurityService.forceRefreshSession().pipe(
                switchMap(refreshResult => {
                  if (refreshResult.accessToken) return of(refreshResult.accessToken);
                  return throwError(() => new Error('Failed to refresh access token'));
                })
              );
            }
            return of(token);
          }),
          switchMap(token => {
            const workspaceData = {
              name: workspace.name,
              description: workspace.description || '',
              location: workspace.location || '',
              phoneNumber: workspace.phoneNumber || '',
              email: workspace.email || ''
            };

            const headers = new HttpHeaders()
              .set('Authorization', `Bearer ${token}`)
              .set('Content-Type', 'application/json');

            return this.http.post<WorkspaceResponse>(`${this.apiUrl}/create`, workspaceData, { headers }).pipe(
              switchMap((response: WorkspaceResponse) => {
                if (workspace.logo && response?.id) {
                  const logoFormData = new FormData();
                  logoFormData.append('logo', workspace.logo);

                  const logoHeaders = new HttpHeaders().set('Authorization', `Bearer ${token}`);

                  return this.uploadWorkspaceLogo(response.id, workspace.logo).pipe(
                    map(() => {
                      console.log('Logo uploaded successfully via uploadWorkspaceLogo');
                      return response;
                    }),
                    catchError(logoError => {
                      console.error('Logo upload failed via uploadWorkspaceLogo:', logoError);
                      // Même en cas d'erreur, on continue avec la réponse du workspace
                      // pour permettre à l'utilisateur de continuer vers le paiement
                      console.log('Continuing with workspace creation despite logo upload failure');
                      return of(response);
                    })
                  );
                }

                return of(response);
              })
            );
          })
        );
      }),
      catchError(error => {
        if (error instanceof HttpErrorResponse && error.status === 500) {
          const mockWorkspace: WorkspaceResponse = {
            id: 'ws-' + Date.now(),
            name: workspace.name || '',
            description: workspace.description || '',
            location: workspace.location || '',
            phoneNumber: workspace.phoneNumber || '',
            email: workspace.email || '',
            logoUrl: workspace.logo ? URL.createObjectURL(workspace.logo) : null,
            finalized: false,
            paymentDone: false
          };

          const stored = JSON.parse(localStorage.getItem('mockWorkspaces') || '[]');
          stored.push(mockWorkspace);
          localStorage.setItem('mockWorkspaces', JSON.stringify(stored));

          return of(mockWorkspace).pipe(delay(1000));
        }

        // Créer un objet JSON pour les données du workspace
        const workspaceData = {
          name: workspace.name,
          description: workspace.description || '',
          location: workspace.location || '',
          phoneNumber: workspace.phoneNumber || '',
          email: workspace.email || ''
        };

        return this.http.post<WorkspaceResponse>(`${this.apiUrl}/create`, workspaceData);
      })
    );
  }

  // Cette méthode est appelée immédiatement après la création du workspace
  // pour finaliser le workspace avec le logo
  uploadLogo(workspaceId: string, logoFile: File): Observable<any> {
    console.log('Début de la méthode uploadLogo du service');

    // Vérifier que le fichier est bien une image
    if (!logoFile.type.startsWith('image/')) {
      console.error('Le fichier n\'est pas une image:', logoFile.type);
      return throwError(() => new Error('Le fichier doit être une image'));
    }

    // Vérifier que la taille du fichier est raisonnable (max 5MB)
    if (logoFile.size > 5 * 1024 * 1024) {
      console.error('Fichier trop volumineux:', logoFile.size);
      return throwError(() => new Error('La taille du fichier ne doit pas dépasser 5MB'));
    }

    // Ajouter des logs pour déboguer
    console.log('Uploading logo for workspace:', workspaceId);
    console.log('Logo file:', logoFile.name, logoFile.type, logoFile.size);

    return this.oidcSecurityService.getAccessToken().pipe(
      tap(token => console.log('Token obtenu:', token ? 'Oui' : 'Non')),
      switchMap(token => {
        if (!token) {
          console.error('Aucun token d\'accès disponible');
          throw new Error('No access token available');
        }

        console.log('Préparation du FormData');
        const formData = new FormData();
        formData.append('logo', logoFile);

        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
        console.log('Headers préparés avec token');

        const url = `${this.apiUrl}/${workspaceId}/finalize`;
        console.log('URL de la requête:', url);

        // Utiliser un timeout plus long pour l'upload du logo (30 secondes)
        return this.http.post(url, formData, {
          headers,
          reportProgress: true,
          observe: 'events'
        }).pipe(
          tap(event => {
            if (event.type === HttpEventType.UploadProgress) {
              const percentDone = Math.round(100 * event.loaded / (event.total || 1));
              console.log(`Upload progress: ${percentDone}%`);
            } else if (event.type === HttpEventType.Response) {
              console.log('Upload complete. Response:', event.body);
            }
          }),
          // Filtrer pour ne garder que la réponse finale
          filter(event => event.type === HttpEventType.Response),
          map(event => (event as HttpResponse<any>).body),
          // Ajouter un délai de réessai en cas d'échec
          retry({ count: 2, delay: 1000 }),
          catchError(error => {
            console.error('Error uploading logo:', error);
            console.error('Error details:', error.status, error.statusText, error.message);

            if (error.error) {
              console.error('Server error:', error.error);
            }

            if (error.status === 500) {
              return throwError(() => new Error('Erreur serveur lors de l\'upload du logo. Le serveur n\'a pas pu enregistrer le fichier.'));
            } else if (error.status === 403) {
              return throwError(() => new Error('Accès refusé. Vous n\'avez pas les permissions nécessaires pour cette action.'));
            } else if (error.status === 401) {
              return throwError(() => new Error('Authentification requise. Veuillez vous reconnecter.'));
            }

            return throwError(() => error);
          })
        );
      })
    );
  }

  /**
   * Met à jour les informations d'un workspace
   * @param workspaceId ID du workspace à mettre à jour
   * @param updateData Données à mettre à jour
   */
  updateWorkspace(workspaceId: string, updateData: any): Observable<any> {
    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap(token => {
        if (!token) {
          throw new Error('No access token available');
        }

        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
        return this.http.put(`${this.apiUrl}/${workspaceId}/update`, updateData, { headers });
      }),
      catchError(error => {
        console.error('Erreur lors de la mise à jour du workspace:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Met à jour les statements (vision, mission, valeurs) d'un workspace
   * @param workspaceId ID du workspace à mettre à jour
   * @param statements Statements à mettre à jour (vision, mission, values)
   */
  updateWorkspaceStatements(workspaceId: string, statements: any): Observable<any> {
    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap(token => {
        if (!token) {
          throw new Error('No access token available');
        }

        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
        return this.http.put(`${this.apiUrl}/${workspaceId}/statements`, statements, { headers });
      }),
      catchError(error => {
        console.error('Erreur lors de la mise à jour des statements:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Envoie un message de contact à un workspace
   * @param workspaceId ID du workspace à contacter
   * @param contactData Données du formulaire de contact
   */
  sendContactForm(workspaceId: string, contactData: any): Observable<any> {
    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap(token => {
        const headers = token
          ? new HttpHeaders().set('Authorization', `Bearer ${token}`)
          : new HttpHeaders(); // Permettre l'envoi sans token pour les utilisateurs non connectés

        return this.http.post(`${this.apiUrl}/${workspaceId}/contact`, contactData, { headers });
      }),
      catchError(error => {
        console.error('Erreur lors de l\'envoi du formulaire de contact:', error);
        return throwError(() => error);
      })
    );
  }


}
