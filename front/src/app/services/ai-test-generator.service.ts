import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

export interface TestSettings {
  level: string;
  questionCount: number;
  testType: string;
  duration: number;
}

export interface TestQuestion {
  id: string;
  type: 'qcm' | 'code' | 'text';
  question: string;
  options?: string[];
  correctAnswer?: string | number;
  codeTemplate?: string;
  expectedOutput?: string;
  difficulty: string;
  points: number;
}

export interface GeneratedTest {
  id: string;
  title: string;
  description: string;
  settings: TestSettings;
  questions: TestQuestion[];
  totalPoints: number;
  estimatedDuration: number;
  createdAt: Date;
  candidateId: string;
}

export interface AITestRequest {
  settings: TestSettings;
  candidateId: string;
  candidateProfile?: any;
  jobRequirements?: string[];
}

@Injectable({
  providedIn: 'root'
})
export class AiTestGeneratorService {
  private apiUrl = 'http://localhost:3000/api'; // URL de votre backend
  
  constructor(private http: HttpClient) {}

  /**
   * Génère un test personnalisé en utilisant l'IA
   */
  generateTest(request: AITestRequest): Observable<GeneratedTest> {
    const headers = new HttpHeaders({
      'Content-Type': 'application/json'
    });

    console.log('🤖 Envoi de la demande de génération de test à l\'IA:', request);

    return this.http.post<any>(`${this.apiUrl}/ai/generate-test`, request, { headers })
      .pipe(
        map(response => {
          console.log('✅ Test généré par l\'IA:', response);
          return this.transformAIResponse(response, request);
        }),
        catchError(error => {
          console.error('❌ Erreur lors de la génération du test:', error);
          return throwError(() => error);
        })
      );
  }

  /**
   * Transforme la réponse de l'IA en format de test standardisé
   */
  private transformAIResponse(aiResponse: any, request: AITestRequest): GeneratedTest {
    const test: GeneratedTest = {
      id: this.generateTestId(),
      title: this.generateTestTitle(request.settings),
      description: this.generateTestDescription(request.settings),
      settings: request.settings,
      questions: this.parseAIQuestions(aiResponse.questions || [], request.settings),
      totalPoints: 0,
      estimatedDuration: request.settings.duration,
      createdAt: new Date(),
      candidateId: request.candidateId
    };

    // Calculer le total des points
    test.totalPoints = test.questions.reduce((total, q) => total + q.points, 0);

    return test;
  }

  /**
   * Parse les questions générées par l'IA
   */
  private parseAIQuestions(aiQuestions: any[], settings: TestSettings): TestQuestion[] {
    const questions: TestQuestion[] = [];
    
    for (let i = 0; i < Math.min(aiQuestions.length, settings.questionCount); i++) {
      const aiQ = aiQuestions[i];
      
      const question: TestQuestion = {
        id: `q_${i + 1}`,
        type: this.determineQuestionType(aiQ, settings.testType),
        question: aiQ.question || aiQ.text || `Question ${i + 1}`,
        difficulty: settings.level,
        points: this.calculateQuestionPoints(settings.level)
      };

      // Ajouter les options pour les QCM
      if (question.type === 'qcm' && aiQ.options) {
        question.options = aiQ.options;
        question.correctAnswer = aiQ.correctAnswer || 0;
      }

      // Ajouter le template de code pour les questions de programmation
      if (question.type === 'code') {
        question.codeTemplate = aiQ.codeTemplate || this.getDefaultCodeTemplate();
        question.expectedOutput = aiQ.expectedOutput || '';
      }

      questions.push(question);
    }

    return questions;
  }

  /**
   * Détermine le type de question basé sur la réponse IA et les paramètres
   */
  private determineQuestionType(aiQuestion: any, testType: string): 'qcm' | 'code' | 'text' {
    if (testType === 'qcm') return 'qcm';
    if (testType === 'code') return 'code';
    
    // Pour le type mixte, alterner ou utiliser le type suggéré par l'IA
    if (aiQuestion.type) {
      return aiQuestion.type;
    }
    
    // Par défaut, alterner entre QCM et code
    return Math.random() > 0.5 ? 'qcm' : 'code';
  }

  /**
   * Calcule les points pour une question selon le niveau
   */
  private calculateQuestionPoints(level: string): number {
    const pointsMap: { [key: string]: number } = {
      'debutant': 5,
      'intermediaire': 10,
      'expert': 15
    };
    return pointsMap[level] || 10;
  }

  /**
   * Génère un titre pour le test
   */
  private generateTestTitle(settings: TestSettings): string {
    const levelLabels: { [key: string]: string } = {
      'debutant': 'Débutant',
      'intermediaire': 'Intermédiaire',
      'expert': 'Expert'
    };
    
    const typeLabels: { [key: string]: string } = {
      'qcm': 'QCM',
      'code': 'Programmation',
      'mixte': 'Mixte'
    };

    return `Test ${typeLabels[settings.testType]} - Niveau ${levelLabels[settings.level]}`;
  }

  /**
   * Génère une description pour le test
   */
  private generateTestDescription(settings: TestSettings): string {
    return `Test technique de ${settings.questionCount} questions, niveau ${settings.level}, durée estimée: ${settings.duration} minutes.`;
  }

  /**
   * Template de code par défaut
   */
  private getDefaultCodeTemplate(): string {
    return `// Écrivez votre code ici
function solution() {
    // Votre implémentation
    return result;
}`;
  }

  /**
   * Génère un ID unique pour le test
   */
  private generateTestId(): string {
    return `test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Sauvegarde le test généré
   */
  saveGeneratedTest(test: GeneratedTest): Observable<any> {
    const headers = new HttpHeaders({
      'Content-Type': 'application/json'
    });

    return this.http.post(`${this.apiUrl}/tests`, test, { headers })
      .pipe(
        catchError(error => {
          console.error('❌ Erreur lors de la sauvegarde du test:', error);
          return throwError(() => error);
        })
      );
  }

  /**
   * Récupère un test par son ID
   */
  getTest(testId: string): Observable<GeneratedTest> {
    return this.http.get<GeneratedTest>(`${this.apiUrl}/tests/${testId}`)
      .pipe(
        catchError(error => {
          console.error('❌ Erreur lors de la récupération du test:', error);
          return throwError(() => error);
        })
      );
  }
}
