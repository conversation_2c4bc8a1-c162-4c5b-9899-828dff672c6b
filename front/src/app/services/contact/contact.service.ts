import { Injectable } from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ContactService {

  private apiUrl = 'http://localhost:8081/api/contact';  // Change l'URL selon ton backend

  constructor(private http: HttpClient) {}

  sendContactMessage(contact: any): Observable<any> {
    return this.http.post<any>(this.apiUrl, contact);
  }
}
