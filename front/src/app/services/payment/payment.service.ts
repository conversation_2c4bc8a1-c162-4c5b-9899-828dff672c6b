// src/app/services/payment/payment.service.ts
import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse, HttpHeaders } from '@angular/common/http';
import { environment } from '../../../environments/environment';
import { catchError, map, Observable, switchMap, tap, throwError, of } from 'rxjs';
import { OidcSecurityService } from 'angular-auth-oidc-client';

declare var Stripe: any; // Déclaration pour Stripe JS

@Injectable({
  providedIn: 'root'
})
export class PaymentService {
  private apiUrl = `${environment.apiUrl}/payment`;
  private workspaceId: string | null = null;
  private stripePromise: Promise<any>;
  private stripePublicKey = 'pk_test_51RGqvWQ990p20cocRKhQZGzfMfC1MKY3UxR84DQHnEuxEy0jWM6FOy1EQHtV2mgDM3DrdLbSne2EgsZqw6OEVmI600PyE75Xcy';

  constructor(
    private http: HttpClient,
    private oidcSecurityService: OidcSecurityService
  ) {
    // Initialiser Stripe avec la clé publique
    this.stripePromise = this.loadStripe();
  }

  // Charger la bibliothèque Stripe
  private loadStripe(): Promise<any> {
    if (!window.document.getElementById('stripe-script')) {
      const script = window.document.createElement('script');
      script.id = 'stripe-script';
      script.type = 'text/javascript';
      script.src = 'https://js.stripe.com/v3/';
      script.onload = () => {
        console.log('Stripe script loaded');
      };
      window.document.body.appendChild(script);
    }

    return new Promise((resolve) => {
      if (typeof Stripe !== 'undefined') {
        resolve(Stripe(this.stripePublicKey));
      } else {
        window.document.getElementById('stripe-script')!.addEventListener('load', () => {
          resolve(Stripe(this.stripePublicKey));
        });
      }
    });
  }

  // Méthode pour stocker l'ID du workspace en cours de création
  setWorkspaceId(id: string): void {
    this.workspaceId = id;
    sessionStorage.setItem('pending-workspace-id', id);
  }

  // Méthode pour récupérer l'ID du workspace en cours de création
  getWorkspaceId(): string | null {
    if (!this.workspaceId) {
      this.workspaceId = sessionStorage.getItem('pending-workspace-id');
    }
    return this.workspaceId;
  }

  // Méthode pour effacer l'ID du workspace après le paiement
  clearWorkspaceId(): void {
    this.workspaceId = null;
    sessionStorage.removeItem('pending-workspace-id');
  }

  getAllPayments(page: number = 0, size: number = 20, sort: string = 'id,asc'): Observable<{ content: any[], totalElements: number }> {
    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap(token => {
        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
        const url = `${this.apiUrl}?page=${page}&size=${size}&sort=${sort}`;
        return this.http.get<any>(url, { headers }).pipe(
          map(response => ({
            content: response.content || [],
            totalElements: response.totalElements || 0
          })),
          catchError((error: HttpErrorResponse) => {
            console.error('Error fetching payments:', error.message);
            return throwError(() => new Error('Failed to fetch payments: ' + error.message));
          })
        );
      })
    );
  }

  refundPayment(paymentId: string): Observable<any> {
    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap(token => {
        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
        return this.http.post<any>(`${this.apiUrl}/${paymentId}/refund`, {}, { headers });
      })
    );
  }

  // Méthode pour créer une session de paiement Stripe
  createCheckoutSession(plan: string): Observable<any> {
    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap(token => {
        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
        const workspaceId = this.getWorkspaceId();

        if (!workspaceId) {
          return throwError(() => new Error('Workspace ID not found. Please try creating your workspace again.'));
        }

        // Préparer les données pour la session Stripe
        const data = {
          plan: plan,
          workspaceId: workspaceId
        };

        return this.http.post<any>(`${this.apiUrl}/create-checkout-session`, data, { headers }).pipe(
          tap(response => {
            console.log('Checkout session created successfully:', response);
          }),
          catchError((error: HttpErrorResponse) => {
            console.error('Error creating checkout session:', error.message);
            return throwError(() => new Error('Failed to create checkout session: ' + error.message));
          })
        );
      })
    );
  }

  // Méthode pour rediriger vers la page de paiement Stripe
  redirectToCheckout(sessionId: string): Promise<any> {
    return this.stripePromise.then(stripe => {
      return stripe.redirectToCheckout({ sessionId });
    });
  }

  // Méthode pour vérifier le statut d'un paiement
  verifyPayment(sessionId: string): Observable<any> {
    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap(token => {
        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
        return this.http.get<any>(`${this.apiUrl}/verify-payment/${sessionId}`, { headers }).pipe(
          tap(response => {
            console.log('Payment verification result:', response);
            if (response.paid) {
              // Effacer l'ID du workspace après un paiement réussi
              this.clearWorkspaceId();
            }
          }),
          catchError((error: HttpErrorResponse) => {
            console.error('Error verifying payment:', error.message);
            return throwError(() => new Error('Failed to verify payment: ' + error.message));
          })
        );
      })
    );
  }

  // Méthode pour traiter le paiement du workspace (version simplifiée pour les tests)
  processWorkspacePayment(paymentData: any): Observable<any> {
    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap(token => {
        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
        const workspaceId = this.getWorkspaceId();

        if (!workspaceId) {
          return throwError(() => new Error('Workspace ID not found. Please try creating your workspace again.'));
        }

        // Ajouter l'ID du workspace aux données de paiement
        const data = {
          ...paymentData,
          workspaceId: workspaceId
        };

        // Pour les tests, simuler un paiement réussi
        console.log('Simulating successful payment for workspace:', workspaceId);
        this.clearWorkspaceId();
        return of({ success: true, message: 'Payment processed successfully' });

        // En production, décommenter le code ci-dessous pour utiliser l'API de paiement
        /*
        return this.http.post<any>(`${this.apiUrl}/workspace-subscription`, data, { headers }).pipe(
          tap(response => {
            console.log('Workspace payment processed successfully:', response);
            // Effacer l'ID du workspace après un paiement réussi
            this.clearWorkspaceId();
          }),
          catchError((error: HttpErrorResponse) => {
            console.error('Error processing workspace payment:', error.message);
            return throwError(() => new Error('Failed to process payment: ' + error.message));
          })
        );
        */
      })
    );
  }
}
