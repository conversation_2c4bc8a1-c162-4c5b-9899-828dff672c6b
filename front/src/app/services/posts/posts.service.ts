import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { Injectable } from '@angular/core';
import { environment } from '../../../environments/environment';
import { OidcSecurityService } from 'angular-auth-oidc-client';
import { switchMap, catchError } from 'rxjs/operators';

type Post = {
  id: string;
  titre: string;
  description: string;
  entreprise: string;
  userId: string;
  profileRequest: string;
  contractType: string;
  datePublication: string;
  archived: boolean;
  workspace?: {
    id: string;
    name: string;
    logoUrl: string | null;
  };
};

@Injectable({
  providedIn: 'root'
})
export class PostsService {
  private apiUrl = `${environment.apiUrl}/posts`;

  constructor(
    private http: HttpClient,
    private oidcSecurityService: OidcSecurityService
  ) {}

  private getAuthHeaders(): Observable<HttpHeaders> {
    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap(token => {
        console.log('Token récupéré:', token); // Ajoute ce log
        if (!token) {
          throw new Error('No token found. Please log in.');
        }
        return [new HttpHeaders({
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        })];
      }),
      catchError(err => {
        console.error('Error fetching token:', err);
        return throwError(() => err);
      })
    );
  }

  getPostById(id: string): Observable<Post> {
    return this.getAuthHeaders().pipe(
      switchMap(headers =>
        this.http.get<Post>(`${this.apiUrl}/${id}`, { headers }).pipe(
          catchError(error => {
            console.error('Error in getPostById:', error);
            return throwError(() => error);
          })
        )
      )
    );
  }

  updatePost(id: string, post: {
    titre: any;
    description: any;
    entreprise: any;
    profileRequest: any;
    contractType: any
  }): Observable<Post> {
    return this.getAuthHeaders().pipe(
      switchMap(headers =>
        this.http.put<Post>(`${this.apiUrl}/${id}`, post, { headers }).pipe(
          catchError(error => {
            console.error('Error in updatePost:', error);
            return throwError(() => error);
          })
        )
      )
    );
  }

  deletePost(id: string): Observable<void> {
    return this.getAuthHeaders().pipe(
      switchMap(headers =>
        this.http.delete<void>(`${this.apiUrl}/${id}`, { headers }).pipe(
          catchError(error => {
            console.error('Error in deletePost:', error);
            return throwError(() => error);
          })
        )
      )
    );
  }

  getPosts(page: number, size: number): Observable<{ content: Post[], totalElements: number }> {
    return this.getAuthHeaders().pipe(
      switchMap(headers =>
        this.http.get<{ content: Post[], totalElements: number }>(`${this.apiUrl}/search?page=${page}&size=${size}`, { headers }).pipe(
          catchError(error => {
            console.error('Error in getPosts:', error);
            return throwError(() => error);
          })
        )
      )
    );
  }

  createPost(post: Post): Observable<Post> {
    console.log('📡 Envoi de la requête au backend...', post);
    return this.getAuthHeaders().pipe(
      switchMap(headers =>
        this.http.post<Post>(`${this.apiUrl}/create`, post, { headers }).pipe(
          catchError(error => {
            console.error('Error in createPost:', error);
            return throwError(() => error);
          })
        )
      )
    );
  }

  addReaction(reactionData: { postId: string, type: string }): Observable<any> {
    return this.getAuthHeaders().pipe(
      switchMap(headers =>
        this.http.post(`${this.apiUrl}/reactions`, reactionData, { headers }).pipe(
          catchError(error => {
            console.error('Error in addReaction:', error);
            return throwError(() => error);
          })
        )
      )
    );
  }

  archivePost(id: string): Observable<Post> {
    return this.getAuthHeaders().pipe(
      switchMap(headers =>
        this.http.post<Post>(`${this.apiUrl}/${id}/archive`, {}, { headers }).pipe(
          catchError(error => {
            console.error('Error in archivePost:', error);
            return throwError(() => error);
          })
        )
      )
    );
  }

  unarchivePost(id: string): Observable<Post> {
    return this.getAuthHeaders().pipe(
      switchMap(headers =>
        this.http.patch<Post>(`${this.apiUrl}/${id}/archive`, { archived: false }, { headers }).pipe(
          catchError(error => {
            console.error('Error in unarchivePost:', error);
            return throwError(() => error);
          })
        )
      )
    );
  }

  getCandidates(postId: string): Observable<any[]> {
    return this.http.get<any[]>(`${this.apiUrl}/${postId}/candidates`);
  }

  getMyPosts(page: number = 0, size: number = 10): Observable<any> {
    const params = new HttpParams()
      .set('page', page.toString())
      .set('size', size.toString());

    return this.getAuthHeaders().pipe(
      switchMap(headers =>
        this.http.get<any>(`${this.apiUrl}/my-posts`, { headers, params }).pipe(
          catchError(error => {
            console.error('Error fetching my posts:', error);
            return throwError(() => new Error('Failed to fetch posts'));
          })
        )
      )
    );
  }

  getArchivedPosts(): Observable<Post[]> {
    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap(token => {
        if (!token) {
          throw new Error('No token found. Please log in.');
        }
        return this.oidcSecurityService.getUserData().pipe(
          switchMap(userData => {
            const userId = userData?.sub; // L'ID utilisateur peut être dans la propriété 'sub' du token
            console.log('User ID:', userId);
            return this.http.get<Post[]>(`${this.apiUrl}/archived?userId=${userId}`);
          })
        );
      }),
      catchError(error => {
        console.error('Error fetching archived posts:', error);
        return throwError(() => error);
      })
    );
  }
}
