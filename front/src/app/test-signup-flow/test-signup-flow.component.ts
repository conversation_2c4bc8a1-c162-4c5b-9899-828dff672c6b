import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { SignupStateService } from '../services/signup-state.service';
import { ProfileCompletionMessageComponent } from '../shared/profile-completion-message/profile-completion-message.component';

@Component({
  selector: 'app-test-signup-flow',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatCardModule,
    ProfileCompletionMessageComponent
  ],
  template: `
    <div class="test-container">
      <h1>🧪 Test Flux Inscription → Connexion → Message</h1>
      
      <mat-card class="instruction-card">
        <mat-card-header>
          <mat-card-title>📋 Instructions</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <ol>
            <li><strong>Étape 1:</strong> Cliquez sur "Simuler Inscription"</li>
            <li><strong>Étape 2:</strong> Connectez-vous normalement (OAuth2)</li>
            <li><strong>Étape 3:</strong> Le message "Votre profil sera complété dans quelques minutes" doit s'afficher</li>
            <li><strong>Étape 4:</strong> Cliquez sur "Parfait, j'ai compris" pour fermer le message</li>
          </ol>
        </mat-card-content>
      </mat-card>

      <mat-card class="control-card">
        <mat-card-header>
          <mat-card-title>🎮 Contrôles</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="button-group">
            <button 
              mat-raised-button 
              color="primary" 
              (click)="simulateSignup()"
            >
              📝 Simuler Inscription
            </button>
            
            <button 
              mat-raised-button 
              color="accent" 
              (click)="checkStatus()"
            >
              🔍 Vérifier État
            </button>
            
            <button 
              mat-raised-button 
              color="warn" 
              (click)="resetState()"
            >
              🗑️ Reset
            </button>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card class="status-card">
        <mat-card-header>
          <mat-card-title>📊 État Actuel</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="status-grid">
            <div class="status-item">
              <span class="label">Signup Completed:</span>
              <span class="value" [class.true]="signupCompleted" [class.false]="!signupCompleted">
                {{ signupCompleted }}
              </span>
            </div>
            <div class="status-item">
              <span class="label">LocalStorage:</span>
              <span class="value">{{ localStorageValue }}</span>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Le message s'affichera ici -->
      <app-profile-completion-message></app-profile-completion-message>
    </div>
  `,
  styles: [`
    .test-container {
      padding: 20px;
      max-width: 800px;
      margin: 0 auto;
      background: #f5f5f5;
      min-height: 100vh;
    }

    h1 {
      text-align: center;
      color: #001040FF;
      margin-bottom: 30px;
    }

    .instruction-card,
    .control-card,
    .status-card {
      margin-bottom: 20px;
    }

    .button-group {
      display: flex;
      gap: 15px;
      flex-wrap: wrap;
    }

    .status-grid {
      display: grid;
      gap: 10px;
    }

    .status-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px;
      background: white;
      border-radius: 5px;
      border: 1px solid #ddd;
    }

    .label {
      font-weight: 600;
      color: #333;
    }

    .value {
      font-family: monospace;
      padding: 4px 8px;
      border-radius: 4px;
      background: #f8f9fa;
    }

    .value.true {
      background: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }

    .value.false {
      background: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }

    ol {
      padding-left: 20px;
    }

    ol li {
      margin-bottom: 10px;
      line-height: 1.5;
    }

    @media (max-width: 600px) {
      .button-group {
        flex-direction: column;
      }
      
      .status-item {
        flex-direction: column;
        gap: 5px;
        text-align: center;
      }
    }
  `]
})
export class TestSignupFlowComponent {
  signupCompleted = false;
  localStorageValue = 'null';

  constructor(private signupStateService: SignupStateService) {
    this.checkStatus();
  }

  simulateSignup(): void {
    console.log('🚀 Simulation de l\'inscription...');
    
    // Marquer l'inscription comme complétée
    this.signupStateService.markSignupCompleted();
    
    console.log('✅ Inscription simulée - signup_completed: true');
    console.log('🔗 Maintenant connectez-vous normalement pour voir le message');
    
    this.checkStatus();
    
    // Afficher une alerte pour guider l'utilisateur
    alert('✅ Inscription simulée !\n\n🔗 Maintenant connectez-vous normalement (OAuth2) pour voir le message "Votre profil sera complété dans quelques minutes"');
  }

  checkStatus(): void {
    this.signupCompleted = localStorage.getItem('signup_completed') === 'true';
    this.localStorageValue = localStorage.getItem('signup_completed') || 'null';
    
    console.log('📊 État actuel:', {
      signupCompleted: this.signupCompleted,
      localStorage: this.localStorageValue
    });
  }

  resetState(): void {
    console.log('🗑️ Reset de l\'état...');
    this.signupStateService.resetForTesting();
    this.checkStatus();
    console.log('✅ État réinitialisé');
    
    alert('🗑️ État réinitialisé !');
  }
}
