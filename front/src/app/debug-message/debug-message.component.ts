import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { SignupStateService } from '../services/signup-state.service';
import { OidcSecurityService } from 'angular-auth-oidc-client';

@Component({
  selector: 'app-debug-message',
  standalone: true,
  imports: [CommonModule, MatButtonModule, MatCardModule],
  template: `
    <div style="position: fixed; top: 10px; right: 10px; z-index: 10000; background: white; padding: 20px; border: 2px solid #ccc; border-radius: 8px; max-width: 400px;">
      <h3>🔍 Debug Message System</h3>
      
      <div style="margin: 10px 0;">
        <strong>État localStorage:</strong>
        <div style="background: #f5f5f5; padding: 10px; margin: 5px 0; border-radius: 4px;">
          signup_completed: {{ localStorageState }}
        </div>
      </div>

      <div style="margin: 10px 0;">
        <strong>État authentification:</strong>
        <div style="background: #f5f5f5; padding: 10px; margin: 5px 0; border-radius: 4px;">
          Authentifié: {{ isAuthenticated }}
        </div>
      </div>

      <div style="margin: 10px 0;">
        <strong>Devrait afficher message:</strong>
        <div style="background: #f5f5f5; padding: 10px; margin: 5px 0; border-radius: 4px;">
          {{ shouldShowMessage }}
        </div>
      </div>

      <div style="margin: 10px 0;">
        <strong>Composant ProfileCompletion chargé:</strong>
        <div style="background: #f5f5f5; padding: 10px; margin: 5px 0; border-radius: 4px;">
          {{ profileComponentLoaded }}
        </div>
      </div>

      <div style="margin: 15px 0;">
        <button mat-raised-button color="primary" (click)="simulateSignup()" style="margin: 5px;">
          🧪 Simuler Signup
        </button>
        <button mat-raised-button color="accent" (click)="forceShowMessage()" style="margin: 5px;">
          🔧 Forcer Message
        </button>
        <button mat-raised-button color="warn" (click)="resetState()" style="margin: 5px;">
          🗑️ Reset
        </button>
        <button mat-raised-button (click)="refreshStatus()" style="margin: 5px;">
          🔄 Refresh
        </button>
        <button mat-raised-button color="primary" (click)="testCompleteFlow()" style="margin: 5px; background: #4CAF50;">
          🎯 Test Flux Complet
        </button>
      </div>

      <div style="margin: 10px 0;">
        <strong>Logs récents:</strong>
        <div style="background: #f5f5f5; padding: 10px; margin: 5px 0; border-radius: 4px; max-height: 200px; overflow-y: auto; font-size: 12px;">
          <div *ngFor="let log of logs">{{ log }}</div>
        </div>
      </div>
    </div>
  `,
  styles: []
})
export class DebugMessageComponent implements OnInit {
  localStorageState = 'null';
  isAuthenticated = false;
  shouldShowMessage = false;
  profileComponentLoaded = false;
  logs: string[] = [];

  constructor(
    private signupStateService: SignupStateService,
    private oidcSecurityService: OidcSecurityService
  ) {}

  ngOnInit(): void {
    this.addLog('🚀 DebugMessageComponent initialized');
    this.refreshStatus();
    this.checkProfileComponent();
    
    // Vérifier l'authentification
    this.oidcSecurityService.checkAuth().subscribe(loginResponse => {
      this.isAuthenticated = loginResponse.isAuthenticated;
      this.addLog(`🔐 Auth status: ${this.isAuthenticated}`);
      this.refreshStatus();
    });
  }

  refreshStatus(): void {
    this.localStorageState = localStorage.getItem('signup_completed') || 'null';
    this.shouldShowMessage = this.signupStateService.checkAndShowMessageAfterLogin();
    this.addLog(`📊 Status refreshed - localStorage: ${this.localStorageState}, shouldShow: ${this.shouldShowMessage}`);
  }

  simulateSignup(): void {
    this.addLog('🧪 Simulating signup...');
    this.signupStateService.markSignupCompleted();
    this.refreshStatus();
    this.addLog('✅ Signup simulated');
  }

  forceShowMessage(): void {
    this.addLog('🔧 Forcing message display...');
    this.signupStateService.forceShowMessage();
    this.refreshStatus();
    this.addLog('✅ Message forced');
  }

  resetState(): void {
    this.addLog('🗑️ Resetting state...');
    this.signupStateService.resetForTesting();
    this.refreshStatus();
    this.addLog('✅ State reset');
  }

  testCompleteFlow(): void {
    this.addLog('🎯 Testing complete flow: Signup + Login + Message');
    
    // 1. Simuler signup
    this.signupStateService.markSignupCompleted();
    this.addLog('✅ Step 1: Signup completed');
    
    // 2. Vérifier l'état d'authentification
    this.oidcSecurityService.checkAuth().subscribe(loginResponse => {
      if (loginResponse.isAuthenticated) {
        this.addLog('✅ Step 2: User is authenticated');
        
        // 3. Forcer la vérification du message
        const shouldShow = this.signupStateService.checkAndShowMessageAfterLogin();
        this.addLog(`✅ Step 3: Should show message: ${shouldShow}`);
        
        if (shouldShow) {
          this.addLog('🎉 SUCCESS: Message should appear now!');
        } else {
          this.addLog('❌ FAILED: Message conditions not met');
        }
      } else {
        this.addLog('❌ User not authenticated - please login first');
      }
      
      this.refreshStatus();
    });
  }

  checkProfileComponent(): void {
    // Vérifier si le composant ProfileCompletionMessage est présent dans le DOM
    const profileComponent = document.querySelector('app-profile-completion-message');
    this.profileComponentLoaded = !!profileComponent;
    this.addLog(`🔍 ProfileCompletionMessage component in DOM: ${this.profileComponentLoaded}`);
  }

  private addLog(message: string): void {
    const timestamp = new Date().toLocaleTimeString();
    this.logs.unshift(`[${timestamp}] ${message}`);
    
    // Garder seulement les 10 derniers logs
    if (this.logs.length > 10) {
      this.logs = this.logs.slice(0, 10);
    }
    
    console.log(message);
  }
}
