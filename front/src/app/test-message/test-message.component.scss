.test-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
  background: #f5f5f5;
  min-height: 100vh;
}

.test-card {
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.button-group {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.button-group button {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logs {
  max-height: 300px;
  overflow-y: auto;
  background: #1e1e1e;
  color: #00ff00;
  padding: 15px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  border: 1px solid #333;
}

.log-entry {
  font-size: 12px;
  margin-bottom: 5px;
  padding: 2px 0;
  border-bottom: 1px solid #333;
  line-height: 1.4;
}

h1 {
  text-align: center;
  color: #001040FF;
  margin-bottom: 30px;
}

mat-card-title {
  color: #001040FF;
}

.test-card mat-card-content p {
  margin: 8px 0;
  font-size: 14px;
}

.test-card mat-card-content p strong {
  color: #001040FF;
}

/* Styles pour les boutons */
button[color="primary"] {
  background-color: #001040FF !important;
}

button[color="accent"] {
  background-color: #ff6600 !important;
}

button[color="warn"] {
  background-color: #f44336 !important;
}

/* Responsive */
@media (max-width: 600px) {
  .test-container {
    padding: 10px;
  }
  
  .button-group {
    flex-direction: column;
  }
  
  .button-group button {
    width: 100%;
    justify-content: center;
  }
}
