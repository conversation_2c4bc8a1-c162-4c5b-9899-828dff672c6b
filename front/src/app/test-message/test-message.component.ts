import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { SignupStateService } from '../services/signup-state.service';
import { ProfileCompletionMessageComponent } from '../shared/profile-completion-message/profile-completion-message.component';
import { OidcSecurityService } from 'angular-auth-oidc-client';

@Component({
  selector: 'app-test-message',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatCardModule,
    MatIconModule,
    ProfileCompletionMessageComponent
  ],
  template: `
    <div class="test-container">
      <h1>Test du Message de Profil</h1>

      <mat-card class="test-card">
        <mat-card-header>
          <mat-card-title>État actuel</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <p><strong>Signup completed:</strong> {{ signupCompleted }}</p>
          <p><strong>User authenticated:</strong> {{ userAuthenticated }}</p>
          <p><strong>Should show message:</strong> {{ shouldShowMessage }}</p>
        </mat-card-content>
      </mat-card>

      <mat-card class="test-card">
        <mat-card-header>
          <mat-card-title>Actions de test</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="button-group">
            <button mat-raised-button color="primary" (click)="simulateSignup()">
              <mat-icon>person_add</mat-icon>
              Simuler Signup
            </button>

            <button mat-raised-button style="background-color: #4CAF50 !important; color: white;" (click)="simulateSignupAndLogin()">
              <mat-icon>login</mat-icon>
              Signup + Login
            </button>

            <button mat-raised-button color="accent" (click)="checkAuthStatus()">
              <mat-icon>refresh</mat-icon>
              Vérifier Auth
            </button>

            <button mat-raised-button color="warn" (click)="resetState()">
              <mat-icon>clear</mat-icon>
              Reset État
            </button>

            <button mat-raised-button (click)="forceShowMessage()">
              <mat-icon>visibility</mat-icon>
              Forcer Message
            </button>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card class="test-card">
        <mat-card-header>
          <mat-card-title>Logs</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="logs">
            <div *ngFor="let log of logs" class="log-entry">
              {{ log }}
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Le composant de message -->
      <app-profile-completion-message></app-profile-completion-message>
    </div>
  `,
  styleUrls: ['./test-message.component.scss']
})
export class TestMessageComponent implements OnInit {
  signupCompleted = false;
  userAuthenticated = false;
  shouldShowMessage = false;
  logs: string[] = [];

  constructor(
    private signupStateService: SignupStateService,
    private oidcSecurityService: OidcSecurityService
  ) {}

  ngOnInit(): void {
    this.updateStatus();
    this.checkAuthStatus();
  }

  simulateSignup(): void {
    this.addLog('Simulation du signup...');
    this.signupStateService.markSignupCompleted();
    this.updateStatus();
    this.addLog('Signup simulé - signup_completed: true');
  }

  simulateSignupAndLogin(): void {
    this.addLog('🚀 Simulation complète: Signup + Login...');

    // 1. Simuler le signup
    this.signupStateService.markSignupCompleted();
    this.addLog('✅ Étape 1: Signup complété');

    // 2. Vérifier l'état d'authentification et forcer l'affichage
    this.oidcSecurityService.checkAuth().subscribe(loginResponse => {
      if (loginResponse.isAuthenticated) {
        this.addLog('✅ Étape 2: Utilisateur authentifié');

        // 3. Vérifier si le message doit s'afficher
        const shouldShow = this.signupStateService.checkAndShowMessageAfterLogin();
        this.addLog(`✅ Étape 3: Vérification message - Résultat: ${shouldShow}`);

        if (shouldShow) {
          this.addLog('🎉 Le message devrait maintenant s\'afficher !');
        } else {
          this.addLog('❌ Le message ne s\'affiche pas - vérifiez la logique');
        }
      } else {
        this.addLog('❌ Utilisateur non authentifié - connectez-vous d\'abord');
      }

      this.updateStatus();
    });
  }

  checkAuthStatus(): void {
    this.addLog('Vérification du statut d\'authentification...');
    this.oidcSecurityService.checkAuth().subscribe(loginResponse => {
      this.userAuthenticated = loginResponse.isAuthenticated;
      this.addLog(`Utilisateur authentifié: ${this.userAuthenticated}`);

      if (this.userAuthenticated) {
        const shouldShow = this.signupStateService.checkAndShowMessageAfterLogin();
        this.addLog(`Devrait afficher le message: ${shouldShow}`);
        this.shouldShowMessage = shouldShow;
      } else {
        this.shouldShowMessage = false;
      }

      this.updateStatus();
    });
  }

  resetState(): void {
    this.addLog('Reset de l\'état...');
    this.signupStateService.resetForTesting();
    this.updateStatus();
    this.addLog('État réinitialisé');
  }

  forceShowMessage(): void {
    this.addLog('Forçage de l\'affichage du message...');
    this.signupStateService.forceShowMessage();
    this.updateStatus();
    this.addLog('Message forcé - rechargez la page pour voir le résultat');
  }

  private updateStatus(): void {
    this.signupCompleted = localStorage.getItem('signup_completed') === 'true';
    this.shouldShowMessage = this.signupStateService.shouldShowProfileMessage();
  }

  private addLog(message: string): void {
    const timestamp = new Date().toLocaleTimeString();
    this.logs.unshift(`[${timestamp}] ${message}`);

    // Garder seulement les 10 derniers logs
    if (this.logs.length > 10) {
      this.logs = this.logs.slice(0, 10);
    }
  }
}
