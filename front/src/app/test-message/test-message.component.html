<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Message de Profil</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        h1 {
            color: #001040FF;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }

        .test-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #001040FF;
        }

        .test-section h2 {
            color: #001040FF;
            margin-top: 0;
        }

        .button-group {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            margin: 15px 0;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: #001040FF;
            color: white;
        }

        .btn-primary:hover {
            background: #000830;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #545b62;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-success:hover {
            background: #218838;
            transform: translateY(-2px);
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .btn-warning:hover {
            background: #e0a800;
            transform: translateY(-2px);
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background: #c82333;
            transform: translateY(-2px);
        }

        .status-display {
            background: #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            border: 1px solid #dee2e6;
        }

        .status-item {
            margin: 8px 0;
            padding: 8px;
            border-radius: 4px;
        }

        .status-true {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-false {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .flow-diagram {
            background: white;
            border: 2px solid #001040FF;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .flow-step {
            display: flex;
            align-items: center;
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #001040FF;
        }

        .flow-number {
            background: #001040FF;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
            flex-shrink: 0;
        }

        .arrow {
            text-align: center;
            font-size: 24px;
            color: #001040FF;
            margin: 10px 0;
        }

        .message-preview {
            border: 2px dashed #001040FF;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            background: #f8f9fa;
            text-align: center;
        }

        .console-output {
            background: #2d3748;
            color: #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }

        .highlight {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test du Message de Profil</h1>
        
        <!-- Flux attendu -->
        <div class="test-section">
            <h2>📋 Flux Attendu</h2>
            <div class="flow-diagram">
                <div class="flow-step">
                    <div class="flow-number">1</div>
                    <div>
                        <strong>Signup</strong><br>
                        L'utilisateur s'inscrit → <code>signup_completed: true</code> stocké
                    </div>
                </div>
                <div class="arrow">⬇️</div>
                <div class="flow-step">
                    <div class="flow-number">2</div>
                    <div>
                        <strong>Connexion OAuth2</strong><br>
                        Redirection vers le backend OAuth2 pour se connecter
                    </div>
                </div>
                <div class="arrow">⬇️</div>
                <div class="flow-step">
                    <div class="flow-number">3</div>
                    <div>
                        <strong>Après Connexion</strong><br>
                        Le message "Profil en cours de traitement" s'affiche automatiquement
                    </div>
                </div>
                <div class="arrow">⬇️</div>
                <div class="flow-step">
                    <div class="flow-number">4</div>
                    <div>
                        <strong>Clic "Parfait, j'ai compris"</strong><br>
                        Le message disparaît et l'état est nettoyé
                    </div>
                </div>
            </div>
        </div>

        <!-- État actuel -->
        <div class="test-section">
            <h2>📊 État Actuel</h2>
            <div class="status-display" id="statusDisplay">
                <div class="status-item status-info">Chargement de l'état...</div>
            </div>
            <div class="button-group">
                <button class="btn btn-primary" onclick="checkCurrentState()">🔄 Actualiser l'État</button>
                <button class="btn btn-secondary" onclick="showConsoleOutput()">📝 Voir Console</button>
            </div>
        </div>

        <!-- Actions de test -->
        <div class="test-section">
            <h2>🎮 Actions de Test</h2>
            
            <div class="highlight">
                <strong>⚠️ Important :</strong> Ces boutons simulent les étapes du processus pour tester le message.
            </div>

            <div class="button-group">
                <button class="btn btn-success" onclick="simulateSignup()">1️⃣ Simuler Signup</button>
                <button class="btn btn-primary" onclick="simulateLogin()">2️⃣ Simuler Connexion</button>
                <button class="btn btn-warning" onclick="forceShowMessage()">🔧 Forcer Affichage</button>
                <button class="btn btn-danger" onclick="resetState()">🗑️ Reset État</button>
            </div>
        </div>

        <!-- Aperçu du message -->
        <div class="test-section">
            <h2>👁️ Aperçu du Message</h2>
            <div class="message-preview">
                <h3>🔄 Profil en cours de traitement</h3>
                <p>Votre profil sera complété dans quelques minutes</p>
                <button class="btn btn-primary">Parfait, j'ai compris</button>
            </div>
        </div>

        <!-- Console de debug -->
        <div class="test-section">
            <h2>🐛 Console de Debug</h2>
            <div class="console-output" id="consoleOutput">
                <div>Ouvrez la console du navigateur (F12) pour voir les logs détaillés...</div>
            </div>
        </div>

        <!-- Navigation -->
        <div class="test-section">
            <h2>🧭 Navigation</h2>
            <div class="button-group">
                <a href="/auth/sign-up" class="btn btn-primary">📝 Page Signup</a>
                <a href="/profile" class="btn btn-secondary">👤 Page Profil</a>
                <a href="/acceuil" class="btn btn-success">🏠 Accueil</a>
                <a href="/" class="btn btn-warning">🏠 Home</a>
            </div>
        </div>
    </div>

    <script>
        // Variables globales pour le debug
        let signupStateService = null;
        let oidcSecurityService = null;

        // Fonction pour vérifier l'état actuel
        function checkCurrentState() {
            const statusDisplay = document.getElementById('statusDisplay');
            const signupCompleted = localStorage.getItem('signup_completed');
            const isAuthenticated = 'Vérification en cours...'; // On ne peut pas facilement vérifier OIDC ici
            
            statusDisplay.innerHTML = `
                <div class="status-item ${signupCompleted === 'true' ? 'status-true' : 'status-false'}">
                    <strong>signup_completed:</strong> ${signupCompleted || 'null'}
                </div>
                <div class="status-item status-info">
                    <strong>Authentifié:</strong> ${isAuthenticated}
                </div>
                <div class="status-item status-info">
                    <strong>Timestamp:</strong> ${new Date().toLocaleString()}
                </div>
            `;
            
            console.log('État actuel vérifié:', {
                signup_completed: signupCompleted,
                timestamp: new Date().toISOString()
            });
        }

        // Simuler un signup
        function simulateSignup() {
            localStorage.setItem('signup_completed', 'true');
            console.log('✅ Signup simulé - signup_completed défini à true');
            checkCurrentState();
            alert('✅ Signup simulé ! L\'état signup_completed a été défini à true.');
        }

        // Simuler une connexion
        function simulateLogin() {
            console.log('🔐 Simulation de connexion...');
            // Ici on ne peut pas vraiment simuler OIDC, mais on peut déclencher la vérification
            alert('🔐 Connexion simulée ! Vérifiez la console pour les logs.');
            checkCurrentState();
        }

        // Forcer l'affichage du message
        function forceShowMessage() {
            localStorage.setItem('signup_completed', 'true');
            console.log('🔧 Forçage de l\'affichage du message');
            alert('🔧 Message forcé ! Rechargez la page pour voir le message.');
            checkCurrentState();
        }

        // Reset de l'état
        function resetState() {
            localStorage.removeItem('signup_completed');
            console.log('🗑️ État réinitialisé');
            checkCurrentState();
            alert('🗑️ État réinitialisé ! L\'état signup_completed a été supprimé.');
        }

        // Afficher la sortie console
        function showConsoleOutput() {
            const consoleOutput = document.getElementById('consoleOutput');
            consoleOutput.innerHTML = `
                <div>📝 Logs récents (ouvrez F12 pour plus de détails) :</div>
                <div>- État vérifié à ${new Date().toLocaleString()}</div>
                <div>- signup_completed: ${localStorage.getItem('signup_completed') || 'null'}</div>
                <div>- Utilisez F12 → Console pour voir tous les logs Angular</div>
            `;
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 Page de test du message de profil chargée');
            checkCurrentState();
            
            // Vérifier périodiquement l'état
            setInterval(checkCurrentState, 5000);
        });
    </script>
</body>
</html>
