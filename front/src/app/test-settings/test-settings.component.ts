import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';

import { MatChipsModule } from '@angular/material/chips';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { Router, ActivatedRoute } from '@angular/router';
import { trigger, state, style, transition, animate } from '@angular/animations';

interface TestSettings {
  level: string;
  questionCount: number;
  testType: string;
  duration: number;
}

@Component({
  selector: 'app-test-settings',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatSelectModule,

    MatChipsModule,
    MatProgressBarModule
  ],
  template: `
    <div class="test-settings-container" [@fadeIn]>
      <!-- Header Section -->
      <div class="header-section">
        <div class="header-content">
          <div class="icon-wrapper">
            <mat-icon class="main-icon">quiz</mat-icon>
            <div class="icon-glow"></div>
          </div>
          <h1 class="page-title">Configuration du Test</h1>
          <p class="page-subtitle">Personnalisez votre évaluation technique pour le candidat</p>
          <div class="title-underline"></div>
        </div>
      </div>

      <!-- Settings Cards -->
      <div class="settings-grid">

        <!-- Niveau de difficulté -->
        <div class="setting-card" [@slideUp]>
          <div class="card-header">
            <mat-icon class="card-icon">trending_up</mat-icon>
            <h3>Niveau de difficulté</h3>
          </div>
          <div class="card-content">
            <div class="level-options">
              <div
                class="level-option"
                [class.selected]="selectedSettings.level === 'debutant'"
                (click)="selectLevel('debutant')"
              >
                <mat-icon>school</mat-icon>
                <span class="level-name">Débutant</span>
                <span class="level-desc">Questions de base</span>
              </div>
              <div
                class="level-option"
                [class.selected]="selectedSettings.level === 'intermediaire'"
                (click)="selectLevel('intermediaire')"
              >
                <mat-icon>psychology</mat-icon>
                <span class="level-name">Intermédiaire</span>
                <span class="level-desc">Concepts avancés</span>
              </div>
              <div
                class="level-option"
                [class.selected]="selectedSettings.level === 'expert'"
                (click)="selectLevel('expert')"
              >
                <mat-icon>emoji_events</mat-icon>
                <span class="level-name">Expert</span>
                <span class="level-desc">Défis complexes</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Nombre de questions -->
        <div class="setting-card" [@slideUp]>
          <div class="card-header">
            <mat-icon class="card-icon">format_list_numbered</mat-icon>
            <h3>Nombre de questions</h3>
          </div>
          <div class="card-content">
            <div class="question-selector">
              <div class="question-display">
                <span class="question-count">{{ selectedSettings.questionCount }}</span>
                <span class="question-label">questions</span>
              </div>
              <div class="question-options">
                <div
                  class="question-option"
                  [class.selected]="selectedSettings.questionCount === 10"
                  (click)="setQuestionCount(10)"
                >
                  <mat-icon>flash_on</mat-icon>
                  <div class="option-info">
                    <span class="option-name">Rapide</span>
                    <span class="option-desc">10 questions</span>
                  </div>
                </div>
                <div
                  class="question-option"
                  [class.selected]="selectedSettings.questionCount === 25"
                  (click)="setQuestionCount(25)"
                >
                  <mat-icon>schedule</mat-icon>
                  <div class="option-info">
                    <span class="option-name">Standard</span>
                    <span class="option-desc">25 questions</span>
                  </div>
                </div>
                <div
                  class="question-option"
                  [class.selected]="selectedSettings.questionCount === 40"
                  (click)="setQuestionCount(40)"
                >
                  <mat-icon>hourglass_full</mat-icon>
                  <div class="option-info">
                    <span class="option-name">Complet</span>
                    <span class="option-desc">40 questions</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Type de test -->
        <div class="setting-card" [@slideUp]>
          <div class="card-header">
            <mat-icon class="card-icon">category</mat-icon>
            <h3>Type de test</h3>
          </div>
          <div class="card-content">
            <div class="test-types">
              <div
                class="test-type-option"
                [class.selected]="selectedSettings.testType === 'qcm'"
                (click)="selectTestType('qcm')"
              >
                <mat-icon>radio_button_checked</mat-icon>
                <div class="type-info">
                  <span class="type-name">QCM</span>
                  <span class="type-desc">Questions à choix multiples</span>
                </div>
              </div>
              <div
                class="test-type-option"
                [class.selected]="selectedSettings.testType === 'code'"
                (click)="selectTestType('code')"
              >
                <mat-icon>code</mat-icon>
                <div class="type-info">
                  <span class="type-name">Codage</span>
                  <span class="type-desc">Exercices de programmation</span>
                </div>
              </div>
              <div
                class="test-type-option"
                [class.selected]="selectedSettings.testType === 'mixte'"
                (click)="selectTestType('mixte')"
              >
                <mat-icon>shuffle</mat-icon>
                <div class="type-info">
                  <span class="type-name">Mixte</span>
                  <span class="type-desc">QCM + Exercices pratiques</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Durée du test -->
        <div class="setting-card" [@slideUp]>
          <div class="card-header">
            <mat-icon class="card-icon">timer</mat-icon>
            <h3>Durée du test</h3>
          </div>
          <div class="card-content">
            <div class="duration-selector">
              <div class="time-options">
                <div
                  class="time-option"
                  [class.selected]="selectedSettings.duration === 30"
                  (click)="selectDuration(30)"
                >
                  <mat-icon>flash_on</mat-icon>
                  <span class="time-value">30 min</span>
                  <span class="time-desc">Test rapide</span>
                </div>
                <div
                  class="time-option"
                  [class.selected]="selectedSettings.duration === 60"
                  (click)="selectDuration(60)"
                >
                  <mat-icon>schedule</mat-icon>
                  <span class="time-value">1 heure</span>
                  <span class="time-desc">Test standard</span>
                </div>
                <div
                  class="time-option"
                  [class.selected]="selectedSettings.duration === 120"
                  (click)="selectDuration(120)"
                >
                  <mat-icon>hourglass_full</mat-icon>
                  <span class="time-value">2 heures</span>
                  <span class="time-desc">Test approfondi</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Summary Section -->
      <div class="summary-section" [@slideUp]>
        <div class="summary-card">
          <h3>Résumé de votre test</h3>
          <div class="summary-details">
            <div class="summary-item">
              <mat-icon>trending_up</mat-icon>
              <span>Niveau : {{ getLevelLabel(selectedSettings.level) }}</span>
            </div>
            <div class="summary-item">
              <mat-icon>format_list_numbered</mat-icon>
              <span>{{ selectedSettings.questionCount }} questions</span>
            </div>
            <div class="summary-item">
              <mat-icon>category</mat-icon>
              <span>Type : {{ getTestTypeLabel(selectedSettings.testType) }}</span>
            </div>
            <div class="summary-item">
              <mat-icon>timer</mat-icon>
              <span>Durée : {{ getDurationLabel(selectedSettings.duration) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="action-section" [@slideUp]>
        <button
          mat-stroked-button
          class="secondary-button"
          (click)="goBack()"
        >
          <mat-icon>arrow_back</mat-icon>
          Retour
        </button>

        <button
          mat-raised-button
          class="primary-button"
          (click)="startTest()"
          [disabled]="!isConfigurationValid()"
        >
          <span>Commencer le test</span>
          <mat-icon>play_arrow</mat-icon>
        </button>
      </div>
    </div>
  `,
  styleUrls: ['./test-settings.component.scss'],
  animations: [
    trigger('fadeIn', [
      transition(':enter', [
        style({ opacity: 0 }),
        animate('0.5s ease-out', style({ opacity: 1 }))
      ])
    ]),
    trigger('slideUp', [
      transition(':enter', [
        style({ transform: 'translateY(30px)', opacity: 0 }),
        animate('0.4s ease-out', style({ transform: 'translateY(0)', opacity: 1 }))
      ])
    ])
  ]
})
export class TestSettingsComponent implements OnInit {
  candidateId: string | null = null;
  selectedSettings: TestSettings = {
    level: 'intermediaire',
    questionCount: 25,
    testType: 'mixte',
    duration: 60
  };

  constructor(
    private router: Router,
    private route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    this.candidateId = this.route.snapshot.paramMap.get('candidateId');
    console.log('🎯 TestSettingsComponent initialized for candidate:', this.candidateId);
  }

  selectLevel(level: string): void {
    this.selectedSettings.level = level;
    console.log('Level selected:', level);
  }



  setQuestionCount(count: number): void {
    this.selectedSettings.questionCount = count;
  }

  selectTestType(type: string): void {
    this.selectedSettings.testType = type;
    console.log('Test type selected:', type);
  }

  selectDuration(duration: number): void {
    this.selectedSettings.duration = duration;
    console.log('Duration selected:', duration);
  }

  getLevelLabel(level: string): string {
    const labels: { [key: string]: string } = {
      'debutant': 'Débutant',
      'intermediaire': 'Intermédiaire',
      'expert': 'Expert'
    };
    return labels[level] || level;
  }

  getTestTypeLabel(type: string): string {
    const labels: { [key: string]: string } = {
      'qcm': 'QCM',
      'code': 'Codage',
      'mixte': 'Mixte'
    };
    return labels[type] || type;
  }

  getDurationLabel(duration: number): string {
    if (duration < 60) {
      return `${duration} minutes`;
    } else {
      const hours = Math.floor(duration / 60);
      const minutes = duration % 60;
      return minutes > 0 ? `${hours}h${minutes}min` : `${hours} heure${hours > 1 ? 's' : ''}`;
    }
  }

  isConfigurationValid(): boolean {
    return !!(
      this.selectedSettings.level &&
      this.selectedSettings.questionCount > 0 &&
      this.selectedSettings.testType &&
      this.selectedSettings.duration > 0
    );
  }

  goBack(): void {
    // Retourner à la liste des candidats avec le postId
    if (this.candidateId) {
      // Récupérer le postId depuis l'URL précédente ou le localStorage
      const postId = localStorage.getItem('currentPostId');
      if (postId) {
        this.router.navigate(['/candidat', postId]);
      } else {
        this.router.navigate(['/acceuil']);
      }
    } else {
      this.router.navigate(['/acceuil']);
    }
  }

  startTest(): void {
    console.log('🚀 Starting test with settings:', this.selectedSettings);
    console.log('🎯 For candidate:', this.candidateId);

    // Sauvegarder les paramètres dans localStorage
    localStorage.setItem('test_settings', JSON.stringify(this.selectedSettings));
    localStorage.setItem('test_candidate_id', this.candidateId || '');

    // Rediriger vers la page de test avec l'ID du candidat
    this.router.navigate(['/test', this.candidateId], {
      queryParams: this.selectedSettings
    });
  }
}
