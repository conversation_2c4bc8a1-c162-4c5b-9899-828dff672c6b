.test-settings-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  padding: 0;
  color: #1e293b;
  overflow-x: hidden;
  position: relative;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;

  // Fond moderne et épuré
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      linear-gradient(135deg, rgba(0, 22, 96, 0.01) 0%, transparent 50%),
      radial-gradient(circle at 90% 10%, rgba(255, 107, 53, 0.02) 0%, transparent 40%);
    pointer-events: none;
    z-index: 0;
  }

  // Header moderne et épuré
  .header-section {
    background: linear-gradient(135deg, #001660FF 0%, #001660FF 100%);
    padding: 4rem 0 6rem 0;
    position: relative;
    z-index: 1;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      width: 40%;
      height: 100%;
      background: linear-gradient(135deg, transparent 0%, rgba(255, 107, 53, 0.1) 100%);
      clip-path: polygon(20% 0%, 100% 0%, 100% 100%, 0% 100%);
    }

    .header-content {
      max-width: 1400px;
      margin: 0 auto;
      padding: 0 3rem;
      position: relative;
      z-index: 2;

      .page-title {
        font-size: 3.5rem;
        font-weight: 900;
        color: white;
        margin: 0 0 1rem 0;
        letter-spacing: -0.02em;
        text-align: left;

        .highlight {
          color: #ff6b35;
        }
      }

      .page-subtitle {
        font-size: 1.2rem;
        color: rgba(255, 255, 255, 0.8);
        margin: 0;
        font-weight: 400;
        text-align: left;
        max-width: 600px;
      }
    }
  }

  // Layout principal moderne
  .main-content {
    max-width: 1400px;
    margin: -4rem auto 0 auto;
    padding: 0 3rem 4rem 3rem;
    position: relative;
    z-index: 2;
  }

  // Grille moderne pour PC
  .settings-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
    margin-bottom: 3rem;

    @media (max-width: 1200px) {
      grid-template-columns: repeat(2, 1fr);
    }

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }

    .setting-card {
      background: white;
      border-radius: 20px;
      padding: 2rem;
      border: 1px solid rgba(226, 232, 240, 0.6);
      box-shadow:
        0 4px 20px rgba(0, 0, 0, 0.04),
        0 1px 3px rgba(0, 0, 0, 0.02);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #001660FF 0%, #ff6b35 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      &:hover {
        transform: translateY(-4px);
        box-shadow:
          0 12px 40px rgba(0, 22, 96, 0.08),
          0 4px 16px rgba(0, 0, 0, 0.04);
        border-color: rgba(0, 22, 96, 0.1);

        &::before {
          opacity: 1;
        }
      }

      .card-header {
        display: flex;
        align-items: center;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid #f1f5f9;

        .card-icon {
          font-size: 1.5rem;
          color: #001660FF;
          margin-right: 0.75rem;
          width: 40px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: linear-gradient(135deg, rgba(0, 22, 96, 0.1), rgba(255, 107, 53, 0.1));
          border-radius: 12px;
          transition: all 0.3s ease;
        }

        h3 {
          font-size: 1.1rem;
          font-weight: 600;
          margin: 0;
          color: #1e293b;
          letter-spacing: -0.01em;
        }
      }

      .card-content {
        // Options modernes
        .level-options,
        .question-options,
        .test-type-options,
        .time-options {
          display: flex;
          flex-direction: column;
          gap: 0.75rem;

          .level-option,
          .question-option,
          .test-type-option,
          .time-option {
            display: flex;
            align-items: center;
            padding: 1rem;
            border-radius: 12px;
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;

            &:hover {
              background: #f1f5f9;
              border-color: #001660FF;
            }

            &.selected {
              background: linear-gradient(135deg, rgba(0, 22, 96, 0.05), rgba(255, 107, 53, 0.05));
              border-color: #001660FF;
              box-shadow: 0 2px 8px rgba(0, 22, 96, 0.1);

              &::before {
                content: '';
                position: absolute;
                left: 0;
                top: 0;
                bottom: 0;
                width: 3px;
                background: linear-gradient(180deg, #001660FF, #ff6b35);
                border-radius: 0 2px 2px 0;
              }
            }

            mat-icon {
              font-size: 1.25rem;
              color: #001660FF;
              margin-right: 0.75rem;
            }

            .option-info {
              flex: 1;

              .option-name,
              .level-name,
              .type-name,
              .time-value {
                font-weight: 600;
                font-size: 0.95rem;
                color: #1e293b;
                margin-bottom: 0.25rem;
                display: block;
              }

              .option-desc,
              .level-desc,
              .type-desc,
              .time-desc {
                font-size: 0.8rem;
                color: #64748b;
              }
            }
          }
        }

        // Question Selector
        .question-selector {
          .question-display {
            text-align: center;
            margin-bottom: 1.5rem;
            padding: 1rem;
            background: linear-gradient(135deg, rgba(0, 22, 96, 0.05), rgba(255, 107, 53, 0.05));
            border-radius: 12px;

            .question-count {
              font-size: 1.5rem;
              font-weight: 700;
              color: #001660FF;
            }

            .question-label {
              font-size: 0.9rem;
              color: #64748b;
              margin-left: 0.5rem;
            }
          }
        }


      }
    }
  }

  // Section résumé moderne
  .summary-section {
    margin-bottom: 3rem;

    .summary-card {
      background: white;
      border-radius: 20px;
      padding: 2rem;
      border: 1px solid #e2e8f0;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);

      h3 {
        font-size: 1.25rem;
        font-weight: 600;
        margin: 0 0 1.5rem 0;
        color: #1e293b;
        display: flex;
        align-items: center;

        mat-icon {
          color: #001660FF;
          margin-right: 0.5rem;
          font-size: 1.5rem;
        }
      }

      .summary-details {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 1rem;

        @media (max-width: 1200px) {
          grid-template-columns: repeat(2, 1fr);
        }

        @media (max-width: 768px) {
          grid-template-columns: 1fr;
        }

        .summary-item {
          display: flex;
          align-items: center;
          padding: 1rem;
          background: #f8fafc;
          border-radius: 12px;
          border-left: 3px solid #001660FF;

          mat-icon {
            color: #001660FF;
            margin-right: 0.75rem;
            font-size: 1.25rem;
          }

          span {
            font-weight: 500;
            color: #1e293b;
            font-size: 0.9rem;
          }
        }
      }
    }
  }

  // Action Section - Design professionnel
  .action-section {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2.5rem;
    max-width: 600px;
    margin: 0 auto;
    padding: 2rem;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.8), rgba(248, 250, 252, 0.6));
    border-radius: 24px;
    border: 1px solid rgba(226, 232, 240, 0.5);
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.06),
      inset 0 1px 0 rgba(255, 255, 255, 0.9);

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 1.5rem;
      padding: 1.5rem;
    }

    .secondary-button {
      padding: 1.2rem 2.5rem;
      border: 2px solid #e2e8f0;
      color: #64748b;
      background: linear-gradient(135deg, #ffffff, #f8fafc);
      font-weight: 600;
      font-size: 1rem;
      border-radius: 16px;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow:
        0 4px 16px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 107, 53, 0.1), transparent);
        transition: left 0.5s ease;
      }

      &:hover {
        border-color: #ff6b35;
        background: linear-gradient(135deg, rgba(255, 107, 53, 0.05), rgba(255, 107, 53, 0.02));
        color: #ff6b35;
        transform: translateY(-3px);
        box-shadow:
          0 8px 24px rgba(255, 107, 53, 0.15),
          inset 0 1px 0 rgba(255, 255, 255, 0.9);

        &::before {
          left: 100%;
        }
      }

      mat-icon {
        margin-right: 0.6rem;
        font-size: 1.2rem;
      }

      @media (max-width: 768px) {
        width: 100%;
        justify-content: center;
      }
    }

    .primary-button {
      padding: 1.2rem 3rem;
      background: linear-gradient(135deg, #ff6b35 0%, #ff8c42 50%, #ff6b35 100%);
      color: white;
      font-weight: 700;
      font-size: 1.1rem;
      border: none;
      border-radius: 16px;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow:
        0 8px 24px rgba(255, 107, 53, 0.3),
        0 4px 12px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
      position: relative;
      overflow: hidden;
      letter-spacing: 0.02em;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.6s ease;
      }

      &:hover:not(:disabled) {
        background: linear-gradient(135deg, #ff8c42 0%, #ff6b35 50%, #ff8c42 100%);
        box-shadow:
          0 12px 32px rgba(255, 107, 53, 0.4),
          0 6px 16px rgba(0, 0, 0, 0.15);
        transform: translateY(-4px) scale(1.02);

        &::before {
          left: 100%;
        }
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        background: linear-gradient(135deg, #cbd5e1, #94a3b8);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transform: none;
      }

      mat-icon {
        margin-left: 0.6rem;
        font-size: 1.3rem;
      }

      @media (max-width: 768px) {
        width: 100%;
        justify-content: center;
        padding: 1.2rem 2rem;
      }
    }
  }
}

// Animations sophistiquées avec #001660FF
@keyframes pulse {
  0%, 100% {
    opacity: 0.4;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 0.8;
    transform: translate(-50%, -50%) scale(1.15);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: 200px 0;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-8px);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow:
      0 4px 12px rgba(0, 22, 96, 0.3),
      0 2px 6px rgba(255, 107, 53, 0.2);
  }
  50% {
    box-shadow:
      0 8px 24px rgba(0, 22, 96, 0.4),
      0 4px 12px rgba(255, 107, 53, 0.3);
  }
}

@keyframes gradientShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .test-settings-container {
    padding: 1rem;

    .settings-grid {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }

    .action-section {
      flex-direction: column;
      gap: 1rem;

      button {
        width: 100%;
      }
    }
  }
}
