.test-settings-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 50%, #f1f5f9 100%);
  padding: 2rem;
  color: #1e293b;
  overflow-x: hidden;

  // Header Section
  .header-section {
    text-align: center;
    margin-bottom: 3rem;

    .header-content {
      position: relative;

      .icon-wrapper {
        position: relative;
        display: inline-block;
        margin-bottom: 1rem;

        .main-icon {
          font-size: 4rem;
          color: #ff6b35;
          filter: drop-shadow(0 0 20px rgba(255, 107, 53, 0.5));
        }

        .icon-glow {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 80px;
          height: 80px;
          background: radial-gradient(circle, rgba(255, 107, 53, 0.3) 0%, transparent 70%);
          border-radius: 50%;
          animation: pulse 2s infinite;
        }
      }

      .page-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin: 0 0 0.5rem 0;
        background: linear-gradient(45deg, #1e293b, #ff6b35);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      .page-subtitle {
        font-size: 1.1rem;
        color: #64748b;
        margin: 0 0 1rem 0;
      }

      .title-underline {
        width: 100px;
        height: 3px;
        background: linear-gradient(90deg, #ff6b35, #001040FF);
        margin: 0 auto;
        border-radius: 2px;
      }
    }
  }

  // Settings Grid - Nouveau layout professionnel
  .settings-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto;
    gap: 2.5rem;
    margin-bottom: 4rem;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;

    // Layout responsive
    @media (max-width: 968px) {
      grid-template-columns: 1fr;
      gap: 2rem;
    }

    .setting-card {
      background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
      border-radius: 24px;
      padding: 2.5rem;
      backdrop-filter: blur(20px);
      border: 1px solid rgba(226, 232, 240, 0.6);
      box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.06),
        0 1px 4px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;

      // Effet de brillance subtil
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
        transition: left 0.6s ease;
      }

      &:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow:
          0 20px 60px rgba(255, 107, 53, 0.12),
          0 8px 32px rgba(0, 0, 0, 0.08),
          inset 0 1px 0 rgba(255, 255, 255, 0.9);
        border-color: rgba(255, 107, 53, 0.3);

        &::before {
          left: 100%;
        }
      }

      // Cartes spéciales avec positionnement
      &:nth-child(1) { // Niveau
        grid-column: 1;
        grid-row: 1;
      }

      &:nth-child(2) { // Questions
        grid-column: 2;
        grid-row: 1;
      }

      &:nth-child(3) { // Type de test
        grid-column: 1;
        grid-row: 2;
      }

      &:nth-child(4) { // Durée
        grid-column: 2;
        grid-row: 2;
      }

      @media (max-width: 968px) {
        &:nth-child(n) {
          grid-column: 1 !important;
          grid-row: auto !important;
        }
      }

      .card-header {
        display: flex;
        align-items: center;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid rgba(226, 232, 240, 0.5);
        position: relative;

        .card-icon {
          font-size: 2.2rem;
          color: #ff6b35;
          margin-right: 1.2rem;
          padding: 0.8rem;
          background: linear-gradient(135deg, rgba(255, 107, 53, 0.1), rgba(255, 107, 53, 0.05));
          border-radius: 16px;
          box-shadow: 0 4px 12px rgba(255, 107, 53, 0.15);
          transition: all 0.3s ease;
        }

        h3 {
          font-size: 1.4rem;
          font-weight: 700;
          margin: 0;
          color: #1e293b;
          letter-spacing: -0.02em;
        }

        // Effet de gradient subtil sur le titre
        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 0;
          width: 60px;
          height: 2px;
          background: linear-gradient(90deg, #ff6b35, transparent);
          border-radius: 1px;
        }
      }

      .card-content {
        // Level Options
        .level-options {
          display: flex;
          flex-direction: column;
          gap: 1rem;

          .level-option {
            display: flex;
            align-items: center;
            padding: 1rem;
            border-radius: 12px;
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            cursor: pointer;
            transition: all 0.3s ease;

            &:hover {
              background: rgba(255, 107, 53, 0.05);
              border-color: rgba(255, 107, 53, 0.3);
              transform: translateY(-2px);
            }

            &.selected {
              background: rgba(255, 107, 53, 0.1);
              border-color: #ff6b35;
              box-shadow: 0 8px 25px rgba(255, 107, 53, 0.2);
            }

            mat-icon {
              font-size: 1.5rem;
              margin-right: 1rem;
              color: #ff6b35;
            }

            .level-name {
              font-weight: 600;
              font-size: 1.1rem;
              margin-bottom: 0.2rem;
              color: #1e293b;
            }

            .level-desc {
              font-size: 0.9rem;
              color: #64748b;
              margin-left: auto;
            }
          }
        }

        // Question Selector
        .question-selector {
          .question-display {
            text-align: center;
            margin-bottom: 1.5rem;

            .question-count {
              font-size: 2rem;
              font-weight: 700;
              color: #ff6b35;
            }

            .question-label {
              font-size: 1rem;
              opacity: 0.8;
              margin-left: 0.5rem;
            }
          }

          .question-options {
            display: flex;
            flex-direction: column;
            gap: 1rem;

            .question-option {
              display: flex;
              align-items: center;
              padding: 1rem;
              border-radius: 12px;
              background: #f8fafc;
              border: 2px solid #e2e8f0;
              cursor: pointer;
              transition: all 0.3s ease;

              &:hover {
                background: rgba(255, 107, 53, 0.05);
                border-color: rgba(255, 107, 53, 0.3);
                transform: translateY(-2px);
              }

              &.selected {
                background: rgba(255, 107, 53, 0.1);
                border-color: #ff6b35;
                box-shadow: 0 8px 25px rgba(255, 107, 53, 0.2);
              }

              mat-icon {
                font-size: 1.5rem;
                margin-right: 1rem;
                color: #ff6b35;
              }

              .option-info {
                .option-name {
                  font-weight: 600;
                  font-size: 1.1rem;
                  display: block;
                  color: #1e293b;
                }

                .option-desc {
                  font-size: 0.9rem;
                  color: #64748b;
                }
              }
            }
          }
        }

        // Test Types
        .test-types {
          display: flex;
          flex-direction: column;
          gap: 1rem;

          .test-type-option {
            display: flex;
            align-items: center;
            padding: 1rem;
            border-radius: 12px;
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            cursor: pointer;
            transition: all 0.3s ease;

            &:hover {
              background: rgba(255, 107, 53, 0.05);
              border-color: rgba(255, 107, 53, 0.3);
              transform: translateY(-2px);
            }

            &.selected {
              background: rgba(255, 107, 53, 0.1);
              border-color: #ff6b35;
              box-shadow: 0 8px 25px rgba(255, 107, 53, 0.2);
            }

            mat-icon {
              font-size: 1.5rem;
              margin-right: 1rem;
              color: #ff6b35;
            }

            .type-info {
              .type-name {
                font-weight: 600;
                font-size: 1.1rem;
                display: block;
                color: #1e293b;
              }

              .type-desc {
                font-size: 0.9rem;
                color: #64748b;
              }
            }
          }
        }

        // Duration Selector
        .duration-selector {
          .time-options {
            display: flex;
            flex-direction: column;
            gap: 1rem;

            .time-option {
              display: flex;
              align-items: center;
              padding: 1rem;
              border-radius: 12px;
              background: #f8fafc;
              border: 2px solid #e2e8f0;
              cursor: pointer;
              transition: all 0.3s ease;

              &:hover {
                background: rgba(255, 107, 53, 0.05);
                border-color: rgba(255, 107, 53, 0.3);
                transform: translateY(-2px);
              }

              &.selected {
                background: rgba(255, 107, 53, 0.1);
                border-color: #ff6b35;
                box-shadow: 0 8px 25px rgba(255, 107, 53, 0.2);
              }

              mat-icon {
                font-size: 1.5rem;
                margin-right: 1rem;
                color: #ff6b35;
              }

              .time-value {
                font-weight: 600;
                font-size: 1.1rem;
                margin-right: auto;
                color: #1e293b;
              }

              .time-desc {
                font-size: 0.9rem;
                color: #64748b;
              }
            }
          }
        }
      }
    }
  }

  // Summary Section - Design professionnel
  .summary-section {
    margin-bottom: 4rem;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;

    .summary-card {
      background: linear-gradient(135deg,
        rgba(255, 107, 53, 0.03) 0%,
        rgba(255, 255, 255, 0.95) 50%,
        rgba(255, 107, 53, 0.02) 100%);
      border-radius: 24px;
      padding: 3rem;
      border: 1px solid rgba(255, 107, 53, 0.15);
      box-shadow:
        0 12px 40px rgba(255, 107, 53, 0.08),
        0 4px 16px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
      position: relative;
      overflow: hidden;

      // Effet de fond décoratif
      &::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255, 107, 53, 0.03) 0%, transparent 70%);
        pointer-events: none;
      }

      h3 {
        font-size: 1.8rem;
        font-weight: 700;
        margin: 0 0 2rem 0;
        color: #ff6b35;
        text-align: center;
        position: relative;
        z-index: 1;

        &::after {
          content: '';
          position: absolute;
          bottom: -8px;
          left: 50%;
          transform: translateX(-50%);
          width: 80px;
          height: 3px;
          background: linear-gradient(90deg, #ff6b35, #ff8c42);
          border-radius: 2px;
        }
      }

      .summary-details {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
        position: relative;
        z-index: 1;

        @media (max-width: 768px) {
          grid-template-columns: 1fr;
        }

        .summary-item {
          display: flex;
          align-items: center;
          padding: 1.2rem 1.5rem;
          background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.8));
          border-radius: 16px;
          border: 1px solid rgba(255, 107, 53, 0.1);
          box-shadow:
            0 4px 16px rgba(0, 0, 0, 0.04),
            inset 0 1px 0 rgba(255, 255, 255, 0.8);
          transition: all 0.3s ease;
          position: relative;
          overflow: hidden;

          &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(180deg, #ff6b35, #ff8c42);
            border-radius: 0 2px 2px 0;
          }

          &:hover {
            transform: translateY(-2px);
            box-shadow:
              0 8px 24px rgba(255, 107, 53, 0.1),
              0 4px 16px rgba(0, 0, 0, 0.06);
            border-color: rgba(255, 107, 53, 0.2);
          }

          mat-icon {
            color: #ff6b35;
            margin-right: 1rem;
            font-size: 1.4rem;
            padding: 0.5rem;
            background: rgba(255, 107, 53, 0.1);
            border-radius: 12px;
          }

          span {
            font-weight: 600;
            color: #1e293b;
            font-size: 0.95rem;
          }
        }
      }
    }
  }

  // Action Section - Design professionnel
  .action-section {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2.5rem;
    max-width: 600px;
    margin: 0 auto;
    padding: 2rem;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.8), rgba(248, 250, 252, 0.6));
    border-radius: 24px;
    border: 1px solid rgba(226, 232, 240, 0.5);
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.06),
      inset 0 1px 0 rgba(255, 255, 255, 0.9);

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 1.5rem;
      padding: 1.5rem;
    }

    .secondary-button {
      padding: 1.2rem 2.5rem;
      border: 2px solid #e2e8f0;
      color: #64748b;
      background: linear-gradient(135deg, #ffffff, #f8fafc);
      font-weight: 600;
      font-size: 1rem;
      border-radius: 16px;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow:
        0 4px 16px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 107, 53, 0.1), transparent);
        transition: left 0.5s ease;
      }

      &:hover {
        border-color: #ff6b35;
        background: linear-gradient(135deg, rgba(255, 107, 53, 0.05), rgba(255, 107, 53, 0.02));
        color: #ff6b35;
        transform: translateY(-3px);
        box-shadow:
          0 8px 24px rgba(255, 107, 53, 0.15),
          inset 0 1px 0 rgba(255, 255, 255, 0.9);

        &::before {
          left: 100%;
        }
      }

      mat-icon {
        margin-right: 0.6rem;
        font-size: 1.2rem;
      }

      @media (max-width: 768px) {
        width: 100%;
        justify-content: center;
      }
    }

    .primary-button {
      padding: 1.2rem 3rem;
      background: linear-gradient(135deg, #ff6b35 0%, #ff8c42 50%, #ff6b35 100%);
      color: white;
      font-weight: 700;
      font-size: 1.1rem;
      border: none;
      border-radius: 16px;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow:
        0 8px 24px rgba(255, 107, 53, 0.3),
        0 4px 12px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
      position: relative;
      overflow: hidden;
      letter-spacing: 0.02em;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.6s ease;
      }

      &:hover:not(:disabled) {
        background: linear-gradient(135deg, #ff8c42 0%, #ff6b35 50%, #ff8c42 100%);
        box-shadow:
          0 12px 32px rgba(255, 107, 53, 0.4),
          0 6px 16px rgba(0, 0, 0, 0.15);
        transform: translateY(-4px) scale(1.02);

        &::before {
          left: 100%;
        }
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        background: linear-gradient(135deg, #cbd5e1, #94a3b8);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transform: none;
      }

      mat-icon {
        margin-left: 0.6rem;
        font-size: 1.3rem;
      }

      @media (max-width: 768px) {
        width: 100%;
        justify-content: center;
        padding: 1.2rem 2rem;
      }
    }
  }
}

// Animations
@keyframes pulse {
  0%, 100% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1.1);
  }
}

// Responsive Design
@media (max-width: 768px) {
  .test-settings-container {
    padding: 1rem;

    .settings-grid {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }

    .action-section {
      flex-direction: column;
      gap: 1rem;

      button {
        width: 100%;
      }
    }
  }
}
