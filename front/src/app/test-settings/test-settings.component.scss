.test-settings-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #001040FF 0%, #1a1a2e 50%, #16213e 100%);
  padding: 2rem;
  color: white;
  overflow-x: hidden;

  // Header Section
  .header-section {
    text-align: center;
    margin-bottom: 3rem;

    .header-content {
      position: relative;

      .icon-wrapper {
        position: relative;
        display: inline-block;
        margin-bottom: 1rem;

        .main-icon {
          font-size: 4rem;
          color: #ff6b35;
          filter: drop-shadow(0 0 20px rgba(255, 107, 53, 0.5));
        }

        .icon-glow {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 80px;
          height: 80px;
          background: radial-gradient(circle, rgba(255, 107, 53, 0.3) 0%, transparent 70%);
          border-radius: 50%;
          animation: pulse 2s infinite;
        }
      }

      .page-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin: 0 0 0.5rem 0;
        background: linear-gradient(45deg, #ffffff, #ff6b35);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      .page-subtitle {
        font-size: 1.1rem;
        opacity: 0.8;
        margin: 0 0 1rem 0;
      }

      .title-underline {
        width: 100px;
        height: 3px;
        background: linear-gradient(90deg, #ff6b35, #001040FF);
        margin: 0 auto;
        border-radius: 2px;
      }
    }
  }

  // Settings Grid
  .settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;

    .setting-card {
      background: rgba(255, 255, 255, 0.05);
      border-radius: 16px;
      padding: 2rem;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.1);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        border-color: rgba(255, 107, 53, 0.3);
      }

      .card-header {
        display: flex;
        align-items: center;
        margin-bottom: 1.5rem;

        .card-icon {
          font-size: 1.8rem;
          color: #ff6b35;
          margin-right: 1rem;
        }

        h3 {
          font-size: 1.3rem;
          font-weight: 600;
          margin: 0;
        }
      }

      .card-content {
        // Level Options
        .level-options {
          display: flex;
          flex-direction: column;
          gap: 1rem;

          .level-option {
            display: flex;
            align-items: center;
            padding: 1rem;
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.03);
            border: 2px solid transparent;
            cursor: pointer;
            transition: all 0.3s ease;

            &:hover {
              background: rgba(255, 107, 53, 0.1);
              border-color: rgba(255, 107, 53, 0.3);
            }

            &.selected {
              background: rgba(255, 107, 53, 0.2);
              border-color: #ff6b35;
              box-shadow: 0 0 20px rgba(255, 107, 53, 0.3);
            }

            mat-icon {
              font-size: 1.5rem;
              margin-right: 1rem;
              color: #ff6b35;
            }

            .level-name {
              font-weight: 600;
              font-size: 1.1rem;
              margin-bottom: 0.2rem;
            }

            .level-desc {
              font-size: 0.9rem;
              opacity: 0.7;
              margin-left: auto;
            }
          }
        }

        // Question Selector
        .question-selector {
          .slider-container {
            margin-bottom: 1.5rem;

            .question-slider {
              width: 100%;
              --mdc-slider-active-track-color: #ff6b35;
              --mdc-slider-inactive-track-color: rgba(255, 255, 255, 0.2);
              --mdc-slider-handle-color: #ff6b35;
            }
          }

          .question-display {
            text-align: center;
            margin-bottom: 1.5rem;

            .question-count {
              font-size: 2rem;
              font-weight: 700;
              color: #ff6b35;
            }

            .question-label {
              font-size: 1rem;
              opacity: 0.8;
              margin-left: 0.5rem;
            }
          }

          .question-presets {
            display: flex;
            gap: 0.5rem;
            justify-content: center;

            button {
              border-color: rgba(255, 255, 255, 0.3);
              color: white;
              transition: all 0.3s ease;

              &:hover {
                border-color: #ff6b35;
                background: rgba(255, 107, 53, 0.1);
              }

              &.selected {
                border-color: #ff6b35;
                background: rgba(255, 107, 53, 0.2);
                color: #ff6b35;
              }
            }
          }
        }

        // Test Types
        .test-types {
          display: flex;
          flex-direction: column;
          gap: 1rem;

          .test-type-option {
            display: flex;
            align-items: center;
            padding: 1rem;
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.03);
            border: 2px solid transparent;
            cursor: pointer;
            transition: all 0.3s ease;

            &:hover {
              background: rgba(255, 107, 53, 0.1);
              border-color: rgba(255, 107, 53, 0.3);
            }

            &.selected {
              background: rgba(255, 107, 53, 0.2);
              border-color: #ff6b35;
              box-shadow: 0 0 20px rgba(255, 107, 53, 0.3);
            }

            mat-icon {
              font-size: 1.5rem;
              margin-right: 1rem;
              color: #ff6b35;
            }

            .type-info {
              .type-name {
                font-weight: 600;
                font-size: 1.1rem;
                display: block;
              }

              .type-desc {
                font-size: 0.9rem;
                opacity: 0.7;
              }
            }
          }
        }

        // Duration Selector
        .duration-selector {
          .time-options {
            display: flex;
            flex-direction: column;
            gap: 1rem;

            .time-option {
              display: flex;
              align-items: center;
              padding: 1rem;
              border-radius: 12px;
              background: rgba(255, 255, 255, 0.03);
              border: 2px solid transparent;
              cursor: pointer;
              transition: all 0.3s ease;

              &:hover {
                background: rgba(255, 107, 53, 0.1);
                border-color: rgba(255, 107, 53, 0.3);
              }

              &.selected {
                background: rgba(255, 107, 53, 0.2);
                border-color: #ff6b35;
                box-shadow: 0 0 20px rgba(255, 107, 53, 0.3);
              }

              mat-icon {
                font-size: 1.5rem;
                margin-right: 1rem;
                color: #ff6b35;
              }

              .time-value {
                font-weight: 600;
                font-size: 1.1rem;
                margin-right: auto;
              }

              .time-desc {
                font-size: 0.9rem;
                opacity: 0.7;
              }
            }
          }
        }
      }
    }
  }

  // Summary Section
  .summary-section {
    margin-bottom: 3rem;

    .summary-card {
      background: rgba(255, 107, 53, 0.1);
      border-radius: 16px;
      padding: 2rem;
      border: 1px solid rgba(255, 107, 53, 0.3);

      h3 {
        font-size: 1.5rem;
        font-weight: 600;
        margin: 0 0 1.5rem 0;
        color: #ff6b35;
      }

      .summary-details {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;

        .summary-item {
          display: flex;
          align-items: center;
          padding: 0.8rem;
          background: rgba(255, 255, 255, 0.05);
          border-radius: 8px;

          mat-icon {
            color: #ff6b35;
            margin-right: 0.8rem;
          }

          span {
            font-weight: 500;
          }
        }
      }
    }
  }

  // Action Section
  .action-section {
    display: flex;
    justify-content: center;
    gap: 2rem;

    .secondary-button {
      padding: 1rem 2rem;
      border-color: rgba(255, 255, 255, 0.3);
      color: white;
      font-weight: 600;
      transition: all 0.3s ease;

      &:hover {
        border-color: #ff6b35;
        background: rgba(255, 107, 53, 0.1);
      }

      mat-icon {
        margin-right: 0.5rem;
      }
    }

    .primary-button {
      padding: 1rem 2rem;
      background: linear-gradient(45deg, #ff6b35, #ff8c42);
      color: white;
      font-weight: 600;
      border: none;
      transition: all 0.3s ease;

      &:hover:not(:disabled) {
        background: linear-gradient(45deg, #ff8c42, #ff6b35);
        box-shadow: 0 10px 30px rgba(255, 107, 53, 0.4);
        transform: translateY(-2px);
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      mat-icon {
        margin-left: 0.5rem;
      }
    }
  }
}

// Animations
@keyframes pulse {
  0%, 100% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1.1);
  }
}

// Responsive Design
@media (max-width: 768px) {
  .test-settings-container {
    padding: 1rem;

    .settings-grid {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }

    .action-section {
      flex-direction: column;
      gap: 1rem;

      button {
        width: 100%;
      }
    }
  }
}
