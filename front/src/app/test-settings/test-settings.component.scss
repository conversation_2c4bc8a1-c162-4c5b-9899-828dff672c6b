.test-settings-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  padding: 0;
  color: #1e293b;
  overflow-x: hidden;
  position: relative;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;

  // Fond moderne avec motifs géométriques
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      linear-gradient(135deg, rgba(0, 22, 96, 0.01) 0%, transparent 50%),
      radial-gradient(circle at 90% 10%, rgba(255, 107, 53, 0.02) 0%, transparent 40%),
      url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23001660' fill-opacity='0.02'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    pointer-events: none;
    z-index: 0;
  }

  // Effet de particules flottantes
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
      radial-gradient(circle at 20% 30%, rgba(255, 107, 53, 0.03) 1px, transparent 1px),
      radial-gradient(circle at 80% 70%, rgba(0, 22, 96, 0.03) 1px, transparent 1px),
      radial-gradient(circle at 40% 80%, rgba(255, 107, 53, 0.02) 1px, transparent 1px);
    background-size: 100px 100px, 150px 150px, 200px 200px;
    animation: float 20s infinite linear;
    pointer-events: none;
    z-index: 0;
  }

  // Header moderne et épuré
  .header-section {
    background: linear-gradient(135deg, #001660FF 0%, #001660FF 100%);
    padding: 4rem 0 6rem 0;
    position: relative;
    z-index: 1;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      width: 40%;
      height: 100%;
      background: linear-gradient(135deg, transparent 0%, rgba(255, 107, 53, 0.1) 100%);
      clip-path: polygon(20% 0%, 100% 0%, 100% 100%, 0% 100%);
    }

    .header-content {
      max-width: 1400px;
      margin: 0 auto;
      padding: 0 3rem;
      position: relative;
      z-index: 2;

      .page-title {
        font-size: 3.5rem;
        font-weight: 900;
        color: white;
        margin: 0 0 1rem 0;
        letter-spacing: -0.02em;
        text-align: left;

        .highlight {
          color: #ff6b35;
        }
      }

      .page-subtitle {
        font-size: 1.2rem;
        color: rgba(255, 255, 255, 0.8);
        margin: 0;
        font-weight: 400;
        text-align: left;
        max-width: 600px;
      }
    }
  }

  // Layout principal moderne
  .main-content {
    max-width: 1400px;
    margin: -4rem auto 0 auto;
    padding: 0 3rem 4rem 3rem;
    position: relative;
    z-index: 2;
  }

  // Grille moderne pour PC
  .settings-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
    margin-bottom: 3rem;

    @media (max-width: 1200px) {
      grid-template-columns: repeat(2, 1fr);
    }

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }

    .setting-card {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      border-radius: 24px;
      padding: 2.5rem;
      border: 1px solid rgba(255, 255, 255, 0.2);
      box-shadow:
        0 8px 32px rgba(0, 22, 96, 0.06),
        0 2px 8px rgba(0, 0, 0, 0.02),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;

      // Barre de progression en haut
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #001660FF 0%, #ff6b35 100%);
        opacity: 0;
        transition: all 0.4s ease;
        transform: scaleX(0);
        transform-origin: left;
      }

      // Effet de brillance
      &::after {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg,
          transparent 30%,
          rgba(255, 255, 255, 0.1) 50%,
          transparent 70%);
        transform: rotate(45deg);
        transition: transform 0.6s ease;
        pointer-events: none;
      }

      &:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow:
          0 20px 60px rgba(0, 22, 96, 0.12),
          0 8px 32px rgba(255, 107, 53, 0.08),
          inset 0 1px 0 rgba(255, 255, 255, 0.9);
        border-color: rgba(0, 22, 96, 0.15);

        &::before {
          opacity: 1;
          transform: scaleX(1);
        }

        &::after {
          transform: rotate(45deg) translate(50%, 50%);
        }
      }

      // Animation de pulsation pour la carte active
      &.active {
        animation: cardPulse 2s infinite;

        &::before {
          opacity: 1;
          transform: scaleX(1);
          animation: progressGlow 2s infinite;
        }
      }

      .card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 2rem;
        padding-bottom: 1.5rem;
        border-bottom: 1px solid rgba(226, 232, 240, 0.5);
        position: relative;

        .header-left {
          display: flex;
          align-items: center;
          flex: 1;
        }

        .card-icon {
          font-size: 1.5rem;
          color: #001660FF;
          margin-right: 1rem;
          width: 48px;
          height: 48px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: linear-gradient(135deg, rgba(0, 22, 96, 0.1), rgba(255, 107, 53, 0.1));
          border-radius: 16px;
          transition: all 0.3s ease;
          position: relative;

          &::after {
            content: '';
            position: absolute;
            inset: -2px;
            background: linear-gradient(135deg, #001660FF, #ff6b35);
            border-radius: 18px;
            opacity: 0;
            z-index: -1;
            transition: opacity 0.3s ease;
          }

          &:hover::after {
            opacity: 0.1;
          }
        }

        h3 {
          font-size: 1.2rem;
          font-weight: 700;
          margin: 0;
          color: #1e293b;
          letter-spacing: -0.01em;
          flex: 1;
        }

        .status-badge {
          padding: 0.25rem 0.75rem;
          border-radius: 20px;
          font-size: 0.75rem;
          font-weight: 600;
          text-transform: uppercase;
          letter-spacing: 0.05em;
          transition: all 0.3s ease;

          &.pending {
            background: rgba(251, 191, 36, 0.1);
            color: #f59e0b;
            border: 1px solid rgba(251, 191, 36, 0.2);
          }

          &.completed {
            background: rgba(34, 197, 94, 0.1);
            color: #22c55e;
            border: 1px solid rgba(34, 197, 94, 0.2);
          }

          &.active {
            background: rgba(0, 22, 96, 0.1);
            color: #001660FF;
            border: 1px solid rgba(0, 22, 96, 0.2);
            animation: badgePulse 2s infinite;
          }
        }

        // Ligne de progression sous le header
        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 0;
          height: 2px;
          background: linear-gradient(90deg, #001660FF, #ff6b35);
          border-radius: 1px;
          transition: width 0.4s ease;
          width: 0%;
        }

        &.completed::after {
          width: 100%;
        }

        &.active::after {
          width: 60%;
          animation: progressFlow 2s infinite;
        }
      }

      .card-content {
        // Options modernes
        .level-options,
        .question-options,
        .test-type-options,
        .time-options {
          display: flex;
          flex-direction: column;
          gap: 0.75rem;

          .level-option,
          .question-option,
          .test-type-option,
          .time-option {
            display: flex;
            align-items: center;
            padding: 1.25rem;
            border-radius: 16px;
            background: rgba(248, 250, 252, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(226, 232, 240, 0.6);
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;

            // Effet de vague au clic
            &::before {
              content: '';
              position: absolute;
              top: 50%;
              left: 50%;
              width: 0;
              height: 0;
              background: radial-gradient(circle, rgba(0, 22, 96, 0.1) 0%, transparent 70%);
              border-radius: 50%;
              transform: translate(-50%, -50%);
              transition: all 0.4s ease;
              pointer-events: none;
            }

            // Indicateur de sélection
            &::after {
              content: '';
              position: absolute;
              left: 0;
              top: 0;
              bottom: 0;
              width: 4px;
              background: linear-gradient(180deg, #001660FF, #ff6b35);
              border-radius: 0 4px 4px 0;
              transform: scaleY(0);
              transition: transform 0.3s ease;
            }

            &:hover {
              background: rgba(241, 245, 249, 0.9);
              border-color: rgba(0, 22, 96, 0.3);
              transform: translateY(-2px);
              box-shadow: 0 8px 25px rgba(0, 22, 96, 0.08);

              &::before {
                width: 100px;
                height: 100px;
              }
            }

            &:active {
              transform: translateY(-1px) scale(0.98);
            }

            &.selected {
              background: linear-gradient(135deg,
                rgba(0, 22, 96, 0.08) 0%,
                rgba(255, 107, 53, 0.08) 100%);
              border-color: #001660FF;
              box-shadow:
                0 4px 20px rgba(0, 22, 96, 0.12),
                inset 0 1px 0 rgba(255, 255, 255, 0.5);

              &::after {
                transform: scaleY(1);
              }

              .check-icon {
                opacity: 1;
                transform: scale(1);
              }
            }

            mat-icon {
              font-size: 1.5rem;
              color: #001660FF;
              margin-right: 1rem;
              transition: all 0.3s ease;

              &.check-icon {
                position: absolute;
                right: 1rem;
                top: 50%;
                transform: translateY(-50%) scale(0);
                opacity: 0;
                color: #22c55e;
                font-size: 1.25rem;
              }
            }

            .option-info {
              flex: 1;

              .option-name,
              .level-name,
              .type-name,
              .time-value {
                font-weight: 700;
                font-size: 1rem;
                color: #1e293b;
                margin-bottom: 0.25rem;
                display: block;
                transition: color 0.3s ease;
              }

              .option-desc,
              .level-desc,
              .type-desc,
              .time-desc {
                font-size: 0.85rem;
                color: #64748b;
                transition: color 0.3s ease;
              }
            }

            &:hover .option-info {
              .option-name,
              .level-name,
              .type-name,
              .time-value {
                color: #001660FF;
              }
            }
          }
        }

        // Question Selector
        .question-selector {
          .question-display {
            text-align: center;
            margin-bottom: 1.5rem;
            padding: 1rem;
            background: linear-gradient(135deg, rgba(0, 22, 96, 0.05), rgba(255, 107, 53, 0.05));
            border-radius: 12px;

            .question-count {
              font-size: 1.5rem;
              font-weight: 700;
              color: #001660FF;
            }

            .question-label {
              font-size: 0.9rem;
              color: #64748b;
              margin-left: 0.5rem;
            }
          }
        }


      }
    }
  }

  // Section résumé ultra-moderne
  .summary-section {
    margin-bottom: 3rem;
    animation: slideInUp 0.6s ease-out 0.4s both;

    .summary-card {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      border-radius: 24px;
      padding: 2.5rem;
      border: 1px solid rgba(255, 255, 255, 0.2);
      box-shadow:
        0 8px 32px rgba(0, 22, 96, 0.08),
        0 2px 8px rgba(0, 0, 0, 0.02),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #001660FF 0%, #ff6b35 100%);
        animation: shimmer 3s infinite;
      }

      h3 {
        font-size: 1.5rem;
        font-weight: 700;
        margin: 0 0 2rem 0;
        color: #1e293b;
        display: flex;
        align-items: center;
        position: relative;

        mat-icon {
          color: #001660FF;
          margin-right: 0.75rem;
          font-size: 1.8rem;
          padding: 0.5rem;
          background: linear-gradient(135deg, rgba(0, 22, 96, 0.1), rgba(255, 107, 53, 0.1));
          border-radius: 12px;
        }

        &::after {
          content: '';
          position: absolute;
          bottom: -8px;
          left: 0;
          width: 60px;
          height: 2px;
          background: linear-gradient(90deg, #001660FF, #ff6b35);
          border-radius: 1px;
        }
      }

      .summary-details {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 1.5rem;

        @media (max-width: 1200px) {
          grid-template-columns: repeat(2, 1fr);
        }

        @media (max-width: 768px) {
          grid-template-columns: 1fr;
        }

        .summary-item {
          display: flex;
          align-items: center;
          padding: 1.25rem;
          background: rgba(248, 250, 252, 0.8);
          backdrop-filter: blur(10px);
          border-radius: 16px;
          border: 1px solid rgba(226, 232, 240, 0.6);
          transition: all 0.3s ease;
          position: relative;
          overflow: hidden;

          &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: linear-gradient(180deg, #001660FF, #ff6b35);
            border-radius: 0 4px 4px 0;
          }

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 22, 96, 0.1);
            background: rgba(255, 255, 255, 0.9);
          }

          mat-icon {
            color: #001660FF;
            margin-right: 1rem;
            font-size: 1.5rem;
            padding: 0.5rem;
            background: rgba(0, 22, 96, 0.1);
            border-radius: 12px;
            transition: all 0.3s ease;
          }

          span {
            font-weight: 600;
            color: #1e293b;
            font-size: 0.95rem;
          }

          // Animation d'apparition séquentielle
          &:nth-child(1) { animation: fadeInScale 0.5s ease-out 0.1s both; }
          &:nth-child(2) { animation: fadeInScale 0.5s ease-out 0.2s both; }
          &:nth-child(3) { animation: fadeInScale 0.5s ease-out 0.3s both; }
          &:nth-child(4) { animation: fadeInScale 0.5s ease-out 0.4s both; }
        }
      }
    }
  }

  // Boutons d'action modernes
  .action-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 1rem;
    }

    .secondary-button {
      padding: 0.75rem 1.5rem;
      border: 1px solid #e2e8f0;
      color: #64748b;
      background: white;
      font-weight: 500;
      font-size: 0.9rem;
      border-radius: 12px;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;

      &:hover {
        border-color: #001660FF;
        color: #001660FF;
        background: #f8fafc;
      }

      mat-icon {
        margin-right: 0.5rem;
        font-size: 1.1rem;
      }

      @media (max-width: 768px) {
        width: 100%;
        justify-content: center;
      }
    }

    .primary-button {
      padding: 0.75rem 2rem;
      background: linear-gradient(135deg, #001660FF 0%, #ff6b35 100%);
      color: white !important;
      font-weight: 600;
      font-size: 0.9rem;
      border: none;
      border-radius: 12px;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      box-shadow: 0 2px 8px rgba(0, 22, 96, 0.2);

      span {
        color: white !important;
      }

      &:hover:not(:disabled) {
        background: linear-gradient(135deg, #ff6b35 0%, #001660FF 100%);
        box-shadow: 0 4px 16px rgba(0, 22, 96, 0.3);
        transform: translateY(-1px);

        span {
          color: white !important;
        }
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        background: #cbd5e1;
        box-shadow: none;
        transform: none;

        span {
          color: white !important;
        }
      }

      mat-icon {
        margin-left: 0.5rem;
        font-size: 1.1rem;
        color: white !important;
      }

      @media (max-width: 768px) {
        width: 100%;
        justify-content: center;
      }
    }
  }
}

// Animations sophistiquées et professionnelles
@keyframes pulse {
  0%, 100% {
    opacity: 0.4;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 0.8;
    transform: translate(-50%, -50%) scale(1.15);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateX(0px);
  }
  33% {
    transform: translateX(10px);
  }
  66% {
    transform: translateX(-5px);
  }
}

@keyframes cardPulse {
  0%, 100% {
    box-shadow:
      0 8px 32px rgba(0, 22, 96, 0.06),
      0 2px 8px rgba(0, 0, 0, 0.02);
  }
  50% {
    box-shadow:
      0 12px 40px rgba(0, 22, 96, 0.1),
      0 4px 16px rgba(255, 107, 53, 0.05);
  }
}

@keyframes progressGlow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(0, 22, 96, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(255, 107, 53, 0.5);
  }
}

@keyframes badgePulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.9;
  }
}

@keyframes progressFlow {
  0% {
    width: 0%;
  }
  50% {
    width: 80%;
  }
  100% {
    width: 60%;
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

@keyframes gradientShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

// Styles pour les snackbars
:host ::ng-deep {
  .success-snackbar {
    background-color: #4caf50 !important;
    color: white !important;
  }

  .error-snackbar {
    background-color: #f44336 !important;
    color: white !important;
  }

  .warning-snackbar {
    background-color: #ff9800 !important;
    color: white !important;
  }

  .mat-mdc-snack-bar-container {
    border-radius: 12px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .test-settings-container {
    padding: 1rem;

    .settings-grid {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }

    .action-section {
      flex-direction: column;
      gap: 1rem;

      button {
        width: 100%;
      }
    }
  }
}
