<div class="workspaces-wrapper">
  <div class="workspaces-header">
    <h1 class="workspaces-title">Workspaces</h1>
    <mat-form-field class="search-bar">
      <mat-label class="mat-lab">Rechercher</mat-label>
      <input matInput [(ngModel)]="searchTerm" (input)="filterWorkspaces()" placeholder="Rechercher un workspace..." class="mat-inputt"/>
      <mat-icon matSuffix >search</mat-icon>
    </mat-form-field>
  </div>

  <mat-spinner *ngIf="loading" class="spinner" diameter="50"></mat-spinner>

  <div *ngIf="!loading" class="workspaces-container">
    <mat-card *ngFor="let workspace of paginatedWorkspaces" class="workspace-card">
      <div class="workspace-header">
        <!-- Display logo if available -->
        <img *ngIf="workspace.logo" [src]="workspace.logo" class="workspace-logo" alt="Workspace Logo" />
        <!-- Fallback to icon if no logo -->
        <mat-icon *ngIf="!workspace.logo" class="workspace-icon">groups</mat-icon>
        <span class="workspace-name">{{ workspace.name }}</span>
      </div>
      <mat-card-content class="workspace-content">
        <div class="workspace-details">
          <p matLine class="workspace-detail">
            <mat-icon class="detail-icon">location_on</mat-icon>
            <span>{{ workspace.location || 'N/A' }}</span>
          </p>
          <p matLine class="workspace-detail">
            <mat-icon class="detail-icon">call</mat-icon>
            <span>{{ workspace.phoneNumber || 'N/A' }}</span>
          </p>
          <p matLine class="workspace-detail">
            <mat-icon class="detail-icon">mail</mat-icon>
            <span>{{ workspace.email || 'N/A' }}</span>
          </p>
        </div>

        <p matLine class="workspace-description">{{ workspace.description }}</p>
      </mat-card-content>
    </mat-card>

    <div *ngIf="paginatedWorkspaces.length === 0" class="no-workspaces-card">
      <div class="no-workspaces-header">
        <mat-icon class="no-workspaces-icon">groups</mat-icon>
        <span class="no-workspaces-text">Aucun workspace trouvé</span>
      </div>
    </div>
  </div>

  <mat-paginator
    *ngIf="!loading && filteredWorkspaces.length > 0"
    class="workspaces-paginator"
    [length]="filteredWorkspaces.length"
    [pageSize]="pageSize"
    [pageSizeOptions]="[4]"
    (page)="onPageChange($event)"
    showFirstLastButtons
  ></mat-paginator>
</div>
