// src/app/protected/recruiter-profile/recruiter-profile.component.ts
import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import {MatDialog, MatDialogClose} from '@angular/material/dialog';
import { HttpErrorResponse } from '@angular/common/http';
import { AddJobPostingModalComponent } from '../add-job-posting-modal/add-job-posting-modal.component';
import { EditRecruiterProfileComponent } from '../edit-recruiter-profile/edit-recruiter-profile.component';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatGridListModule } from '@angular/material/grid-list';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { UserService } from '../../services/user/user.service';
import { PostsService } from '../../services/posts/posts.service';
import { PostsComponent } from '../posts/posts.component';
import { AvatarService } from '../../services/avatar/avatar.service';
import { trigger, state, style, animate, transition } from '@angular/animations';
import { forkJoin, of } from 'rxjs';
import { catchError, switchMap } from 'rxjs/operators';

@Component({
  selector: 'app-recruiter-profile',
  templateUrl: './recruiter-profile.component.html',
  styleUrls: ['./recruiter-profile.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatGridListModule,
    MatIconModule,
    MatButtonModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatTooltipModule,
    MatDialogClose
  ],
  animations: [
    trigger('fadeInOut', [
      transition(':enter', [
        style({ opacity: 0 }),
        animate('300ms ease-in', style({ opacity: 1 }))
      ]),
      transition(':leave', [
        animate('300ms ease-out', style({ opacity: 0 }))
      ])
    ]),
    trigger('slideUpDown', [
      transition(':enter', [
        style({ transform: 'translateY(20px)', opacity: 0 }),
        animate('300ms ease-out', style({ transform: 'translateY(0)', opacity: 1 }))
      ]),
      transition(':leave', [
        animate('300ms ease-in', style({ transform: 'translateY(20px)', opacity: 0 }))
      ])
    ])
  ]
})
export class RecruiterProfileComponent implements OnInit {
  // Templates pour les boîtes de dialogue
  @ViewChild('contactDialog') contactDialog!: TemplateRef<any>;
  @ViewChild('shareDialog') shareDialog!: TemplateRef<any>;

  // Modèle pour le formulaire de contact
  contactForm = {
    name: '',
    email: '',
    subject: '',
    message: ''
  };

  // Plateformes de partage social
  socialPlatforms = [
    { name: 'LinkedIn', icon: 'linkedin', color: '#0077B5', url: '' },
    { name: 'Twitter', icon: 'twitter', color: '#1DA1F2', url: '' },
    { name: 'Facebook', icon: 'facebook', color: '#4267B2', url: '' },
    { name: 'WhatsApp', icon: 'whatsapp', color: '#25D366', url: '' },
    { name: 'Email', icon: 'email', color: '#D44638', url: '' }
  ];
  recruiter: any = null;
  recruiterProfile: any = { jobPostings: [] }; // Initialize with empty jobPostings array
  company: any = null;
  companyLogoUrl: string | null = null;
  user: any = null;
  userProfile: any = null;
  workspace: any = null;
  isLoading = true;
  avatarUrl: string | ArrayBuffer | null = null;
  isUploadingPhoto: boolean = false;
  isUploadingLogo: boolean = false;
  photoUploadError: string | null = null;
  uploadError: string | null = null;

  // Avatar par défaut en SVG (cercle orange avec silhouette blanche)
  defaultAvatarUrl: string = 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48Y2lyY2xlIGN4PSI1MCIgY3k9IjUwIiByPSI1MCIgZmlsbD0iI0ZGOTUwMCIvPjxwYXRoIGQ9Ik01MCwyMCBDNTguMjg0MjcxLDIwIDY1LDI2LjcxNTcyODggNjUsMzUgQzY1LDQzLjI4NDI3MTIgNTguMjg0MjcxLDUwIDUwLDUwIEM0MS43MTU3Mjg4LDUwIDM1LDQzLjI4NDI3MTIgMzUsMzUgQzM1LDI2LjcxNTcyODggNDEuNzE1NzI4OCwyMCA1MCwyMCBaIE01MCw1NSBDNjYuNTY4NTQyNSw1NSA4MCw2MS43MTU3Mjg4IDgwLDcwIEw4MCw4MCBMMjAsODAgTDIwLDcwIEMyMCw2MS43MTU3Mjg4IDMzLjQzMTQ1NzUsNTUgNTAsNTUgWiIgZmlsbD0iI0ZGRkZGRiIvPjwvc3ZnPg==';
  post: any;
  isEditingBio: boolean = false;
  tempBio: string = '';
  isSavingBio: boolean = false;
  bioError: string | null = null;
  bioSaved: boolean = false;

  constructor(
    private userService: UserService,
    private postsService: PostsService,
    private dialog: MatDialog,
    private avatarService: AvatarService
  ) {}

  /**
   * Gère les erreurs de chargement d'image et affiche l'avatar par défaut
   */
  onImageError(event: any): void {
    console.log('Erreur de chargement de l\'image, utilisation de l\'avatar par défaut');
    event.target.src = this.defaultAvatarUrl;
  }

  ngOnInit() {
    this.loadUser();

    // Initialiser les URLs des plateformes sociales
    this.initSocialPlatformUrls();
  }

  /**
   * Initialise les URLs des plateformes sociales pour le partage
   */
  initSocialPlatformUrls() {
    const baseUrl = encodeURIComponent(window.location.origin + '/recruiter/' + (this.user?.id || 'profile'));
    const title = encodeURIComponent(`Profil de ${this.user?.firstName || ''} ${this.user?.lastName || ''} sur Kairos IT`);
    const description = encodeURIComponent(`Consultez le profil de ${this.user?.firstName || ''} ${this.user?.lastName || ''}, ${this.userProfile?.title || 'Recruteur'} chez ${this.workspace?.name || 'Entreprise'}`);

    // Mettre à jour les URLs des plateformes sociales
    this.socialPlatforms = [
      {
        name: 'LinkedIn',
        icon: 'linkedin',
        color: '#0077B5',
        url: `https://www.linkedin.com/sharing/share-offsite/?url=${baseUrl}`
      },
      {
        name: 'Twitter',
        icon: 'twitter',
        color: '#1DA1F2',
        url: `https://twitter.com/intent/tweet?url=${baseUrl}&text=${title}`
      },
      {
        name: 'Facebook',
        icon: 'facebook',
        color: '#4267B2',
        url: `https://www.facebook.com/sharer/sharer.php?u=${baseUrl}`
      },
      {
        name: 'WhatsApp',
        icon: 'whatsapp',
        color: '#25D366',
        url: `https://wa.me/?text=${title}%20${baseUrl}`
      },
      {
        name: 'Email',
        icon: 'email',
        color: '#D44638',
        url: `mailto:?subject=${title}&body=${description}%0A%0A${baseUrl}`
      }
    ];
  }

  loadUser(): void {
    this.isLoading = true;

    // Récupérer le profil utilisateur
    this.userService.getUserProfile().pipe(
      switchMap((data: any) => {
        if (data) {
          this.user = data.user || {};
          this.userProfile = data.userProfile || {};
          this.workspace = data.workspace || {};
          this.company = data.workspace || {}; // Utiliser les données du workspace comme données de l'entreprise
          this.recruiterProfile.bio = this.userProfile.bio || '';
          this.tempBio = this.userProfile.bio || '';

          // Récupérer les photos et logos en utilisant le service AvatarService
          // Utiliser l'avatar par défaut si aucune photo n'est définie
          this.avatarUrl = this.avatarService.getAvatarUrl(this.userProfile?.photoUrl || null);
          console.log('Photo URL (recruiter):', this.avatarUrl);

          // Récupérer le logo de l'entreprise s'il existe
          if (this.workspace && this.workspace.logoUrl) {
            // Vérifier si l'URL est complète ou relative
            if (this.workspace.logoUrl.startsWith('http')) {
              this.companyLogoUrl = this.workspace.logoUrl;
            } else if (this.workspace.logoUrl.startsWith('data:image')) {
              // Si c'est une URL de données (data URL), l'utiliser directement
              this.companyLogoUrl = this.workspace.logoUrl;
            } else {
              // Utiliser le service AvatarService pour construire l'URL du logo
              const logoUrl = this.workspace.logoUrl;
              // Ajouter un timestamp pour éviter la mise en cache du navigateur
              this.companyLogoUrl = `${this.avatarService.getAvatarUrl(logoUrl)}?t=${new Date().getTime()}`;
            }
            console.log('Logo URL chargée:', this.companyLogoUrl);
          } else {
            this.companyLogoUrl = null;
            console.log('Aucun logo d\'entreprise trouvé');
          }

          // Récupérer les offres d'emploi du recruteur connecté
          console.log('Récupération des offres d\'emploi du recruteur...');
          return this.postsService.getMyPosts(0, 100).pipe(
            catchError(error => {
              console.error('Erreur lors de la récupération des offres d\'emploi:', error);
              return of({ content: [], totalElements: 0 });
            })
          );
        } else {
          this.user = {};
          this.workspace = {};
          this.company = {};
          return of({ content: [], totalElements: 0 });
        }
      })
    ).subscribe(
      (postsData: any) => {
        console.log('Offres d\'emploi récupérées:', postsData);

        // Transformer les données des posts pour correspondre au format attendu
        if (postsData && postsData.content) {
          this.recruiterProfile.jobPostings = postsData.content.map((post: any) => ({
            id: post.id,
            title: post.titre,
            description: post.description,
            company: post.entreprise,
            location: post.entreprise, // Utiliser l'entreprise comme localisation si non spécifiée
            postedDate: post.datePublication,
            status: post.archived ? 'closed' : 'active',
            tags: [post.contractType, post.profileRequest]
          }));
        } else {
          this.recruiterProfile.jobPostings = [];
        }

        // Mettre à jour les statistiques
        this.recruiterProfile.hiresMade = this.userProfile.hiresMade || 0;
        this.recruiterProfile.activeJobs = this.recruiterProfile.jobPostings.filter(
          (job: any) => job.status === 'active' || !job.status
        ).length;
        this.recruiterProfile.viewsCount = this.userProfile.viewsCount || 0;

        console.log('Current job postings count:', this.recruiterProfile.jobPostings.length);
        console.log('Active jobs count:', this.recruiterProfile.activeJobs);

        // Mettre à jour les URLs des plateformes sociales avec les données utilisateur chargées
        this.initSocialPlatformUrls();

        this.isLoading = false;
      },
      (error) => {
        console.error('Erreur lors de la récupération des données:', error);
        this.user = {};
        this.workspace = {};
        this.company = {};
        this.recruiterProfile.jobPostings = [];
        this.isLoading = false;
      }
    );
  }

  openFileInput(type: 'photo' | 'logo') {
    const inputId = type === 'photo' ? 'photoInput' : 'logoInput';
    const input = document.getElementById(inputId) as HTMLInputElement;
    if (input) {
      input.click();
    }
  }

  onFileSelected(event: Event, type: 'photo' | 'logo') {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files[0]) {
      const file = input.files[0];
      const allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
      if (!allowedTypes.includes(file.type)) {
        this.uploadError = 'Please select an image (JPEG, PNG, GIF).';
        return;
      }
      if (file.size > 2 * 1024 * 1024) {
        this.uploadError = 'File is too large. Maximum size is 2 MB.';
        return;
      }
      this.uploadError = null;
      if (type === 'photo') {
        this.uploadPhoto(file);
      } else {
        this.uploadCompanyLogo(file);
      }
    }
  }

  uploadPhoto(file: File) {
    this.isUploadingPhoto = true;
    this.userService.uploadPhoto(file).subscribe(
      (response: any) => {
        console.log('Photo téléchargée avec succès:', response);
        // Utiliser le service AvatarService pour obtenir l'URL correcte
        this.avatarUrl = this.avatarService.getAvatarUrl(response.photoUrl);
        console.log('Nouvelle photo URL (recruiter):', this.avatarUrl);
        this.isUploadingPhoto = false;
        this.loadUser();
      },
      (error: HttpErrorResponse) => {
        console.error('Erreur lors de l\'upload de la photo:', error);
        this.isUploadingPhoto = false;

        // Essayer d'extraire un message d'erreur détaillé de la réponse
        let errorMessage = 'Une erreur est survenue lors de l\'upload de la photo.';

        if (error.error && error.error.error) {
          // Si le backend renvoie un objet avec une propriété 'error'
          errorMessage = error.error.error;
        } else if (typeof error.error === 'string') {
          // Si le backend renvoie une chaîne de caractères
          errorMessage = error.error;
        } else if (error.message) {
          // Si l'erreur a une propriété 'message'
          errorMessage = error.message;
        }

        this.uploadError = errorMessage;
        this.isUploadingPhoto = false;
      }
    );
  }

  uploadCompanyLogo(file: File) {
    this.isUploadingLogo = true;
    this.uploadError = null;

    // Vérifier si l'ID du workspace est disponible
    if (!this.workspace || !this.workspace.id) {
      console.error('ID du workspace non disponible');
      this.uploadError = 'Impossible de télécharger le logo : ID du workspace non disponible.';
      this.isUploadingLogo = false;
      return;
    }

    console.log('Téléchargement du logo pour le workspace ID:', this.workspace.id);

    // Créer une URL temporaire pour le fichier sélectionné (pour l'affichage immédiat)
    const reader = new FileReader();
    reader.onload = (event: any) => {
      // Afficher le logo immédiatement à partir du fichier local
      this.companyLogoUrl = event.target.result;
      console.log('URL temporaire du logo créée:', this.companyLogoUrl);
    };
    reader.readAsDataURL(file);

    // Envoyer le fichier au serveur
    this.userService.uploadWorkspaceLogo(file, this.workspace.id).subscribe(
      (response: any) => {
        console.log('Logo téléchargé avec succès:', response);

        // Vérifier si la réponse contient une URL de logo
        if (response && response.logoUrl) {
          // Construire l'URL complète du logo
          let finalLogoUrl;
          if (response.logoUrl.startsWith('http')) {
            finalLogoUrl = response.logoUrl;
          } else {
            // Utiliser le service AvatarService pour construire l'URL du logo
            finalLogoUrl = this.avatarService.getAvatarUrl(response.logoUrl);
          }

          console.log('URL finale du logo:', finalLogoUrl);

          // Mettre à jour l'URL du logo dans le workspace
          this.workspace.logoUrl = response.logoUrl;

          // Forcer le rafraîchissement de l'image en ajoutant un timestamp
          this.companyLogoUrl = `${finalLogoUrl}?t=${new Date().getTime()}`;
        } else {
          console.error('La réponse ne contient pas d\'URL de logo');
        }

        this.isUploadingLogo = false;
      },
      (error: HttpErrorResponse) => {
        console.error('Erreur lors de l\'upload du logo:', error);
        this.uploadError = 'Une erreur est survenue lors de l\'upload du logo.';
        this.isUploadingLogo = false;
      }
    );
  }

  addJobPosting() {
    const dialogRef = this.dialog.open(PostsComponent, {
      width: '600px',
      data: {
        title: '',
        company: this.company?.name || '',
        location: this.workspace?.location || '',
        postedDate: new Date()
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        console.log('New job posting added:', result);

        // Après avoir ajouté une offre via le composant Posts, recharger les offres
        // Le composant Posts s'occupe déjà de sauvegarder l'offre via le PostsService
        this.isLoading = true;

        // Attendre un peu pour laisser le temps au backend de traiter la nouvelle offre
        setTimeout(() => {
          this.loadUser();
        }, 1000);
      }
    });
  }
  /**
   * Ouvre la boîte de dialogue pour modifier le profil
   */
  editProfile() {
    // Afficher un indicateur de chargement
    const loadingIndicator = document.createElement('div');
    loadingIndicator.className = 'loading-overlay';
    loadingIndicator.innerHTML = `
      <div class="loading-spinner"></div>
      <p>Chargement de l'éditeur de profil...</p>
    `;
    document.body.appendChild(loadingIndicator);

    // Ouvrir la boîte de dialogue après un court délai pour montrer l'animation
    setTimeout(() => {
      // Supprimer l'indicateur de chargement
      document.body.removeChild(loadingIndicator);

      // Ouvrir la boîte de dialogue
      const dialogRef = this.dialog.open(EditRecruiterProfileComponent, {
        width: '800px', // Plus large pour un meilleur confort
        panelClass: 'custom-dialog-container',
        data: { recruiter: this.recruiter, recruiterProfile: this.recruiterProfile }
      });

      dialogRef.afterClosed().subscribe((result) => {
        if (result === 'updated') {
          console.log('Profil mis à jour avec succès');

          // Afficher un message de succès
          this.showNotification('Profil mis à jour avec succès!', 'success');

          // Recharger les données utilisateur
          this.loadUser();
        }
      });
    }, 300);
  }

  /**
   * Ouvre la boîte de dialogue pour contacter le recruteur
   */
  contactRecruiter() {
    // Créer une boîte de dialogue pour le contact
    const dialogRef = this.dialog.open(this.contactDialog, {
      width: '500px',
      panelClass: 'contact-dialog-container'
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        // Simuler l'envoi d'un message
        this.showNotification('Message envoyé avec succès!', 'success');
      }
    });
  }

  /**
   * Partage le profil du recruteur
   */
  shareProfile() {
    // Générer l'URL du profil
    const profileUrl = `${window.location.origin}/recruiter/${this.user?.id || 'profile'}`;

    // Vérifier si l'API Web Share est disponible
    if (navigator.share) {
      navigator.share({
        title: `Profil de ${this.user?.firstName || ''} ${this.user?.lastName || ''}`,
        text: `Consultez le profil de ${this.user?.firstName || ''} ${this.user?.lastName || ''} sur Kairos IT`,
        url: profileUrl
      })
      .then(() => this.showNotification('Profil partagé avec succès!', 'success'))
      .catch(error => console.error('Erreur lors du partage:', error));
    } else {
      // Fallback pour les navigateurs qui ne supportent pas l'API Web Share
      // Copier l'URL dans le presse-papier
      this.copyToClipboard(profileUrl);
      this.showNotification('URL du profil copiée dans le presse-papier!', 'info');

      // Ouvrir la boîte de dialogue de partage
      const dialogRef = this.dialog.open(this.shareDialog, {
        width: '500px',
        data: { profileUrl }
      });
    }
  }

  /**
   * Copie le texte dans le presse-papier
   */
  copyToClipboard(text: string) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    document.body.appendChild(textArea);
    textArea.select();
    document.execCommand('copy');
    document.body.removeChild(textArea);
  }

  /**
   * Affiche une notification à l'utilisateur
   */
  showNotification(message: string, type: 'success' | 'error' | 'info' = 'info') {
    // Créer l'élément de notification
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;

    // Ajouter l'icône appropriée
    let icon = 'info';
    if (type === 'success') icon = 'check_circle';
    if (type === 'error') icon = 'error';

    notification.innerHTML = `
      <div class="notification-icon">
        <span class="material-icons">${icon}</span>
      </div>
      <div class="notification-content">
        <p>${message}</p>
      </div>
      <div class="notification-close">
        <span class="material-icons">close</span>
      </div>
    `;

    // Ajouter la notification au DOM
    document.body.appendChild(notification);

    // Ajouter l'événement de fermeture
    const closeBtn = notification.querySelector('.notification-close');
    if (closeBtn) {
      closeBtn.addEventListener('click', () => {
        notification.classList.add('notification-hiding');
        setTimeout(() => {
          document.body.removeChild(notification);
        }, 300);
      });
    }

    // Afficher la notification avec animation
    setTimeout(() => {
      notification.classList.add('notification-visible');
    }, 10);

    // Fermer automatiquement après 5 secondes
    setTimeout(() => {
      if (document.body.contains(notification)) {
        notification.classList.add('notification-hiding');
        setTimeout(() => {
          if (document.body.contains(notification)) {
            document.body.removeChild(notification);
          }
        }, 300);
      }
    }, 5000);
  }

  editJob(job: any) {
    // Préparer les données pour le composant Posts
    const postData = {
      id: job.id,
      jobTitle: job.title,
      description: job.description,
      location: job.location || job.company,
      company: job.company,
      postedDate: job.postedDate,
      profileRequest: job.tags && job.tags.length > 0 ? job.tags[1] : '',
      contractType: job.tags && job.tags.length > 0 ? job.tags[0] : ''
    };

    const dialogRef = this.dialog.open(PostsComponent, {
      width: '600px',
      data: postData
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        console.log('Job posting updated:', result);

        // Après avoir modifié une offre via le composant Posts, recharger les offres
        this.isLoading = true;

        // Attendre un peu pour laisser le temps au backend de traiter la modification
        setTimeout(() => {
          this.loadUser();
        }, 1000);
      }
    });
  }

  deleteJob(job: any) {
    if (confirm('Êtes-vous sûr de vouloir supprimer cette offre d\'emploi ?')) {
      this.isLoading = true;

      // Appeler le service pour supprimer l'offre
      this.postsService.deletePost(job.id).subscribe(
        () => {
          console.log('Job posting deleted successfully');

          // Mettre à jour l'affichage après la suppression
          this.loadUser();
        },
        (error) => {
          console.error('Error deleting job posting:', error);
          this.isLoading = false;

          // En cas d'erreur, supprimer quand même l'offre localement pour l'expérience utilisateur
          const index = this.recruiterProfile.jobPostings.findIndex((j: any) => j.id === job.id);
          if (index !== -1) {
            this.recruiterProfile.jobPostings.splice(index, 1);
            this.recruiterProfile.activeJobs = this.recruiterProfile.jobPostings.filter(
              (j: any) => j.status === 'active' || !j.status
            ).length;
          }
        }
      );
    }
  }

  toggleEditBio(): void {
    this.isEditingBio = !this.isEditingBio;
    if (this.isEditingBio) {
      this.tempBio = this.recruiterProfile.bio || '';
    }
  }

  saveBio(): void {
    this.isSavingBio = true;
    this.bioError = null;

    console.log('Sauvegarde de la bio:', this.tempBio);

    // Utiliser directement la méthode alternative qui est plus simple et plus fiable
    this.directBioUpdate();
  }

  // Méthode directe pour mettre à jour la bio
  directBioUpdate(): void {
    console.log('Mise à jour directe de la bio');

    // Utiliser une API simple pour mettre à jour uniquement la bio
    const bioUpdateData = {
      bio: this.tempBio
    };

    // Mettre à jour les données locales immédiatement pour une meilleure expérience utilisateur
    this.recruiterProfile.bio = this.tempBio;
    if (this.userProfile) {
      this.userProfile.bio = this.tempBio;
    }

    // Appel direct à l'API pour mettre à jour la bio
    this.userService.updateBio(this.tempBio).subscribe(
      (response) => {
        console.log('Bio mise à jour avec succès:', response);

        // Afficher le message de succès
        this.bioSaved = true;
        setTimeout(() => {
          this.bioSaved = false;
          // Fermer le mode édition après avoir affiché le message de succès
          this.isEditingBio = false;
        }, 2000);

        this.isSavingBio = false;
        this.bioError = null;
      },
      (error) => {
        console.error('Erreur lors de la mise à jour de la bio:', error);
        this.bioError = 'Une erreur est survenue lors de la mise à jour de la bio. Veuillez réessayer.';
        this.isSavingBio = false;

        // Essayer une méthode alternative en cas d'échec
        this.fallbackBioUpdate();
      }
    );
  }

  // Méthode de secours pour mettre à jour la bio
  fallbackBioUpdate(): void {
    console.log('Tentative de mise à jour de secours pour la bio');

    // Utiliser l'API générale pour mettre à jour le profil
    if (!this.userProfile) {
      console.error('Profil utilisateur non disponible pour la mise à jour de secours');
      return;
    }

    const updatedProfile = {
      ...this.userProfile,
      bio: this.tempBio
    };

    this.userService.updateFullUserProfile(updatedProfile).subscribe(
      (response) => {
        console.log('Bio mise à jour avec succès (méthode de secours):', response);

        // Afficher le message de succès
        this.bioSaved = true;
        setTimeout(() => {
          this.bioSaved = false;
          // Fermer le mode édition après avoir affiché le message de succès
          this.isEditingBio = false;
        }, 2000);

        this.isSavingBio = false;
        this.bioError = null;
      },
      (error) => {
        console.error('Erreur lors de la mise à jour de secours de la bio:', error);
        // Garder l'erreur affichée
      }
    );
  }

  cancelEditBio(): void {
    this.isEditingBio = false;
    this.tempBio = this.recruiterProfile.bio || '';
    this.bioError = null;
  }

  /**
   * Obtient les initiales de l'utilisateur pour l'avatar par défaut
   */
  getInitials(): string {
    if (!this.user) return 'R';

    const firstName = this.user.firstName || '';
    const lastName = this.user.lastName || '';

    if (!firstName && !lastName) return 'R';

    return (firstName.charAt(0) + (lastName ? lastName.charAt(0) : '')).toUpperCase();
  }

  /**
   * Fait défiler jusqu'à la section bio
   */
  scrollToBio(): void {
    const bioElement = document.querySelector('.bio-card');
    if (bioElement) {
      bioElement.scrollIntoView({ behavior: 'smooth', block: 'center' });

      // Ajouter une classe pour mettre en évidence la carte bio
      bioElement.classList.add('highlight-card');

      // Retirer la classe après l'animation
      setTimeout(() => {
        bioElement.classList.remove('highlight-card');
      }, 2000);
    }
  }
}
