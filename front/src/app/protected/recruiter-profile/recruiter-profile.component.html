<!-- src/app/protected/recruiter-profile/recruiter-profile.component.html -->
<div class="profile-container">
  <!-- Loader -->
  <div *ngIf="isLoading" class="loader-container">
    <div class="loader"></div>
    <p>Chargement du profil...</p>
  </div>

  <!-- Profile Header - Design premium et attrayant -->
  <header class="profile-header" *ngIf="!isLoading">
    <div class="header-background">
      <div class="header-overlay"></div>
      <div class="header-pattern"></div>
      <div class="header-accent"></div>
    </div>

    <div class="profile-info">
      <!-- Avatar avec badge -->
      <div class="avatar-section">
        <div class="avatar-container">
          <div class="avatar-badge">
            <span>Recruteur</span>
            <div class="badge-dot"></div>
          </div>
          <label for="photoInput">
            <div class="avatar-wrapper">
              <img [src]="avatarUrl" class="avatar-icon" alt="Photo de profil" (error)="onImageError($event)" />
              <div class="avatar-overlay">
                <mat-icon>camera_alt</mat-icon>
              </div>
            </div>
            <input type="file" id="photoInput" (change)="onFileSelected($event, 'photo')" hidden>
          </label>
          <div class="upload-status" *ngIf="isUploadingPhoto">
            <div class="upload-progress"></div>
            <span>Téléchargement...</span>
          </div>
        </div>
      </div>

      <!-- Informations principales -->
      <div class="user-info-container">
        <!-- Nom et titre -->
        <div class="user-identity">
          <div class="user-name-section">
            <h1>{{ user?.firstName || 'Prénom' }} {{ user?.lastName || 'Nom' }}</h1>
            <div class="user-status online" title="En ligne"></div>
          </div>
          <p class="title">{{ userProfile?.title || 'Recruteur' }}</p>
        </div>

        <!-- Badges d'entreprise et d'expérience -->
        <div class="badges-container">
          <span class="company-badge">
            <mat-icon>business</mat-icon>
            {{ workspace?.name || 'Entreprise' }}
          </span>
          <span class="experience-badge">
            <mat-icon>people</mat-icon>
            {{ recruiterProfile?.hiresMade || 0 }} embauches
          </span>
          <span class="location-badge" *ngIf="workspace?.location">
            <mat-icon>location_on</mat-icon>
            {{ workspace?.location }}
          </span>
        </div>
      </div>

      <!-- Bio et actions -->
      <div class="bio-actions-container">
        <!-- Bio courte -->
        <div class="bio-short" *ngIf="recruiterProfile?.bio">
          <p>{{ (recruiterProfile.bio.length > 60) ? (recruiterProfile.bio | slice:0:60) + '...' : recruiterProfile.bio }}</p>
          <button mat-button class="view-more-btn" (click)="scrollToBio()">
            <span>Plus</span>
            <mat-icon>arrow_downward</mat-icon>
          </button>
        </div>

        <!-- Actions -->
        <div class="actions">
          <button mat-fab color="primary" class="edit-profile-btn" (click)="editProfile()" matTooltip="Modifier le profil">
            <mat-icon>edit</mat-icon>
          </button>
          <button mat-fab class="contact-btn" (click)="contactRecruiter()" matTooltip="Contacter">
            <mat-icon>message</mat-icon>
          </button>
          <button mat-fab class="share-btn" (click)="shareProfile()" matTooltip="Partager le profil">
            <mat-icon>share</mat-icon>
          </button>
        </div>
      </div>
    </div>
  </header>

  <!-- Main Content -->
  <main class="profile-content" *ngIf="!isLoading">
    <!-- Left Column -->
    <aside class="left-column">
      <!-- Personal Information -->
      <mat-card class="info-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon class="card-icon">person</mat-icon> Informations personnelles
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <p><mat-icon>email</mat-icon> <strong>Email:</strong> {{ user?.email || 'Non spécifié' }}</p>
          <p><mat-icon>phone</mat-icon> <strong>Téléphone:</strong> {{ user?.phoneNumber || 'Non spécifié' }}</p>
          <p><mat-icon>location_on</mat-icon> <strong>Adresse:</strong> {{ user?.address || 'Non spécifié' }}</p>
        </mat-card-content>
      </mat-card>

      <!-- Company Overview -->
      <mat-card class="info-card company-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon class="card-icon">business</mat-icon> Entreprise
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="company-logo">
            <label for="logoInput">
              <div class="logo-wrapper">
                <!-- Utiliser une approche différente pour l'affichage du logo -->
                <ng-container *ngIf="companyLogoUrl; else defaultLogo">
                  <div class="logo-image" [style.background-image]="'url(' + companyLogoUrl + ')'">
                  </div>
                </ng-container>
                <ng-template #defaultLogo>
                  <mat-icon class="logo-icon">business</mat-icon>
                </ng-template>
                <div class="logo-overlay">
                  <mat-icon>add_photo_alternate</mat-icon>
                </div>
              </div>
              <input type="file" id="logoInput" (change)="onFileSelected($event, 'logo')" hidden>
            </label>
            <div class="upload-status" *ngIf="isUploadingLogo">
              <div class="upload-progress"></div>
              <span>Téléchargement...</span>
            </div>
            <button mat-stroked-button class="change-logo-btn" (click)="openFileInput('logo')" [disabled]="isUploadingLogo">
              <mat-icon>image</mat-icon> Changer le logo
            </button>
          </div>
          <p><mat-icon>badge</mat-icon> <strong>Société:</strong> {{ workspace?.name || 'Non spécifié' }}</p>
          <p><mat-icon>email</mat-icon> <strong>Email:</strong> {{ workspace?.email || 'Non spécifié' }}</p>
          <p><mat-icon>phone</mat-icon> <strong>Téléphone:</strong> {{ workspace?.phoneNumber || 'Non spécifié' }}</p>
          <p><mat-icon>location_on</mat-icon> <strong>Localisation:</strong> {{ workspace?.location || 'Non spécifié' }}</p>
        </mat-card-content>
      </mat-card>

      <!-- Bio -->
      <mat-card class="info-card bio-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon class="card-icon">info</mat-icon> À propos
            <span class="bio-indicator">Profil</span>
          </mat-card-title>
          <div class="card-actions" *ngIf="!isEditingBio">
            <button mat-icon-button (click)="toggleEditBio()">
              <mat-icon>edit</mat-icon>
            </button>
          </div>
        </mat-card-header>
        <mat-card-content>
          <!-- Mode affichage -->
          <div *ngIf="!isEditingBio" class="bio-content">
            <p *ngIf="recruiterProfile?.bio">{{ recruiterProfile.bio }}</p>
            <div *ngIf="!recruiterProfile?.bio" class="empty-bio">
              <mat-icon>info</mat-icon>
              <p>Ajoutez une bio pour décrire votre expérience et vos objectifs de recrutement.</p>
              <button mat-stroked-button color="primary" (click)="toggleEditBio()">
                <mat-icon>add</mat-icon> Ajouter ma bio
              </button>
            </div>
          </div>

          <!-- Mode édition -->
          <div *ngIf="isEditingBio" class="bio-edit">
            <textarea
              [(ngModel)]="tempBio"
              rows="5"
              placeholder="Décrivez votre expérience et vos objectifs de recrutement..."
              class="bio-textarea"
              [class.error-textarea]="bioError"
              maxlength="500"
            ></textarea>
            <div class="char-count" [class.error-count]="tempBio.length >= 490">
              {{ tempBio.length }}/500
            </div>

            <div class="error-message" *ngIf="bioError">
              <mat-icon>error</mat-icon>
              <span>{{ bioError }}</span>
            </div>

            <div class="bio-actions">
              <button mat-stroked-button (click)="cancelEditBio()" [disabled]="isSavingBio">
                Annuler
              </button>
              <button mat-raised-button color="primary" (click)="saveBio()" [disabled]="isSavingBio">
                <mat-icon *ngIf="isSavingBio" class="spinner">refresh</mat-icon>
                <span *ngIf="!isSavingBio">Enregistrer</span>
                <span *ngIf="isSavingBio">Enregistrement...</span>
              </button>
            </div>

            <!-- Indicateur de succès -->
            <div class="success-message" *ngIf="bioSaved">
              <mat-icon>check_circle</mat-icon>
              <span>Bio enregistrée avec succès!</span>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </aside>

    <!-- Right Column -->
    <section class="right-column">
      <!-- Recruitment Stats -->
      <mat-card class="info-card stats-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon class="card-icon">bar_chart</mat-icon> Statistiques de recrutement
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="stats-grid">
            <div class="stat-item">
              <h4>{{ recruiterProfile?.hiresMade || 0 }}</h4>
              <p>Embauches réalisées</p>
              <div class="stat-icon">
                <mat-icon>people</mat-icon>
              </div>
            </div>
            <div class="stat-item">
              <h4>{{ recruiterProfile?.activeJobs || 0 }}</h4>
              <p>Offres actives</p>
              <div class="stat-icon">
                <mat-icon>work</mat-icon>
              </div>
            </div>
            <div class="stat-item">
              <h4>{{ recruiterProfile?.viewsCount || 0 }}</h4>
              <p>Vues du profil</p>
              <div class="stat-icon">
                <mat-icon>visibility</mat-icon>
              </div>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Job Postings -->
      <mat-card class="info-card job-postings-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon class="card-icon">work</mat-icon> Offres d'emploi publiées
            <span class="job-count" [class.highlight-count]="recruiterProfile?.jobPostings?.length > 0">{{ recruiterProfile?.jobPostings?.length || 0 }}</span>
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <ng-container *ngIf="recruiterProfile?.jobPostings?.length > 0; else noJobs">
            <div class="job-list">
              <div *ngFor="let job of recruiterProfile.jobPostings" class="job-item">
                <div class="job-header">
                  <div class="job-info">
                    <h4 class="job-title">{{ job.title || job.jobTitle || 'Titre du poste' }}</h4>
                    <p class="job-subtitle">{{ workspace?.name || job.company || 'Entreprise' }} - {{ job.location || workspace?.location || 'Localisation' }}</p>
                    <p class="job-date">Publié le: {{ job.postedDate ? (job.postedDate | date:'mediumDate') : 'Date non spécifiée' }}</p>
                  </div>
                  <div class="job-actions">
                    <button mat-icon-button class="action-btn" (click)="editJob(job)" matTooltip="Modifier">
                      <mat-icon>edit</mat-icon>
                    </button>
                    <button mat-icon-button class="action-btn delete-btn" (click)="deleteJob(job)" matTooltip="Supprimer">
                      <mat-icon>delete</mat-icon>
                    </button>
                  </div>
                </div>
                <div class="job-details" *ngIf="job.description">
                  <p class="job-description">{{ job.description }}</p>
                </div>
                <div class="job-tags" *ngIf="job.tags && job.tags.length > 0">
                  <span class="job-tag" *ngFor="let tag of job.tags">{{ tag }}</span>
                </div>
                <div class="job-status" [ngClass]="{'status-active': job.status === 'active', 'status-closed': job.status === 'closed', 'status-draft': job.status === 'draft'}">
                  {{ job.status === 'active' ? 'Actif' : (job.status === 'closed' ? 'Fermé' : 'Brouillon') }}
                </div>
              </div>
            </div>
          </ng-container>
          <ng-template #noJobs>
            <div class="empty-message">
              <mat-icon>work_off</mat-icon>
              <p>Aucune offre d'emploi active pour le moment.</p>
              <p class="empty-subtitle">Créez votre première offre d'emploi pour commencer à recruter.</p>
            </div>
          </ng-template>
          <div class="action-row">
            <button mat-raised-button color="primary" class="add-btn" (click)="addJobPosting()">
              <mat-icon>add</mat-icon> Ajouter une offre
            </button>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Espace supplémentaire pour les offres d'emploi -->
      <div class="spacer-card"></div>
    </section>
  </main>
</div>

<!-- Template de dialogue pour contacter le recruteur -->
<ng-template #contactDialog>
  <div class="contact-dialog">
    <h2 class="dialog-title">Contacter {{ user?.firstName || '' }} {{ user?.lastName || '' }}</h2>

    <div class="dialog-content">
      <div class="form-group">
        <label for="name">Votre nom</label>
        <input type="text" id="name" [(ngModel)]="contactForm.name" placeholder="Entrez votre nom complet">
      </div>

      <div class="form-group">
        <label for="email">Votre email</label>
        <input type="email" id="email" [(ngModel)]="contactForm.email" placeholder="Entrez votre adresse email">
      </div>

      <div class="form-group">
        <label for="subject">Sujet</label>
        <input type="text" id="subject" [(ngModel)]="contactForm.subject" placeholder="Sujet de votre message">
      </div>

      <div class="form-group">
        <label for="message">Message</label>
        <textarea id="message" [(ngModel)]="contactForm.message" rows="5" placeholder="Votre message"></textarea>
      </div>
    </div>

    <div class="dialog-actions">
      <button mat-stroked-button mat-dialog-close>Annuler</button>
      <button mat-raised-button color="primary" [mat-dialog-close]="contactForm" [disabled]="!contactForm.name || !contactForm.email || !contactForm.message">
        <mat-icon>send</mat-icon> Envoyer
      </button>
    </div>
  </div>
</ng-template>

<!-- Template de dialogue pour partager le profil -->
<ng-template #shareDialog let-data>
  <div class="share-dialog">
    <h2 class="dialog-title">Partager ce profil</h2>

    <div class="dialog-content">
      <div class="profile-preview">
        <div class="preview-avatar">
          <img *ngIf="avatarUrl; else defaultPreviewAvatar" [src]="avatarUrl" alt="Photo de profil">
          <ng-template #defaultPreviewAvatar>
            <div class="default-avatar-preview">{{ getInitials() }}</div>
          </ng-template>
        </div>

        <div class="preview-info">
          <h3>{{ user?.firstName || '' }} {{ user?.lastName || '' }}</h3>
          <p>{{ userProfile?.title || 'Recruteur' }} chez {{ workspace?.name || 'Entreprise' }}</p>
        </div>
      </div>

      <div class="share-url">
        <input type="text" [value]="data?.profileUrl || ''" readonly>
        <button mat-icon-button (click)="copyToClipboard(data?.profileUrl || '')" matTooltip="Copier l'URL">
          <mat-icon>content_copy</mat-icon>
        </button>
      </div>

      <div class="share-platforms">
        <h4>Partager sur</h4>
        <div class="platform-buttons">
          <a *ngFor="let platform of socialPlatforms"
             [href]="platform.url + (data?.profileUrl || '')"
             target="_blank"
             class="platform-button"
             [style.background-color]="platform.color">
            <mat-icon>{{ platform.icon }}</mat-icon>
            <span>{{ platform.name }}</span>
          </a>
        </div>
      </div>
    </div>

    <div class="dialog-actions">
      <button mat-raised-button mat-dialog-close>Fermer</button>
    </div>
  </div>
</ng-template>
