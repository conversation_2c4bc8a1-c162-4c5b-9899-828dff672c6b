/* src/app/protected/recruiter-profile/recruiter-profile.component.css */
.profile-container {
  width: 100%;
  margin: 0;
  padding: 0;
  background-color: #f5f7fa;
  min-height: 100vh;
  font-family: 'Roboto', sans-serif;
  box-sizing: border-box;
  position: relative;
}

/* Loader */
.loader-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.9);
  z-index: 1000;
}

.loader {
  width: 50px;
  height: 50px;
  border: 5px solid #f3f3f3;
  border-top: 5px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Header - Design premium et attrayant */
.profile-header {
  background: linear-gradient(135deg, #0a192f 0%, #112a5e 100%);
  color: #fff;
  padding: 25px 30px 25px 20px; /* Padding ajusté pour décaler vers le coin */
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  position: relative;
  width: 100%;
  border-radius: 0;
  overflow: hidden;
  margin-bottom: 25px;
  border-bottom: 3px solid #ed8936; /* Bordure orange plus épaisse */
  height: 170px; /* Hauteur légèrement augmentée */
}

.header-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 0;
}

.header-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at top right, rgba(255, 152, 0, 0.15), transparent 60%);
  z-index: 1;
}

.header-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('https://www.transparenttextures.com/patterns/cubes.png');
  opacity: 0.05;
  z-index: 2;
}

.header-accent {
  position: absolute;
  top: 0;
  right: 0;
  width: 30%;
  height: 100%;
  background: linear-gradient(to left, rgba(237, 137, 54, 0.1), transparent);
  z-index: 3;
  clip-path: polygon(100% 0, 100% 100%, 0 100%, 30% 0);
}

.profile-info {
  display: flex;
  align-items: center;
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  position: relative;
  z-index: 10;
  height: 100%;
  padding-left: 10px; /* Ajout d'un padding à gauche */
}

/* Bio et actions container */
.bio-actions-container {
  display: flex;
  align-items: center;
  margin-left: auto;
  gap: 20px;
  height: 100%;
}

/* Avatar Section */
.avatar-section {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  position: relative;
  z-index: 10;
  margin-right: 30px; /* Marge droite augmentée */
  margin-left: -20px; /* Marge négative à gauche pour décaler vers le bord */
  margin-top: -10px; /* Décalage vers le haut */
}

.avatar-container {
  display: flex;
  align-items: center;
  position: relative;
  animation: fadeInUp 0.5s ease-out;
}

.avatar-badge {
  position: absolute;
  top: -10px;
  right: -10px;
  background: linear-gradient(135deg, #ff9800, #f57c00);
  color: white;
  font-size: 13px;
  font-weight: 600;
  padding: 5px 14px;
  border-radius: 20px;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.25);
  z-index: 20;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  animation: badgePulse 2s infinite;
  display: flex;
  align-items: center;
  gap: 6px;
}

.badge-dot {
  width: 8px;
  height: 8px;
  background-color: #fff;
  border-radius: 50%;
  animation: pulseDot 2s infinite;
}

@keyframes badgePulse {
  0% { box-shadow: 0 0 0 0 rgba(255, 152, 0, 0.7); }
  70% { box-shadow: 0 0 0 10px rgba(255, 152, 0, 0); }
  100% { box-shadow: 0 0 0 0 rgba(255, 152, 0, 0); }
}

@keyframes pulseDot {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.avatar-wrapper {
  width: 140px; /* Avatar encore plus grand */
  height: 140px; /* Avatar encore plus grand */
  border-radius: 50%;
  overflow: hidden;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 5px solid rgba(255, 255, 255, 0.9); /* Bordure plus épaisse */
  transition: all 0.3s ease;
  position: relative;
  cursor: pointer;
  z-index: 10;
  transform: translateX(-20px); /* Décalage vers la gauche */
}

.avatar-wrapper:hover {
  transform: scale(1.05) translateX(-20px);
  border-color: #ff9800;
}

.avatar-wrapper .avatar-icon {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.default-avatar {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #3b82f6, #1e40af);
  color: white;
  font-size: 60px;
  font-weight: 700;
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.avatar-wrapper:hover .avatar-overlay {
  opacity: 1;
}

.avatar-overlay mat-icon {
  color: white;
  font-size: 40px;
  animation: fadeInZoom 0.3s ease-out;
}

@keyframes fadeInZoom {
  from { opacity: 0; transform: scale(0.5); }
  to { opacity: 1; transform: scale(1); }
}

.upload-status {
  margin-top: 15px;
  width: 100%;
  text-align: center;
  font-size: 14px;
  color: #fff;
}

.upload-progress {
  height: 4px;
  background-color: #ff9800;
  width: 0;
  margin-bottom: 5px;
  border-radius: 2px;
  animation: progressAnimation 1.5s infinite ease-in-out;
}

@keyframes progressAnimation {
  0% { width: 0; }
  50% { width: 100%; }
  100% { width: 0; }
}

.change-photo-btn {
  margin-top: 10px; /* Réduction de la marge */
  color: #fff;
  border: 1px solid rgba(255, 255, 255, 0.8);
  border-radius: 20px; /* Réduction du rayon */
  padding: 6px 15px; /* Réduction du padding */
  font-weight: 500;
  font-size: 12px; /* Réduction de la taille de la police */
  background-color: rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.change-photo-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* User Details */
.user-info-container {
  flex: 1;
  min-width: 300px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-right: 20px;
  margin-left: 10px; /* Ajout d'une marge à gauche */
}

.user-identity {
  display: flex;
  flex-direction: column;
  gap: 3px;
  margin-bottom: 3px;
}

.user-name-section {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 5px;
}

.user-name-section h1 {
  font-size: 30px; /* Taille de police augmentée */
  font-weight: 700;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  background: linear-gradient(to right, #ffffff, #dbeafe);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: slideInRight 0.5s ease-out;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 350px;
}

.user-status {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-top: 8px;
}

.user-status.online {
  background-color: #10b981;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.3);
  animation: statusPulse 2s infinite;
}

@keyframes statusPulse {
  0% { box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7); }
  70% { box-shadow: 0 0 0 5px rgba(16, 185, 129, 0); }
  100% { box-shadow: 0 0 0 0 rgba(16, 185, 129, 0); }
}

.title {
  font-size: 20px; /* Taille de police augmentée */
  font-weight: 500;
  color: rgba(255, 255, 255, 0.95);
  margin: 0;
  margin-top: 5px;
  text-shadow: 0 2px 3px rgba(0, 0, 0, 0.2);
  border-left: 3px solid #ed8936; /* Bordure plus épaisse */
  padding-left: 10px;
  animation: fadeInUp 0.6s ease-out;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 350px;
}

.badges-container {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin: 3px 0;
  animation: fadeInUp 0.7s ease-out;
}

.company-badge, .experience-badge, .location-badge {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 5px 12px;
  border-radius: 16px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.12);
}

.company-badge {
  background: linear-gradient(135deg, rgba(255, 152, 0, 0.2), rgba(255, 152, 0, 0.4));
  color: #fff;
  border: 1px solid rgba(255, 152, 0, 0.4);
}

.experience-badge {
  background: rgba(59, 130, 246, 0.2);
  color: #fff;
  border: 1px solid rgba(59, 130, 246, 0.4);
}

.location-badge {
  background: rgba(16, 185, 129, 0.2);
  color: #fff;
  border: 1px solid rgba(16, 185, 129, 0.4);
}

.company-badge:hover, .experience-badge:hover, .location-badge:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
}

.bio-section {
  flex: 1;
  margin-right: 20px;
  max-width: 70%;
  animation: fadeInUp 0.8s ease-out;
}

.bio-short {
  flex: 1;
  max-width: 350px;
  animation: fadeInUp 0.8s ease-out;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 12px 16px;
  position: relative;
  border-left: 3px solid #ed8936; /* Bordure plus épaisse */
  margin-right: 20px;
}

.bio-short p {
  margin: 0;
  font-size: 14px; /* Taille de police augmentée */
  color: rgba(255, 255, 255, 0.95);
  line-height: 1.5;
  font-style: italic;
}

.view-more-btn {
  position: absolute;
  bottom: -10px;
  right: 10px;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 15px;
  padding: 3px 10px;
  font-size: 12px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 5px;
  color: white;
  min-width: auto;
  line-height: 1;
}

.view-more-btn mat-icon {
  font-size: 14px;
  height: 14px;
  width: 14px;
}

.bio-preview p {
  margin: 0;
  font-style: italic;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  padding: 5px 0;
  text-indent: 20px;
}

.view-more-btn:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
}

.view-more-btn:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.contact-info {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 5px;
  animation: fadeInUp 0.8s ease-out;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 6px;
  background: rgba(255, 255, 255, 0.1);
  padding: 6px 12px;
  border-radius: 20px;
  transition: all 0.3s ease;
  font-size: 13px;
}

.contact-item:nth-child(1) { animation-delay: 0.1s; }
.contact-item:nth-child(2) { animation-delay: 0.2s; }
.contact-item:nth-child(3) { animation-delay: 0.3s; }

.contact-item:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.contact-item mat-icon {
  font-size: 18px;
  color: #ff9800;
}

.contact-item span {
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

/* Actions */
.actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.edit-profile-btn {
  background: linear-gradient(135deg, #3b82f6, #1e40af);
  color: white;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.edit-profile-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);
}

.contact-btn {
  color: white;
  background: rgba(255, 255, 255, 0.15);
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
}

.contact-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.share-btn {
  color: white;
  background: rgba(255, 152, 0, 0.2);
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
}

.share-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: rotate(15deg);
}

/* Les styles des statistiques ont été supprimés pour rendre le header plus compact */

@keyframes slideInRight {
  from { opacity: 0; transform: translateX(-20px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes fadeInUp {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Main Content */
.profile-content {
  display: flex;
  width: 100%;
  margin: 40px 0;
  gap: 40px;
  padding: 0 20px;
  box-sizing: border-box;
  animation: fadeIn 0.8s ease-in-out;
}

.left-column {
  width: 35%;
  min-width: 300px;
}

.right-column {
  width: 65%;
}

/* Info Cards - Design moderne et attrayant */
.info-card {
  background-color: #fff;
  border-radius: 16px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
  margin-bottom: 40px;
  transition: all 0.4s ease;
  border: 1px solid #e5e7eb;
  overflow: hidden;
  animation: slideUp 0.5s ease-out;
  animation-fill-mode: both;
  position: relative;
}

.left-column .info-card:nth-child(1) { animation-delay: 0.1s; }
.left-column .info-card:nth-child(2) { animation-delay: 0.2s; }
.left-column .info-card:nth-child(3) { animation-delay: 0.3s; }
.right-column .info-card:nth-child(1) { animation-delay: 0.2s; }
.right-column .info-card:nth-child(2) { animation-delay: 0.3s; }
.right-column .info-card:nth-child(3) { animation-delay: 0.4s; }

@keyframes slideUp {
  from { opacity: 0; transform: translateY(30px); }
  to { opacity: 1; transform: translateY(0); }
}

.info-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.info-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #1e40af);
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.4s ease;
}

.info-card:hover::before {
  transform: scaleX(1);
}

.info-card mat-card-header {
  background: linear-gradient(to right, #f8fafc, #f1f5f9);
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-card mat-card-header mat-card-title {
  display: flex;
  align-items: center;
  font-size: 22px;
  font-weight: 600;
  color: #1e3a8a;
  margin: 0;
  position: relative;
}

.info-card mat-card-header mat-card-title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 40px;
  height: 3px;
  background-color: #ff9800;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.info-card:hover mat-card-header mat-card-title::after {
  width: 60px;
}

.info-card mat-card-header mat-card-title .card-icon {
  font-size: 28px;
  margin-right: 12px;
  color: #3b82f6;
  transition: transform 0.3s ease;
}

.info-card:hover mat-card-header mat-card-title .card-icon {
  transform: scale(1.1) rotate(5deg);
  color: #1e40af;
}

.info-card mat-card-header .card-actions {
  display: flex;
}

.info-card mat-card-header .card-actions button {
  color: #6b7280;
  transition: all 0.3s ease;
  background: rgba(241, 245, 249, 0.8);
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.info-card mat-card-header .card-actions button:hover {
  color: #ff9800;
  transform: scale(1.1);
  background: white;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.info-card mat-card-content {
  padding: 24px;
  font-size: 15px;
  color: #374151;
  line-height: 1.6;
}

.info-card mat-card-content p {
  margin: 14px 0;
  display: flex;
  align-items: center;
}

.info-card mat-card-content p mat-icon {
  font-size: 20px;
  margin-right: 10px;
  color: #1e3a8a;
}

.info-card mat-card-content p strong {
  font-weight: 600;
  margin-right: 8px;
  color: #1f2937;
}

/* Bio Card */
.bio-card mat-card-title .bio-indicator {
  font-size: 12px;
  background-color: #ff9800;
  color: white;
  padding: 2px 8px;
  border-radius: 10px;
  margin-left: 10px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.highlight-card {
  animation: highlightCard 2s ease-in-out;
  position: relative;
  z-index: 100;
}

@keyframes highlightCard {
  0%, 100% { box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05); }
  50% { box-shadow: 0 0 25px rgba(255, 152, 0, 0.6); }
}

.bio-content {
  min-height: 100px;
}

.empty-bio {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 20px;
  background-color: #f9fafb;
  border-radius: 8px;
  border: 1px dashed #d1d5db;
}

.empty-bio mat-icon {
  font-size: 36px;
  color: #9ca3af;
  margin-bottom: 10px;
}

.empty-bio p {
  color: #6b7280;
  margin-bottom: 15px;
}

.empty-bio button {
  margin-top: 10px;
}

.bio-edit {
  display: flex;
  flex-direction: column;
}

.bio-textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-family: inherit;
  font-size: 15px;
  resize: vertical;
  margin-bottom: 5px;
  transition: all 0.3s ease;
}

.bio-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.error-textarea {
  border-color: #ef4444;
  background-color: #fef2f2;
}

.char-count {
  text-align: right;
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 15px;
  transition: color 0.3s ease;
}

.error-count {
  color: #ef4444;
  font-weight: bold;
}

.error-message {
  display: flex;
  align-items: center;
  color: #ef4444;
  margin-bottom: 15px;
  padding: 10px;
  background-color: #fee2e2;
  border-radius: 8px;
}

.error-message mat-icon {
  margin-right: 8px;
  font-size: 20px;
}

.success-message {
  display: flex;
  align-items: center;
  color: #10b981;
  margin-top: 15px;
  padding: 10px;
  background-color: #d1fae5;
  border-radius: 8px;
  animation: fadeIn 0.5s ease-in-out;
}

.success-message mat-icon {
  margin-right: 8px;
  font-size: 20px;
  color: #059669;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.bio-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.spinner {
  animation: spin 1s linear infinite;
}

/* Company Card - Design moderne et attrayant */
.company-card {
  position: relative;
  overflow: hidden;
}

.company-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 5px;
  background: linear-gradient(to bottom, #ff9800, #f57c00);
  z-index: 1;
}

.company-card::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100px;
  height: 100px;
  background-image: url('https://www.transparenttextures.com/patterns/cubes.png');
  opacity: 0.05;
  z-index: 0;
}

.company-logo {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30px;
  position: relative;
}

.company-logo::after {
  content: '';
  position: absolute;
  bottom: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 50%;
  height: 1px;
  background: linear-gradient(to right, transparent, rgba(59, 130, 246, 0.3), transparent);
}

.company-logo .logo-wrapper {
  width: 120px;
  height: 120px;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.4s ease;
  position: relative;
  cursor: pointer;
  border: 4px solid #f8fafc;
}

.company-logo .logo-wrapper:hover {
  transform: scale(1.05) rotate(2deg);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
  border-color: rgba(255, 152, 0, 0.3);
}

.company-logo .logo-wrapper .logo-icon {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 12px;
}

.company-logo .logo-wrapper .logo-image {
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  border-radius: 12px;
  transition: all 0.4s ease;
}

.company-logo .logo-wrapper:hover .logo-image {
  transform: scale(1.1);
}

.company-logo .logo-wrapper mat-icon.logo-icon {
  font-size: 70px;
  color: #93c5fd;
  transition: all 0.3s ease;
}

.company-logo .logo-wrapper:hover mat-icon.logo-icon {
  color: #3b82f6;
  transform: scale(1.1);
}

.company-logo .logo-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 12px;
}

.company-logo .logo-wrapper:hover .logo-overlay {
  opacity: 1;
}

.company-logo .logo-overlay mat-icon {
  color: white;
  font-size: 40px;
  animation: fadeInZoom 0.3s ease-out;
}

.company-logo .change-logo-btn {
  margin-top: 15px;
  color: #3b82f6;
  border: 1px solid #3b82f6;
  border-radius: 25px;
  padding: 10px 20px;
  font-weight: 500;
  background-color: rgba(59, 130, 246, 0.05);
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.company-logo .change-logo-btn:hover {
  background-color: rgba(59, 130, 246, 0.1);
  transform: translateY(-3px);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
}

/* Stats Card - Design moderne et attrayant */
.stats-card {
  position: relative;
  overflow: hidden;
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(to right, #ff9800, #f57c00);
  z-index: 1;
}

.stats-card .stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 30px;
  text-align: center;
  padding: 10px;
}

.stats-card .stat-item {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 25px 20px;
  border-radius: 16px;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
  min-height: 140px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
  overflow: hidden;
  transition: all 0.4s ease;
  border: 1px solid #e5e7eb;
}

.stats-card .stat-item:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
  border-color: rgba(59, 130, 246, 0.3);
}

.stats-card .stat-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 0;
  background: linear-gradient(to bottom, #3b82f6, #1e40af);
  transition: height 0.4s ease;
}

.stats-card .stat-item:hover::before {
  height: 100%;
}

.stats-card .stat-item h4 {
  font-size: 36px;
  font-weight: 700;
  background: linear-gradient(135deg, #1e40af, #3b82f6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin: 0 0 10px;
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;
}

.stats-card .stat-item:hover h4 {
  transform: scale(1.1);
  background: linear-gradient(135deg, #3b82f6, #60a5fa);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.stats-card .stat-item p {
  font-size: 16px;
  color: #4b5563;
  margin: 5px 0 0;
  position: relative;
  z-index: 2;
  font-weight: 500;
  transition: all 0.3s ease;
}

.stats-card .stat-item:hover p {
  color: #1e3a8a;
}

.stats-card .stat-item .stat-icon {
  position: absolute;
  bottom: -20px;
  right: -20px;
  width: 100px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.07;
  z-index: 1;
  transition: all 0.4s ease;
}

.stats-card .stat-item:hover .stat-icon {
  opacity: 0.12;
  transform: scale(1.1) rotate(10deg);
}

.stats-card .stat-item .stat-icon mat-icon {
  font-size: 80px;
  color: #1e3a8a;
}

/* Job Postings Card - Design moderne et attrayant */
.job-postings-card {
  position: relative;
  overflow: hidden;
}

.job-postings-card::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 150px;
  height: 150px;
  background-image: url('https://www.transparenttextures.com/patterns/cubes.png');
  opacity: 0.03;
  z-index: 0;
}

.job-postings-card mat-card-title .job-count {
  font-size: 14px;
  background: linear-gradient(135deg, #ff9800, #f57c00);
  color: white;
  padding: 4px 10px;
  border-radius: 20px;
  margin-left: 10px;
  font-weight: 600;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  animation: pulseCount 2s infinite;
  transition: all 0.3s ease;
}

.job-postings-card mat-card-title .highlight-count {
  background-color: #ff9800;
  color: white;
  transform: scale(1.1);
}

@keyframes pulseCount {
  0% { box-shadow: 0 0 0 0 rgba(255, 152, 0, 0.5); }
  70% { box-shadow: 0 0 0 8px rgba(255, 152, 0, 0); }
  100% { box-shadow: 0 0 0 0 rgba(255, 152, 0, 0); }
}

.job-list {
  max-height: 600px;
  overflow-y: auto;
  padding-right: 15px;
  padding-left: 5px;
  margin: 10px 0;
}

.job-list::-webkit-scrollbar {
  width: 8px;
}

.job-list::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 10px;
}

.job-list::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #3b82f6, #1e40af);
  border-radius: 10px;
  border: 2px solid #f1f5f9;
}

.job-list::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #2563eb, #1e3a8a);
}

.job-item {
  background-color: #fff;
  border: 1px solid #e5e7eb;
  border-radius: 16px;
  padding: 25px;
  margin-bottom: 25px;
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.03);
}

.job-item:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
  border-color: rgba(59, 130, 246, 0.3);
}

.job-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 5px;
  height: 100%;
  background: linear-gradient(to bottom, #ff9800, #f57c00);
  border-top-left-radius: 16px;
  border-bottom-left-radius: 16px;
}

.job-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.job-info {
  flex: 1;
  padding-right: 20px;
}

.job-title {
  margin: 0 0 8px;
  font-size: 20px;
  font-weight: 600;
  color: #1e3a8a;
  transition: color 0.3s ease;
}

.job-item:hover .job-title {
  color: #3b82f6;
}

.job-subtitle {
  font-size: 15px;
  color: #6b7280;
  margin: 0 0 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.job-subtitle::before {
  content: '';
  display: inline-block;
  width: 8px;
  height: 8px;
  background-color: #3b82f6;
  border-radius: 50%;
}

.job-date {
  font-size: 13px;
  color: #9ca3af;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 5px;
}

.job-date::before {
  content: '\2022'; /* Bullet point */
  color: #d1d5db;
}

.job-actions {
  display: flex;
  gap: 8px;
}

.job-actions .action-btn {
  color: #6b7280;
  transition: all 0.3s ease;
  background: rgba(241, 245, 249, 0.8);
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.job-actions .action-btn:hover {
  color: #3b82f6;
  transform: scale(1.1);
  background: white;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.job-actions .delete-btn:hover {
  color: #ef4444;
}

.job-details {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #e5e7eb;
  position: relative;
}

.job-details::before {
  content: '';
  position: absolute;
  top: -1px;
  left: 0;
  width: 50px;
  height: 2px;
  background-color: #3b82f6;
  border-radius: 2px;
}

.job-description {
  font-size: 15px;
  color: #4b5563;
  margin: 0;
  line-height: 1.6;
}

.job-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 20px;
}

.job-tag {
  font-size: 13px;
  color: #3b82f6;
  background-color: rgba(59, 130, 246, 0.1);
  padding: 6px 12px;
  border-radius: 20px;
  font-weight: 500;
  transition: all 0.3s ease;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.job-tag:hover {
  background-color: rgba(59, 130, 246, 0.2);
  transform: translateY(-2px);
}

.job-status {
  position: absolute;
  top: 25px;
  right: 25px;
  font-size: 12px;
  font-weight: 600;
  padding: 5px 12px;
  border-radius: 20px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.job-status:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.status-active {
  background-color: #dcfce7;
  color: #166534;
  border: 1px solid rgba(22, 101, 52, 0.2);
}

.status-closed {
  background-color: #fee2e2;
  color: #991b1b;
  border: 1px solid rgba(153, 27, 27, 0.2);
}

.status-draft {
  background-color: #f3f4f6;
  color: #4b5563;
  border: 1px solid rgba(75, 85, 99, 0.2);
}

.empty-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 40px;
  background: linear-gradient(135deg, #f8fafc, #f1f5f9);
  border-radius: 16px;
  border: 1px dashed #d1d5db;
  margin: 20px 0;
}

.empty-message mat-icon {
  font-size: 60px;
  color: #9ca3af;
  margin-bottom: 20px;
  animation: fadeInDown 1s ease-out;
}

@keyframes fadeInDown {
  from { opacity: 0; transform: translateY(-20px); }
  to { opacity: 1; transform: translateY(0); }
}

.empty-message p {
  color: #4b5563;
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 10px;
  animation: fadeInUp 1s ease-out;
}

.empty-message .empty-subtitle {
  color: #6b7280;
  font-size: 15px;
  font-weight: 400;
  max-width: 80%;
  line-height: 1.5;
  animation: fadeInUp 1s ease-out 0.2s;
  animation-fill-mode: both;
}

.action-row {
  text-align: right;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
}

.add-btn {
  background: linear-gradient(135deg, #3b82f6, #1e40af);
  color: #fff;
  padding: 12px 30px;
  border-radius: 30px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  border: none;
  letter-spacing: 0.5px;
}

.add-btn:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
  background: linear-gradient(135deg, #2563eb, #1e3a8a);
}

/* Spacer pour remplacer les préférences */
.spacer-card {
  height: 20px;
  margin-bottom: 40px;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.preference-toggle mat-icon {
  color: #ff9800;
  font-size: 28px;
  transition: transform 0.3s ease;
}

.preference-toggle button:hover mat-icon {
  transform: scale(1.1);
}

/* Notifications */
.notification {
  position: fixed;
  bottom: 30px;
  right: 30px;
  display: flex;
  align-items: center;
  padding: 15px 20px;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  background-color: white;
  z-index: 9999;
  transform: translateY(100px);
  opacity: 0;
  transition: transform 0.3s ease, opacity 0.3s ease;
  max-width: 400px;
}

.notification-visible {
  transform: translateY(0);
  opacity: 1;
}

.notification-hiding {
  transform: translateY(100px);
  opacity: 0;
}

.notification-success {
  border-left: 5px solid #10b981;
}

.notification-error {
  border-left: 5px solid #ef4444;
}

.notification-info {
  border-left: 5px solid #3b82f6;
}

.notification-icon {
  margin-right: 15px;
}

.notification-icon .material-icons {
  font-size: 24px;
}

.notification-success .notification-icon .material-icons {
  color: #10b981;
}

.notification-error .notification-icon .material-icons {
  color: #ef4444;
}

.notification-info .notification-icon .material-icons {
  color: #3b82f6;
}

.notification-content {
  flex: 1;
}

.notification-content p {
  margin: 0;
  font-size: 15px;
  color: #1f2937;
}

.notification-close {
  margin-left: 15px;
  cursor: pointer;
}

.notification-close .material-icons {
  font-size: 20px;
  color: #9ca3af;
  transition: color 0.3s ease;
}

.notification-close:hover .material-icons {
  color: #1f2937;
}

/* Loading Overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  color: white;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 5px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Dialog Styles */
.contact-dialog, .share-dialog {
  padding: 20px;
}

.dialog-title {
  font-size: 24px;
  font-weight: 600;
  color: #1e3a8a;
  margin-bottom: 20px;
  position: relative;
}

.dialog-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 50px;
  height: 3px;
  background-color: #ff9800;
  border-radius: 3px;
}

.dialog-content {
  margin: 30px 0;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #4b5563;
  margin-bottom: 8px;
}

.form-group input, .form-group textarea {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 15px;
  color: #1f2937;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-group input:focus, .form-group textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin-top: 30px;
}

/* Share Dialog Specific Styles */
.profile-preview {
  display: flex;
  align-items: center;
  background-color: #f8fafc;
  padding: 15px;
  border-radius: 10px;
  margin-bottom: 20px;
}

.preview-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 15px;
  flex-shrink: 0;
}

.preview-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.default-avatar-preview {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #3b82f6, #1e40af);
  color: white;
  font-size: 24px;
  font-weight: 600;
}

.preview-info h3 {
  margin: 0 0 5px;
  font-size: 18px;
  font-weight: 600;
  color: #1e3a8a;
}

.preview-info p {
  margin: 0;
  font-size: 14px;
  color: #6b7280;
}

.share-url {
  display: flex;
  margin-bottom: 25px;
}

.share-url input {
  flex: 1;
  padding: 12px 15px;
  border: 1px solid #d1d5db;
  border-radius: 8px 0 0 8px;
  font-size: 14px;
  color: #4b5563;
  background-color: #f9fafb;
}

.share-url button {
  background-color: #f3f4f6;
  border: 1px solid #d1d5db;
  border-left: none;
  border-radius: 0 8px 8px 0;
}

.share-platforms h4 {
  font-size: 16px;
  font-weight: 600;
  color: #4b5563;
  margin-bottom: 15px;
}

.platform-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.platform-button {
  display: flex;
  align-items: center;
  padding: 8px 15px;
  border-radius: 8px;
  color: white;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.platform-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
}

.platform-button mat-icon {
  margin-right: 8px;
  font-size: 18px;
}

/* Custom Dialog Container */
.custom-dialog-container .mat-dialog-container {
  border-radius: 16px;
  padding: 0;
  overflow: hidden;
}

.contact-dialog-container .mat-dialog-container {
  border-radius: 16px;
  padding: 0;
  overflow: hidden;
}

/* Responsive Design */
@media (max-width: 960px) {
  .profile-content {
    flex-direction: column;
    padding: 20px;
  }

  .left-column,
  .right-column {
    width: 100%;
  }

  .main-profile-section {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 15px;
  }

  .secondary-profile-section {
    flex-direction: column;
    align-items: center;
    gap: 20px;
  }

  .bio-section {
    max-width: 100%;
    margin-right: 0;
    margin-bottom: 20px;
  }

  .user-info-container {
    padding: 10px 0;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .contact-info {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .actions {
    margin-top: 10px;
    align-items: center;
  }

  .edit-profile-btn, .contact-btn {
    width: 100%;
    max-width: 250px;
  }

  .stats-card .stats-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }
}

@media (max-width: 768px) {
  .profile-header {
    height: auto;
    min-height: 200px;
    padding: 20px;
  }

  .profile-info {
    flex-direction: column;
    align-items: center;
    gap: 20px;
  }

  .avatar-section {
    margin-right: 0;
    margin-left: 0;
    margin-bottom: 15px;
  }

  .avatar-wrapper {
    width: 110px;
    height: 110px;
    transform: none;
  }

  .avatar-wrapper:hover {
    transform: scale(1.05);
  }

  .user-info-container {
    text-align: center;
    margin-right: 0;
  }

  .user-identity {
    align-items: center;
  }

  .user-name-section h1 {
    font-size: 24px;
    max-width: 100%;
  }

  .title {
    border-left: none;
    border-bottom: 3px solid #ed8936;
    padding-left: 0;
    padding-bottom: 5px;
    max-width: 100%;
    font-size: 18px;
  }

  .badges-container {
    justify-content: center;
  }

  .company-badge, .experience-badge, .location-badge {
    font-size: 13px;
  }

  .bio-actions-container {
    flex-direction: column;
    width: 100%;
    gap: 15px;
    margin-top: 10px;
  }

  .bio-short {
    width: 100%;
    max-width: 100%;
    margin-right: 0;
  }

  .actions {
    width: 100%;
    justify-content: center;
    gap: 15px;
  }
}

  .info-card mat-card-header mat-card-title {
    font-size: 18px;
  }

  .info-card mat-card-content {
    padding: 15px;
  }

  .job-item {
    padding: 15px;
  }

  .job-header {
    flex-direction: column;
  }

  .job-actions {
    margin-top: 10px;
    align-self: flex-end;
  }

  .job-count {
    font-size: 18px;
  }

