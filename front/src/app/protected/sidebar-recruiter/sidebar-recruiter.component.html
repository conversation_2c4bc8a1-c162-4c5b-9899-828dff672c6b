<div class="sidebar" [ngClass]="{ collapsed: collapsed }">
  <button class="toggle-btn" (click)="toggleSidebar()">
    <mat-icon>{{ collapsed ? 'chevron_right' : 'chevron_left' }}</mat-icon>
  </button>

  <div class="user-profile">
    <img *ngIf="avatarUrl" [src]="avatarUrl" class="avatar" alt="User Avatar" />
    <mat-icon *ngIf="!avatarUrl" class="avatar-icon">person</mat-icon>
    <span class="user-name">{{ userName }}</span>
  </div>

  <div class="sidebar-header">
    <div class="logo">
      <mat-icon class="logo-icon">dashboard</mat-icon>
      <h2>Dashboard Recruiter</h2>
    </div>
  </div>

  <ul class="sidebar-menu">
    <li routerLinkActive="active">
      <a routerLink="/dashboard/candidatures">
        <mat-icon>people</mat-icon>
        <span>Candidatures</span>
      </a>
    </li>
    <li routerLinkActive="active">
      <a routerLink="/dashboard/offres">
        <mat-icon>work</mat-icon>
        <span>Workspace</span>
      </a>
    </li>

    <li routerLinkActive="active">
      <a routerLink="/dashboard/profil">
        <mat-icon>account_circle</mat-icon>
        <span>Profil</span>
      </a>
    </li>

  </ul>
</div>
