/* src/app/sidebar/sidebar.component.css */

/* Import Google Font */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

.sidebar {
  width: 250px;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  background: linear-gradient(180deg, #021c6c 0%, #190505 100%);
  color: #f0f0f0;
  display: flex;
  flex-direction: column;
  padding-top: 20px;
  box-shadow: 4px 0 10px rgba(0, 0, 0, 0.3);
  font-family: 'Inter', sans-serif;
  transition: width 0.3s ease;
  z-index: 1000;
  overflow-y: auto;
}

/* User Profile */
.user-profile {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.user-profile .avatar,
.user-profile .avatar-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 10px;
  object-fit: cover;
}

.user-profile .user-name {
  font-size: 16px;
  font-weight: 500;
}

/* Toggle Button */
.toggle-btn {
  position: absolute;
  top: 20px;
  right: -40px;
  background-color: #424242;
  color: #f0f0f0;
  border: none;
  border-radius: 0 8px 8px 0;
  padding: 10px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.toggle-btn:hover {
  background-color: #00c853;
}

.toggle-btn mat-icon {
  font-size: 24px;
}

/* Sidebar Header */
.sidebar-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 0 20px;
}

.logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.logo-icon {
  font-size: 30px;
  color: #f0f0f0;
  animation: pulse 2s infinite ease-in-out;
}

.sidebar-header h2 {
  font-size: 22px;
  font-weight: 600;
  margin: 0;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  background: linear-gradient(90deg, #f0f0f0, #00c853);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Sidebar Menu */
.sidebar-menu {
  list-style: none;
  padding: 0;
  margin: 0;
  flex: 1;
}

.sidebar-menu li {
  padding: 0;
  margin: 5px 10px;
  border-radius: 6px;
  overflow: hidden;
  transition: background-color 0.3s ease, transform 0.2s ease, box-shadow 0.3s ease;
}

.sidebar-menu li:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateX(5px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.sidebar-menu li.active {
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  box-shadow: inset 3px 0 0 #69f0ae;
  position: relative;
}

.sidebar-menu li.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 3px;
  height: 100%;
  background: linear-gradient(180deg, #69f0ae, #00c853);
  filter: blur(2px);
}

/* Liens */
.sidebar-menu a {
  display: flex;
  align-items: center;
  padding: 15px;
  color: #f0f0f0;
  text-decoration: none;
  font-size: 16px;
  font-weight: 500;
  transition: color 0.3s ease, padding-left 0.3s ease;
}

.sidebar-menu a:hover {
  color: #00c853;
  padding-left: 20px;
}

.sidebar-menu li:hover a {
  color: #ffffff;
}

/* Icônes */
.sidebar-menu mat-icon {
  margin-right: 16px;
  font-size: 24px;
  color: #f0f0f0;
  transition: transform 0.3s ease;
}

.sidebar-menu a:hover mat-icon {
  transform: scale(1.1);
}

.sidebar-menu li.active mat-icon {
  color: #00e676;
}

/* Footer */
.sidebar-footer {
  padding: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Logout Button */
.logout-btn {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 12px;
  background-color: #190505;
  color: #ffffff;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease, transform 0.2s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
}

.logout-btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: 0.5s;
}

.logout-btn:hover::after {
  left: 100%;
}

.logout-btn:hover {
  background-color: #021c6c;
  transform: scale(1.02);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.logout-btn mat-icon {
  margin-right: 15px;
  font-size: 24px;
}

/* Animation Logo */
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Responsive */
@media (max-width: 768px) {
  .sidebar {
    width: 70px;
    align-items: center;
    padding-top: 16px;
  }

  .sidebar-header h2,
  .user-profile .user-name {
    display: none;
  }

  .sidebar-menu a {
    font-size: 0;
    justify-content: center;
  }

  .sidebar-menu mat-icon {
    margin: 0;
    font-size: 24px;
  }

  .logout-btn {
    font-size: 0;
    justify-content: center;
  }

  .logout-btn mat-icon {
    margin: 0;
  }

  .user-profile {
    justify-content: center;
  }

  .user-profile .avatar {
    margin: 0;
  }
}
