// src/app/protected/add-job-posting-modal/add-job-posting-modal.component.ts
import { Component, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { provideNativeDateAdapter } from '@angular/material/core'; // Import the DateAdapter provider
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-add-job-posting-modal',
  templateUrl: './add-job-posting-modal.component.html',
  styleUrls: ['./add-job-posting-modal.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatDatepickerModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule
  ],
  providers: [
    provideNativeDateAdapter() // Provide the NativeDateAdapter
  ]
})
export class AddJobPostingModalComponent {
  jobPosting: any = {
    title: '',
    company: '',
    location: '',
    postedDate: new Date()
  };
  isSubmitting = false;

  constructor(
    public dialogRef: MatDialogRef<AddJobPostingModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.jobPosting = { ...this.data };
  }

  cancel(): void {
    this.dialogRef.close();
  }

  save(): void {
    this.isSubmitting = true;
    // Simulate a delay for submission (replace with actual service call in parent component)
    setTimeout(() => {
      this.dialogRef.close(this.jobPosting);
      this.isSubmitting = false;
    }, 1000);
  }
}
