/* src/app/protected/add-job-posting-modal/add-job-posting-modal.component.css */
.dialog-container {
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  background: linear-gradient(145deg, #ffffff, #f5f7fa);
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  font-family: 'Inter', 'Roboto', sans-serif;
  position: relative;
  overflow: hidden;
}

/* Subtle background pattern */
.dialog-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('https://www.transparenttextures.com/patterns/subtle-stripes.png');
  opacity: 0.05;
  z-index: 0;
}

/* Header */
.dialog-header {
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;
  z-index: 1;
}

.dialog-header h2 {
  margin: 0;
  font-size: 22px;
  font-weight: 600;
  color: #1e3a8a;
  letter-spacing: 0.2px;
}

.dialog-header .header-icon {
  font-size: 28px;
  color: #3b82f6;
  transition: transform 0.3s ease;
}

.dialog-header .header-icon:hover {
  transform: rotate(15deg);
}

.dialog-header .close-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  color: #6b7280;
  transition: color 0.3s ease;
}

.dialog-header .close-btn:hover {
  color: #1e3a8a;
}

/* Form */
.dialog-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
  position: relative;
  z-index: 1;
}

mat-form-field {
  width: 100%;
}

mat-form-field .mat-form-field-wrapper {
  padding-bottom: 0;
}

mat-form-field .mat-form-field-label {
  color: #374151;
  font-weight: 500;
}

mat-form-field .mat-form-field-outline {
  transition: all 0.3s ease;
}

mat-form-field.mat-focused .mat-form-field-outline {
  color: #3b82f6;
}

mat-form-field .mat-form-field-outline-thick {
  border-width: 2px;
}

mat-error {
  font-size: 12px;
  color: #ef4444;
  margin-top: 4px;
}

/* Actions */
.actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 16px;
}

.actions button[mat-button] {
  color: #6b7280;
  font-weight: 500;
  transition: color 0.3s ease, transform 0.3s ease;
}

.actions button[mat-button]:hover {
  color: #1e3a8a;
  transform: translateY(-2px);
}

.actions button[mat-raised-button] {
  background: linear-gradient(135deg, #3b82f6, #1e3a8a);
  color: #fff;
  font-weight: 600;
  padding: 8px 24px;
  border-radius: 25px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: transform 0.3s ease, box-shadow 0.3s ease, background 0.3s ease;
  position: relative;
  overflow: hidden;
}

.actions button[mat-raised-button]:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
  background: linear-gradient(135deg, #1e3a8a, #3b82f6);
}

.actions button[mat-raised-button]:disabled {
  background: #d1d5db;
  box-shadow: none;
  cursor: not-allowed;
}

/* Glowing effect for the "Ajouter" button */
.actions button[mat-raised-button]::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: 0.5s;
}

.actions button[mat-raised-button]:hover::before {
  left: 100%;
}

/* Spinner */
mat-spinner {
  display: inline-block;
  vertical-align: middle;
  margin-right: 8px;
}

/* Responsive Design */
@media (max-width: 600px) {
  .dialog-container {
    padding: 16px;
  }

  .dialog-header h2 {
    font-size: 18px;
  }

  .dialog-header .header-icon {
    font-size: 24px;
  }

  .actions {
    flex-direction: column;
    gap: 8px;
  }

  .actions button {
    width: 100%;
  }
}
