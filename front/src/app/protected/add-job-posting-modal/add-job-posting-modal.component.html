<!-- src/app/protected/add-job-posting-modal/add-job-posting-modal.component.html -->
<div class="dialog-container">
  <div class="dialog-header">
    <mat-icon class="header-icon">work</mat-icon>
    <h2>Ajouter une offre d’emploi</h2>
    <button mat-icon-button class="close-btn" (click)="cancel()" aria-label="Fermer">
      <mat-icon>close</mat-icon>
    </button>
  </div>
  <form #jobForm="ngForm" (ngSubmit)="save()" class="dialog-form">
    <mat-form-field appearance="outline">
      <mat-label>Titre</mat-label>
      <input matInput [(ngModel)]="jobPosting.title" name="title" required #title="ngModel">
      <mat-error *ngIf="title.invalid && title.touched">Le titre est requis</mat-error>
    </mat-form-field>
    <mat-form-field appearance="outline">
      <mat-label>Société</mat-label>
      <input matInput [(ngModel)]="jobPosting.company" name="company" required #company="ngModel">
      <mat-error *ngIf="company.invalid && company.touched">La société est requise</mat-error>
    </mat-form-field>
    <mat-form-field appearance="outline">
      <mat-label>Localisation</mat-label>
      <input matInput [(ngModel)]="jobPosting.location" name="location" required #location="ngModel">
      <mat-error *ngIf="location.invalid && location.touched">La localisation est requise</mat-error>
    </mat-form-field>
    <mat-form-field appearance="outline">
      <mat-label>Date de publication</mat-label>
      <input matInput [matDatepicker]="picker" [(ngModel)]="jobPosting.postedDate" name="postedDate" required #postedDate="ngModel">
      <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
      <mat-datepicker #picker></mat-datepicker>
      <mat-error *ngIf="postedDate.invalid && postedDate.touched">La date de publication est requise</mat-error>
    </mat-form-field>
    <div class="actions">
      <button mat-button type="button" (click)="cancel()" [disabled]="isSubmitting">Annuler</button>
      <button mat-raised-button color="primary" type="submit" [disabled]="jobForm.invalid || isSubmitting">
        <mat-spinner *ngIf="isSubmitting" diameter="20"></mat-spinner>
        <span *ngIf="!isSubmitting">Ajouter</span>
      </button>
    </div>
  </form>
</div>
