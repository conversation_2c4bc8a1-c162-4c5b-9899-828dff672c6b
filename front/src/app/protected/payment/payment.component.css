.payment-container {
  max-width: 600px;
  margin: 30px auto;
  padding: 30px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  text-align: center;
  font-family: 'Arial', sans-serif;
}

h2 {
  font-size: 28px;
  margin-bottom: 20px;
  color: #333;
}

h3 {
  font-size: 22px;
  margin-bottom: 15px;
  color: #007bff;
}

.step-container {
  display: none;
  opacity: 0;
  transition: opacity 0.5s ease-in-out;
}

.step-container.active {
  display: block;
  opacity: 1;
}

.plans {
  display: flex;
  justify-content: space-between;
  gap: 20px;
  margin-top: 30px;
}

.plan-card {
  border: 1px solid #e0e0e0;
  padding: 20px;
  border-radius: 12px;
  cursor: pointer;
  transition: transform 0.3s, box-shadow 0.3s;
  background-color: #f9f9f9;
  width: 30%;
  text-align: left;
}

.plan-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
}

.plan-card h3 {
  font-size: 20px;
  margin-bottom: 10px;
  color: #007bff;
}

.plan-card p {
  font-size: 16px;
  color: #666;
}

.payment-methods {
  display: flex;
  justify-content: space-between;
  gap: 20px;
  margin-top: 20px;
}

.payment-methods button {
  padding: 12px 20px;
  border-radius: 8px;
  border: 1px solid #007bff;
  background-color: #007bff;
  color: white;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.3s;
  width: 30%;
}

.payment-methods button:hover {
  background-color: #0056b3;
}

form {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-top: 20px;
  font-size: 16px;
}

label {
  font-weight: 600;
  text-align: left;
  color: #333;
}

input {
  padding: 12px;
  border: 1px solid #ccc;
  border-radius: 8px;
  font-size: 16px;
  width: 100%;
  margin-bottom: 10px;
}

input:focus {
  border-color: #007bff;
  outline: none;
}

button[type="button"] {
  background-color: #f8f9fa;
  color: #007bff;
  border: 1px solid #ccc;
  padding: 12px 20px;
  cursor: pointer;
  border-radius: 8px;
  font-weight: bold;
}

button[type="button"]:hover {
  background-color: #e9ecef;
}

button[type="submit"] {
  padding: 12px 20px;
  border-radius: 8px;
  border: none;
  background-color: #28a745;
  color: white;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.3s;
}

button[type="submit"]:hover {
  background-color: #218838;
}

button[disabled] {
  background-color: #ccc;
  cursor: not-allowed;
}

/* Progress bar */
.step-progress {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

.step-progress div {
  width: 30%;
  height: 8px;
  background-color: #ccc;
  border-radius: 4px;
}

.step-progress .active {
  background-color: #007bff;
}

/* Specific styles for each step */
.step1 {
  background-color: #f0f8ff;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

/* Style pour le message de chargement */
.payment-loading-message {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  color: white;
}

.payment-loading-message .spinner {
  width: 50px;
  height: 50px;
  border: 5px solid #f3f3f3;
  border-top: 5px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

.payment-loading-message .message {
  font-size: 18px;
  font-weight: 500;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.step2 {
  background-color: #f7f7f7;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.step3 {
  background-color: #f0fff0;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}
