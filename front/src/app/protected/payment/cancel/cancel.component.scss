.cancel-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 20px;
  background: linear-gradient(135deg, #f0f4f8 0%, #e6ecf5 100%);
}

.cancel-card {
  width: 100%;
  max-width: 600px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border-top: 3px solid #f44336;
}

.card-content {
  padding: 40px;
  text-align: center;
}

.cancel-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.cancel-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: #ffebee;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24px;
}

.cancel-icon mat-icon {
  font-size: 48px;
  height: 48px;
  width: 48px;
  color: #f44336;
}

.cancel-title {
  font-size: 28px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
}

.cancel-message {
  font-size: 16px;
  line-height: 1.6;
  color: #666;
  margin-bottom: 32px;
  max-width: 400px;
}

.button-group {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
  justify-content: center;
}

.retry-btn {
  padding: 10px 24px;
  font-size: 16px;
  border-radius: 30px;
  background-color: #2196f3;
  color: white;
  transition: all 0.3s ease;
}

.retry-btn:hover {
  background-color: #1976d2;
  box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
  transform: translateY(-2px);
}

.dashboard-btn {
  padding: 10px 24px;
  font-size: 16px;
  border-radius: 30px;
  border-color: #bdbdbd;
  color: #666;
  transition: all 0.3s ease;
}

.dashboard-btn:hover {
  border-color: #9e9e9e;
  background-color: rgba(0, 0, 0, 0.05);
}

@media (max-width: 600px) {
  .card-content {
    padding: 30px 20px;
  }
  
  .cancel-title {
    font-size: 24px;
  }
  
  .cancel-message {
    font-size: 14px;
  }
  
  .cancel-icon {
    width: 70px;
    height: 70px;
  }
  
  .cancel-icon mat-icon {
    font-size: 40px;
    height: 40px;
    width: 40px;
  }
  
  .button-group {
    flex-direction: column;
    width: 100%;
  }
  
  .retry-btn,
  .dashboard-btn {
    width: 100%;
  }
}
