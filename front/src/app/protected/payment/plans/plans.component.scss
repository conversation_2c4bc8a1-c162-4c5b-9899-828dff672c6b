.plans-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
}

.plans-header {
  text-align: center;
  margin-bottom: 40px;
  
  h1 {
    font-size: 32px;
    font-weight: 600;
    margin-bottom: 16px;
    color: #333;
  }
  
  p {
    font-size: 18px;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
  }
}

.plans-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  justify-content: center;
}

.plan-card {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  }
  
  &.recommended {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border: 2px solid #ff9800;
    transform: scale(1.05);
    
    &:hover {
      transform: translateY(-5px) scale(1.05);
    }
  }
}

.plan-badge {
  position: absolute;
  top: 12px;
  right: 12px;
  background-color: #ff9800;
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  z-index: 1;
}

mat-card-header {
  padding: 24px;
  border-bottom: 1px solid #eee;
  border-top: 5px solid;
  
  mat-card-title {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 8px;
  }
  
  mat-card-subtitle {
    display: flex;
    align-items: baseline;
    
    .price {
      font-size: 36px;
      font-weight: 700;
      color: #333;
    }
    
    .period {
      font-size: 16px;
      color: #666;
      margin-left: 4px;
    }
  }
}

mat-card-content {
  padding: 24px;
  flex-grow: 1;
}

.features-list {
  list-style: none;
  padding: 0;
  margin: 0;
  
  li {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    
    mat-icon {
      color: #4caf50;
      margin-right: 12px;
      font-size: 20px;
    }
    
    span {
      font-size: 16px;
      color: #555;
    }
  }
}

mat-card-actions {
  padding: 24px;
  display: flex;
  justify-content: center;
}

.select-plan-btn {
  width: 100%;
  padding: 10px;
  color: white;
  font-weight: 500;
  font-size: 16px;
  border-radius: 30px;
  
  &:hover {
    opacity: 0.9;
  }
}

@media (max-width: 768px) {
  .plans-grid {
    grid-template-columns: 1fr;
  }
  
  .plan-card.recommended {
    transform: none;
    
    &:hover {
      transform: translateY(-5px);
    }
  }
}
