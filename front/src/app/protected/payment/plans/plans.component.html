<div class="plans-container">
  <div class="plans-header">
    <h1>Choisissez votre plan d'abonnement</h1>
    <p>Sélectionnez le plan qui correspond le mieux aux besoins de votre entreprise</p>
  </div>

  <div class="plans-grid">
    <mat-card *ngFor="let plan of plans" class="plan-card" [ngClass]="{'recommended': plan.recommended}">
      <div class="plan-badge" *ngIf="plan.recommended">Recommandé</div>

      <mat-card-header [style.borderColor]="plan.color">
        <mat-card-title>{{ plan.name }}</mat-card-title>
        <mat-card-subtitle>
          <span class="price">{{ plan.price }}€</span>
          <span class="period">/{{ plan.period }}</span>
        </mat-card-subtitle>
      </mat-card-header>

      <mat-card-content>
        <ul class="features-list">
          <li *ngFor="let feature of plan.features">
            <mat-icon>check_circle</mat-icon>
            <span>{{ feature }}</span>
          </li>
        </ul>
      </mat-card-content>

      <mat-card-actions>
        <button
          mat-raised-button
          [style.backgroundColor]="plan.color"
          class="select-plan-btn"
          (click)="selectPlan(plan)"
          [disabled]="isLoading">
          <mat-spinner *ngIf="isLoading" diameter="20" color="accent"></mat-spinner>
          <span *ngIf="!isLoading">Sélectionner</span>
        </button>
      </mat-card-actions>
    </mat-card>
  </div>
</div>
