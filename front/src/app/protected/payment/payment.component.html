<div class="payment-container">
  <h2>Choose Your Plan</h2>

  <!-- Step 1: Select Plan -->
  <div *ngIf="currentStep === 0">
    <div class="plans">
      <div *ngFor="let plan of plans" class="plan-card" (click)="selectPlan(plan)">
        <h3>{{ plan.name }}</h3>
        <p>${{ plan.price }}/year</p>
      </div>
    </div>
  </div>

  <!-- Step 2: Select Payment Method -->
  <div *ngIf="currentStep === 1">
    <h3>Select Payment Method</h3>
    <div class="payment-methods">
      <button *ngFor="let method of paymentMethods" (click)="selectPaymentMethod(method)">
        {{ method }}
      </button>
    </div>
  </div>

  <!-- Step 3: Enter Payment Details -->
  <div *ngIf="currentStep === 2">
    <h3>Enter Payment Details</h3>
    <form [formGroup]="paymentForm" (ngSubmit)="submitPayment()">
      <div *ngIf="paymentForm.get('paymentMethod')?.value === 'Credit Card'">
        <label>Card Number</label>
        <input type="text" formControlName="cardNumber" placeholder="1234 5678 9012 3456">

        <label>Expiration Date</label>
        <input type="text" formControlName="expirationDate" placeholder="MM/YY">

        <label>CVV</label>
        <input type="text" formControlName="cvv" placeholder="123">
      </div>

      <div *ngIf="paymentForm.get('paymentMethod')?.value === 'PayPal'">
        <label>Email</label>
        <input type="email" formControlName="email" placeholder="<EMAIL>">
      </div>

      <button type="button" (click)="previousStep()">Back</button>
      <button type="submit" [disabled]="!paymentForm.valid">Confirm Payment</button>
    </form>
  </div>
</div>
