.success-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 20px;
  background: linear-gradient(135deg, #f0f4f8 0%, #e6ecf5 100%);
}

.success-card {
  width: 100%;
  max-width: 600px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border-top: 3px solid #4caf50;
}

.card-content {
  padding: 40px;
  text-align: center;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
}

.loading-text {
  margin-top: 20px;
  font-size: 16px;
  color: #666;
}

.success-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.success-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: #e8f5e9;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24px;
}

.success-icon mat-icon {
  font-size: 48px;
  height: 48px;
  width: 48px;
  color: #4caf50;
}

.success-title {
  font-size: 28px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
}

.success-message {
  font-size: 16px;
  line-height: 1.6;
  color: #666;
  margin-bottom: 32px;
  max-width: 400px;
}

.dashboard-btn {
  padding: 10px 24px;
  font-size: 16px;
  border-radius: 30px;
  background-color: #4caf50;
  color: white;
  transition: all 0.3s ease;
}

.dashboard-btn:hover {
  background-color: #43a047;
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
  transform: translateY(-2px);
}

@media (max-width: 600px) {
  .card-content {
    padding: 30px 20px;
  }
  
  .success-title {
    font-size: 24px;
  }
  
  .success-message {
    font-size: 14px;
  }
  
  .success-icon {
    width: 70px;
    height: 70px;
  }
  
  .success-icon mat-icon {
    font-size: 40px;
    height: 40px;
    width: 40px;
  }
}
