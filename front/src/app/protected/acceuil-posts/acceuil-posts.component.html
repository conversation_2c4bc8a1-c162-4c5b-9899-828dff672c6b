<div class="main-container">
  <div class="header-container">
    <div class="title-add-container">
      <button mat-icon-button class="add-button" (click)="addPost()" aria-label="Ajouter un post" matTooltip="Ajouter un post">
        <mat-icon>add</mat-icon>
      </button>
      <h2 class="posts-title">Liste des Posts</h2>
    </div>
    <mat-form-field appearance="fill" class="search-bar">
      <input matInput [(ngModel)]="searchTerm" (input)="filterPosts()" placeholder="Rechercher un post">
      <mat-icon matSuffix class="search-icon">search</mat-icon>
    </mat-form-field>
  </div>

  <div class="post-cards-container">
    <mat-card *ngFor="let post of filteredPosts" class="post-card">
      <mat-card-header class="post-card-header">
        <!-- Logo du workspace -->
        <img
          *ngIf="post.workspace?.logoUrl"
          mat-card-avatar
          [src]="getLogoUrl(post.workspace?.logoUrl)"
          [alt]="post.workspace?.name || 'Workspace'"
          class="avatar-icon"
          (error)="handleImageError($event)"
        />
        <mat-icon
          *ngIf="!post.workspace?.logoUrl"
          mat-card-avatar
          class="avatar-icon"
        >
          business
        </mat-icon>

        <mat-card-title>{{ post.titre }}</mat-card-title>
        <mat-card-subtitle>
          <div class="workspace-info" *ngIf="post.workspace?.name">
            <span class="workspace-name">{{ post.workspace?.name }}</span>
          </div>
          <div class="post-meta">
            <span>Publié par {{ post.userId || 'Utilisateur inconnu' }}</span>
            <span class="post-date">{{ post.datePublication | date:'dd/MM/yyyy HH:mm' }}</span>
          </div>
        </mat-card-subtitle>
      </mat-card-header>
      <mat-card-content>
        <p class="description">{{ post.description }}</p>
        <p class="info">Location : {{ post.location }}</p>
        <p class="info">Profil: {{ post.profileRequest }}</p>
        <p class="info">Type de contrat: {{ post.contractType }}</p>
      </mat-card-content>
      <mat-card-actions class="post-actions">
        <button *ngIf="post.userId"
                mat-button
                color="primary"
                (click)="getCandidates(post.id, post.userId)"
                matTooltip="Voir les candidats">
          <mat-icon>group</mat-icon>
          Voir les candidats
        </button>

        <button *ngIf="!post.userId"
                mat-button
                color="warn"
                disabled
                matTooltip="Aucun candidat disponible">
          <mat-icon>block</mat-icon>
          Pas de candidats
        </button>

        <button mat-icon-button class="btn-edit" (click)="editPost(post.id)" aria-label="Modifier le post" matTooltip="Modifier">
          <mat-icon>edit</mat-icon>
        </button>
        <button mat-icon-button class="btn-delete" (click)="deletePost(post)" aria-label="Supprimer le post" matTooltip="Supprimer">
          <mat-icon>delete</mat-icon>
        </button>
        <button mat-icon-button class="btn-archive" (click)="archivePost(post)" aria-label="Archiver le post" matTooltip="Archiver">
          <mat-icon>archive</mat-icon>
        </button>
      </mat-card-actions>
    </mat-card>
  </div>

  <div class="paginator-container">
    <mat-paginator [length]="length"
                   [pageSize]="pageSize"
                   [pageIndex]="pageIndex"
                   (page)="onPageChange($event)"
                   class="paginator">
    </mat-paginator>
  </div>
</div>
<app-candidates-list *ngIf="selectedPostId" [postId]="selectedPostId!"></app-candidates-list>
<button *ngIf="selectedPostId" (click)="closeCandidates()">Fermer la liste</button>
