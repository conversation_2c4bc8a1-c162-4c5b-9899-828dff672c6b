import { Component, OnInit } from '@angular/core';
import { MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { FormsModule } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { MatTooltipModule } from '@angular/material/tooltip';
import { NgForOf, NgClass, DatePipe, NgIf } from '@angular/common';
import { PostsService } from '../../services/posts/posts.service';
import { CandidatesListComponent } from '../candidates-list/candidates-list.component';
import { environment } from '../../../environments/environment';
import { ConfirmationService } from '../../services/confirmation/confirmation.service';
import { WorkspaceService } from '../../services/workspace/workspace.service';
import { UserRoleService } from '../../services/user-role/user-role.service';

type Post = {
  location: string;
  id: string;
  titre: string;
  description: string;
  entreprise: string;
  userId: string | null;
  profileRequest: string;
  contractType: string;
  datePublication: string;
  archived: boolean;
  createdBy?: string; // Nom de l'utilisateur qui a créé le post
  userEmail?: string; // Email de l'utilisateur
  userProfilePicture?: string; // URL de la photo de profil
  workspace?: {
    id: string;
    name: string;
    logoUrl: string | null;
  };
};

@Component({
  selector: 'app-acceuil-posts',
  standalone: true,
  imports: [
    MatCardModule,
    MatPaginatorModule,
    NgForOf,
    MatIconModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    FormsModule,
    MatTooltipModule,
    DatePipe,
    RouterModule,
    CandidatesListComponent,
    NgIf
  ],
  templateUrl: './acceuil-posts.component.html',
  styleUrls: ['./acceuil-posts.component.css']
})
export class AcceuilPostsComponent implements OnInit {
  posts: Post[] = [];
  filteredPosts: Post[] = [];
  pageSize = 4;
  pageIndex = 0;
  length = 0;
  searchTerm = '';
  isLoading = false;
  public selectedPostId: string | null = null; // Changé en public

  assetsUrl = environment.assetsUrl;

  constructor(
    private postsService: PostsService,
    private router: Router,
    private confirmationService: ConfirmationService,
    private workspaceService: WorkspaceService,
    private userRoleService: UserRoleService
  ) {}

  /**
   * Construit l'URL complète du logo
   */
  getLogoUrl(logoPath: string | null | undefined): string {
    if (!logoPath) return 'assets/images/placeholder-logo.png';

    // Vérifier si le chemin est une URL complète
    if (logoPath.startsWith('http://') || logoPath.startsWith('https://')) {
      return logoPath;
    }

    // Construire l'URL avec le chemin du serveur
    const fullPath = this.assetsUrl + (logoPath.startsWith('/') ? '' : '/') + logoPath;
    return fullPath;
  }

  /**
   * Gère les erreurs de chargement d'image
   */
  handleImageError(event: any): void {
    console.log('Erreur de chargement d\'image');
    const imgElement = event.target;
    const parent = imgElement.parentNode;

    // Créer une icône mat-icon comme fallback
    const iconElement = document.createElement('mat-icon');
    iconElement.className = 'avatar-icon';
    iconElement.textContent = 'business';

    // Remplacer l'image par l'icône
    if (parent) {
      parent.replaceChild(iconElement, imgElement);
    }
  }

  ngOnInit(): void {
    // Vérifier si l'utilisateur est un recruteur avant de charger les posts
    this.preserveRecruiterRole();
    this.loadPosts();
  }

  /**
   * Vérifie si l'utilisateur est un recruteur et a un workspace actif
   * Si non, le redirige vers l'accueil car cette page est réservée aux recruteurs
   */
  private preserveRecruiterRole(): void {
    // Récupérer l'ID du workspace actif
    const workspaceId = this.workspaceService.getActiveWorkspaceId() ||
                        this.workspaceService.getSelectedWorkspaceId() ||
                        sessionStorage.getItem('Workspace-Id') ||
                        localStorage.getItem('Workspace-Id');

    if (workspaceId) {
      // Forcer le mode recruteur car cette page est réservée aux recruteurs
      console.log('AcceuilPostsComponent - Forçage du mode recruteur car page réservée aux recruteurs');
      this.userRoleService.forceUserAsRecruiter(workspaceId);
    } else {
      // Si l'utilisateur n'a pas de workspace, le rediriger vers l'accueil
      console.log('AcceuilPostsComponent - Utilisateur sans workspace, redirection vers l\'accueil');
      this.router.navigate(['/acceuil']);
    }
  }

  loadPosts(): void {
    this.isLoading = true;
    this.postsService.getMyPosts(this.pageIndex, this.pageSize).subscribe({
      next: (response: { content: Post[], totalElements: number }) => {
        this.posts = response.content;
        this.filteredPosts = this.posts;
        this.length = response.totalElements;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Erreur lors du chargement des posts:', error);
        this.isLoading = false;
      }
    });
  }

  onPageChange(event: PageEvent): void {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.loadPosts();
  }

  filterPosts(): void {
    if (!this.searchTerm.trim()) {
      this.filteredPosts = this.posts;
      return;
    }

    this.filteredPosts = this.posts.filter(post =>
      post.titre.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
      post.description.toLowerCase().includes(this.searchTerm.toLowerCase())
    );
  }

  addPost(): void {
    this.router.navigate(['/posts']);
  }

  viewPost(post: Post): void {
    this.router.navigate(['/posts', post.id]);
  }

  editPost(postId: string): void {
    this.router.navigate(['/edit-post', postId]);
  }

  deletePost(post: Post): void {
    this.confirmationService.confirmDeletion(`le post "${post.titre}"`).subscribe(confirmed => {
      if (confirmed) {
        this.postsService.deletePost(post.id).subscribe({
          next: () => {
            this.posts = this.posts.filter(p => p.id !== post.id);
            this.filteredPosts = this.filteredPosts.filter(p => p.id !== post.id);
            this.length--;
            console.log('Post supprimé:', post.id);
          },
          error: (error: any) => {
            console.error('Erreur lors de la suppression du post:', error);
          }
        });
      }
    });
  }

  archivePost(post: Post): void {
    this.confirmationService.confirmArchive(`le post "${post.titre}"`).subscribe(confirmed => {
      if (confirmed) {
        this.postsService.archivePost(post.id).subscribe({
          next: () => {
            this.posts = this.posts.filter(p => p.id !== post.id);
            this.filteredPosts = this.filteredPosts.filter(p => p.id !== post.id);
            this.length--;
            console.log('Post archivé:', post.id);
          },
          error: (error: any) => {
            console.error('Erreur lors de l\'archivage du post:', error);
          }
        });
      }
    });
  }
  getCandidates(postId: string, userId: string | null | undefined): void {
    if (!postId || !userId) {
      console.error('Impossible d\'afficher les candidats : informations manquantes.', { postId, userId });
      return;
    }
    this.router.navigate(['/candidat', postId]);
  }


  closeCandidates(): void {
    this.selectedPostId = null;
  }
}
