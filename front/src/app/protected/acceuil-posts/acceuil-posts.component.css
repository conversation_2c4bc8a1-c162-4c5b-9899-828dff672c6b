/* Variables de couleurs et design - Style LinkedIn */
:host {
  /* Palette de couleurs principale - Bleu professionnel */
  --primary-color: #001660;
  --primary-light: rgba(0, 22, 96, 0.08);
  --primary-lighter: rgba(0, 22, 96, 0.04);
  --primary-dark: #000d33;

  /* Palette d'accent - Orange subtil */
  --accent-color: #FF6B00;
  --accent-light: rgba(255, 107, 0, 0.08);
  --accent-dark: #e05e00;

  /* Palette de texte - Haute lisibilité */
  --text-primary: #191919;
  --text-secondary: #666666;
  --text-light: #040140;
  --text-on-primary: #ffffff;
  --text-on-accent: #ffffff;

  /* Palette de fond - Minimaliste */
  --background-main: #f5f5f5;
  --background-card: #ffffff;
  --background-subtle: #fafafa;

  /* Bordures et séparateurs - Subtils */
  --border-light: #e0e0e0;
  --border-medium: #d0d0d0;
  --divider: #eeeeee;

  /* Ombres et élévations - Douces */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.08);
  --shadow-md: 0 2px 6px rgba(0, 0, 0, 0.12);
  --shadow-lg: 0 4px 12px rgba(0, 0, 0, 0.08);

  /* Rayons de bordure - Consistants */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-full: 9999px;

  /* Espacement - Respirant */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-xxl: 48px;

  /* Transitions - Subtiles */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.2s ease;
}

/* Global container */
.main-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-xl);
  background: var(--background-main);
  min-height: 100vh;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  color: var(--text-primary);
  letter-spacing: 0;
  line-height: 1.5;
  position: relative;
}

/* Header */
.header-container {
  padding: var(--spacing-lg);
  background: var(--background-card);
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: var(--shadow-sm);
  margin-bottom: var(--spacing-xl);
  position: relative;
  border-bottom: 3px solid var(--primary-color);
}

.header-container:hover {
  box-shadow: var(--shadow-md);
}

/* Title and add button container */
.title-add-container {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

/* Title */
.posts-title {
  font-size: 1.75rem;
  font-weight: 600;
  color: var(--primary-color);
  margin: 0;
}

/* Add button */
.add-button {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  background: var(--accent-color);
  color: var(--text-on-accent);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color var(--transition-normal), transform var(--transition-normal);
  cursor: pointer;
  border: none;
  outline: none;
}

.add-button:hover {
  background: var(--accent-dark);
  transform: translateY(-2px);
}

.add-button:active {
  transform: translateY(0);
}

.add-button mat-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
}

/* Search */
.search-bar {
  width: 100%;
  max-width: 320px; /* Slightly wider */
  border-radius: 6px;
  background: #FFFFFF;
  border: 1px solid #E5E7EB; /* Light gray */
  transition: all 0.2s ease;
}

.search-bar:hover {
  border-color: #0A66C2; /* Blue border on hover */
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
}

.search-bar .mat-form-field-flex {
  padding: 0 12px;
}

.search-bar input {
  font-size: 0.95rem;
  color: #1C2526;
  font-weight: 400;
}

.search-icon {
  color: #6B7280; /* Gray */
  font-size: 20px;
  transition: color 0.2s ease;
}

.search-bar:hover .search-icon {
  color: #0A66C2; /* Blue on hover */
}

/* Cards container */
.post-cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 20px;
  padding: 24px 0;
  align-items: stretch; /* Étire les éléments pour qu'ils aient la même hauteur */
}

/* Card */
.post-card {
  margin-bottom: var(--spacing-lg);
  border-radius: var(--radius-md);
  background: var(--background-card);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: box-shadow var(--transition-normal), transform var(--transition-normal);
  border: 1px solid var(--border-light);
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 350px; /* Hauteur minimale pour uniformiser les cartes */
}

.post-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-light);
}

/* Card header */
.post-card-header {
  margin-bottom: var(--spacing-sm);
  display: flex;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-md) 0;
  gap: var(--spacing-md);
}

/* Avatar/Logo */
.avatar-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  object-fit: cover;
  background: var(--primary-color);
  border: 1px solid var(--border-light);
}

.avatar-container {
  position: relative;
}

/* Styles pour les informations du workspace */
.workspace-info {
  margin-bottom: var(--spacing-xs);
}

.workspace-name {
  font-weight: 600;
  color: var(--primary-color);
  font-size: 14px;
}

.post-meta {
  display: flex;
  flex-direction: column;
  font-size: 12px;
  color: var(--text-secondary);
  line-height: 1.4;
}

.post-date {
  margin-top: 2px;
  font-size: 12px;
  color: var(--text-light);
}



.mat-card-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  flex-grow: 1;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.post-card:hover .mat-card-title {
  color: var(--primary-color);
}

mat-card-subtitle {
  margin-top: var(--spacing-xs);
  color: var(--text-secondary);
  font-weight: 400;
}

/* Card content */
.mat-card-content {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.description {
  margin: var(--spacing-md) 0;
  white-space: pre-line;
  font-size: 0.9rem;
  color: var(--text-primary);
  line-height: 1.5;
  padding: 0 var(--spacing-md);
  font-weight: 400;
  text-align: justify;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  flex-grow: 1;
}

/* Informations complémentaires */
.info {
  margin: var(--spacing-sm) 0 var(--spacing-md);
  color: var(--text-secondary);
  font-weight: 500;
  font-size: 0.85rem;
  padding: 0 var(--spacing-md);
  line-height: 1.4;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.info mat-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
  color: var(--primary-color);
}

/* Actions */
.post-actions {
  display: flex;
  justify-content: flex-start;
  padding: var(--spacing-md);
  border-top: 1px solid var(--border-light);
  background: var(--background-subtle);
  border-radius: 0 0 var(--radius-md) var(--radius-md);
  gap: var(--spacing-sm);
  margin-top: auto; /* Pousse les actions vers le bas */
}

.post-actions button {
  margin-right: var(--spacing-xs);
  transition: transform var(--transition-normal);
}

.post-actions button:hover {
  transform: translateY(-2px);
}

.btn-details,
.btn-edit,
.btn-delete,
.btn-archive {
  width: 36px;
  height: 36px;
  border-radius: var(--radius-full);
  background: var(--background-card);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color var(--transition-normal);
  border: 1px solid var(--border-light);
}

.btn-details {
  color: var(--primary-color);
}

.btn-edit {
  color: var(--primary-color);
}

.btn-delete {
  color: #EF4444;
}

.btn-archive {
  color: var(--accent-color);
}

.btn-details:hover,
.btn-edit:hover {
  background-color: var(--primary-light);
}

.btn-delete:hover {
  background-color: rgba(239, 68, 68, 0.1);
}

.btn-archive:hover {
  background-color: var(--accent-light);
}

.btn-details mat-icon,
.btn-edit mat-icon,
.btn-delete mat-icon,
.btn-archive mat-icon {
  font-size: 18px;
  width: 18px;
  height: 18px;
}





/* Paginator */
.paginator-container {
  display: flex;
  justify-content: center;
  margin-top: var(--spacing-xl);
  padding: var(--spacing-md) 0;
}

.paginator {
  width: 100%;
  max-width: 600px;
  display: flex;
  justify-content: center;
  border-radius: var(--radius-md);
  padding: var(--spacing-sm);
  background: var(--background-card);
  border: 1px solid var(--border-light);
}

.paginator ::ng-deep .mat-paginator-container {
  justify-content: center;
  font-size: 0.9rem;
  color: var(--text-primary);
  background: transparent;
  min-height: 48px;
}

.paginator ::ng-deep .mat-paginator-page-size-label,
.paginator ::ng-deep .mat-paginator-range-label {
  font-weight: 400;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.paginator ::ng-deep .mat-select-value {
  color: var(--primary-color);
  font-weight: 500;
}

.paginator ::ng-deep .mat-paginator-icon {
  color: var(--primary-color);
  font-size: 20px;
  width: 20px;
  height: 20px;
  transition: color var(--transition-normal);
}

.paginator ::ng-deep .mat-paginator-icon:hover {
  color: var(--accent-color);
}

/* Responsive */
@media (max-width: 1024px) {
  .main-container {
    padding: 24px;
  }

  .header-container {
    padding: 16px;
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .search-bar {
    max-width: 100%;
  }

  .post-cards-container {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }

  .paginator-container {
    padding: 12px 0;
  }
}

@media (max-width: 640px) {
  .main-container {
    padding: 16px;
  }

  .header-container {
    padding: 12px;
  }

  .posts-title {
    font-size: 1.75rem;
  }

  .posts-title::after {
    width: 60px;
  }

  .add-button {
    width: 40px;
    height: 40px;
  }

  .add-button mat-icon {
    font-size: 20px;
    width: 20px;
    height: 20px;
  }

  .post-card-header {
    padding: 12px;
    gap: 8px;
  }

  .avatar-icon {
    font-size: 28px;
    padding: 6px;
  }

  .mat-card-title {
    font-size: 1.1rem;
  }

  .description,
  .info {
    font-size: 0.85rem;
  }

  .post-actions {
    padding: 8px 12px;
  }

  .btn-details,
  .btn-edit,
  .btn-delete,
  .btn-archive {
    width: 36px;
    height: 36px;
  }

  .btn-details mat-icon,
  .btn-edit mat-icon,
  .btn-delete mat-icon,
  .btn-archive mat-icon {
    font-size: 18px;
    width: 18px;
    height: 18px;
  }

  .paginator-container {
    padding: 8px 0;
  }
}
