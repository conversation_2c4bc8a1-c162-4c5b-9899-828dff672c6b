import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { PostsService } from '../../services/posts/posts.service';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { NgIf } from '@angular/common';
import {MatSnackBar} from '@angular/material/snack-bar';

@Component({
  selector: 'app-edit-post',
  templateUrl: './edit-post.component.html',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatCardModule,
    MatIconModule,
    MatProgressSpinnerModule,
    NgIf
  ],
  styleUrls: ['./edit-post.component.css']
})
export class EditPostComponent implements OnInit {
  postForm: FormGroup;
  error: string | null = null;
  isLoading: boolean = true;
  constructor(
    private postService: PostsService,
    private route: ActivatedRoute,
    protected router: Router,
    private fb: FormBuilder,
    private snackBar: MatSnackBar // Injecte MatSnackBar
  ) {
    this.postForm = this.fb.group({
      jobTitle: ['', Validators.required],
      profileRequest: [''],
      contractType: [''],
      location: ['', Validators.required],
      description: ['', Validators.required]
    });
  }

  ngOnInit() {
    const postId = this.route.snapshot.paramMap.get('id');
    console.log('Post ID:', postId);

    if (postId) {
      this.loadPost(postId);
    } else {
      this.error = 'ID du post non fourni';
      this.isLoading = false;
      this.postForm.enable();
    }
  }

  loadPost(postId: string) {
    this.isLoading = true;
    console.log('Loading post with ID:', postId);
    this.postService.getPostById(postId).subscribe({
      next: (data) => {
        console.log('Post data received:', JSON.stringify(data, null, 2));
        this.postForm.patchValue({
          jobTitle: data.titre || '',
          profileRequest: data.profileRequest || '',
          contractType: data.contractType || '',
          location: data.entreprise || '',
          description: data.description || ''
        });
        this.isLoading = false;
      },
      error: (err) => {
        console.error('Error fetching post:', err);
        console.log('Error details:', JSON.stringify(err.error, null, 2));
        if (err.status === 404) {
          this.error = 'Post non trouvé';
        } else if (err.status === 500) {
          this.error = 'Erreur serveur interne. Veuillez réessayer plus tard.';
        } else if (err.status === 403) {
          this.error = 'Accès refusé. Vérifiez vos permissions.';
        } else {
          this.error = 'Erreur inconnue: ' + (err.message || 'Veuillez réessayer.');
        }
        this.isLoading = false;
        this.postForm.enable();
      }
    });
  }

  savePost() {
    const postId = this.route.snapshot.paramMap.get('id');
    if (postId && this.postForm.valid) {
      const updatedPost = {
        titre: this.postForm.value.jobTitle,
        description: this.postForm.value.description,
        entreprise: this.postForm.value.location,
        profileRequest: this.postForm.value.profileRequest,
        contractType: this.postForm.value.contractType
      };
      console.log('Saving post:', JSON.stringify(updatedPost, null, 2));
      this.isLoading = true;
      this.postService.updatePost(postId, updatedPost).subscribe({
        next: () => {
          console.log('Post updated successfully');
          // Affiche une alerte de succès
          this.snackBar.open('Post modifié avec succès !', 'Fermer', {
            duration: 3000, // Affiche pendant 3 secondes
            horizontalPosition: 'center',
            verticalPosition: 'top',
            panelClass: ['success-snackbar'] // Classe CSS personnalisée
          });
          this.isLoading = false;
          this.router.navigate(['/acceuilposts']);
        },
        error: (err) => {
          console.error('Error updating post:', err);
          this.error = 'Erreur lors de la mise à jour du post';
          this.isLoading = false;
          // Optionnel : alerte pour l'erreur
          this.snackBar.open('Erreur lors de la mise à jour du post.', 'Fermer', {
            duration: 3000,
            panelClass: ['error-snackbar']
          });
        }
      });
    } else {
      this.error = 'Veuillez remplir tous les champs requis';
      this.snackBar.open('Veuillez remplir tous les champs requis.', 'Fermer', {
        duration: 3000,
        panelClass: ['error-snackbar']
      });
    }
  }

  cancel() {
    this.router.navigate(['/posts']);
  }
}
