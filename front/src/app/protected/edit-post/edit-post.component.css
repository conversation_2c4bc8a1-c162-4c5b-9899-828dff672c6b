/* Container for the entire edit post form */
.edit-post-container {
  padding: 32px 16px;
  max-width: 700px; /* Slightly wider for better form readability */
  margin: 0 auto;
  background-color: #f5f7fa; /* Subtle background for contrast */
  min-height: 100vh; /* Full viewport height for consistency */
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* Styling for the Material card */
.workspace-card {
  width: 100%;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); /* Softer shadow for depth */
  border-radius: 12px; /* Rounded corners for modern look */
  background-color: #ffffff;
  padding: 24px;
}

/* Centered header styling */
.header-center {
  justify-content: center;
  padding-bottom: 16px;
}

mat-card-title {
  font-size: 24px;
  font-weight: 600;
  color: #1a3c34; /* Darker, professional color */
  letter-spacing: 0.5px;
}

/* Form field styling */
mat-form-field {
  margin-bottom: 20px;
}

.full-width {
  width: 100%;
}

/* Input field appearance */
mat-form-field.appearance-outline .mat-form-field-outline {
  background-color: #ffffff;
  border-radius: 8px;
}

mat-form-field .mat-form-field-label {
  color: #4a5568; /* Neutral gray for labels */
  font-weight: 500;
}

mat-form-field .mat-input-element {
  font-size: 16px;
  color: #2d3748;
}

/* Error message styling */
mat-error {
  font-size: 14px;
  color: #e53e3e; /* Vibrant red for errors */
  margin-top: 4px;
}

/* Custom error message */
.error-message {
  color: #e53e3e;
  font-size: 14px;
  text-align: center;
  margin: 16px 0;
  background-color: #fff5f5; /* Light red background for emphasis */
  padding: 12px;
  border-radius: 8px;
}

/* Loading spinner */
.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh; /* Center vertically in viewport */
}

mat-spinner {
  margin: 0 auto;
}

/* Button styling */
mat-card-actions.center-actions {
  display: flex;
  justify-content: center;
  gap: 16px; /* More spacing between buttons */
  padding: 16px 0;
}

button[mat-raised-button] {
  padding: 0 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s ease; /* Smooth hover effect */
}

button[color="primary"] {
  background-color: #2b6cb0; /* Professional blue */
}

button[color="primary"]:hover {
  background-color: #2c5282; /* Darker blue on hover */
}

button[color="warn"] {
  background-color: #c53030; /* Professional red */
}

button[color="warn"]:hover {
  background-color: #9b2c2c; /* Darker red on hover */
}

button[disabled] {
  background-color: #e2e8f0 !important;
  color: #a0aec0 !important;
  cursor: not-allowed;
}

/* Icon alignment */
mat-icon {
  vertical-align: middle;
  margin-right: 8px; /* Space between icon and text */
}

/* Responsive adjustments */
@media (max-width: 600px) {
  .edit-post-container {
    padding: 16px;
  }

  .workspace-card {
    padding: 16px;
  }

  mat-card-title {
    font-size: 20px;
  }

  button[mat-raised-button] {
    padding: 0 16px;
    font-size: 14px;
  }
}
