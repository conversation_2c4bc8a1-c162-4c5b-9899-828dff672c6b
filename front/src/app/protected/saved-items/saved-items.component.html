<div class="saved-items-container">
  <mat-card class="saved-items-header-card">
    <mat-card-header>
      <mat-card-title>Éléments enregistrés</mat-card-title>
      <mat-card-subtitle>Retrouvez ici toutes les publications que vous avez sauvegardées</mat-card-subtitle>
    </mat-card-header>
  </mat-card>

  <!-- Affichage du chargement -->
  <div class="loading-container" *ngIf="isLoading">
    <mat-spinner diameter="40"></mat-spinner>
    <p>Chargement de vos éléments sauvegardés...</p>
  </div>

  <!-- Message si aucun élément sauvegardé -->
  <mat-card class="empty-state" *ngIf="!isLoading && savedPosts.length === 0">
    <mat-card-content>
      <mat-icon class="empty-icon">bookmark_border</mat-icon>
      <h3>Aucun élément sauvegardé</h3>
      <p>Les publications que vous enregistrerez apparaîtront ici.</p>
    </mat-card-content>
  </mat-card>

  <!-- Liste des éléments sauvegardés -->
  <div class="saved-posts-list" *ngIf="!isLoading && savedPosts.length > 0">
    <mat-card class="saved-post-card" *ngFor="let post of savedPosts">
      <mat-card-header>
        <div mat-card-avatar class="post-avatar">
          <mat-icon *ngIf="!post.workspace?.logoUrl">business</mat-icon>
          <img *ngIf="post.workspace?.logoUrl" [src]="post.workspace.logoUrl" alt="Logo" />
        </div>
        <mat-card-title>{{ post.titre }}</mat-card-title>
        <mat-card-subtitle>
          <span *ngIf="post.workspace?.name">{{ post.workspace.name }}</span>
          <span class="post-date">{{ post.datePublication | date: 'medium' }}</span>
        </mat-card-subtitle>
      </mat-card-header>

      <mat-card-content>
        <p class="post-description">{{ post.description }}</p>
      </mat-card-content>

      <mat-card-actions>
        <button mat-button color="primary" (click)="navigateToPostDetail(post.id, post.titre)">
          <mat-icon>visibility</mat-icon> Voir détails
        </button>
        <button mat-button color="warn" (click)="removeFromSaved(post)">
          <mat-icon>bookmark_remove</mat-icon> Retirer
        </button>
      </mat-card-actions>
    </mat-card>
  </div>
</div>
