import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { SavedItemsService } from '../../services/saved-items/saved-items.service';

@Component({
  selector: 'app-saved-items',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatIconModule,
    MatButtonModule,
    MatProgressSpinnerModule,
  ],
  templateUrl: './saved-items.component.html',
  styleUrls: ['./saved-items.component.scss'],
})
export class SavedItemsComponent implements OnInit {
  userId: number | null = null;
  savedPosts: any[] = [];
  isLoading: boolean = true;

  constructor(
    private route: ActivatedRoute,
    private savedItemsService: SavedItemsService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.route.paramMap.subscribe(params => {
      const id = params.get('userId');
      this.userId = id && !isNaN(Number(id)) ? Number(id) : null;
      console.log('User ID for saved items:', this.userId);

      // Charger les publications sauvegardées
      this.loadSavedPosts();
    });
  }

  /**
   * Charge les publications sauvegardées
   */
  loadSavedPosts(): void {
    this.isLoading = true;
    this.savedPosts = this.savedItemsService.getSavedPosts();
    this.isLoading = false;
    console.log('Publications sauvegardées chargées:', this.savedPosts);
  }

  /**
   * Supprime une publication des éléments sauvegardés
   */
  removeFromSaved(post: any): void {
    this.savedItemsService.removeItem(post.id).subscribe(
      (result) => {
        if (result) {
          // Recharger les publications sauvegardées
          this.loadSavedPosts();
        }
      }
    );
  }

  /**
   * Navigue vers les détails d'une publication
   */
  navigateToPostDetail(postId: string, title: string): void {
    this.router.navigate(['/posts', postId]);
  }
}
