.saved-items-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
}

.saved-items-header-card {
  margin-bottom: 24px;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.saved-items-header-card mat-card-title {
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8px;
}

.saved-items-header-card mat-card-subtitle {
  font-size: 16px;
  color: rgba(0, 0, 0, 0.6);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 0;
}

.loading-container p {
  margin-top: 16px;
  font-size: 16px;
  color: rgba(0, 0, 0, 0.6);
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 64px 24px;
  text-align: center;
  border-radius: 10px;
  background-color: #f9fafb;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.empty-icon {
  font-size: 64px;
  width: 64px;
  height: 64px;
  color: rgba(0, 0, 0, 0.3);
  margin-bottom: 16px;
}

.empty-state h3 {
  font-size: 20px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8px;
}

.empty-state p {
  font-size: 16px;
  color: rgba(0, 0, 0, 0.6);
  max-width: 400px;
}

.saved-posts-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 24px;
}

.saved-post-card {
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.saved-post-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.post-avatar {
  background-color: #f0f7ff;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #0a66c2;
}

.post-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.saved-post-card mat-card-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 4px;
  color: #1a1a1a;
}

.saved-post-card mat-card-subtitle {
  display: flex;
  flex-direction: column;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.6);
}

.post-date {
  font-size: 12px;
  margin-top: 4px;
}

.post-description {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.8);
  line-height: 1.5;
  margin: 0;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

mat-card-content {
  flex: 1;
  padding: 16px;
}

mat-card-actions {
  display: flex;
  justify-content: space-between;
  padding: 8px 16px 16px;
}

mat-card-actions button {
  display: flex;
  align-items: center;
}

mat-card-actions mat-icon {
  margin-right: 8px;
}

@media (max-width: 768px) {
  .saved-posts-list {
    grid-template-columns: 1fr;
  }
  
  .saved-items-container {
    padding: 16px;
  }
}
