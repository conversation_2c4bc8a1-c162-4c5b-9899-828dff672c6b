.job-post-container {
  max-width: 800px;
  margin: 20px auto;
  padding: 0;
  background-color: #fff;
  font-family: 'Inter', 'Arial', sans-serif; /* Modern font stack */
  color: #2d3748; /* Darker text for better contrast */
}

.job-header {
  position: relative;
  margin-bottom: 30px; /* More breathing room */
}

.header-content {
  margin-top: 50px; /* Space for the button */
}

.job-title {
  font-size: 36px;
  color: #1a202c; /* Darker shade for headings */
  margin: 0 0 12px;
  font-weight: 800; /* Bolder for a modern look */
  text-transform: uppercase;
  line-height: 1.2;
}

.job-description {
  font-size: 16px;
  color: #4a5568; /* Softer gray for body text */
  line-height: 1.8; /* Better readability */
  margin: 0 0 20px;
}

.ratings {
  font-size: 14px;
  color: #4a5568;
  text-align: right;
}

.ratings p {
  margin: 8px 0;
  transition: transform 0.2s ease; /* Subtle hover animation */
}

.ratings p:hover {
  transform: translateX(-5px); /* Slight shift on hover */
}

.stars {
  color: #f6c344; /* Gold color for stars */
  font-size: 12px;
}

.apply-btn {
  position: absolute;
  top: 0;
  right: 0;
  background: linear-gradient(90deg, #c2d6ed, #040140); /* Modern blue-to-purple gradient */
  color: #fff;
  border: none;
  padding: 10px 20px; /* Slightly larger for better clickability */
  border-radius: 6px; /* Softer corners */
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
  cursor: pointer;
  transition: background 0.3s ease, transform 0.1s ease;
}

.apply-btn:hover {
  background: linear-gradient(90deg, #040140, #80b3f3); /* Darker gradient on hover */
  transform: translateY(-2px); /* Slight lift effect */
}

.job-details, .job-requirements, .job-nice-to-have, .workspace-info {
  margin-bottom: 40px; /* More spacing between sections */
}

.job-details h3, .job-requirements h3, .job-nice-to-have h3, .workspace-info h3 {
  font-size: 18px;
  color: #1a202c;
  margin-bottom: 15px;
  font-weight: 700;
  text-transform: uppercase;
  position: relative;
}

.job-details h3::after, .job-requirements h3::after, .job-nice-to-have h3::after, .workspace-info h3::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: -5px;
  width: 50px;
  height: 3px;
  background: #4f46e5; /* Blue underline for headings */
}

.job-details ul, .job-requirements ul, .job-nice-to-have ul {
  list-style: none; /* Remove default bullets */
  padding-left: 0;
}

.job-details li, .job-requirements li, .job-nice-to-have li {
  font-size: 16px;
  color: #4a5568;
  line-height: 1.8;
  margin: 10px 0;
  position: relative;
  padding-left: 20px;
}

.job-details li::before, .job-requirements li::before, .job-nice-to-have li::before {
  content: '•'; /* Custom bullet */
  position: absolute;
  left: 0;
  color: #4f46e5; /* Blue bullet */
  font-size: 20px;
}

.section-divider {
  border: 0;
  border-top: 1px solid #e5e7eb; /* Lighter gray for divider */
  margin: 30px 0;
}

.workspace-info p {
  font-size: 16px;
  color: #4a5568;
  line-height: 1.8;
  margin: 10px 0;
}

.workspace-logo {
  margin-top: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); /* Subtle shadow for the logo */
}

/* Responsive adjustments */
@media (max-width: 600px) {
  .job-post-container {
    margin: 10px;
    padding: 0;
  }

  .header-content {
    margin-top: 60px; /* Adjust for button on mobile */
  }

  .ratings {
    text-align: left;
  }

  .apply-btn {
    position: static;
    margin-bottom: 15px;
    width: 100%;
    text-align: center;
  }

  .job-title {
    font-size: 28px;
  }

  .job-details h3, .job-requirements h3, .job-nice-to-have h3, .workspace-info h3 {
    font-size: 16px;
  }
}
