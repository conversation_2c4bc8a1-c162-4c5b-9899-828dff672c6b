import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import {PostsService} from '../../services/posts/posts.service';
import {DatePipe, NgIf} from '@angular/common';

@Component({
  selector: 'app-post-detail',
  templateUrl: './post-detail.component.html',
  styleUrls: ['./post-detail.component.css'],
  imports: [
    NgIf,
  ]
})
export class PostDetailComponent implements OnInit {
  postId!: string;
  post?: any;

  constructor(
    private route: ActivatedRoute,
    private postsService: PostsService
  ) {}

  ngOnInit() {
    this.postId = this.route.snapshot.paramMap.get('id')!;
    this.postsService.getPostById(this.postId).subscribe((data) => {
      console.log('Post data:', data); // Add this to debug
      this.post = data;
    });
  }
}
