<div class="job-post-container" *ngIf="post">
  <div class="job-header">
    <button class="apply-btn">APPLY NOW</button>
    <div class="header-content">
      <h2 class="job-title">{{ post.titre }}</h2>
      <p class="job-description">
        {{ post.description }}
      </p>
      <div class="ratings">
        <p>Customer Relationship <span class="stars">★★★★★</span></p>
        <p>Personal Evolution <span class="stars">★★★★★</span>
        <p>Autonomy <span class="stars">★★★★★</span>
        <p>Administrative Work <span class="stars">★★★★★</span>
        <p>Technical Expertise <span class="stars">★★★★★</span>
      </div>
    </div>
  </div>

  <div class="job-details">
    <h3>RESPONSIBILITY :</h3>
    <ul>
      <li>Concevoir, développer et maintenir des applications web complètes.</li>
      <li>Concevoir et optimiser des bases de données relationnelles et NoSQL.</li>
      <li>Implémenter des API sécurisées et performantes.</li>
      <li>Développer des interfaces utilisateur ergonomiques et responsives.</li>
      <!-- Replace with actual responsibilities from post.responsibilities if available -->
    </ul>
  </div>

  <hr class="section-divider" />

  <div class="job-requirements">
    <h3>MUST HAVE :</h3>
    <ul>
      <li>Langages backend : Node.js, Python, Java, PHP ou autre.</li>
      <li>Frameworks backend : Express.js, Django, Spring Boot, Laravel.</li>
      <li>Langages frontend : JavaScript, TypeScript.</li>
      <li>Frameworks frontend : React.js, Angular, Vue.js.</li>
      <li>Base de données : MySQL, PostgreSQL, MongoDB.</li>
      <li>API & sécurité : REST, GraphQL, JWT, OAuth2.</li>
      <!-- Replace with actual data from post.mustHave if available -->
    </ul>
  </div>

  <div class="job-nice-to-have">
    <h3>NICE TO HAVE :</h3>
    <ul>
      <li>Expérience en DevOps et monitoring (Grafana, Prometheus).</li>
      <li>Connaissance en web3 et blockchain.</li>
      <li>Expérience en mobile (React Native, Flutter).</li>
      <li>Bonne maîtrise des tests automatisés (Jest, Cypress).</li>
      <!-- Replace with actual data from post.niceToHave if available -->
    </ul>
  </div>

  <!-- Info du workspace -->
  <div class="workspace-info" *ngIf="post?.workspace">
    <h3>À PROPOS DE L'ENTREPRISE :</h3>
    <p><strong>Nom :</strong> {{ post.workspace?.name }}</p>
    <p><strong>Description :</strong> {{ post.workspace?.description }}</p>
    <p><strong>Email :</strong> {{ post.workspace?.email }}</p>
    <p><strong>Localisation :</strong> {{ post.workspace?.location }}</p>
    <p><strong>Numéro de téléphone :</strong> {{ post.workspace?.phoneNumber }}</p>
    <img *ngIf="post.workspace?.logoUrl" [src]="post.workspace.logoUrl" width="80" alt="Logo" class="workspace-logo" />
  </div>
</div>
