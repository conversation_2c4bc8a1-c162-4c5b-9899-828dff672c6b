// src/app/protected/edit-recruiter-profile/edit-recruiter-profile.component.ts
import { Component, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { HttpErrorResponse } from '@angular/common/http';
import { UserService } from '../../services/user/user.service';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

interface User {
  firstName?: string;
  lastName?: string;
  email?: string;
  phoneNumber?: string;
  address?: string;
}

interface UserProfile {
  bio?: string;
}

interface Workspace {
  name?: string;
  industry?: string;
  size?: string;
  location?: string;
}

@Component({
  selector: 'app-edit-recruiter-profile',
  templateUrl: './edit-recruiter-profile.component.html',
  styleUrls: ['./edit-recruiter-profile.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatTabsModule,
    MatProgressSpinnerModule
  ]
})
export class EditRecruiterProfileComponent implements OnInit {
  userProfileForm: FormGroup;
  isLoading = false;
  isSaving = false;
  private cdr: any;

  constructor(
    public dialogRef: MatDialogRef<EditRecruiterProfileComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { user: User; userProfile: UserProfile; workspace: Workspace },
    private fb: FormBuilder,
    private userService: UserService,

  ) {
    this.userProfileForm = this.fb.group({
      firstName: ['', Validators.required],
      lastName: ['', Validators.required],
      email: ['', [Validators.required, Validators.email]],
      phoneNumber: [''],
      address: [''],
      workspaceName: [''],
      workspaceIndustry: [''],
      workspaceSize: [''],
      workspaceLocation: ['']
    });
  }



  ngOnInit(): void {
    this.fetchUserProfile();
  }

  loadData() {
    this.isLoading = true;
    setTimeout(() => {
      if (this.data.user) {
        this.userProfileForm.patchValue({
          firstName: this.data.user.firstName || '',
          lastName: this.data.user.lastName || '',
          email: this.data.user.email || '',
          phoneNumber: this.data.user.phoneNumber || '',
          address: this.data.user.address || ''
        });
      }
      if (this.data.workspace) {
        this.userProfileForm.patchValue({
          workspaceName: this.data.workspace.name || '',
          workspaceIndustry: this.data.workspace.industry || '',
          workspaceSize: this.data.workspace.size || '',
          workspaceLocation: this.data.workspace.location || ''
        });
      }
      this.isLoading = false;
    }, 500);
  }

  fetchUserProfile(): void {
    this.isLoading = true;
    this.userService.getUserProfile().subscribe({
      next: (data: any) => {
        console.log('User Profile Fetched:', data);
        // Extract the 'user' property from the fetched data
        const userProfile = data.user || {};
        // Patch the form with the user data
        this.userProfileForm.patchValue({
          firstName: userProfile.firstName || '',
          lastName: userProfile.lastName || '',
          email: userProfile.email || '',
          phoneNumber: userProfile.phoneNumber || '',
          address: userProfile.address || ''
        });
        console.log('Form Values After Patching:', this.userProfileForm.value);
        this.isLoading = false;
        this.cdr.detectChanges();
      },
      error: (err) => {
        console.error('Erreur lors de la récupération du profil:', err);
        this.isLoading = false;
        alert('Une erreur est survenue lors de la récupération des données.');
        this.cdr.detectChanges();
      }
    });
  }

  onSubmit(): void {
    if (this.userProfileForm.valid) {
      this.isSaving = true;
      const updatedProfile: UserProfile = {
        ...this.userProfileForm.value
      };

      console.log('Updated Profile:', updatedProfile);

      this.userService.updateFullUserProfile(updatedProfile).subscribe({
        next: (response) => {
          console.log('Profil mis à jour avec succès:', response);
          this.isSaving = false;
          this.dialogRef.close('updated');
        },
        error: (err) => {
          console.error('Erreur lors de la mise à jour du profil:', err);
          this.isSaving = false;
          alert('Une erreur est survenue lors de la mise à jour du profil.');
        }
      });
    } else {
      console.log('Formulaire invalide:', this.userProfileForm.errors);
    }
  }
}
