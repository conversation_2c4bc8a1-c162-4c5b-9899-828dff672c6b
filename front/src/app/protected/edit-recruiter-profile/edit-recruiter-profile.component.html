<!-- src/app/protected/edit-recruiter-profile/edit-recruiter-profile.component.html -->
<div class="edit-profile-container">
  <div class="modal-header">
    <h2>Modifier le Profil</h2>
    <button mat-icon-button class="close-button" (click)="dialogRef.close()" aria-label="Fe<PERSON><PERSON> la fenêtre">
      <mat-icon>close</mat-icon>
    </button>
  </div>

  <!-- Loading Spinner -->
  <div class="loading-container" *ngIf="isLoading">
    <mat-spinner diameter="40"></mat-spinner>
    <p>Chargement des données...</p>
  </div>

  <!-- Form -->
  <form [formGroup]="userProfileForm" (ngSubmit)="onSubmit()" *ngIf="!isLoading">
    <mat-tab-group mat-align-tabs="start" animationDuration="0ms">
      <!-- Onglet Infos personnelles -->
      <mat-tab label="Infos personnelles">
        <div class="tab-content">
          <div class="form-row">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Prénom</mat-label>
              <input matInput formControlName="firstName" required>
              <mat-error *ngIf="userProfileForm.get('firstName')?.hasError('required')">
                Le prénom est requis
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Nom</mat-label>
              <input matInput formControlName="lastName" required>
              <mat-error *ngIf="userProfileForm.get('lastName')?.hasError('required')">
                Le nom est requis
              </mat-error>
            </mat-form-field>
          </div>

          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Email</mat-label>
            <input matInput formControlName="email" type="email" required>
            <mat-error *ngIf="userProfileForm.get('email')?.hasError('required')">
              L'email est requis
            </mat-error>
            <mat-error *ngIf="userProfileForm.get('email')?.hasError('email')">
              Veuillez entrer un email valide
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Téléphone</mat-label>
            <input matInput formControlName="phoneNumber">
          </mat-form-field>

          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Adresse</mat-label>
            <input matInput formControlName="address">
          </mat-form-field>
        </div>
      </mat-tab>

      <!-- Onglet Entreprise (Workspace) -->
      <mat-tab label="Entreprise">
        <div class="tab-content">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Nom de l'espace de travail</mat-label>
            <input matInput formControlName="workspaceName">
          </mat-form-field>

          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Industrie</mat-label>
            <input matInput formControlName="workspaceIndustry">
          </mat-form-field>

          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Taille</mat-label>
            <input matInput formControlName="workspaceSize">
          </mat-form-field>

          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Localisation</mat-label>
            <input matInput formControlName="workspaceLocation">
          </mat-form-field>
        </div>
      </mat-tab>
    </mat-tab-group>

    <div class="action-buttons">
      <button mat-stroked-button type="button" class="cancel-button" (click)="dialogRef.close()" [disabled]="isSaving">
        Annuler
      </button>
      <button mat-raised-button color="primary" type="submit" [disabled]="userProfileForm.invalid || isSaving">
        <mat-spinner *ngIf="isSaving" diameter="20"></mat-spinner>
        <span *ngIf="!isSaving">Enregistrer les modifications</span>
      </button>
    </div>
  </form>
</div>
