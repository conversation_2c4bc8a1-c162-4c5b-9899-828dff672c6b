import {Component, OnInit} from '@angular/core';
import {WorkspaceService} from '../../services/workspace/workspace.service';
import {ProfileComponent} from '../profile/profile/profile.component';
import {RecruiterProfileComponent} from '../recruiter-profile/recruiter-profile.component';
import {NgIf} from '@angular/common';

@Component({
  selector: 'app-profile-generic',
  imports: [
    ProfileComponent,
    RecruiterProfileComponent,
    NgIf
  ],
  templateUrl: './profile-generic.component.html',
  standalone: true,
  styleUrl: './profile-generic.component.css'
})
export class ProfileGenericComponent implements OnInit{
  currentWorkspaceId: string = '';
  constructor(private workspaceService: WorkspaceService) {
  }
    ngOnInit(): void {
        this.currentWorkspaceId = this.workspaceService.getCurrentWorkspaceId();
    }

}
