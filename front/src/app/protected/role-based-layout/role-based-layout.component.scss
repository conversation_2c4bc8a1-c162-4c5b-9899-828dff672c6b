.layout-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
}

.content-container {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.main-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  transition: all 0.3s ease;
}

.recruiter-mode {
  background-color: #f8f9fa;
  margin-left: 250px; /* Ajuster en fonction de la largeur de la sidebar recruteur */
}

.candidate-mode {
  background-color: #ffffff;
  margin-left: 0; /* Ajuster si nécessaire */
}

/* Styles pour les sidebars */
app-sidenav, app-sidebar-recruiter {
  position: fixed;
  height: 100%;
  z-index: 1000;
  transition: all 0.3s ease;
}

/* Responsive */
@media (max-width: 768px) {
  .recruiter-mode {
    margin-left: 70px; /* Largeur réduite pour mobile */
  }
}
