import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterOutlet } from '@angular/router';
import { Subscription } from 'rxjs';
import { UserRoleService } from '../../services/user-role/user-role.service';
import { AppNavComponent } from '../app-nav/app-nav.component';
import { SidenavComponent } from '../sidenav/sidenav.component';
import { SidebarRecruiterComponent } from '../sidebar-recruiter/sidebar-recruiter.component';

@Component({
  selector: 'app-role-based-layout',
  standalone: true,
  imports: [
    CommonModule,
    RouterOutlet,
    AppNavComponent,
    SidenavComponent,
    SidebarRecruiterComponent
  ],
  templateUrl: './role-based-layout.component.html',
  styleUrls: ['./role-based-layout.component.scss']
})
export class RoleBasedLayoutComponent implements OnInit, OnD<PERSON>roy {
  isRecruiter: boolean = false;
  private subscription: Subscription = new Subscription();

  constructor(private userRoleService: UserRoleService) {}

  ngOnInit(): void {
    // S'abonner aux changements de rôle
    this.subscription.add(
      this.userRoleService.isRecruiter$.subscribe(isRecruiter => {
        console.log('RoleBasedLayout - Changement de rôle détecté:', isRecruiter ? 'Recruteur' : 'Candidat');
        this.isRecruiter = isRecruiter;
      })
    );
  }

  ngOnDestroy(): void {
    // Se désabonner pour éviter les fuites de mémoire
    this.subscription.unsubscribe();
  }

  toggleSideNav(): void {
    // Implémenter la logique pour basculer la barre latérale
    const sideNav = document.querySelector('.sidenav') as HTMLElement;
    if (sideNav) {
      sideNav.classList.toggle('expanded');
    }
  }
}
