<div class="layout-container">
  <!-- Navbar commune avec contenu adapté au rôle -->
  <app-nav (menuClicked)="toggleSideNav()"></app-nav>
  
  <div class="content-container">
    <!-- Sidebar conditionnelle basée sur le rôle -->
    <app-sidenav *ngIf="!isRecruiter"></app-sidenav>
    <app-sidebar-recruiter *ngIf="isRecruiter"></app-sidebar-recruiter>
    
    <!-- Contenu principal -->
    <main class="main-content" [ngClass]="{'recruiter-mode': isRecruiter, 'candidate-mode': !isRecruiter}">
      <router-outlet></router-outlet>
    </main>
  </div>
</div>
