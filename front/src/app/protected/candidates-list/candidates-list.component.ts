import { Component, OnInit, Input } from '@angular/core';
import { JobApplicationService } from '../../services/job-application/job-application.service';
import { MatCardModule } from '@angular/material/card';
import { MatListModule } from '@angular/material/list';
import { CommonModule } from '@angular/common';
import { ActivatedRoute } from '@angular/router';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatDividerModule } from '@angular/material/divider';
import { trigger, state, style, transition, animate } from '@angular/animations';

@Component({
  selector: 'app-candidates-list',
  templateUrl: './candidates-list.component.html',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatListModule,
    MatButtonModule,
    MatIconModule,
    MatDividerModule
  ],
  styleUrls: ['./candidates-list.component.scss'],
  animations: [
    trigger('statusChange', [
      state('pending', style({
        backgroundColor: '#FFF8E1'
      })),
      state('accepted', style({
        backgroundColor: '#E8F5E9'
      })),
      state('rejected', style({
        backgroundColor: '#FFEBEE'
      })),
      transition('pending => accepted', [
        animate('0.3s ease-in-out')
      ]),
      transition('pending => rejected', [
        animate('0.3s ease-in-out')
      ])
    ]),
    trigger('cardAnimation', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateY(20px)' }),
        animate('0.3s ease-out', style({ opacity: 1, transform: 'translateY(0)' }))
      ])
    ])
  ]
})
export class CandidatesListComponent implements OnInit {
  @Input() postId: string | null = null;
  candidates: any[] = [];
  userIds: string[] = []; // Nouvelle propriété pour stocker les userIds

  constructor(
    private jobApplicationService: JobApplicationService,
    private route: ActivatedRoute,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.postId = this.route.snapshot.paramMap.get('postId');
    if (this.postId) {
      this.loadCandidates();
    }
  }

  private loadCandidates(): void {
    if (!this.postId) return;

    this.jobApplicationService.getCandidates(this.postId).subscribe({
      next: (response) => {
        console.log('Réponse des candidats:', response); // Affichez la réponse complète
        if (response && response.length > 0) {
          // S'assurer que chaque candidat a un statut défini
          this.candidates = response.map(candidate => {
            // Si le statut n'est pas défini, on le met à PENDING par défaut
            if (!candidate.status) {
              candidate.status = 'PENDING';
            }
            return candidate;
          });
          console.log('Candidats avec statut:', this.candidates);
          this.extractUserIds();
        } else {
          console.log('Aucun candidat trouvé');
          this.candidates = []; // Assurez-vous que la liste est vide si aucun candidat n'est trouvé
        }
      },
      error: (error) => {
        console.error('Erreur lors du chargement des candidats:', error);
      }
    });
  }

  // Méthode pour extraire les userIds
  private extractUserIds(): void {
    this.userIds = this.candidates.map(candidate => candidate.userId);
  }

  acceptCandidate(applicationId: string): void {
    this.jobApplicationService.acceptApplication(applicationId).subscribe({
      next: (response) => {
        // Mettre à jour le statut dans la liste locale
        const candidateIndex = this.candidates.findIndex(c => c.id === applicationId);
        if (candidateIndex !== -1) {
          this.candidates[candidateIndex].status = 'ACCEPTED';
        }
        this.snackBar.open('Candidature acceptée avec succès', 'Fermer', {
          duration: 3000,
          panelClass: ['success-snackbar']
        });
      },
      error: (error) => {
        console.error('Erreur lors de l\'acceptation de la candidature:', error);
        this.snackBar.open('Erreur lors de l\'acceptation de la candidature', 'Fermer', {
          duration: 3000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  rejectCandidate(applicationId: string): void {
    this.jobApplicationService.rejectApplication(applicationId).subscribe({
      next: (response) => {
        // Mettre à jour le statut dans la liste locale
        const candidateIndex = this.candidates.findIndex(c => c.id === applicationId);
        if (candidateIndex !== -1) {
          this.candidates[candidateIndex].status = 'REJECTED';
        }
        this.snackBar.open('Candidature refusée', 'Fermer', {
          duration: 3000,
          panelClass: ['warning-snackbar']
        });
      },
      error: (error) => {
        console.error('Erreur lors du refus de la candidature:', error);
        this.snackBar.open('Erreur lors du refus de la candidature', 'Fermer', {
          duration: 3000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  // Méthode pour vérifier si les boutons d'action doivent être affichés
  shouldShowActionButtons(candidate: any): boolean {
    console.log('Vérification des boutons pour candidat:', candidate);
    return candidate.status === 'PENDING' || !candidate.status;
  }
}
