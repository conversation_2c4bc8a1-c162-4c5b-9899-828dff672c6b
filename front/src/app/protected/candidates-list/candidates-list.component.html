<div class="candidates-container">
  <div class="candidates-header">
    <h2>Liste des candidats</h2>
    <div class="subtitle"><PERSON><PERSON><PERSON> les candidatures et sélectionnez les meilleurs profils pour votre entreprise</div>
  </div>

  <div *ngIf="candidates.length === 0" class="no-candidates">
    Aucun candidat pour le moment.
  </div>

  <div *ngIf="candidates.length > 0">
    <div *ngFor="let candidate of candidates" class="candidate-card" [@cardAnimation]>
      <div class="candidate-header">
        <h3 class="candidate-name">{{ candidate.name }}</h3>
        <div class="application-date">
          <mat-icon>event</mat-icon> {{ candidate.applicationDate | date: 'dd/MM/yyyy' }}
          <span class="status-badge"
                [@statusChange]="candidate.status === 'PENDING' ? 'pending' :
                                candidate.status === 'ACCEPTED' ? 'accepted' : 'rejected'"
                [ngClass]="{
                  'pending': candidate.status === 'PENDING',
                  'accepted': candidate.status === 'ACCEPTED',
                  'rejected': candidate.status === 'REJECTED'
                }">
            {{ candidate.status === 'PENDING' ? 'En attente' :
               candidate.status === 'ACCEPTED' ? 'Accepté' : 'Refusé' }}
          </span>
        </div>
      </div>

      <div class="candidate-content">
        <div class="candidate-info">
          <div class="info-item">
            <div class="label">Email</div>
            <div class="value">{{ candidate.email }}</div>
          </div>

          <div class="info-item">
            <div class="label">Téléphone</div>
            <div class="value">{{ candidate.phone }}</div>
          </div>

          <div class="info-item" *ngIf="candidate.linkedin">
            <div class="label">LinkedIn</div>
            <div class="value">
              <a [href]="candidate.linkedin" target="_blank">{{ candidate.linkedin }}</a>
            </div>
          </div>

          <div class="info-item" *ngIf="candidate.cvFileName">
            <div class="label">CV</div>
            <div class="value">
              <a href="#" class="cv-link">
                <mat-icon>description</mat-icon>
                {{ candidate.cvFileName }}
              </a>
            </div>
          </div>
        </div>

        <mat-divider></mat-divider>

        <div class="candidate-introduction" *ngIf="candidate.introduction">
          <div class="label">Présentation</div>
          <div class="value">{{ candidate.introduction }}</div>
        </div>
      </div>

      <div class="candidate-actions" *ngIf="shouldShowActionButtons(candidate)">
        <!-- Bouton Accepter -->
        <button mat-raised-button class="accept-button" (click)="acceptCandidate(candidate.id)"
               style="background-color: #001660 !important; color: white !important;">
          <mat-icon class="action-icon">check_circle</mat-icon>
          <span>Accepter</span>
        </button>

        <!-- Bouton Refuser -->
        <button mat-raised-button class="reject-button" (click)="rejectCandidate(candidate.id)"
               style="background-color: #ff6600 !important; color: white !important;">
          <mat-icon class="action-icon">cancel</mat-icon>
          <span>Refuser</span>
        </button>
      </div>

      <div class="candidate-actions" *ngIf="candidate.status === 'ACCEPTED'">
        <div class="status-message accepted">
          <mat-icon>verified</mat-icon>
          Candidature acceptée
        </div>
      </div>

      <div class="candidate-actions" *ngIf="candidate.status === 'REJECTED'">
        <div class="status-message rejected">
          <mat-icon>block</mat-icon>
          Candidature refusée
        </div>
      </div>
    </div>
  </div>
</div>
