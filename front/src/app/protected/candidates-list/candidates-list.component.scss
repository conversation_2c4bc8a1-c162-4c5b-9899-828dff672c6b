// Variables
$primary-color: #001660;
$primary-light: lighten(#001660, 10%);
$primary-dark: darken(#001660, 10%);
$accent-color: #ff6600;
$accent-light: lighten(#ff6600, 10%);
$accent-dark: darken(#ff6600, 10%);
$light-gray: #f5f5f5;
$medium-gray: #e0e0e0;
$dark-gray: #757575;
$white: #ffffff;
$shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
$shadow-hover: 0 8px 15px rgba(0, 0, 0, 0.15);
$border-radius: 8px;
$transition: all 0.3s ease;

.candidates-container {
  padding: 30px;
  max-width: 1200px;
  margin: 0 auto;
  font-family: 'Roboto', 'Segoe UI', Arial, sans-serif;
}

.candidates-header {
  margin-bottom: 30px;
  position: relative;
  padding-bottom: 15px;

  &:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 60px;
    height: 3px;
    background-color: $accent-color;
  }

  h2 {
    color: $primary-color;
    font-size: 28px;
    font-weight: 700;
    margin: 0;
    letter-spacing: -0.5px;
  }

  .subtitle {
    color: $dark-gray;
    font-size: 15px;
    margin-top: 8px;
    font-weight: 400;
  }
}

.no-candidates {
  background-color: $light-gray;
  border-radius: $border-radius;
  padding: 40px;
  text-align: center;
  color: $dark-gray;
  font-size: 16px;
  box-shadow: $shadow;
  border: 1px solid $medium-gray;
  position: relative;

  &:before {
    content: '\2639';
    display: block;
    font-size: 40px;
    margin-bottom: 15px;
    color: $primary-color;
    opacity: 0.5;
  }
}

.candidate-card {
  background-color: $white;
  border-radius: $border-radius;
  box-shadow: $shadow;
  margin-bottom: 24px;
  overflow: hidden;
  transition: $transition;
  border: 1px solid rgba($primary-color, 0.05);

  &:hover {
    transform: translateY(-3px);
    box-shadow: $shadow-hover;
  }
}

.candidate-header {
  background: linear-gradient(135deg, $primary-color, $primary-dark);
  color: $white;
  padding: 20px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  overflow: hidden;

  &:before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 150px;
    height: 100%;
    background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.1) 100%);
    transform: skewX(-30deg) translateX(50%);
  }

  .candidate-name {
    font-size: 20px;
    font-weight: 600;
    margin: 0;
    text-shadow: 0 1px 2px rgba(0,0,0,0.2);
    position: relative;
    z-index: 1;
  }

  .application-date {
    font-size: 13px;
    opacity: 0.9;
    display: flex;
    align-items: center;
    gap: 10px;
    position: relative;
    z-index: 1;
  }
}

.candidate-content {
  padding: 24px;

  .candidate-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 20px;
    margin-bottom: 20px;

    .info-item {
      .label {
        color: $dark-gray;
        font-size: 13px;
        margin-bottom: 6px;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .value {
        color: $primary-color;
        font-size: 15px;
        font-weight: 500;
        line-height: 1.4;

        a {
          color: $primary-color;
          text-decoration: none;
          transition: $transition;

          &:hover {
            color: $accent-color;
          }
        }
      }
    }
  }

  .candidate-introduction {
    margin-top: 20px;
    background-color: rgba($light-gray, 0.5);
    padding: 16px;
    border-radius: $border-radius;
    border-left: 3px solid $primary-color;

    .label {
      color: $primary-color;
      font-size: 13px;
      margin-bottom: 8px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .value {
      color: $dark-gray;
      font-size: 15px;
      line-height: 1.6;
      white-space: pre-line;
    }
  }
}

.candidate-actions {
  display: flex;
  justify-content: flex-end;
  padding: 20px 24px;
  background-color: rgba($light-gray, 0.7);
  gap: 16px;
  border-top: 1px solid $medium-gray;

  .accept-button, .reject-button {
    min-width: 140px;
    padding: 10px 20px;
    border-radius: 50px;
    font-weight: 500;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    font-size: 14px;
    transition: $transition;
    box-shadow: 0 2px 5px rgba(0,0,0,0.15);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .accept-button {
    background: linear-gradient(135deg, $primary-color, $primary-dark) !important;
    color: $white !important;
    border: none !important;
    z-index: 10;
    position: relative;

    &:hover {
      background: linear-gradient(135deg, $primary-dark, $primary-color) !important;
      box-shadow: 0 4px 8px rgba($primary-dark, 0.4) !important;
      transform: translateY(-2px);
    }

    &:active {
      transform: translateY(0);
      box-shadow: 0 2px 3px rgba($primary-dark, 0.4) !important;
    }
  }

  .reject-button {
    background: linear-gradient(135deg, $accent-color, $accent-dark) !important;
    color: $white !important;
    border: none !important;
    z-index: 10;
    position: relative;

    &:hover {
      background: linear-gradient(135deg, $accent-dark, $accent-color) !important;
      box-shadow: 0 4px 8px rgba($accent-dark, 0.4) !important;
      transform: translateY(-2px);
    }

    &:active {
      transform: translateY(0);
      box-shadow: 0 2px 3px rgba($accent-dark, 0.4) !important;
    }
  }

  .action-icon {
    margin-right: 8px;
    font-size: 18px;
  }

  .status-message {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px 20px;
    border-radius: 50px;
    font-weight: 500;
    font-size: 14px;
    width: 100%;
    max-width: 250px;
    margin-left: auto;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);

    mat-icon {
      margin-right: 8px;
      font-size: 20px;
    }

    &.accepted {
      background-color: #E8F5E9;
      color: #2E7D32;
      border: 1px solid rgba(#2E7D32, 0.2);
    }

    &.rejected {
      background-color: #FFEBEE;
      color: #C62828;
      border: 1px solid rgba(#C62828, 0.2);
    }
  }
}

.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 5px 10px;
  border-radius: 50px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-left: 10px;

  &:before {
    content: '';
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 6px;
  }

  &.pending {
    background-color: #FFF8E1;
    color: #FFA000;

    &:before {
      background-color: #FFA000;
    }
  }

  &.accepted {
    background-color: #E8F5E9;
    color: #2E7D32;

    &:before {
      background-color: #2E7D32;
    }
  }

  &.rejected {
    background-color: #FFEBEE;
    color: #C62828;

    &:before {
      background-color: #C62828;
    }
  }
}

.cv-link {
  display: inline-flex;
  align-items: center;
  color: $primary-color;
  text-decoration: none;
  font-size: 14px;
  margin-top: 8px;
  padding: 6px 12px;
  border-radius: 4px;
  transition: $transition;
  background-color: rgba($primary-color, 0.05);

  mat-icon {
    font-size: 18px;
    height: 18px;
    width: 18px;
    margin-right: 6px;
    color: $primary-color;
  }

  &:hover {
    background-color: rgba($primary-color, 0.1);
    color: $primary-dark;
    transform: translateY(-1px);
  }
}

@media (max-width: 768px) {
  .candidate-info {
    grid-template-columns: 1fr !important;
  }

  .candidate-actions {
    flex-direction: column;

    button {
      width: 100%;
    }
  }
}
