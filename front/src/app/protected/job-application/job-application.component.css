/* Global container for the job application page */
.job-application-container {
  display: flex;
  justify-content: space-between;
  max-width: 1000px;
  margin: 50px auto;
  padding: 0 20px;
  gap: 40px;
  font-family: "<PERSON><PERSON>", <PERSON><PERSON>, sans-serif;
}

/* Form wrapper (left side) */
.application-form-wrapper {
  flex: 1;
  max-width: 600px;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.12);
  padding: 35px;
  border-radius: 8px;
  background-color: #ffffff;
}

/* Job description wrapper (right side) */
.job-description-wrapper {
  flex: 0 0 300px;
  padding: 50px;
  border-radius: 12px;
  margin-left: 100px;
}

/* Page title */
.page-title {
  font-size: 28px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20px;
  text-transform: uppercase;
}

/* Form title */
.form-title {
  font-size: 20px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 30px;
  text-transform: uppercase;
}

/* Application card */
.application-card {
  border-radius: 12px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  padding: 25px;
  background-color: #ffffff;
  border: 1px solid #e0e7ff;
  min-height: 400px;
}

/* Form fields */
.form-field {
  display: flex; /* Alignement horizontal */
  align-items: center; /* Centrage vertical */
  margin-bottom: 25px; /* Réduit pour éviter trop d'espace */
  gap: 15px; /* Espacement entre label et input */
}

/* Labels */
.form-field label {
  font-size: 15px;
  font-weight: 500;
  color: #555555;
  flex: 0 0 150px; /* Largeur fixe pour les labels */
  margin-bottom: 0; /* Plus besoin d'espace en bas */
}

/* Full width (inputs) */
.full-width {
  flex: 1; /* L'input prend le reste de l'espace */
}

mat-form-field {
  font-family: "Roboto", Arial, sans-serif;
}

mat-form-field .mat-form-field-wrapper {
  padding-bottom: 0;
}

mat-form-field .mat-form-field-flex {
  border: 1px solid #b8cce4;
  border-radius: 4px;
  padding: 8px 12px;
  background-color: #F0F5FF;
  height: 50px;
}

mat-form-field .mat-form-field-infix {
  padding: 0;
  border-top: none;
}

input[matInput] {
  color: #555555;
  font-size: 15px;
  padding: 5px 0;
  height: 32px;
}

/* File upload styling */
.file-upload {
  display: flex;
  align-items: center;
  margin-bottom: 25px;
  gap: 15px; /* Espacement cohérent */
}

.file-upload label {
  flex: 0 0 150px; /* Même largeur que les autres labels */
  font-size: 15px;
  font-weight: 500;
  color: #555555;
  margin-bottom: 0;
}

.file-input-wrapper {
  flex: 1; /* Prend le reste de l'espace */
  display: flex;
  align-items: center;
  gap: 15px;
  border: 1px solid #b8cce4;
  border-radius: 4px;
  padding: 8px 12px;
  background-color: #F0F5FF;
  height: 50px;
}

.file-input-wrapper input[type="file"] {
  display: none;
}

.file-name {
  color: #555555;
  font-size: 15px;
  font-family: "Roboto", Arial, sans-serif;
}

/* Submit button */
.submit-btn {
  width: 100%;
  padding: 14px;
  font-size: 15px;
  font-weight: 600;
  font-family: "Roboto", Arial, sans-serif;
  background-color: rgba(1, 18, 87, 0.9);
  color: #ffffff;
  border-radius: 6px;
  border: none;
  text-transform: uppercase;
  transition: background-color 0.3s ease, transform 0.2s ease;
}

.submit-btn:hover {
  background-color: rgba(0, 95, 153, 1);
  transform: translateY(-2px);
}

.submit-btn[disabled] {
  background-color: #d3d3d3;
  color: #999999;
  cursor: not-allowed;
  transform: none;
}

/* Job description section */
.job-description {
  background-color: transparent;
  border-radius: 0;
  padding: 0;
  border: none;
  font-family: "Roboto", Arial, sans-serif;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 25px;
  text-transform: uppercase;
  background-color: #cce5ff;
  padding: 8px 15px;
  display: inline-block;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.job-detail {
  margin-bottom: 25px;
  font-size: 16px;
  color: #555555;
  padding-bottom: 15px;
  border-bottom: 1px solid #e0e7ff;
}

.job-detail:last-child {
  border-bottom: none;
}

.job-detail strong {
  display: block;
  font-size: 16px;
  font-weight: 600;
  text-decoration: underline;
  margin-bottom: 8px;
  color: #011257;
  text-transform: uppercase;
}

.job-detail p {
  margin: 0;
  line-height: 1.7;
}

/* Responsive design */
@media (max-width: 960px) {
  .job-application-container {
    flex-direction: column;
    padding: 0 15px;
  }

  .application-form-wrapper,
  .job-description-wrapper {
    max-width: 100%;
    flex: 1;
  }

  .job-description-wrapper {
    margin-top: 40px;
    margin-left: 0;
  }

  .page-title {
    font-size: 24px;
  }

  .form-title {
    font-size: 18px;
  }

  .form-field {
    flex-direction: column; /* Retour à la colonne pour petits écrans */
    align-items: flex-start;
  }

  .form-field label {
    flex: none; /* Réinitialisé */
    margin-bottom: 8px; /* Espace repris */
  }

  .file-upload label {
    flex: none;
  }
}

@media (max-width: 600px) {
  .page-title {
    font-size: 20px;
  }

  .form-title {
    font-size: 16px;
  }

  .form-field label,
  .file-name,
  .submit-btn {
    font-size: 13px;
  }

  mat-form-field .mat-form-field-flex,
  .file-input-wrapper {
    height: 46px;
  }

  input[matInput] {
    height: 28px;
  }

  .application-card {
    padding: 20px;
    min-height: 350px;
  }

  .submit-btn {
    padding: 12px;
  }
}
