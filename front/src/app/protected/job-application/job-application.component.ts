import { Component, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { UserService } from '../../services/user/user.service';
import { OidcSecurityService } from 'angular-auth-oidc-client';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { NgIf } from '@angular/common';
import { JobApplicationService } from '../../services/job-application/job-application.service';

@Component({
  selector: 'app-job-application',
  templateUrl: './job-application.component.html',
  styleUrls: ['./job-application.component.css'],
  standalone: true,
  imports: [
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    NgIf,
  ]
})
export class JobApplicationComponent implements OnInit {
  applicationForm: FormGroup;
  postTitle: string = '';
  postId: string = '';  // Ajout du postId
  selectedFile: File | null = null;
  selectedFileName: string = '';
  userId: string = ''; // Changé en string pour correspondre au backend
  fullName: string = ''; // Stocke le nom complet

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private userService: UserService,
    private oidcSecurityService: OidcSecurityService,
    private snackBar: MatSnackBar,
    private jobApplicationService: JobApplicationService
  ) {
    this.applicationForm = this.fb.group({
      name: [{ value: '', disabled: true }, Validators.required],
      email: ['', [Validators.required, Validators.email]],
      phone: ['', Validators.required],
      linkedin: ['', Validators.pattern(/^(https?:\/\/)?([\w\d-]+\.)*[\w\d-]+\.\w{2,}(\/.*)?$/i)],
      introduction: [''],
    });
  }

  ngOnInit(): void {
    this.route.paramMap.subscribe((params) => {
      this.postId = params.get('postId') || '';
      this.postTitle = decodeURIComponent(params.get('postTitle') || 'Unknown Job Post');
    });

    this.oidcSecurityService.checkAuth().subscribe(({ isAuthenticated, userData }) => {
      if (!isAuthenticated) {
        console.error('User not authenticated. Redirecting to login.');
        this.oidcSecurityService.authorize();
        return;
      }

      this.userId = userData?.sub || '';
      this.loadUserProfile();
    });
  }

  private loadUserProfile(): void {
    this.userService.getUserProfile().subscribe(
      (data: any) => {
        if (data && data.user && data.user.email) {
          const user = {
            firstName: data.user.firstName || data.user.given_name || 'Utilisateur',
            lastName: data.user.lastName || data.user.family_name || '',
            email: data.user.email || '',
            phoneNumber: data.user.phoneNumber || data.user.phone_number || '',
          };

          this.fullName = `${user.firstName} ${user.lastName}`.trim();

          this.applicationForm.patchValue({
            name: this.fullName,
            email: user.email,
            phone: user.phoneNumber,
          });
        } else {
          this.snackBar.open('Impossible de charger le profil. Veuillez remplir le formulaire manuellement.', 'Fermer', {
            duration: 5000,
          });
        }
      },
      (error) => {
        this.snackBar.open('Échec du chargement du profil. Veuillez remplir le formulaire manuellement.', 'Fermer', {
          duration: 5000,
        });
        if (error.status === 401) {
          this.oidcSecurityService.authorize();
        }
      }
    );
  }

  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      this.selectedFile = input.files[0];
      this.selectedFileName = this.selectedFile.name;
    }
  }

  onSubmit(): void {
    if (this.applicationForm.valid) {
      const formData = new FormData();
      formData.append('name', this.fullName);
      formData.append('email', this.applicationForm.get('email')?.value);
      formData.append('phone', this.applicationForm.get('phone')?.value);
      formData.append('linkedin', this.applicationForm.get('linkedin')?.value);
      formData.append('introduction', this.applicationForm.get('introduction')?.value);
      formData.append('postId', this.postId);
      formData.append('postTitle', this.postTitle);
      formData.append('userId', this.userId);

      if (this.selectedFile) {
        formData.append('cv', this.selectedFile, this.selectedFile.name);
      }

      this.jobApplicationService.saveApplication(formData).subscribe({
        next: (response: any) => {
          console.log('Candidature enregistrée avec succès', response);
          this.snackBar.open('Candidature envoyée avec succès !', 'Fermer', {
            duration: 3000,
          });
          this.applicationForm.reset();
          this.selectedFile = null;
          this.selectedFileName = '';
        },
        error: (error: any) => {
          console.error('Erreur lors de l\'envoi de la candidature', error);
          this.snackBar.open('Échec de l\'envoi de la candidature. Veuillez réessayer.', 'Fermer', {
            duration: 5000,
          });
        }
      });
    }
  }
}
