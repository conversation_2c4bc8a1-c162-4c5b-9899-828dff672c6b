<div class="job-application-container">
  <!-- Left Section: Application Form -->
  <div class="application-form-wrapper">
    <h1 class="page-title">JOBS / {{ postTitle }}</h1>
    <h2 class="form-title">JOB APPLICATION FORM</h2>
        <form [formGroup]="applicationForm" (ngSubmit)="onSubmit()">
          <!-- Your Name -->
          <div class="form-field">
            <label for="name">Your Name :</label>
            <mat-form-field appearance="outline" class="full-width">
              <input matInput id="name" formControlName="name" placeholder="Your Name" required>
              <mat-error *ngIf="applicationForm.get('name')?.hasError('required')">
                Name is required
              </mat-error>
            </mat-form-field>
          </div>

          <!-- Your Email -->
          <div class="form-field">
            <label for="email">Your Email :</label>
            <mat-form-field appearance="outline" class="full-width">
              <input matInput id="email" formControlName="email" placeholder="Your Email" required>
              <mat-error *ngIf="applicationForm.get('email')?.hasError('required')">
                Email is required
              </mat-error>
              <mat-error *ngIf="applicationForm.get('email')?.hasError('email')">
                Please enter a valid email
              </mat-error>
            </mat-form-field>
          </div>

          <!-- Your Phone Number -->
          <div class="form-field">
            <label for="phone">Your Phone Number :</label>
            <mat-form-field appearance="outline" class="full-width">
              <input matInput id="phone" formControlName="phone" placeholder="Your Phone Number" required>
              <mat-error *ngIf="applicationForm.get('phone')?.hasError('required')">
                Phone number is required
              </mat-error>
            </mat-form-field>
          </div>

          <!-- LinkedIn Profile -->
          <div class="form-field">
            <label for="linkedin">LinkedIn Profile</label>
            <mat-form-field appearance="outline" class="full-width">
              <input matInput id="linkedin" formControlName="linkedin" placeholder="e.g. https://www.linkedin.com">
              <mat-error *ngIf="applicationForm.get('linkedin')?.hasError('pattern')">
                Please enter a valid URL
              </mat-error>
            </mat-form-field>
          </div>

          <!-- CV Upload -->
          <div class="form-field file-upload">
            <label for="cv">CV :</label>
            <div class="file-input-wrapper">
              <input type="file" id="cv" (change)="onFileSelected($event)" accept=".pdf,.doc,.docx">
              <span class="file-name">{{ selectedFileName || 'no file chosen' }}</span>
            </div>
          </div>

          <!-- Short Introduction -->
          <div class="form-field">
            <label for="introduction">Short introduction :</label>
            <mat-form-field appearance="outline" class="full-width">
              <input matInput id="introduction" formControlName="introduction" placeholder="Description">
            </mat-form-field>
          </div>

          <!-- Submit Button -->
          <button mat-raised-button type="submit" class="submit-btn" [disabled]="applicationForm.invalid">
            I'M FEELING LUCKY
          </button>
        </form>
  </div>

  <!-- Right Section: Job Description -->
  <div class="job-description-wrapper">
    <div class="job-description">
      <h2 class="section-title">JOB DESCRIPTION</h2>
      <div class="job-detail">
        <strong class="job-detail-title">JOB</strong>
        <p>{{ postTitle }}</p>
      </div>
      <div class="job-detail">
        <strong class="job-detail-title">Location</strong>
        <p>All Soft multimedia</p>
      </div>
      <div class="job-detail">
        <strong class="job-detail-title">Department</strong>
        <p>Développement logiciel</p>
      </div>
      <div class="job-detail">
        <strong class="job-detail-title">Emploi type</strong>
        <p>{{ postTitle }}</p>
      </div>
      <div class="job-detail">
        <strong class="job-detail-title">Time to Answer</strong>
        <p>2 open days</p>
      </div>
      <div class="job-detail">
        <strong class="job-detail-title">Process</strong>
        <p>1 phone call</p>
      </div>
      <div class="job-detail">
        <strong class="job-detail-title">Days to get an offer</strong>
        <p>4 Days after test</p>
      </div>
    </div>
  </div>
</div>
