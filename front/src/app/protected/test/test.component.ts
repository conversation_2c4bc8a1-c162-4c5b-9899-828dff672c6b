import { Component, OnIni<PERSON>, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { interval, Subscription } from 'rxjs';
import { CommonModule } from '@angular/common';
import {
  MatCard,
  MatCardHeader,
} from '@angular/material/card';
import {
  MatStep,
  MatStepper,
  MatStepLabel,
} from '@angular/material/stepper';
import {MatCheckbox, MatCheckboxChange} from '@angular/material/checkbox';
import { MatIcon, MatIconModule } from '@angular/material/icon';
import { MatButton } from '@angular/material/button';
import { FormsModule } from '@angular/forms';
import {ActivatedRoute, Router} from '@angular/router';
import {Test, TestService, TestSubmission} from '../../services/test/test.service';
import {MatRadioButton, MatRadioChange, MatRadioGroup} from '@angular/material/radio';
import {UserService} from '../../services/user/user.service';

@Component({
  selector: 'app-test',
  templateUrl: './test.component.html',
  styleUrls: ['./test.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    MatCard,
    MatCardHeader,
    MatStepper,
    MatStep,
    MatStepLabel,
    MatCheckbox,
    MatIcon,
    MatButton,
    FormsModule,
    MatIconModule,
    MatRadioButton,
    MatRadioGroup,
  ],

})
export class TestComponent implements OnInit, OnDestroy {
  steps: number[] = Array(10).fill(0).map((_, i) => i); // 10 steps/questions
  currentStep = 0;
  timer = 45 * 60; // 2700 secondes = 45 minutes
  timerSubscription: Subscription | null = null;
  selectedAnswers: boolean[][] = Array(10).fill(null).map(_ => Array(4).fill(false));
  currentSelectedAnswers: boolean[] = Array(4).fill(false);
  timerDisplay: string = '00:00:00'; // 👈 bien visible dans ta classe

  selectedOption: number | null = null;


  // Inject HttpClient via constructeur
  test: Test | undefined = undefined;
  get selectedRadio(): number {
    for (let i = 0; i < 4; i++) {
      if (this.currentSelectedAnswers[i]) {
        return i;
      }
    }
    return -1
  }
  constructor(private testService: TestService,
              private route: ActivatedRoute,
              private userService: UserService,
              private router: Router) {}

  ngOnInit() {
    this.startTimer();
    this.loadTestTitle();
  }

  ngOnDestroy() {
    this.timerSubscription?.unsubscribe();
  }

  startTimer() {
    this.timerSubscription = interval(1000).subscribe(() => {
      if (this.timer > 0) {
        this.timer--;

        if (this.timer === 1620) {
          console.warn("⏳ 27 minutes restantes, avertissement...");
          // Tu peux afficher un toast ou un message ici
        }

        this.updateTimerDisplay();
      } else {
        this.timerSubscription?.unsubscribe();
        this.timerDisplay = '00:00:00';

        // ✅ Alerte de fin
        alert("⏰ Le temps est écoulé ! Vous allez être redirigé vers l'accueil des tests.");

        // ✅ Redirection
        this.router.navigate(['/tests']);
      }
    });
  }




  loadTestTitle(): void {
    const testId = this.route.snapshot.paramMap.get('id')!;

    this.testService.getTestById(testId).subscribe({
      next: (test) => {
        this.test = test;
      },
      error: (err) => {
        console.error('Erreur chargement test', err);
      },
    });
  }

  updateTimerDisplay() {
    const hours = Math.floor(this.timer / 3600);
    const minutes = Math.floor((this.timer % 3600) / 60);
    const seconds = this.timer % 60;
    this.timerDisplay = `${this.pad(hours)}:${this.pad(minutes)}:${this.pad(seconds)}`;
  }

  pad(num: number): string {
    return num < 10 ? `0${num}` : num.toString();
  }


  previousStep() {
    if (this.currentStep > 0) {
      console.log(this.currentSelectedAnswers, this.selectedAnswers);
      this.commitAnswers();
      this.currentStep--;
      this.resetAnswers();
      console.log(this.currentSelectedAnswers, this.selectedAnswers);
    }
  }

  nextStep() {
    if (this.currentStep < this.steps.length - 1) {
      console.log(this.currentSelectedAnswers, this.selectedAnswers);
      this.commitAnswers();
      this.currentStep++;
      this.resetAnswers();
      console.log(this.currentSelectedAnswers, this.selectedAnswers);
    }
  }

  cancelQuiz() {
    console.log('Quiz cancelled');
    this.timerSubscription?.unsubscribe();
  }

  private commitAnswers() {
    for (let i = 0; i < 4; i++) {
      this.selectedAnswers[this.currentStep][i] = this.currentSelectedAnswers[i];
    }

  }

  private resetAnswers() {
    for (let i = 0; i < 4; i++) {
      this.currentSelectedAnswers[i] = this.selectedAnswers[this.currentStep][i];
    }

  }

  radioChange(event: MatRadioChange) {
    for (let i = 0; i < 4; i++) {
      if (i !== event.value) {
        this.currentSelectedAnswers[i] = false;
      } else {
        this.currentSelectedAnswers[i] = true;
      }
    }
  }
  private allQuestionsAnswered(): boolean {
    return this.selectedAnswers.every(answer =>
      answer.some(option => option === true)
    );
  }
  validateTest() {
    this.commitAnswers(); // 🔒 On enregistre les réponses du dernier step

    // ❗ Vérifie si toutes les questions sont répondues
    if (!this.allQuestionsAnswered()) {
      alert("⚠️ Vous devez répondre à toutes les questions avant de valider le test.");
      return;
    }

    const testId = this.route.snapshot.paramMap.get('id')!;
    this.userService.getUserId().subscribe({
      next: (userId: string) => {
        const submission: TestSubmission = {
          testId,
          userId,
          answers: this.selectedAnswers.map((options, index) => ({
            questionIndex: index,
            selectedOptions: options
              .map((checked, i) => (checked ? i : -1))
              .filter(index => index !== -1),
          })),
          submittedAt: new Date(),
        };

        console.log("📤 Submission avec userId:", submission.userId);

        this.testService.submitTestAnswers(submission).subscribe({
          next: (response: any) => {
            console.log('✅ Réponses enregistrées avec succès', response);

            alert("✅ Merci d'avoir passé ce test !");

            setTimeout(() => {
              this.router.navigate(['/welcome']);
            }, 1000); // ⏱️ 5 secondes d'attente
          },

          error: (error: any) => {
            console.error("❌ Erreur lors de l'enregistrement des réponses", error);
            console.error('Erreur détaillée:', error.error);
          }
        });
      },
      error: (err: any) => {
        console.error("❌ Impossible de récupérer l'ID de l'utilisateur", err);
      }
    });
  }
}
