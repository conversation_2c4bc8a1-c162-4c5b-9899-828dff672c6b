<!-- 🕒 En-tête indépendant avec date + timer aligné à droite -->
<div class="quiz-top-bar">
  <div class="date">23 Avril 2025</div>
  <div class="timer-panel">
    <div class="timer-title">Temps restant</div>
    <div class="timer-value">{{ timerDisplay }}</div>
  </div>
</div>

<!-- 🧠 Container principal du quiz -->
<!-- Titre Quiz -->
<mat-card-header class="quiz-title centered-title">
  <mat-icon class="flag-icon" aria-hidden="false" aria-label="Flag icon">flag</mat-icon>
  {{ test?.name ?? 'Loading test' }}
</mat-card-header>

<!-- Stepper -->
<mat-horizontal-stepper [selectedIndex]="currentStep" labelPosition="bottom">
  <mat-step *ngFor="let step of steps; let i = index" [completed]="i < currentStep">
    <ng-template matStepLabel>{{ i + 1 }}</ng-template>
  </mat-step>
</mat-horizontal-stepper>

<mat-card class="quiz-body-card" *ngIf="test">
  <ng-container *ngIf="test.questions[currentStep].type == 'SELECT'">
    <div class="question-section">
      <div class="question-title">Question {{ currentStep + 1 }} :</div>
      <p class="question-text">
        {{ test.questions[currentStep].content }}
      </p>

      <!-- Options -->
      <div class="options">
        <ng-container *ngFor="let option of test.questions[currentStep].options; let i = index">
          <mat-card class="option">
            <mat-checkbox [(ngModel)]="currentSelectedAnswers[i]">
              <span class="option-text">{{ option }}</span>
            </mat-checkbox>
          </mat-card>
        </ng-container>
      </div>
    </div>
  </ng-container>

  <ng-container *ngIf="test.questions[currentStep].type == 'RADIO'">
    <div class="question-section">
      <div class="question-title">Question {{ currentStep + 1 }} :</div>
      <p class="question-text">
        {{ test.questions[currentStep].content }}
      </p>

      <!-- Options -->
      <div class="options">
        <mat-radio-group (change)="radioChange($event)" [value]="selectedRadio">
          <ng-container *ngFor="let option of test.questions[currentStep].options; let i = index">
            <mat-card class="option">
              <mat-radio-button [value]="i">
                <span class="option-text">{{ option }}</span>
              </mat-radio-button>
            </mat-card>
          </ng-container>
        </mat-radio-group>
      </div>
    </div>
  </ng-container>

  <!-- Navigation entre les questions -->
  <div class="quiz-footer">
    <button mat-raised-button color="accent" [disabled]="currentStep === 0" (click)="previousStep()">⬅️ Précédent</button>
    <button mat-raised-button color="accent" *ngIf="currentStep < steps.length - 1" (click)="nextStep()">Suivant ➡️</button>
    <button mat-raised-button color="primary" *ngIf="currentStep === steps.length - 1" (click)="validateTest()">Valider le test</button>
  </div>
</mat-card>
