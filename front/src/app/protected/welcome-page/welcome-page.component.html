<div class="welcome-container">
  <!-- Header -->
  <header class="welcome-header">
    <h1>BIENVENUE SUR LES TESTS EN LIGNE 🖥️</h1>
  </header>

  <!-- Main Content -->
  <div class="content-container">
    <!-- Rules and Roles Wrapper -->
    <div class="rules-roles-wrapper">
      <!-- Test Rules -->
      <mat-card class="info-card rules-card">
        <mat-card-title>Règles du Test</mat-card-title>
        <mat-card-content>
          <ul>
            <li><mat-icon class="role-icon">person</mat-icon> Un seul test autorisé par candidat.</li>
            <li><mat-icon class="role-icon">timer</mat-icon> Test à compléter dans un temps limité.</li>
            <li><mat-icon class="role-icon">check</mat-icon> Résultats disponibles à la fin.</li>
          </ul>
        </mat-card-content>
      </mat-card>

      <!-- Available Roles -->
      <mat-card class="info-card roles-card">
        <mat-card-title>R<PERSON>les Disponibles</mat-card-title>
        <mat-card-content>
          <ul>
            <li><mat-icon class="role-icon">code</mat-icon> Développeur Frontend</li>
            <li><mat-icon class="role-icon">cloud</mat-icon> Développeur Backend</li>
            <li><mat-icon class="role-icon">insights</mat-icon> Data Analyst</li>
            <li><mat-icon class="role-icon">leaderboard</mat-icon> Chef de Projet</li>
          </ul>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Test Description -->
    <mat-card class="info-card test-description-card">
      <mat-card-title>TEST DE COMPÉTENCES</mat-card-title>
      <mat-card-content>
        <p>Évaluez vos connaissances techniques sur divers sujets liés au développement et à la gestion de projet.</p>
      </mat-card-content>
      <mat-card-actions>
        <button mat-raised-button color="primary" (click)="startTest()">COMMENCER</button>
      </mat-card-actions>
    </mat-card>

    <!-- Experimental Test -->
    <mat-card class="info-card experimental-test-card">
      <mat-card-title>TEST EXPÉRIMENTAL</mat-card-title>
      <mat-card-content>
        <p>Il est essentiel de réaliser préalablement un test expérimental pour se familiariser avec le format, avant de procéder au passage du test réel.</p>
      </mat-card-content>
      <mat-card-actions>
        <button mat-raised-button color="primary" (click)="startExperimentalTest()">COMMENCER</button>
      </mat-card-actions>
    </mat-card>
  </div>

  <!-- Icon Explanation -->
  <div class="icon-explanation">
    <div class="icon-item">
      <mat-icon class="flag-icon">flag</mat-icon>
      <span><strong>Icône "Drapeau"</strong> : Signaler une question pour revoir plus tard.</span>
    </div>
    <div class="icon-item">
      <mat-icon class="check-icon">check_circle</mat-icon>
      <span><strong>Icône "Vérifier"</strong> : Valider votre réponse avant de la sauvegarder.</span>
    </div>
  </div>
</div>
