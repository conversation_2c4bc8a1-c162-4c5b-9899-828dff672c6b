import { Component } from '@angular/core';
import {Mat<PERSON>ard, MatCardActions, MatCardContent, MatCardTitle} from '@angular/material/card';
import {MatButton} from '@angular/material/button';
import {MatIcon} from '@angular/material/icon';
import {ActivatedRoute, Router} from '@angular/router';

@Component({
  selector: 'app-welcome-page',
  templateUrl: './welcome-page.component.html',
  imports: [
    MatCard,
    MatCardTitle,
    MatCardContent,
    MatButton,
    MatCardActions,
    MatIcon
  ],
  styleUrls: ['./welcome-page.component.css']
})
export class WelcomePageComponent {
  constructor(private route: ActivatedRoute, private router: Router) {
  }
  startTest() {
    const testId = this.route.snapshot.paramMap.get('id')!;
    this.router.navigate(['/test', testId]);
  }

  startExperimentalTest() {
    console.log('Starting experimental test...');
    // Add navigation or logic for experimental test
  }
}
