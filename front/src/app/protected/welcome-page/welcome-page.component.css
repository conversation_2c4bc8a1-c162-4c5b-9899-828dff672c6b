/* 🌟 General Container - <PERSON><PERSON>t, Professional */
.welcome-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #1A2A44, #2A9D8F);
  background-size: 200% 200%;
  animation: gradientFlow 12s ease infinite;
  padding: 20px;
  font-family: 'Montserrat', sans-serif;
  position: relative;
  overflow: hidden;
  box-sizing: border-box;
}

@keyframes gradientFlow {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Subtle Geometric Overlay */
.welcome-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    45deg,
    rgba(233, 196, 106, 0.05) 25%,
    transparent 25%,
    transparent 50%,
    rgba(233, 196, 106, 0.05) 50%,
    rgba(233, 196, 106, 0.05) 75%,
    transparent 75%
  )
  0 0 / 40px 40px;
  opacity: 0.3;
  animation: patternShift 30s linear infinite;
}

@keyframes patternShift {
  0% { transform: translate(0, 0); }
  100% { transform: translate(-40px, -40px); }
}

/* Header */
.welcome-header {
  width: 100%;
  background: #FFFFFF;
  padding: 15px 20px;
  text-align: center;
  margin-bottom: 20px;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border-bottom: 1px solid #E9C46A;
}

.welcome-header h1 {
  font-size: 1.8rem;
  font-weight: 700;
  color: #1A2A44;
  margin: 0;
  font-family: 'Montserrat', sans-serif;
  letter-spacing: -0.2px;
  text-shadow: 0 1px 2px rgba(233, 196, 106, 0.2);
}

/* Content Container */
.content-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  justify-content: center;
  max-width: 1200px;
  width: 100%;
}

/* Rules and Roles Wrapper */
.rules-roles-wrapper {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 100%;
  max-width: 300px;
}

/* Info Cards */
.info-card {
  flex: 1;
  min-width: 250px;
  max-width: 300px;
  background: #F8F9FA;
  border-radius: 14px;
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.1);
  padding: 20px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
  border: 1px solid transparent;
  border-image: linear-gradient(45deg, #E9C46A, #2A9D8F) 1;
  will-change: transform, box-shadow;
  animation: fadeInUp 0.5s ease-out;
}

.info-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 32px rgba(0, 0, 0, 0.15);
}

mat-card-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #1A2A44;
  margin-bottom: 15px;
  text-align: center;
  font-family: 'Montserrat', sans-serif;
  position: relative;
}

mat-card-title::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 2px;
  background: linear-gradient(90deg, #E9C46A, #2A9D8F);
  border-radius: 1px;
  transition: width 0.3s ease;
}

mat-card-title:hover::after {
  width: 60px;
}

mat-card-content p {
  font-size: 0.95rem;
  color: #264653;
  line-height: 1.6;
  text-align: center;
  font-family: 'Lora', serif;
}

mat-card-content ul {
  list-style-type: disc;
  padding-left: 20px;
  margin: 0;
}

mat-card-content ul li {
  font-size: 0.95rem;
  color: #264653;
  line-height: 1.6;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-family: 'Lora', serif;
}

.role-icon {
  color: #E9C46A;
  font-size: 1.2rem;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  transition: transform 0.3s ease;
}

.role-icon:hover {
  transform: scale(1.1);
}

mat-card-actions {
  display: flex;
  justify-content: center;
  margin-top: 15px;
}

button[mat-raised-button] {
  background: #2A9D8F;
  color: #F8F9FA;
  font-weight: 600;
  padding: 8px 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(42, 157, 143, 0.2);
  transition: all 0.3s ease;
  font-family: 'Montserrat', sans-serif;
  border: 1px solid #E9C46A;
  position: relative;
  overflow: hidden;
  will-change: transform, box-shadow, background;
}

button[mat-raised-button]::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(233, 196, 106, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.4s ease, height 0.4s ease;
  pointer-events: none;
}

button[mat-raised-button]:hover::after {
  width: 200px;
  height: 200px;
}

button[mat-raised-button]:hover {
  background: #1A2A44;
  box-shadow: 0 0 15px rgba(233, 196, 106, 0.4);
  transform: translateY(-2px);
}

/* Test Description and Experimental Test Cards */
.test-description-card,
.experimental-test-card {
  max-width: 350px;
}

/* Icon Explanation */
.icon-explanation {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  justify-content: center;
  margin-top: 30px;
  max-width: 800px;
  width: 100%;
}

.icon-item {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 0.9rem;
  color: #264653;
  font-family: 'Lora', serif;
}

.flag-icon {
  color: #E9C46A;
  font-size: 1.8rem;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  transition: transform 0.3s ease;
}

.flag-icon:hover {
  transform: scale(1.1);
}

.check-icon {
  color: #2A9D8F;
  font-size: 1.8rem;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  transition: transform 0.3s ease;
}

.check-icon:hover {
  transform: scale(1.1);
}

/* Animations */
@keyframes fadeInUp {
  from { opacity: 0; transform: translateY(15px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .welcome-header h1 {
    font-size: 1.5rem;
  }

  .info-card {
    max-width: 100%;
  }

  .test-description-card,
  .experimental-test-card {
    max-width: 100%;
  }

  .rules-roles-wrapper {
    max-width: 100%;
  }

  .icon-explanation {
    flex-direction: column;
    align-items: center;
  }
}

@media (max-width: 480px) {
  .welcome-container {
    padding: 12px;
  }

  .welcome-header h1 {
    font-size: 1.3rem;
  }

  .info-card {
    padding: 15px;
  }

  mat-card-title {
    font-size: 1rem;
  }

  mat-card-content p,
  mat-card-content ul li {
    font-size: 0.85rem;
  }

  button[mat-raised-button] {
    padding: 6px 16px;
    font-size: 0.9rem;
  }

  .icon-item {
    font-size: 0.8rem;
  }

  .flag-icon,
  .check-icon {
    font-size: 1.5rem;
  }
}

/* Dark Mode - Luxurious and Professional */
@media (prefers-color-scheme: dark) {
  .welcome-container {
    background: linear-gradient(135deg, #264653, #2A9D8F);
  }

  .welcome-header {
    background: #2A2F3B;
    border-bottom: 1px solid #E9C46A;
  }

  .welcome-header h1 {
    color: #E9C46A;
    text-shadow: 0 1px 2px rgba(233, 196, 106, 0.3);
  }

  .info-card {
    background: #2A2F3B;
    border-image: linear-gradient(45deg, #E9C46A, #2A9D8F) 1;
    box-shadow: 0 6px 24px rgba(0, 0, 0, 0.2);
  }

  mat-card-title {
    color: #F8F9FA;
  }

  mat-card-content p,
  mat-card-content ul li {
    color: #DDE1E6;
  }

  .role-icon {
    color: #E9C46A;
  }

  button[mat-raised-button] {
    background: #1A2A44;
    border: 1px solid #E9C46A;
    color: #F8F9FA;
  }

  button[mat-raised-button]:hover {
    background: #2A9D8F;
    box-shadow: 0 0 15px rgba(42, 157, 143, 0.4);
  }

  .icon-item {
    color: #DDE1E6;
  }

  .flag-icon {
    color: #E9C46A;
  }

  .check-icon {
    color: #2A9D8F;
  }
}
