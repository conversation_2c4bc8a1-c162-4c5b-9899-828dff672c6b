import { Component, Inject } from '@angular/core';
import {
  MatDialogRef,
  MAT_DIALOG_DATA,
  MatDialogActions,
  MatDialogContent,
  MatDialogTitle
} from '@angular/material/dialog';
import {<PERSON><PERSON><PERSON>r, MatFormField, Mat<PERSON>abel} from '@angular/material/form-field';
import { FormsModule } from '@angular/forms';
import { MatButton } from '@angular/material/button';
import { MatInput } from '@angular/material/input';
import { MatIcon } from '@angular/material/icon';
import { NgIf } from '@angular/common';

@Component({
  selector: 'app-add-experience-modal',
  templateUrl: './add-experience-modal.component.html',
  standalone: true,
  imports: [
    MatFormField,
    FormsModule,
    MatButton,
    MatDialogActions,
    MatInput,
    MatDialogContent,
    MatDialogTitle,
    MatLabel,
    MatIcon,
    NgIf,
    MatError
  ],
  styleUrls: ['./add-experience-modal.component.css']
})
export class AddExperienceModalComponent {
  experience: any = {
    title: '',
    company: '',
    location: '',
    startDate: '',
    endDate: '',
    description: ''
  };

  constructor(
    public dialogRef: MatDialogRef<AddExperienceModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    // If data is provided (edit mode), pre-fill the form with existing values
    if (this.data) {
      this.experience.title = this.data.title || '';
      this.experience.company = this.data.company || '';
      this.experience.location = this.data.location || '';
      this.experience.startDate = this.data.startDate || '';
      this.experience.endDate = this.data.endDate || '';
      this.experience.description = this.data.description || '';
    }
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  onAddExperience(): void {
    // Validate required fields
    if (this.experience.title && this.experience.company && this.experience.startDate) {
      this.dialogRef.close(this.experience);
    } else {
      alert("Veuillez remplir tous les champs requis (Poste, Entreprise, Date de début).");
    }
  }
}
