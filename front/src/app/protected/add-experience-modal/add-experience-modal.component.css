.dialog-title {
  display: flex;
  align-items: center;
  font-size: 20px;
  font-weight: 600;
  color: #254779;
  margin-bottom: 16px;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 8px;
}

.dialog-title .icon-title {
  margin-right: 12px;
  font-size: 24px;
  color: #254779;
  transition: transform 0.3s ease;
}

.dialog-title .icon-title:hover {
  transform: scale(1.1);
}

.dialog-content {
  background-color: #f5f7fa;
  border-radius: 8px;
  padding: 20px;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

.dialog-content .full-width {
  width: 100%;
}

.dialog-content .animate-field {
  transition: all 0.3s ease;
}

.dialog-content .animate-field:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.dialog-content .animate-field .mat-mdc-form-field {
  background-color: white;
  border-radius: 6px;
  transition: box-shadow 0.3s ease;
}

.dialog-content .animate-field .mat-mdc-form-field:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dialog-content .animate-field .mat-mdc-form-field-subscript-wrapper {
  padding-top: 4px;
}

.dialog-content .animate-field .field-icon {
  color: #254779;
  font-size: 18px;
  margin-right: 8px;
}

.dialog-content .animate-field mat-error {
  font-size: 12px;
  color: #f44336;
  font-weight: 400;
  animation: fadeIn 0.3s ease-in;
}

.dialog-content .mat-mdc-select {
  background-color: white;
  border-radius: 6px;
  padding: 8px 0;
}

.dialog-content .mat-mdc-select .mat-mdc-select-value {
  color: #333;
  font-weight: 500;
}

.dialog-content .mat-mdc-select .mat-mdc-select-arrow {
  color: #254779;
}

.dialog-content .mat-mdc-option {
  font-size: 14px;
  color: #333;
}

.dialog-content .mat-mdc-option:hover {
  background-color: #e1f0fa;
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 20px;
  background-color: white;
  border-top: 1px solid #e0e0e0;
  border-radius: 0 0 8px 8px;
}

.dialog-actions .btn-cancel {
  border: 1px solid #f44336;
  color: #f44336;
  font-weight: 500;
  border-radius: 6px;
  padding: 8px 16px;
  transition: all 0.3s ease;
}

.dialog-actions .btn-cancel mat-icon {
  margin-right: 6px;
  vertical-align: middle;
}

.dialog-actions .btn-cancel:hover {
  background-color: #fdeded;
  border-color: #d32f2f;
  color: #d32f2f;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.dialog-actions .btn-add {
  background-color: #0a66c2;
  color: white;
  font-weight: 500;
  border-radius: 6px;
  padding: 8px 16px;
  transition: all 0.3s ease;
}

.dialog-actions .btn-add mat-icon {
  margin-right: 6px;
  vertical-align: middle;
}

.dialog-actions .btn-add:hover {
  background-color: #0959a8;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.dialog-actions .btn-add:disabled {
  background-color: #b0bec5;
  color: #eceff1;
  cursor: not-allowed;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
