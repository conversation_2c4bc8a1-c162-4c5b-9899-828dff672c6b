<h1 mat-dialog-title class="dialog-title">
  <mat-icon class="icon-title">work</mat-icon>
  {{ data ? 'Modifier une expérience' : 'Ajouter une expérience' }}
</h1>

<div mat-dialog-content class="dialog-content dialog-form">
  <mat-form-field appearance="outline" class="full-width animate-field">
    <mat-label>Poste</mat-label>
    <input matInput [(ngModel)]="experience.title" required />
    <mat-error *ngIf="!experience.title">Le poste est requis</mat-error>
  </mat-form-field>

  <mat-form-field appearance="outline" class="full-width animate-field">
    <mat-label>Entreprise</mat-label>
    <input matInput [(ngModel)]="experience.company" required />
    <mat-error *ngIf="!experience.company">L'entreprise est requise</mat-error>
  </mat-form-field>

  <mat-form-field appearance="outline" class="full-width animate-field">
    <mat-label>Lieu</mat-label>
    <input matInput [(ngModel)]="experience.location" />
  </mat-form-field>

  <mat-form-field appearance="outline" class="full-width animate-field">
    <mat-label>Date de début</mat-label>
    <input matInput type="date" [(ngModel)]="experience.startDate" required />
    <mat-error *ngIf="!experience.startDate">La date de début est requise</mat-error>
  </mat-form-field>

  <mat-form-field appearance="outline" class="full-width animate-field">
    <mat-label>Date de fin</mat-label>
    <input matInput type="date" [(ngModel)]="experience.endDate" />
  </mat-form-field>

  <mat-form-field appearance="outline" class="full-width animate-field">
    <mat-label>Description</mat-label>
    <textarea matInput [(ngModel)]="experience.description"></textarea>
  </mat-form-field>
</div>

<div mat-dialog-actions class="dialog-actions">
  <button mat-stroked-button color="warn" (click)="onCancel()" class="btn-cancel">
    <mat-icon>cancel</mat-icon> Annuler
  </button>
  <button mat-flat-button color="primary" (click)="onAddExperience()"
          [disabled]="!experience.title || !experience.company || !experience.startDate"
          class="btn-add">
    <mat-icon>{{ data ? 'edit' : 'add_circle' }}</mat-icon>
    {{ data ? 'Mettre à jour' : 'Ajouter' }}
  </button>
</div>
