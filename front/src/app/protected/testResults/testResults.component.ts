import { Component } from '@angular/core';
import {MatIcon} from '@angular/material/icon';
import {SidebarComponent} from '../sidebar/sidebar.component';
import {<PERSON><PERSON>ard, MatCardContent, MatCardHeader, MatCardTitle} from '@angular/material/card';
import {MatButton, MatIconButton} from '@angular/material/button';
import {NgForOf} from '@angular/common';

@Component({
  selector: 'app-testResults',
  templateUrl: './testResults.component.html',
  standalone: true,
  imports: [
    MatIcon,
    SidebarComponent,
    MatCard,
    MatCardHeader,
    MatCardContent,
    MatIconButton,
    MatButton,
    NgForOf,
    MatCardTitle
  ],
  // Modifier le chemin
  styleUrls: ['./testResults.component.css'] // Modifier le chemin
})
export class TestResultsComponent {
  results = [
    {
      job: 'FullStack developer',
      name: '<PERSON><PERSON><PERSON>',
      avatar: 'assets/images/avatar1.jpg',
      date: '01/01/2025',
      timeLimit: '1h',
      timePassed: '55 Min',
      score: '80%'
    },
    {
      job: 'PFE (sujet1)',
      name: 'Sanad Ammar',
      avatar: 'assets/images/avatar2.jpg',
      date: '10/10/2025',
      timeLimit: '1h',
      timePassed: '55 Min',
      score: '80%'
    },
    {
      job: 'Designer',
      name: 'HAD ben salah',
      avatar: 'assets/images/avatar3.jpg',
      date: '05/05/2025',
      timeLimit: '1h',
      timePassed: '55 Min',
      score: '80%'
    },
    {
      job: 'Web Developer',
      name: 'Rawen Dabbebi',
      avatar: 'assets/images/avatar4.jpg',
      date: '28/06/2025',
      timeLimit: '1h',
      timePassed: '55 Min',
      score: '80%'
    },
    {
      job: 'Software developer',
      name: 'Roua Dabbebi',
      avatar: 'assets/images/avatar5.jpg',
      date: '01/11/2026',
      timeLimit: '1h',
      timePassed: '55 Min',
      score: '80%'
    },
    {
      job: 'PFA (sujet3)',
      name: 'Jesser Ammar',
      avatar: 'assets/images/avatar6.jpg',
      date: '12/12/2025',
      timeLimit: '1h',
      timePassed: '55 Min',
      score: '80%'
    }
  ];

  currentPage = 1;
  pages = [1, 2, 3];

  goToPage(page: number) {
    this.currentPage = page;
  }

  prevPage() {
  }

  nextPage() {
  }
}
