<div class="results-page">
  <app-sidebar></app-sidebar>

  <div class="results-container">
    <header class="results-header">
      <h1>Test Results</h1>
      <button mat-button class="filter-button">
        <mat-icon>filter_list</mat-icon> Filtrer par
      </button>
    </header>

    <div class="results-grid">
      <!-- Résultats fictifs -->
      <mat-card class="result-card" *ngFor="let result of results">
        <mat-card-header class="result-header">
          <mat-card-title>Job: {{ result.job }}</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="candidate-info">
            <!-- Remplacer l'image par une icône de personne -->
            <mat-icon class="avatar" svgIcon="person"></mat-icon>
            <div>
              <p>Nom: {{ result.name }}</p>
              <p><mat-icon>event</mat-icon> {{ result.date }}</p>
            </div>
          </div>

          <div class="test-info">
            <div class="time-limit">
              <p><mat-icon>schedule</mat-icon> Time Limit: <span class="red-text">{{ result.timeLimit }}</span></p>
            </div>
            <p><mat-icon>timer</mat-icon> Time Passed: {{ result.timePassed }}</p>
          </div>

          <div class="score">
            <strong>Score:</strong> {{ result.score }}
          </div>
        </mat-card-content>
        <div class="result-actions">
          <button mat-icon-button color="primary"><mat-icon>visibility</mat-icon></button>
          <button mat-icon-button color="warn"><mat-icon>delete</mat-icon></button>
        </div>
      </mat-card>
    </div>

    <!-- Pagination -->
    <footer class="pagination-footer">
      <!-- Boutons de pagination -->
      <button mat-button class="pagination-button" (click)="prevPage()" [disabled]="currentPage === 1">
        <mat-icon>chevron_left</mat-icon>
      </button>

      <!-- Pages dynamiques -->
      <span *ngFor="let page of pages"
            (click)="goToPage(page)"
            [class.active]="currentPage === page"
            class="pagination-page">
    {{ page }}
  </span>

      <!-- Bouton suivant -->
      <button mat-button class="pagination-button" (click)="nextPage()" [disabled]="currentPage === pages.length">
        <mat-icon>chevron_right</mat-icon>
      </button>
    </footer>

  </div>
</div>
