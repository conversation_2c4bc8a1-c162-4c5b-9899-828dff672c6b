import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatTabsModule } from '@angular/material/tabs';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { MatChipsModule } from '@angular/material/chips';
import { MatBadgeModule } from '@angular/material/badge';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTooltipModule } from '@angular/material/tooltip';
import { InvitationService } from '../../services/invitation/invitation.service';
import { HttpErrorResponse } from '@angular/common/http';

@Component({
  selector: 'app-recruiter-invitations',
  standalone: true,
  imports: [
    CommonModule,
    MatTabsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatDividerModule,
    MatChipsModule,
    MatBadgeModule,
    MatProgressSpinnerModule,
    MatTooltipModule
  ],
  templateUrl: './recruiter-invitations.component.html',
  styleUrls: ['./recruiter-invitations.component.css']
})
export class RecruiterInvitationsComponent implements OnInit {
  sentInvitations: any[] = [];
  acceptedInvitations: any[] = [];
  rejectedInvitations: any[] = [];
  pendingInvitations: any[] = [];

  loading = {
    sent: false,
    accepted: false,
    rejected: false,
    pending: false
  };

  errorMessage = '';

  constructor(private invitationService: InvitationService) { }

  ngOnInit(): void {
    this.loadSentInvitations();
  }

  loadSentInvitations(): void {
    this.loading.sent = true;
    this.errorMessage = '';

    this.invitationService.loadSentInvitations().subscribe({
      next: (data) => {
        console.log('Invitations envoyées:', data);

        // Traiter les données reçues
        if (data && data.content && Array.isArray(data.content)) {
          // Format de réponse avec pagination
          this.sentInvitations = data.content;
        } else if (Array.isArray(data)) {
          // Format de réponse sans pagination
          this.sentInvitations = data;
        } else {
          // Aucune donnée valide
          this.sentInvitations = [];
        }

        // Préparer les données pour l'affichage
        this.sentInvitations = this.sentInvitations.map(inv => ({
          ...inv,
          recipientName: inv.receiver ? inv.receiver.firstName + ' ' + inv.receiver.lastName : 'Utilisateur inconnu',
          email: inv.receiver ? inv.receiver.email : 'Email non disponible'
        }));

        // Filtrer les invitations par statut
        this.pendingInvitations = this.sentInvitations.filter(inv => inv.status === 'PENDING');
        this.acceptedInvitations = this.sentInvitations.filter(inv => inv.status === 'ACCEPTED');
        this.rejectedInvitations = this.sentInvitations.filter(inv => inv.status === 'REJECTED');

        this.loading.sent = false;
        this.loading.pending = false;
        this.loading.accepted = false;
        this.loading.rejected = false;
      },
      error: (error) => {
        this.loading.sent = false;
        this.loading.pending = false;
        this.loading.accepted = false;
        this.loading.rejected = false;
        this.handleHttpError(error);
      }
    });
  }

  resendInvitation(invitation: any): void {
    this.loading.pending = true;

    this.invitationService.resendInvitation(invitation.id).subscribe({
      next: (response) => {
        console.log('Invitation renvoyée avec succès:', response);
        // Afficher un message de succès temporaire
        this.errorMessage = ''; // Effacer les messages d'erreur précédents

        // Mettre à jour l'invitation dans la liste locale
        const updatedInvitation = {
          ...invitation,
          status: 'PENDING',
          updatedAt: new Date().toISOString()
        };

        // Mettre à jour les listes d'invitations
        this.updateInvitationInLists(invitation.id, updatedInvitation);

        this.loading.pending = false;
      },
      error: (error) => {
        this.loading.pending = false;
        this.handleHttpError(error);
      }
    });
  }

  cancelInvitation(invitation: any): void {
    this.loading.pending = true;

    this.invitationService.cancelInvitation(invitation.id).subscribe({
      next: (response) => {
        console.log('Invitation annulée avec succès:', response);
        // Afficher un message de succès temporaire
        this.errorMessage = ''; // Effacer les messages d'erreur précédents

        // Supprimer l'invitation des listes
        this.removeInvitationFromLists(invitation.id);

        this.loading.pending = false;
      },
      error: (error) => {
        this.loading.pending = false;
        this.handleHttpError(error);
      }
    });
  }

  // Méthode pour mettre à jour une invitation dans toutes les listes
  private updateInvitationInLists(id: string, updatedInvitation: any): void {
    // Mettre à jour dans la liste principale
    const index = this.sentInvitations.findIndex(inv => inv.id === id);
    if (index !== -1) {
      this.sentInvitations[index] = updatedInvitation;
    }

    // Mettre à jour les listes filtrées
    this.pendingInvitations = this.sentInvitations.filter(inv => inv.status === 'PENDING');
    this.acceptedInvitations = this.sentInvitations.filter(inv => inv.status === 'ACCEPTED');
    this.rejectedInvitations = this.sentInvitations.filter(inv => inv.status === 'REJECTED');
  }

  // Méthode pour supprimer une invitation de toutes les listes
  private removeInvitationFromLists(id: string): void {
    this.sentInvitations = this.sentInvitations.filter(inv => inv.id !== id);
    this.pendingInvitations = this.pendingInvitations.filter(inv => inv.id !== id);
    this.acceptedInvitations = this.acceptedInvitations.filter(inv => inv.id !== id);
    this.rejectedInvitations = this.rejectedInvitations.filter(inv => inv.id !== id);
  }

  getStatusClass(status: string): string {
    switch (status.toUpperCase()) {
      case 'ACCEPTED':
        return 'status-accepted';
      case 'REJECTED':
        return 'status-rejected';
      case 'PENDING':
        return 'status-pending';
      default:
        return '';
    }
  }

  getStatusIcon(status: string): string {
    switch (status.toUpperCase()) {
      case 'ACCEPTED':
        return 'check_circle';
      case 'REJECTED':
        return 'cancel';
      case 'PENDING':
        return 'schedule';
      default:
        return 'help';
    }
  }

  getFormattedDate(dateString: string): string {
    if (!dateString) return 'Date inconnue';

    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  private handleHttpError(error: HttpErrorResponse): void {
    console.error('Erreur HTTP:', error);

    switch (error.status) {
      case 404:
        this.errorMessage = 'Le serveur ne trouve pas la ressource demandée.';
        break;
      case 401:
        this.errorMessage = 'Session expirée. Veuillez vous reconnecter.';
        localStorage.removeItem('access_token');
        window.location.href = '/login';
        break;
      case 403:
        this.errorMessage = 'Vous n\'avez pas les permissions requises pour cette action.';
        break;
      case 500:
        this.errorMessage = 'Erreur serveur. Veuillez réessayer plus tard.';
        break;
      case 0:
        this.errorMessage = 'Impossible de se connecter au serveur. Vérifiez votre connexion internet.';
        break;
      default:
        if (error.error && typeof error.error === 'string') {
          this.errorMessage = `Erreur: ${error.error}`;
        } else if (error.error && error.error.message) {
          this.errorMessage = `Erreur: ${error.error.message}`;
        } else {
          this.errorMessage = `Une erreur est survenue (${error.status}): ${error.message || 'Détails non disponibles'}`;
        }
    }
  }
}
