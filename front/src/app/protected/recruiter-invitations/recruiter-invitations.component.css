/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 149, 0, 0.4);
  }
  70% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(255, 149, 0, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 149, 0, 0);
  }
}

/* Main Container */
.recruiter-invitations-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
  font-family: 'Inter', sans-serif;
  padding: 40px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* Header */
.invitations-header {
  width: 100%;
  max-width: 1000px;
  margin-bottom: 30px;
  text-align: center;
  animation: fadeIn 0.6s ease-out;
}

.header-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.header-icon {
  width: 70px;
  height: 70px;
  background: linear-gradient(135deg, #FF9500, #FF5722);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  box-shadow: 0 10px 20px rgba(255, 149, 0, 0.2);
  animation: pulse 2s infinite;
}

.header-icon mat-icon {
  font-size: 36px;
  height: 36px;
  width: 36px;
  color: white;
}

.header-title {
  font-size: 32px;
  font-weight: 700;
  color: #1F2937;
  margin: 0 0 8px 0;
}

.header-subtitle {
  font-size: 16px;
  color: #6B7280;
  margin: 0;
  max-width: 500px;
}

/* Card */
.invitations-card {
  width: 100%;
  max-width: 1000px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  animation: fadeIn 0.8s ease-out;
  padding: 0;
}

/* Error Message */
.error-message {
  display: flex;
  align-items: center;
  background-color: rgba(239, 68, 68, 0.1);
  color: #EF4444;
  padding: 12px 16px;
  border-radius: 8px;
  margin: 16px;
  font-size: 14px;
  border-left: 4px solid #EF4444;
  animation: fadeIn 0.3s ease-out;
}

.error-message mat-icon {
  margin-right: 8px;
  font-size: 20px;
  height: 20px;
  width: 20px;
}

/* Refresh Button */
.refresh-button-container {
  display: flex;
  justify-content: flex-end;
  padding: 16px 16px 0;
}

.refresh-button {
  background: linear-gradient(90deg, #2563EB, #3B82F6);
  color: white;
  padding: 8px 16px;
  border-radius: 8px;
  font-weight: 600;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.2);
}

.refresh-button:hover:not(:disabled) {
  background: linear-gradient(90deg, #1D4ED8, #2563EB);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(37, 99, 235, 0.3);
}

.refresh-button mat-icon {
  margin-right: 8px;
}

/* Tabs */
.invitations-tabs {
  padding: 0 16px 16px;
}

::ng-deep .mat-mdc-tab-header {
  margin-bottom: 24px;
}

::ng-deep .mat-mdc-tab-label-container {
  background-color: #F9FAFB;
  border-radius: 8px;
  padding: 4px;
}

::ng-deep .mat-mdc-tab {
  min-width: 120px;
  padding: 0 16px;
  height: 48px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

::ng-deep .mat-mdc-tab:hover:not(.mat-mdc-tab-disabled) {
  background-color: rgba(243, 244, 246, 0.8);
}

::ng-deep .mat-mdc-tab.mat-mdc-tab-active {
  background-color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.tab-label {
  display: flex;
  align-items: center;
  position: relative;
}

.tab-label mat-icon {
  margin-right: 8px;
  font-size: 20px;
  height: 20px;
  width: 20px;
}

.tab-badge {
  position: absolute;
  top: -8px;
  right: -16px;
  background-color: #3B82F6;
  color: white;
  font-size: 10px;
  font-weight: 600;
  min-width: 18px;
  height: 18px;
  border-radius: 9px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 6px;
}

.tab-badge.pending {
  background-color: #F59E0B;
}

.tab-badge.accepted {
  background-color: #10B981;
}

.tab-badge.rejected {
  background-color: #EF4444;
}

/* Tab Content */
.tab-content {
  padding: 16px 0;
}

/* Loading Container */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 0;
  color: #6B7280;
}

.loading-container p {
  margin-top: 16px;
  font-size: 16px;
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 0;
  color: #6B7280;
  text-align: center;
}

.empty-state mat-icon {
  font-size: 48px;
  height: 48px;
  width: 48px;
  margin-bottom: 16px;
  color: #D1D5DB;
}

.empty-state h3 {
  font-size: 20px;
  font-weight: 600;
  color: #4B5563;
  margin: 0 0 8px 0;
}

.empty-state p {
  font-size: 16px;
  margin: 0 0 24px 0;
  max-width: 400px;
}

.empty-state button {
  background: linear-gradient(90deg, #2563EB, #3B82F6);
  color: white;
  padding: 8px 24px;
  border-radius: 8px;
  font-weight: 600;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.2);
}

.empty-state button:hover {
  background: linear-gradient(90deg, #1D4ED8, #2563EB);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(37, 99, 235, 0.3);
}

.empty-state button mat-icon {
  margin-right: 8px;
  font-size: 20px;
  height: 20px;
  width: 20px;
  color: white;
}

/* Invitations List */
.invitations-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 24px;
  padding: 8px;
}

/* Invitation Card */
.invitation-card {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid #E5E7EB;
  animation: fadeIn 0.5s ease-out;
}

.invitation-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.invitation-header {
  display: flex;
  align-items: center;
  padding: 16px;
  position: relative;
}

.user-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: linear-gradient(135deg, #2563EB, #3B82F6);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 18px;
  margin-right: 14px;
  flex-shrink: 0;
  box-shadow: 0 4px 8px rgba(37, 99, 235, 0.2);
  position: relative;
  overflow: hidden;
  border: 2px solid white;
}

.user-avatar.accepted {
  background: linear-gradient(135deg, #059669, #10B981);
  box-shadow: 0 4px 8px rgba(16, 185, 129, 0.2);
}

.user-avatar.rejected {
  background: linear-gradient(135deg, #DC2626, #EF4444);
  box-shadow: 0 4px 8px rgba(239, 68, 68, 0.2);
}

.user-avatar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 60%);
  z-index: 1;
}

.invitation-details {
  flex-grow: 1;
  overflow: hidden;
}

.user-name {
  font-weight: 600;
  color: #1F2937;
  font-size: 16px;
  margin: 0 0 4px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-email {
  color: #6B7280;
  font-size: 14px;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.invitation-status {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
  margin-left: 8px;
}

.invitation-status mat-icon {
  font-size: 14px;
  height: 14px;
  width: 14px;
  margin-right: 4px;
}

.status-pending {
  background-color: rgba(245, 158, 11, 0.1);
  color: #F59E0B;
}

.status-accepted {
  background-color: rgba(16, 185, 129, 0.1);
  color: #10B981;
}

.status-rejected {
  background-color: rgba(239, 68, 68, 0.1);
  color: #EF4444;
}

.invitation-content {
  padding: 16px;
}

.invitation-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-item {
  display: flex;
  flex-direction: column;
}

.info-label {
  font-size: 12px;
  font-weight: 600;
  color: #6B7280;
  margin-bottom: 4px;
}

.info-value {
  font-size: 14px;
  color: #1F2937;
}

.info-value.message {
  background-color: #F9FAFB;
  padding: 8px 12px;
  border-radius: 8px;
  border-left: 3px solid #E5E7EB;
  font-style: italic;
  line-height: 1.5;
}

.invitation-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  padding: 16px;
  border-top: 1px solid #F3F4F6;
}

.invitation-actions button {
  display: flex;
  align-items: center;
  font-size: 13px;
  font-weight: 600;
}

.invitation-actions button mat-icon {
  font-size: 16px;
  height: 16px;
  width: 16px;
  margin-right: 4px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .recruiter-invitations-container {
    padding: 32px 16px;
  }
  
  .header-icon {
    width: 60px;
    height: 60px;
  }
  
  .header-icon mat-icon {
    font-size: 30px;
    height: 30px;
    width: 30px;
  }
  
  .header-title {
    font-size: 28px;
  }
  
  .invitations-list {
    grid-template-columns: 1fr;
  }
  
  ::ng-deep .mat-mdc-tab {
    min-width: 100px;
    padding: 0 12px;
  }
}

@media (max-width: 480px) {
  .recruiter-invitations-container {
    padding: 24px 12px;
  }
  
  .header-icon {
    width: 50px;
    height: 50px;
  }
  
  .header-icon mat-icon {
    font-size: 24px;
    height: 24px;
    width: 24px;
  }
  
  .header-title {
    font-size: 24px;
  }
  
  .header-subtitle {
    font-size: 14px;
  }
  
  .invitation-actions {
    flex-direction: column;
  }
  
  .invitation-actions button {
    width: 100%;
    justify-content: center;
  }
  
  ::ng-deep .mat-mdc-tab {
    min-width: 80px;
    padding: 0 8px;
  }
  
  .tab-label span:not(.tab-badge) {
    display: none;
  }
  
  .tab-label mat-icon {
    margin-right: 0;
  }
  
  .tab-badge {
    right: -8px;
  }
}
