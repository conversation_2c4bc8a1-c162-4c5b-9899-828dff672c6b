<div class="recruiter-invitations-container">
  <div class="invitations-header">
    <div class="header-content">
      <div class="header-icon">
        <mat-icon>mail_outline</mat-icon>
      </div>
      <h1 class="header-title">Gestion des invitations</h1>
      <p class="header-subtitle"><PERSON><PERSON><PERSON> et gérez toutes vos invitations envoyées</p>
    </div>
  </div>

  <div class="invitations-card">
    <!-- Message d'erreur -->
    <div *ngIf="errorMessage" class="error-message">
      <mat-icon>error</mat-icon>
      <span>{{ errorMessage }}</span>
    </div>

    <!-- Bouton de rafraîchissement -->
    <div class="refresh-button-container">
      <button mat-flat-button color="primary" (click)="loadSentInvitations()" [disabled]="loading.sent" class="refresh-button">
        <mat-icon>refresh</mat-icon>
        <span>Rafraîchir</span>
      </button>
    </div>

    <!-- Onglets pour les différentes catégories d'invitations -->
    <mat-tab-group animationDuration="300ms" class="invitations-tabs">
      <!-- Toutes les invitations -->
      <mat-tab>
        <ng-template mat-tab-label>
          <div class="tab-label">
            <mat-icon>list</mat-icon>
            <span>Toutes</span>
            <span class="tab-badge" *ngIf="sentInvitations.length > 0">{{ sentInvitations.length }}</span>
          </div>
        </ng-template>
        
        <div class="tab-content">
          <div *ngIf="loading.sent" class="loading-container">
            <mat-spinner diameter="40"></mat-spinner>
            <p>Chargement des invitations...</p>
          </div>

          <div *ngIf="!loading.sent && sentInvitations.length === 0" class="empty-state">
            <mat-icon>mail</mat-icon>
            <h3>Aucune invitation envoyée</h3>
            <p>Vous n'avez pas encore envoyé d'invitations.</p>
            <button mat-flat-button color="primary" routerLink="/protected/invitation">
              <mat-icon>add</mat-icon>
              <span>Envoyer une invitation</span>
            </button>
          </div>

          <div *ngIf="!loading.sent && sentInvitations.length > 0" class="invitations-list">
            <div *ngFor="let invitation of sentInvitations" class="invitation-card">
              <div class="invitation-header">
                <div class="user-avatar">
                  <span>{{ invitation.recipientName?.charAt(0) || '?' }}</span>
                </div>
                <div class="invitation-details">
                  <h3 class="user-name">{{ invitation.recipientName || 'Utilisateur' }}</h3>
                  <p class="user-email">{{ invitation.email || 'Email non disponible' }}</p>
                </div>
                <div class="invitation-status" [ngClass]="getStatusClass(invitation.status)">
                  <mat-icon>{{ getStatusIcon(invitation.status) }}</mat-icon>
                  <span>{{ invitation.status }}</span>
                </div>
              </div>
              
              <mat-divider></mat-divider>
              
              <div class="invitation-content">
                <div class="invitation-info">
                  <div class="info-item">
                    <span class="info-label">Date d'envoi:</span>
                    <span class="info-value">{{ getFormattedDate(invitation.createdAt) }}</span>
                  </div>
                  <div class="info-item" *ngIf="invitation.updatedAt && invitation.updatedAt !== invitation.createdAt">
                    <span class="info-label">Dernière mise à jour:</span>
                    <span class="info-value">{{ getFormattedDate(invitation.updatedAt) }}</span>
                  </div>
                  <div class="info-item" *ngIf="invitation.message">
                    <span class="info-label">Message:</span>
                    <span class="info-value message">{{ invitation.message }}</span>
                  </div>
                </div>
              </div>
              
              <div class="invitation-actions">
                <button mat-stroked-button color="primary" *ngIf="invitation.status === 'PENDING'" (click)="resendInvitation(invitation)" matTooltip="Renvoyer l'invitation">
                  <mat-icon>send</mat-icon>
                  <span>Renvoyer</span>
                </button>
                <button mat-stroked-button color="warn" *ngIf="invitation.status === 'PENDING'" (click)="cancelInvitation(invitation)" matTooltip="Annuler l'invitation">
                  <mat-icon>delete</mat-icon>
                  <span>Annuler</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </mat-tab>

      <!-- Invitations en attente -->
      <mat-tab>
        <ng-template mat-tab-label>
          <div class="tab-label">
            <mat-icon>schedule</mat-icon>
            <span>En attente</span>
            <span class="tab-badge pending" *ngIf="pendingInvitations.length > 0">{{ pendingInvitations.length }}</span>
          </div>
        </ng-template>
        
        <div class="tab-content">
          <div *ngIf="loading.pending" class="loading-container">
            <mat-spinner diameter="40"></mat-spinner>
            <p>Chargement des invitations...</p>
          </div>

          <div *ngIf="!loading.pending && pendingInvitations.length === 0" class="empty-state">
            <mat-icon>schedule</mat-icon>
            <h3>Aucune invitation en attente</h3>
            <p>Vous n'avez pas d'invitations en attente de réponse.</p>
            <button mat-flat-button color="primary" routerLink="/protected/invitation">
              <mat-icon>add</mat-icon>
              <span>Envoyer une invitation</span>
            </button>
          </div>

          <div *ngIf="!loading.pending && pendingInvitations.length > 0" class="invitations-list">
            <div *ngFor="let invitation of pendingInvitations" class="invitation-card">
              <div class="invitation-header">
                <div class="user-avatar">
                  <span>{{ invitation.recipientName?.charAt(0) || '?' }}</span>
                </div>
                <div class="invitation-details">
                  <h3 class="user-name">{{ invitation.recipientName || 'Utilisateur' }}</h3>
                  <p class="user-email">{{ invitation.email || 'Email non disponible' }}</p>
                </div>
                <div class="invitation-status status-pending">
                  <mat-icon>schedule</mat-icon>
                  <span>En attente</span>
                </div>
              </div>
              
              <mat-divider></mat-divider>
              
              <div class="invitation-content">
                <div class="invitation-info">
                  <div class="info-item">
                    <span class="info-label">Date d'envoi:</span>
                    <span class="info-value">{{ getFormattedDate(invitation.createdAt) }}</span>
                  </div>
                  <div class="info-item" *ngIf="invitation.message">
                    <span class="info-label">Message:</span>
                    <span class="info-value message">{{ invitation.message }}</span>
                  </div>
                </div>
              </div>
              
              <div class="invitation-actions">
                <button mat-stroked-button color="primary" (click)="resendInvitation(invitation)" matTooltip="Renvoyer l'invitation">
                  <mat-icon>send</mat-icon>
                  <span>Renvoyer</span>
                </button>
                <button mat-stroked-button color="warn" (click)="cancelInvitation(invitation)" matTooltip="Annuler l'invitation">
                  <mat-icon>delete</mat-icon>
                  <span>Annuler</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </mat-tab>

      <!-- Invitations acceptées -->
      <mat-tab>
        <ng-template mat-tab-label>
          <div class="tab-label">
            <mat-icon>check_circle</mat-icon>
            <span>Acceptées</span>
            <span class="tab-badge accepted" *ngIf="acceptedInvitations.length > 0">{{ acceptedInvitations.length }}</span>
          </div>
        </ng-template>
        
        <div class="tab-content">
          <div *ngIf="loading.accepted" class="loading-container">
            <mat-spinner diameter="40"></mat-spinner>
            <p>Chargement des invitations...</p>
          </div>

          <div *ngIf="!loading.accepted && acceptedInvitations.length === 0" class="empty-state">
            <mat-icon>check_circle_outline</mat-icon>
            <h3>Aucune invitation acceptée</h3>
            <p>Vous n'avez pas encore d'invitations acceptées.</p>
          </div>

          <div *ngIf="!loading.accepted && acceptedInvitations.length > 0" class="invitations-list">
            <div *ngFor="let invitation of acceptedInvitations" class="invitation-card">
              <div class="invitation-header">
                <div class="user-avatar accepted">
                  <span>{{ invitation.recipientName?.charAt(0) || '?' }}</span>
                </div>
                <div class="invitation-details">
                  <h3 class="user-name">{{ invitation.recipientName || 'Utilisateur' }}</h3>
                  <p class="user-email">{{ invitation.email || 'Email non disponible' }}</p>
                </div>
                <div class="invitation-status status-accepted">
                  <mat-icon>check_circle</mat-icon>
                  <span>Acceptée</span>
                </div>
              </div>
              
              <mat-divider></mat-divider>
              
              <div class="invitation-content">
                <div class="invitation-info">
                  <div class="info-item">
                    <span class="info-label">Date d'envoi:</span>
                    <span class="info-value">{{ getFormattedDate(invitation.createdAt) }}</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">Date d'acceptation:</span>
                    <span class="info-value">{{ getFormattedDate(invitation.updatedAt) }}</span>
                  </div>
                  <div class="info-item" *ngIf="invitation.message">
                    <span class="info-label">Message:</span>
                    <span class="info-value message">{{ invitation.message }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </mat-tab>

      <!-- Invitations refusées -->
      <mat-tab>
        <ng-template mat-tab-label>
          <div class="tab-label">
            <mat-icon>cancel</mat-icon>
            <span>Refusées</span>
            <span class="tab-badge rejected" *ngIf="rejectedInvitations.length > 0">{{ rejectedInvitations.length }}</span>
          </div>
        </ng-template>
        
        <div class="tab-content">
          <div *ngIf="loading.rejected" class="loading-container">
            <mat-spinner diameter="40"></mat-spinner>
            <p>Chargement des invitations...</p>
          </div>

          <div *ngIf="!loading.rejected && rejectedInvitations.length === 0" class="empty-state">
            <mat-icon>cancel_outline</mat-icon>
            <h3>Aucune invitation refusée</h3>
            <p>Vous n'avez pas d'invitations refusées.</p>
          </div>

          <div *ngIf="!loading.rejected && rejectedInvitations.length > 0" class="invitations-list">
            <div *ngFor="let invitation of rejectedInvitations" class="invitation-card">
              <div class="invitation-header">
                <div class="user-avatar rejected">
                  <span>{{ invitation.recipientName?.charAt(0) || '?' }}</span>
                </div>
                <div class="invitation-details">
                  <h3 class="user-name">{{ invitation.recipientName || 'Utilisateur' }}</h3>
                  <p class="user-email">{{ invitation.email || 'Email non disponible' }}</p>
                </div>
                <div class="invitation-status status-rejected">
                  <mat-icon>cancel</mat-icon>
                  <span>Refusée</span>
                </div>
              </div>
              
              <mat-divider></mat-divider>
              
              <div class="invitation-content">
                <div class="invitation-info">
                  <div class="info-item">
                    <span class="info-label">Date d'envoi:</span>
                    <span class="info-value">{{ getFormattedDate(invitation.createdAt) }}</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">Date de refus:</span>
                    <span class="info-value">{{ getFormattedDate(invitation.updatedAt) }}</span>
                  </div>
                  <div class="info-item" *ngIf="invitation.message">
                    <span class="info-label">Message:</span>
                    <span class="info-value message">{{ invitation.message }}</span>
                  </div>
                </div>
              </div>
              
              <div class="invitation-actions">
                <button mat-flat-button color="primary" routerLink="/protected/invitation" matTooltip="Envoyer une nouvelle invitation">
                  <mat-icon>refresh</mat-icon>
                  <span>Réinviter</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </mat-tab>
    </mat-tab-group>
  </div>
</div>
