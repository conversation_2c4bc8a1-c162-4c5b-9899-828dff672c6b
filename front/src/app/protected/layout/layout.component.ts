import {Component, ViewChild} from '@angular/core';
import {AppNavComponent} from '../app-nav/app-nav.component';
import {SidenavComponent} from '../sidenav/sidenav.component';

@Component({
  selector: 'app-layout',
  imports: [
    AppNavComponent,
    SidenavComponent,
  ],
  templateUrl: './layout.component.html',
  standalone: true,
  styleUrl: './layout.component.css'
})
export class LayoutComponent {
  @ViewChild('sideNav') sidenav!: SidenavComponent;
  toggleSideNav() {
    this.sidenav.toggle();
  }
}
