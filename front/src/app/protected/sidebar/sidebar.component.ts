import { Component } from '@angular/core';
import { MatIcon } from '@angular/material/icon';
import { RouterLink, RouterLinkActive } from '@angular/router';
import {NgClass, NgIf} from '@angular/common';

@Component({
  selector: 'app-sidebar',
  imports: [
    MatIcon,
    RouterLink,
    RouterLinkActive,
    NgClass,
    NgIf,
  ],
  templateUrl: './sidebar.component.html',
  standalone: true,
  styleUrl: './sidebar.component.css'
})
export class SidebarComponent {
  isAdmin: any;
  avatarUrl: any;
  userName: string | undefined;
  collapsed: any;
  sidebarOpen: any;
  currentRoute: string | undefined;

  logout() {
    // Implémentation de la déconnexion ici
  }

  toggleSidebar() {
    this.collapsed = !this.collapsed;
  }
}
