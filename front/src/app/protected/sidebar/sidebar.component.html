<div class="sidebar" [ngClass]="{ collapsed: collapsed }">
  <button class="toggle-btn" (click)="toggleSidebar()">
    <mat-icon>{{ collapsed ? 'chevron_right' : 'chevron_left' }}</mat-icon>
  </button>

  <div class="user-profile">
    <img *ngIf="avatarUrl" [src]="avatarUrl" class="avatar" alt="User Avatar" />
    <mat-icon *ngIf="!avatarUrl" class="avatar-icon">person</mat-icon>
    <span class="user-name">{{ userName }}</span>
  </div>

  <div class="sidebar-header">
    <div class="logo">
      <mat-icon class="logo-icon">dashboard</mat-icon>
      <h2>Dashboard Admin</h2>
    </div>
  </div>

  <ul class="sidebar-menu">
    <li routerLinkActive="active">
      <a routerLink="/dashboard/candidatures">
        <mat-icon>people</mat-icon>
        <span>Candidatures</span>
      </a>
    </li>
    <li routerLinkActive="active">
      <a routerLink="/dashboard/offres">
        <mat-icon>work</mat-icon>
        <span>Offres d'emploi</span>
      </a>
    </li>
    <li routerLinkActive="active">
      <a routerLink="/dashboard/messages">
        <mat-icon>message</mat-icon>
        <span>Messages</span>
      </a>
    </li>
    <li routerLinkActive="active">
      <a routerLink="/dashboard/profil">
        <mat-icon>account_circle</mat-icon>
        <span>Profil</span>
      </a>
    </li>
    <li *ngIf="isAdmin" routerLinkActive="active">
      <a routerLink="/dashboard/tests">
        <mat-icon>assessment</mat-icon>
        <span>Résultats des Tests</span>
      </a>
    </li>
    <li *ngIf="isAdmin" routerLinkActive="active">
      <a routerLink="/dashboard/paiements">
        <mat-icon>payment</mat-icon>
        <span>Paiements</span>
      </a>
    </li>
    <li *ngIf="isAdmin" routerLinkActive="active">
      <a routerLink="/dashboard/workspace">
        <mat-icon>business_center</mat-icon>
        <span>Espaces de travail</span>
      </a>
    </li>
    <li *ngIf="isAdmin" routerLinkActive="active">
      <a routerLink="/dashboard/rapports">
        <mat-icon>bar_chart</mat-icon>
        <span>Rapports</span>
      </a>
    </li>
    <li *ngIf="isAdmin" routerLinkActive="active">
      <a routerLink="/dashboard/parametres">
        <mat-icon>settings</mat-icon>
        <span>Paramètres</span>
      </a>
    </li>
  </ul>

  <div class="sidebar-footer">
    <button class="logout-btn" (click)="logout()">
      <mat-icon>logout</mat-icon>
      <span>Déconnexion</span>
    </button>
  </div>
</div>

