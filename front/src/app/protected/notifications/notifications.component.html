  <!-- Main Content -->
  <div class="main-content">
    <div class="notifications-container">
      <h3 class="section-title">Invitations</h3>
      <mat-card
        *ngFor="let invitation of invitations"
        class="invitation-card"
        [ngClass]="{'pending': invitation.status === 'PENDING', 'accepted': invitation.status === 'ACCEPTED'}"
        [@fadeIn]="invitation.id"
      >
        <mat-card-header>
          <mat-icon mat-card-avatar class="invitation-icon">person_add</mat-icon>
          <mat-card-title class="sender-email">{{ invitation.sender.email }}</mat-card-title>
          <mat-card-subtitle class="workspace-info">
            vous a invité à rejoindre {{ invitation.workspace.name }}
          </mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <p class="invitation-message" *ngIf="invitation.message">
            Message : {{ invitation.message }}
          </p>
          <p class="invitation-message" *ngIf="!invitation.message">Aucun message</p>
          <p class="invitation-time">
            Envoyé le : {{ invitation.createdAt | date:'short' }}
          </p>
        </mat-card-content>

        <mat-card-actions align="end">
          <button
            mat-flat-button
            class="action-btn accept-btn"
            (click)="acceptInvitation(invitation)"
            *ngIf="invitation.status === 'PENDING'"
            [disabled]="invitation.isLoading"
          >
            <span *ngIf="!invitation.isLoading">Accepter</span>
            <span *ngIf="invitation.isLoading">Acceptation...</span>
          </button>
          <button
            mat-flat-button
            class="action-btn decline-btn"
            (click)="declineInvitation(invitation)"
            *ngIf="invitation.status === 'PENDING'"
            [disabled]="invitation.isLoading"
          >
            <span *ngIf="!invitation.isLoading">Refuser</span>
            <span *ngIf="invitation.isLoading">Refus...</span>
          </button>
          <span class="status-text" *ngIf="invitation.status !== 'PENDING'">
            {{ invitation.status | titlecase }}
          </span>
        </mat-card-actions>
      </mat-card>

      <div class="no-invitations-wrapper" *ngIf="!invitations || invitations.length === 0">
        <mat-card class="no-invitations-card">
          <mat-card-content>
            <mat-icon class="no-invitations-icon">inbox</mat-icon>
            <p class="no-invitations-text">Aucune invitation pour le moment.</p>
            <button mat-stroked-button class="invite-btn" (click)="inviteSomeone()">
              Inviter quelqu'un
            </button>
          </mat-card-content>
        </mat-card>
      </div>
    </div>
  </div>
