/* src/app/components/invitations/invitations.component.css */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleDown {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.95);
  }
  100% {
    transform: scale(1);
  }
}

.dashboard-container {
  min-height: 100vh;
  background: linear-gradient(rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.95)),
  url('https://images.unsplash.com/photo-1600585154340-be6161a56a0c?ixlib=rb-4.0.3&auto=format&fit=crop&w=1350&q=80');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  font-family: 'Inter', sans-serif;
}

/* Main Content Styles */
.main-content {
  margin-top: 64px; /* Adjust based on the height of app-nav */
  flex: 1;
  padding: 40px 16px;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  min-height: calc(100vh - 64px);
}

.notifications-container {
  max-width: 600px;
  width: 100%;
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  padding: 32px;
}

.section-title {
  font-size: 28px;
  font-weight: 700;
  color: #111827;
  margin-bottom: 24px;
  text-align: left;
  letter-spacing: -0.5px;
}

.invitation-card {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
  margin-bottom: 16px;
  transition: box-shadow 0.3s ease, transform 0.3s ease;
  animation: fadeIn 0.5s ease-in-out;
  position: relative;
  overflow: hidden;
}

.invitation-card.pending {
  border-left: 4px solid #2563eb; /* Blue accent for pending */
}

.invitation-card.accepted {
  border-left: 4px solid #10b981; /* Green accent for accepted */
}

.invitation-card:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.invitation-card mat-card-header {
  padding: 20px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
}

.invitation-icon {
  color: #2563eb;
  font-size: 36px;
  width: 36px;
  height: 36px;
  margin-right: 16px;
}

.sender-email {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
}

.workspace-info {
  font-size: 14px;
  font-weight: 400;
  color: #6b7280;
  margin-top: 6px;
}

.invitation-card mat-card-content {
  padding: 20px;
}

.invitation-message {
  font-size: 15px;
  font-weight: 400;
  color: #4b5563;
  margin: 0 0 12px;
  line-height: 1.5;
}

.invitation-time {
  font-size: 13px;
  font-weight: 400;
  color: #9ca3af;
  margin: 0;
}

mat-card-actions {
  display: flex;
  justify-content: flex-end;
  padding: 12px 20px;
  border-top: 1px solid #e5e7eb;
}

.action-btn {
  font-size: 14px;
  font-weight: 600;
  padding: 8px 20px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.accept-btn {
  background: linear-gradient(90deg, #2563eb 0%, #3b82f6 100%);
  color: white;
}

.accept-btn:hover:not(:disabled) {
  background: linear-gradient(90deg, #1d4ed8 0%, #2563eb 100%);
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
  transform: translateY(-1px);
}

.accept-btn:active:not(:disabled) {
  animation: scaleDown 0.3s ease;
}

.decline-btn {
  background: linear-gradient(90deg, #dc2626 0%, #ef4444 100%);
  color: white;
}

.decline-btn:hover:not(:disabled) {
  background: linear-gradient(90deg, #b91c1c 0%, #dc2626 100%);
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
  transform: translateY(-1px);
}

.decline-btn:active:not(:disabled) {
  animation: scaleDown 0.3s ease;
}

.status-text {
  font-size: 14px;
  font-weight: 600;
  color: #6b7280;
  text-transform: capitalize;
}

.no-invitations-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
  height: calc(100vh - 200px);
}

.no-invitations-card {
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
  text-align: center;
  padding: 32px;
  animation: fadeIn 0.5s ease-in-out;
}

.no-invitations-icon {
  font-size: 56px;
  background: linear-gradient(135deg, #2563eb, #3b82f6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 16px;
}

.no-invitations-text {
  font-size: 18px;
  font-weight: 500;
  color: #4b5563;
  margin: 0 0 16px;
}

.invite-btn {
  font-size: 14px;
  font-weight: 600;
  color: #2563eb;
  border-color: #2563eb;
  padding: 8px 20px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.invite-btn:hover {
  background: #2563eb;
  color: white;
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .main-content {
    margin-top: 56px;
    padding: 32px 12px;
    min-height: calc(100vh - 56px);
  }

  .notifications-container {
    padding: 24px;
  }

  .section-title {
    font-size: 24px;
    margin-bottom: 20px;
  }

  .invitation-card mat-card-header {
    padding: 16px;
  }

  .sender-email {
    font-size: 16px;
  }

  .workspace-info {
    font-size: 13px;
  }

  .invitation-message {
    font-size: 14px;
  }

  .invitation-time {
    font-size: 12px;
  }

  .action-btn {
    font-size: 13px;
    padding: 6px 16px;
  }

  .no-invitations-wrapper {
    height: calc(100vh - 180px);
  }

  .no-invitations-card {
    padding: 24px;
  }

  .no-invitations-icon {
    font-size: 48px;
    margin-bottom: 12px;
  }

  .no-invitations-text {
    font-size: 16px;
  }

  .invite-btn {
    font-size: 13px;
    padding: 6px 16px;
  }
}

@media (max-width: 480px) {
  .main-content {
    margin-top: 48px;
    padding: 24px 8px;
    min-height: calc(100vh - 48px);
  }

  .notifications-container {
    padding: 16px;
  }

  .section-title {
    font-size: 20px;
    margin-bottom: 16px;
  }

  .invitation-card mat-card-header {
    padding: 12px;
  }

  .sender-email {
    font-size: 14px;
  }

  .workspace-info {
    font-size: 12px;
  }

  .invitation-message {
    font-size: 13px;
  }

  .invitation-time {
    font-size: 11px;
  }

  .action-btn {
    font-size: 12px;
    padding: 4px 12px;
  }

  .no-invitations-wrapper {
    height: calc(100vh - 160px);
  }

  .no-invitations-card {
    padding: 16px;
  }

  .no-invitations-icon {
    font-size: 40px;
    margin-bottom: 8px;
  }

  .no-invitations-text {
    font-size: 14px;
  }

  .invite-btn {
    font-size: 12px;
    padding: 4px 12px;
  }
}
