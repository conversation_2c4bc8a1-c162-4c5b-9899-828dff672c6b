import {Component, OnInit} from '@angular/core';
import {MatIcon} from '@angular/material/icon';
import {DatePipe, <PERSON><PERSON><PERSON>, <PERSON>For<PERSON><PERSON>, NgI<PERSON>, TitleCasePipe} from '@angular/common';
import {<PERSON><PERSON><PERSON>on} from '@angular/material/button';
import {InvitationService} from '../../services/invitation/invitation.service';
import {
  MatCard,
  MatCardActions,
  MatCardContent,
  MatCardHeader,
  MatCardSubtitle,
  MatCardTitle
} from '@angular/material/card';
import {Router} from '@angular/router';

@Component({
  selector: 'app-notifications',
  templateUrl: './notifications.component.html',
  standalone: true,
  imports: [
    MatIcon,
    NgIf,
    MatButton,
    NgForOf,
    MatCard,
    MatCardContent,
    DatePipe,
    TitleCasePipe,
    MatCardHeader,
    MatCardActions,
    MatCardTitle,
    MatCardSubtitle,
    NgClass
  ],
  styleUrls: ['./notifications.component.css']
})
export class NotificationsComponent implements OnInit {
  invitations: any[] = [];
  unreadInvitationsCount: number = 0;
  currentUserId: string = 'currentUserId';

  constructor(private invitationService: InvitationService, private router: Router) {
  }

  ngOnInit(): void {
    this.loadNotifications();

  }

  loadNotifications(): void {
    this.invitationService.loadReceivedInvitations().subscribe({
      next: (response) => {
        this.invitations = response.content;
        this.updateNotificationCount();
      },
      error: (error) => console.error('Erreur lors du chargement des invitations:', error)
    });
  }

  acceptInvitation(invitation: any): void {
    this.invitationService.acceptInvitation(invitation.id).subscribe({
      next: (updatedInvitation) => {
        const index = this.invitations.findIndex(inv => inv.id === invitation.id);
        if (index !== -1) {
          this.invitations[index] = updatedInvitation;
        }
        this.updateNotificationCount();
      },
      error: (error) => console.error('Erreur lors de l\'acceptation de l\'invitation:', error)
    });
  }

  declineInvitation(invitation: any): void {
    this.invitationService.rejectInvitation(invitation.id).subscribe({
      next: () => {
        const index = this.invitations.findIndex(inv => inv.id === invitation.id);
        if (index !== -1) {
          this.invitations[index].status = 'REJECTED';
        }
        this.updateNotificationCount();
      },
      error: (error) => console.error('Erreur lors du refus de l\'invitation:', error)
    });
  }

  private updateNotificationCount(): void {
    this.unreadInvitationsCount = this.invitations.filter(invitation => invitation.status === 'PENDING').length;
  }

  updateInvitation(updatedInvitation: any): void {
    const storedInvitations = localStorage.getItem('invitations');
    if (storedInvitations) {
      const invitations = JSON.parse(storedInvitations);
      const index = invitations.findIndex((inv: { userId: any }) => inv.userId === updatedInvitation.userId);
      if (index !== -1) {
        invitations[index] = updatedInvitation;
        localStorage.setItem('invitations', JSON.stringify(invitations));
        this.loadNotifications();
      }
    }
  }

  inviteSomeone() {
    this.router.navigate(['/invite']);
  }
}
