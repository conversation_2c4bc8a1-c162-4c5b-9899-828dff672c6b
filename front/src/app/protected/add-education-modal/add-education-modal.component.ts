import { Component, Inject } from '@angular/core';
import {
  MatDialogRef,
  MAT_DIALOG_DATA,
  MatDialogActions,
  MatDialogContent,
  MatDialogTitle
} from '@angular/material/dialog';
import {<PERSON><PERSON><PERSON>r, MatFormField, Mat<PERSON>abe<PERSON>} from '@angular/material/form-field';
import { FormsModule } from '@angular/forms';
import { MatButton } from '@angular/material/button';
import { MatInput } from '@angular/material/input';
import { MatIcon } from '@angular/material/icon';
import { NgIf } from '@angular/common';

@Component({
  selector: 'app-add-education-modal',
  templateUrl: './add-education-modal.component.html',
  standalone: true,
  imports: [
    MatFormField,
    FormsModule,
    MatDialogActions,
    MatButton,
    MatInput,
    MatDialogContent,
    MatDialogTitle,
    MatLabel,
    MatIcon,
    NgIf,
    MatError
  ],
  styleUrls: ['./add-education-modal.component.css']
})
export class AddEducationModalComponent {
  education: any = {
    school: '',
    degree: '',
    startDate: '',
    endDate: '',
    description: '',
    fieldOfStudy: '' // Added to match the template display in profile.component.html
  };

  constructor(
    public dialogRef: MatDialogRef<AddEducationModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    // If data is provided (edit mode), pre-fill the form with existing values
    if (this.data) {
      this.education.school = this.data.school || '';
      this.education.degree = this.data.degree || '';
      this.education.startDate = this.data.startDate || '';
      this.education.endDate = this.data.endDate || '';
      this.education.description = this.data.description || '';
      this.education.fieldOfStudy = this.data.fieldOfStudy || '';
    }
  }

  onNoClick(): void {
    this.dialogRef.close();
  }

  addEducation(): void {
    // Validate required fields
    if (this.education.school && this.education.degree && this.education.startDate) {
      this.dialogRef.close(this.education);
    } else {
      alert("Veuillez remplir tous les champs requis (École, Diplôme, Date de début).");
    }
  }
}
