<h1 mat-dialog-title class="dialog-title">
  <mat-icon class="icon-title">school</mat-icon>
  {{ data ? 'Modifier une formation' : 'Ajouter une formation' }}
</h1>

<div mat-dialog-content class="dialog-content dialog-form">
  <mat-form-field appearance="outline" class="full-width animate-field">
    <mat-label>École</mat-label>
    <input matInput [(ngModel)]="education.school" required />
    <mat-error *ngIf="!education.school">L'école est requise</mat-error>
  </mat-form-field>

  <mat-form-field appearance="outline" class="full-width animate-field">
    <mat-label>Diplôme</mat-label>
    <input matInput [(ngModel)]="education.degree" required />
    <mat-error *ngIf="!education.degree">Le diplôme est requis</mat-error>
  </mat-form-field>

  <mat-form-field appearance="outline" class="full-width animate-field">
    <mat-label>Domaine d'étude</mat-label>
    <input matInput [(ngModel)]="education.fieldOfStudy" />
  </mat-form-field>

  <mat-form-field appearance="outline" class="full-width animate-field">
    <mat-label>Date de début</mat-label>
    <input matInput [(ngModel)]="education.startDate" type="date" required />
    <mat-error *ngIf="!education.startDate">La date de début est requise</mat-error>
  </mat-form-field>

  <mat-form-field appearance="outline" class="full-width animate-field">
    <mat-label>Date de fin</mat-label>
    <input matInput [(ngModel)]="education.endDate" type="date" />
  </mat-form-field>

  <mat-form-field appearance="outline" class="full-width animate-field">
    <mat-label>Description</mat-label>
    <textarea matInput [(ngModel)]="education.description"></textarea>
  </mat-form-field>
</div>

<div mat-dialog-actions class="dialog-actions">
  <button mat-stroked-button color="warn" (click)="onNoClick()" class="btn-cancel">
    <mat-icon>cancel</mat-icon> Annuler
  </button>
  <button mat-flat-button color="primary" (click)="addEducation()"
          [disabled]="!education.school || !education.degree || !education.startDate"
          class="btn-add">
    <mat-icon>{{ data ? 'edit' : 'add_circle' }}</mat-icon>
    {{ data ? 'Mettre à jour' : 'Ajouter' }}
  </button>
</div>
