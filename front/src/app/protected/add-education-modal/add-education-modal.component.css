.dialog-title {
  display: flex;
  align-items: center;
  font-size: 20px;
  font-weight: 500;
  color: #254779; /* Match your primary color */
}

.dialog-title .icon-title {
  margin-right: 8px;
  font-size: 24px;
  color: #254779;
}

.dialog-content {
  padding: 10px 0;
}

.dialog-content.dialog-form {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.dialog-content .full-width {
  width: 100%;
}

.dialog-content .animate-field {
  transition: all 0.3s ease;
}

.dialog-content .animate-field:hover {
  transform: translateY(-2px);
}

.dialog-content mat-error {
  font-size: 12px;
  color: #f44336;
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 10px 0;
}

.dialog-actions .btn-cancel {
  border-color: #f44336;
  color: #f44336;
}

.dialog-actions .btn-cancel mat-icon {
  margin-right: 5px;
}

.dialog-actions .btn-add {
  background-color: #0a66c2; /* Match your accent color */
  color: white;
}

.dialog-actions .btn-add mat-icon {
  margin-right: 5px;
}
