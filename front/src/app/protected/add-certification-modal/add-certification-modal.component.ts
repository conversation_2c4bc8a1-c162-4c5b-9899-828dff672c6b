import { Component, Inject } from '@angular/core';
import {
  MatDialogRef,
  MAT_DIALOG_DATA,
  MatDialogActions,
  MatDialogContent,
  MatDialogTitle
} from '@angular/material/dialog';
import {<PERSON><PERSON><PERSON>r, MatFormField, Mat<PERSON>abel} from '@angular/material/form-field';
import { FormsModule } from '@angular/forms';
import { MatButton } from '@angular/material/button';
import { MatInput } from '@angular/material/input';
import { MatIcon } from '@angular/material/icon';
import { NgIf } from '@angular/common';

@Component({
  selector: 'app-add-certification-modal',
  templateUrl: './add-certification-modal.component.html',
  standalone: true,
  imports: [
    MatFormField,
    FormsModule,
    MatDialogActions,
    MatButton,
    MatInput,
    MatDialogContent,
    MatDialogTitle,
    MatLabel,
    MatIcon,
    NgIf,
    MatError
  ],
  styleUrls: ['./add-certification-modal.component.css']
})
export class AddCertificationModalComponent {
  certification: any = {
    name: '',
    issuingOrganization: '',
    issueDate: '',
    expirationDate: '',
    description: ''
  };

  constructor(
    public dialogRef: MatDialogRef<AddCertificationModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    // If data is provided (edit mode), pre-fill the form with existing values
    if (this.data) {
      this.certification.name = this.data.name || this.data.nom || '';
      this.certification.issuingOrganization = this.data.issuingOrganization || '';
      this.certification.issueDate = this.data.issueDate || '';
      this.certification.expirationDate = this.data.expirationDate || '';
      this.certification.description = this.data.description || '';
    }
  }

  onNoClick(): void {
    this.dialogRef.close();
  }

  addCertification(): void {
    // Validate required fields
    if (this.certification.name && this.certification.issuingOrganization && this.certification.issueDate) {
      this.dialogRef.close(this.certification);
    } else {
      alert("Veuillez remplir tous les champs requis (Nom, Organisation délivrante, Date d'obtention).");
    }
  }
}
