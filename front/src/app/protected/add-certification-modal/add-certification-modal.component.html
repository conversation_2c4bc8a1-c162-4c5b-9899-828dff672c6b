<h1 mat-dialog-title class="dialog-title">
  <mat-icon class="icon-title">workspace_premium</mat-icon>
  {{ data ? 'Modifier une certification' : 'Ajouter une certification' }}
</h1>

<div mat-dialog-content class="dialog-content certification-form">
  <mat-form-field appearance="outline" class="full-width animate-field">
    <mat-label>Nom de la certification</mat-label>
    <input matInput [(ngModel)]="certification.name" required />
    <mat-error *ngIf="!certification.name">Le nom de la certification est requis</mat-error>
  </mat-form-field>

  <mat-form-field appearance="outline" class="full-width animate-field">
    <mat-label>Organisation délivrante</mat-label>
    <input matInput [(ngModel)]="certification.issuingOrganization" required />
    <mat-error *ngIf="!certification.issuingOrganization">L'organisation délivrante est requise</mat-error>
  </mat-form-field>

  <mat-form-field appearance="outline" class="full-width animate-field">
    <mat-label>Date d'obtention</mat-label>
    <input matInput [(ngModel)]="certification.issueDate" type="date" required />
    <mat-error *ngIf="!certification.issueDate">La date d'obtention est requise</mat-error>
  </mat-form-field>

  <mat-form-field appearance="outline" class="full-width animate-field">
    <mat-label>Date d'expiration</mat-label>
    <input matInput [(ngModel)]="certification.expirationDate" type="date" />
  </mat-form-field>

  <mat-form-field appearance="outline" class="full-width animate-field">
    <mat-label>Description</mat-label>
    <textarea matInput [(ngModel)]="certification.description"></textarea>
  </mat-form-field>
</div>

<div mat-dialog-actions class="dialog-actions">
  <button mat-stroked-button color="warn" (click)="onNoClick()" class="btn-cancel">
    <mat-icon>cancel</mat-icon> Annuler
  </button>
  <button mat-flat-button color="primary" (click)="addCertification()"
          [disabled]="!certification.name || !certification.issuingOrganization || !certification.issueDate"
          class="btn-add">
    <mat-icon>{{ data ? 'edit' : 'add_circle' }}</mat-icon>
    {{ data ? 'Mettre à jour' : 'Ajouter' }}
  </button>
</div>
