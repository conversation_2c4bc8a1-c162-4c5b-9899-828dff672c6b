// Utilisation directe des codes de couleur sans variables

// Conteneur principal
.profile-container {
  max-width: 100%;
  margin: 0 auto;
  padding: 0;
  background-color: #f4f4f4;
}

// Header profil moderne
.profile-container {
  width: 100%;
}

.profile-header {
  position: relative;
  color: #fff;
  border-radius: 0;
  width: 100%;
  display: flex;
  flex-direction: column;
  padding: 0;
  margin-bottom: 30px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  overflow: hidden;

  .header-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 200px;
    background: linear-gradient(135deg, #254779, #021c6c);
    z-index: 0;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-image: url('https://images.unsplash.com/photo-1557683316-973673baf926?ixlib=rb-1.2.1&auto=format&fit=crop&w=1080&q=80');
      background-size: cover;
      background-position: center;
      opacity: 0.1;
      mix-blend-mode: overlay;
    }

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 40px;
      background: linear-gradient(to bottom, rgba(0,0,0,0) 0%, rgba(0,0,0,0.1) 100%);
    }
  }

  .profile-main-info {
    position: relative;
    z-index: 1;
    display: flex;
    padding: 30px 40px;

    .avatar-section {
      margin-right: 30px;
      position: relative;

      .avatar-container {
        display: block;
        position: relative;
        cursor: pointer;

        .avatar-icon {
          width: 150px;
          height: 150px;
          border-radius: 50%;
          border: 4px solid #fff;
          box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
          object-fit: cover;
          background-color: #fff;
        }

        .avatar-icon-placeholder {
          width: 150px;
          height: 150px;
          font-size: 100px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
          background-color: #e0e0e0;
          color: #757575;
          border: 4px solid #fff;
        }

        .avatar-overlay {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          border-radius: 50%;
          background-color: rgba(0, 0, 0, 0.5);
          display: flex;
          align-items: center;
          justify-content: center;
          opacity: 0;
          transition: opacity 0.3s ease;

          mat-icon {
            color: #fff;
            font-size: 36px;
          }
        }

        &:hover .avatar-overlay {
          opacity: 1;
        }
      }
    }

    .user-main-details {
      flex: 1;

      h1 {
        margin: 0;
        font-size: 32px;
        font-weight: 600;
        color: #fff;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
      }

      .user-title {
        font-size: 18px;
        color: rgba(255, 255, 255, 0.9);
        margin: 8px 0 20px;
      }

      .profile-actions {
        display: flex;
        gap: 12px;
        margin-top: 20px;

        button {
          border-radius: 30px;
          padding: 6px 16px;
          display: flex;
          align-items: center;
          gap: 6px;
          font-weight: 500;
          transition: all 0.3s ease;

          mat-icon {
            font-size: 18px;
          }
        }

        .edit-profile-btn {
          background-color: #0a66c2; // Bouton bleu
          color: #fff;
          position: relative;
          overflow: hidden;

          &::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 4px;
            height: 100%;
            background-color: #ff9800; // Petite touche d'orange
          }

          &:hover {
            background-color: #084e96;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
          }
        }

        .cv-btn, .complete-profile-btn {
          background-color: rgba(255, 255, 255, 0.2);
          color: #fff;
          border: 1px solid rgba(255, 255, 255, 0.4);
          position: relative;
          overflow: hidden;

          &::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 3px;
            height: 100%;
            background-color: #ff9800; // Petite touche d'orange
          }

          &:hover {
            background-color: rgba(255, 255, 255, 0.3);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
          }
        }
      }
    }
  }

  .profile-tabs {
    background-color: #fff;
    padding: 20px 40px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    border-top: 1px solid rgba(0, 0, 0, 0.1);

    .contact-info {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      margin-right: 20px;

      .contact-item {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #555;
        background-color: rgba(255, 255, 255, 0.9);
        padding: 6px 12px;
        border-radius: 30px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        mat-icon {
          color: #0a66c2; // Icône bleue
          font-size: 18px;
          position: relative;

          &::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 4px;
            height: 4px;
            background-color: #ff9800; // Petite touche d'orange
            border-radius: 50%;
          }
        }

        span {
          font-size: 13px;
          font-weight: 500;
        }
      }
    }

    // Bio capsule ultra-moderne
    .bio-capsule {
      position: relative;
      margin-left: 20px;
      flex: 1;
      min-width: 200px;
      max-width: 500px;
      height: 40px;
      transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);

      // Mode affichage
      .bio-preview {
        display: flex;
        align-items: center;
        height: 100%;
        position: relative;
        overflow: hidden;
        border-radius: 30px;
        background: rgba(0, 0, 0, 0.4); // Fond plus sombre pour meilleure lisibilité
        backdrop-filter: blur(8px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1),
                  inset 0 0 0 1px rgba(255, 255, 255, 0.2);
        padding: 0 5px;
        border-left: 3px solid #ff9800; // Bordure orange plus visible

        .bio-label {
          display: flex;
          align-items: center;
          justify-content: center;
          min-width: 40px;
          height: 24px;
          border-radius: 12px;
          background: #ff9800; // Badge orange
          margin: 0 10px 0 5px;
          flex-shrink: 0;
          position: relative;
          box-shadow: 0 2px 5px rgba(255, 152, 0, 0.3),
                    0 0 0 2px rgba(255, 152, 0, 0.5); // Contour lumineux
          animation: glow 2s infinite alternate; // Animation de pulsation

          span {
            color: white;
            font-size: 11px;
            font-weight: 700; // Plus gras
            letter-spacing: 0.5px;
            text-transform: uppercase;
            text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);
          }

          &::after {
            content: '';
            position: absolute;
            top: 50%;
            right: -5px;
            width: 8px;
            height: 8px;
            background-color: #ff9800;
            transform: translateY(-50%) rotate(45deg);
            box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
          }

          @keyframes glow {
            from { box-shadow: 0 2px 5px rgba(255, 152, 0, 0.3), 0 0 0 2px rgba(255, 152, 0, 0.5); }
            to { box-shadow: 0 2px 8px rgba(255, 152, 0, 0.5), 0 0 0 3px rgba(255, 152, 0, 0.7); }
          }
        }

        .bio-text-container {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding-right: 15px;
          height: 100%;
          cursor: pointer;
          transition: all 0.3s ease;

          .bio-text {
            color: white;
            font-size: 13px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: calc(100% - 30px);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5); // Ombre plus forte
            font-weight: 500; // Texte plus gras
            letter-spacing: 0.2px; // Meilleur espacement des lettres
            background-color: rgba(0, 0, 0, 0.3); // Fond légèrement sombre
            padding: 4px 8px;
            border-radius: 4px;
          }

          .bio-edit-hint {
            opacity: 0;
            transition: all 0.3s ease;

            mat-icon {
              font-size: 16px;
              color: rgba(255, 255, 255, 0.8);
            }
          }

          &:hover {
            background-color: rgba(255, 255, 255, 0.1);

            .bio-edit-hint {
              opacity: 1;
              transform: translateX(0);
            }
          }
        }

        .bio-empty {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 0 15px 0 5px;
          color: rgba(255, 255, 255, 0.8);
          cursor: pointer;
          height: 100%;
          transition: all 0.3s ease;
          position: relative;

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, rgba(255, 152, 0, 0.1), transparent);
            opacity: 0.5;
            pointer-events: none;
          }

          .empty-message {
            display: flex;
            flex-direction: column;
            background-color: rgba(0, 0, 0, 0.5); // Fond sombre pour meilleure lisibilité
            padding: 6px 10px;
            border-radius: 6px;
            border-left: 2px solid #ff9800;

            .primary-message {
              font-size: 12px;
              font-weight: 500;
              color: white;
              text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }

            .secondary-message {
              font-size: 10px;
              font-style: italic;
              color: #ffcc80; // Orange clair pour le message secondaire
              margin-top: 2px;
              text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
            }
          }

          mat-icon {
            font-size: 18px;
            color: #ff9800;
            animation: pulse 2s infinite;
          }

          &:hover {
            background-color: rgba(255, 152, 0, 0.15);

            .secondary-message {
              color: white;
            }

            mat-icon {
              transform: scale(1.1);
            }
          }

          @keyframes pulse {
            0% { opacity: 0.7; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.1); }
            100% { opacity: 0.7; transform: scale(1); }
          }
        }
      }

      // Mode édition
      &.editing {
        height: auto;
        z-index: 10;

        .bio-editor {
          .editor-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
            overflow: hidden;
            animation: slideDown 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
            border-left: 3px solid #ff9800; // Bordure orange
            position: relative;

            &::before {
              content: 'BIO';
              position: absolute;
              top: 10px;
              right: 10px;
              font-size: 10px;
              font-weight: 700;
              color: white;
              letter-spacing: 0.5px;
              background-color: #ff9800;
              padding: 3px 6px;
              border-radius: 4px;
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            }

            textarea {
              width: 100%;
              padding: 15px;
              border: none;
              background: transparent;
              font-size: 14px;
              line-height: 1.6;
              resize: none;
              font-family: inherit;
              color: #333;

              &:focus {
                outline: none;
              }
            }

            .editor-actions {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 8px 15px;
              background-color: #f5f7fa;
              border-top: 1px solid #eaeef2;

              .editor-shortcuts {
                display: flex;
                gap: 12px;

                span {
                  font-size: 11px;
                  color: #333;
                  background-color: #e9ecf1;
                  padding: 3px 8px;
                  border-radius: 4px;
                  font-weight: 500;
                  border: 1px solid #ddd;
                  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
                }
              }

              .editor-buttons {
                display: flex;
                gap: 5px;

                button {
                  width: 30px;
                  height: 30px;
                  line-height: 30px;

                  mat-icon {
                    font-size: 18px;
                  }
                }

                .cancel-btn {
                  color: #666;
                }

                .save-btn {
                  background-color: #ff9800; // Bouton orange
                  color: white;
                  box-shadow: 0 2px 5px rgba(255, 152, 0, 0.3);

                  &:hover {
                    background-color: #f57c00;
                    box-shadow: 0 3px 8px rgba(255, 152, 0, 0.4);
                  }
                }
              }
            }
          }
        }
      }

      // Animation pour le spinner
      @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
      }

      @keyframes slideDown {
        from {
          opacity: 0;
          transform: translateY(-10px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .spinner {
        animation: spin 1s linear infinite;
      }
    }
    }
  }


// Section content (après header)
.profile-content {
  display: flex;
  gap: 30px;
  padding: 30px 40px;
  margin-top: 0;
}

// Colonnes
.left-column {
  flex: 1;
  padding: 20px;
}

.right-column {
  flex: 2;
  padding: 20px;
  background-color: transparent;
}

// Carte info (applies to both left and right columns)
.info-card {
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background-color: #ff9800; // Petite touche d'orange sur le côté gauche
  }

  &:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }

  mat-card-header {
    background-color: #f8f9fa;
    padding: 16px 20px;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
    border-bottom: 1px solid #e0e0e0;

    mat-card-title {
      display: flex;
      align-items: center;
      font-size: 19px;
      font-weight: 600;
      color: #254779;

      .card-icon {
        font-size: 22px;
        margin-right: 10px;
        color: #0a66c2; // Icône bleue
        background-color: rgba(10, 102, 194, 0.1); // Fond bleu très léger
        padding: 8px;
        border-radius: 50%;
        position: relative;

        &::after {
          content: '';
          position: absolute;
          top: 0;
          right: 0;
          width: 6px;
          height: 6px;
          background-color: #ff9800; // Petite touche d'orange
          border-radius: 50%;
        }
      }
    }
  }

  mat-card-content {
    padding: 18px;
    font-size: 14px;
    color: #333;

    p {
      margin: 12px 0;
      display: flex;
      align-items: center;

      mat-icon {
        font-size: 17px;
        margin-right: 6px;
        color: #254779;
      }
    }
  }
}

// Specific styles for right column content
.right-column {
  .info-card {
    border: 1px solid #e0e0e0;
    transition: transform 0.2s ease;

    &:hover {
      transform: translateY(-2px);
    }

    mat-card-content {
      padding: 18px;

      .item-block {
        background-color: #fff;
        border: 1px solid #e8ecef;
        border-radius: 8px;
        padding: 14px;
        margin-bottom: 14px;
        transition: background-color 0.3s ease, box-shadow 0.3s ease;

        &:hover {
          background-color: #f8f9fa;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .item-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 6px;

          .item-title {
            flex: 1;

            h4 {
              margin: 0;
              font-size: 15px;
              font-weight: 600;
              color: #254779;
              line-height: 1.4;
            }

            .item-subtitle {
              font-size: 13px;
              color: #666;
              font-weight: 400;
              display: block;
              margin-top: 3px;
            }

            .link-title {
              font-size: 15px;
              color: #0a66c2;
              text-decoration: none;
              font-weight: 500;

              &:hover {
                text-decoration: underline;
              }
            }

            .skill-level {
              font-size: 13px;
              color: #254779;
              font-weight: 500;
              background-color: #a9c7ff;
              padding: 2px 6px;
              border-radius: 10px;
              margin-left: 6px;
            }
          }

          .item-actions {
            display: flex;
            gap: 6px;

            .action-btn {
              color: #666;
              font-size: 18px;
              transition: color 0.3s ease;

              &:hover {
                color: #0a66c2;
              }
            }
          }
        }

        .item-details {
          p {
            margin: 0;
            font-size: 13px;
            color: #666;
            line-height: 1.5;
          }
        }

        &.skill-item {
          padding: 11px 14px;

          .item-header {
            margin-bottom: 0;
          }
        }
      }

      .empty-message {
        font-size: 13px;
        color: #999;
        text-align: center;
        padding: 10px;
        background-color: #f9f9f9;
        border-radius: 8px;
        border: 1px dashed #ddd;
      }

      .info-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 16px;
        padding: 12px 16px;
        background-color: #fff;
        border-radius: 10px;
        border: 1px solid #e8ecef;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        transition: transform 0.3s ease, box-shadow 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        span {
          font-size: 14px; // Slightly larger for a professional look
          color: #333;
          font-weight: 600; // Bolder for emphasis
          font-family: 'Roboto', sans-serif; // Professional font (you can import this via Google Fonts)
          text-transform: capitalize; // Capitalize for a polished look
          display: flex;
          align-items: center;
          gap: 8px;
          position: relative;
          padding-bottom: 2px;

          // Subtle underline effect on hover
          &::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 1px;
            background: #0a66c2;
            transition: width 0.3s ease;
          }

          &:hover::after {
            width: 100%;
          }

          .icon-container {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            background: linear-gradient(135deg, #0a66c2, #004182);
            border-radius: 50%;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;

            mat-icon {
              font-size: 16px;
              color: #fff;
              width: 16px;
              height: 16px;
            }
          }

          &:hover .icon-container {
            transform: scale(1.1); // Slight scale effect on hover
          }
        }

        .add-btn {
          background: linear-gradient(135deg, #0a66c2, #004182); // Dégradé bleu
          color: #fff;
          font-weight: 600;
          font-size: 13px;
          font-family: 'Roboto', sans-serif; // Consistent professional font
          padding: 8px 18px;
          border: none;
          border-radius: 24px;
          transition: transform 0.3s ease, box-shadow 0.3s ease;
          display: flex;
          align-items: center;
          gap: 6px;
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
          border-left: 3px solid #ff9800; // Petite touche d'orange

          mat-icon {
            font-size: 16px;
            width: 16px;
            height: 16px;
            transition: transform 0.3s ease;
          }

          &:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);

            mat-icon {
              transform: rotate(90deg); // Subtle icon animation on hover
            }
          }
        }
      }
    }
  }
}

// Carte spéciale (highlight card in left column)
.highlight-card {
  border: 2px solid #a9c7ff;
  background-color: #e6f0ff;

  .add-now-btn {
    background-color: #0a66c2; // Bouton bleu
    color: #fff;
    font-weight: 600;
    font-size: 13px;
    padding: 5px 14px;
    border-radius: 20px;
    margin-top: 10px;
    transition: all 0.3s ease;
    border-left: 3px solid #ff9800; // Petite touche d'orange

    &:hover {
      background-color: #084e96; // Bleu plus foncé au survol
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .profile-content {
    flex-direction: column;
  }

  .left-column,
  .right-column {
    width: 100%;
    padding: 10px;

    .item-block {
      .item-header {
        flex-direction: column;
        align-items: flex-start;

        .item-actions {
          margin-top: 6px;
        }
      }
    }
  }
}

// Adjust the margin-top for the first cards in both columns
.left-column .info-card:first-child,
.right-column .info-card:first-child {
  margin-top: -350px;
}

// Info row styling
.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;

  span {
    flex: 1;
    font-size: 13px;
  }

  button {
    margin-left: 8px;
    white-space: nowrap;
  }
}

// Styles pour la section bio
.bio-card {
  border-left: 3px solid #ff9800; // Bordure orange à gauche

  .bio-content {
    display: flex;
    flex-direction: column;

    p {
      margin-bottom: 16px;
      line-height: 1.6;
      white-space: pre-line;
      color: #333;
    }

    .empty-bio {
      color: #888;
      font-style: italic;
    }

    .edit-bio-btn {
      align-self: flex-end;
      margin-top: 8px;
      color: #ff9800; // Bouton orange

      &:hover {
        background-color: rgba(255, 152, 0, 0.1); // Fond légèrement orange au survol
      }

      mat-icon {
        color: #ff9800; // Icône orange
      }
    }
  }
}

// Styles pour la carte CV
.cv-card {
  transition: all 0.3s ease;

  &.has-cv {
    border-left: 3px solid #0a66c2; // Bordure bleue à gauche quand un CV est présent
  }

  // Contenu quand un CV est présent
  .cv-content {
    padding: 10px 0;

    .cv-preview {
      display: flex;
      align-items: center;
      margin-bottom: 15px;

      .cv-icon {
        width: 50px;
        height: 50px;
        border-radius: 8px;
        background-color: #e3f2fd;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;

        mat-icon {
          color: #0a66c2;
          font-size: 30px;
          height: 30px;
          width: 30px;
        }
      }

      .cv-details {
        flex: 1;

        .cv-name {
          font-size: 16px;
          font-weight: 500;
          margin: 0 0 5px;
          color: #333;
        }

        .cv-date {
          font-size: 12px;
          color: #666;
          margin: 0;
        }
      }
    }

    .cv-actions {
      display: flex;
      gap: 10px;

      .view-cv-btn {
        background-color: #0a66c2;
        position: relative;
        overflow: hidden;

        &::after {
          content: '';
          position: absolute;
          top: 0;
          right: 0;
          width: 3px;
          height: 100%;
          background-color: #ff9800; // Petite touche d'orange
        }
      }

      .replace-cv-btn {
        color: #666;
        border-color: #ccc;
      }
    }
  }

  // Contenu quand aucun CV n'est présent
  .no-cv-content {
    display: flex;
    align-items: center;
    padding: 20px 0;

    .no-cv-icon {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background-color: #f5f5f5;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 20px;

      mat-icon {
        color: #999;
        font-size: 30px;
        height: 30px;
        width: 30px;
      }
    }

    .no-cv-message {
      flex: 1;

      p {
        margin: 0 0 10px;
        color: #666;
      }

      .add-cv-btn {
        background-color: #0a66c2;
        position: relative;
        overflow: hidden;

        &::after {
          content: '';
          position: absolute;
          top: 0;
          right: 0;
          width: 3px;
          height: 100%;
          background-color: #ff9800; // Petite touche d'orange
        }
      }
    }
  }
}

// Les styles pour la section bio sont maintenant dans le composant bio-section

// Message de succès pour l'ajout de CV
.cv-success-message {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background-color: #4caf50;
  color: white;
  padding: 12px 20px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  z-index: 9999;
  animation: slideIn 0.3s ease, fadeOut 0.3s ease 2.7s;

  mat-icon {
    font-size: 20px;
  }

  @keyframes slideIn {
    from { transform: translateX(100px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
  }

  @keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
  }
}
