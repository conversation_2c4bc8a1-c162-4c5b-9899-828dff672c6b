<div class="complete-profile-modal">
  <div class="modal-header">
    <h2>Complétez votre profil</h2>
    <button mat-icon-button class="close-button" (click)="cancel()">
      <mat-icon>close</mat-icon>
    </button>
  </div>

  <div class="progress-container">
    <div class="progress-bar" [style.width.%]="getProgressPercentage()"></div>
  </div>
  <div class="progress-text">
    <span>{{ getProgressPercentage() }}% complété</span>
    <span *ngIf="totalSteps > 0">{{ completedItems.length }} sur {{ totalSteps }} éléments</span>
  </div>

  <div class="modal-content">
    <div class="steps-container">
      <!-- Navigation tabs -->
      <div class="steps-navigation">
        <button
          *ngIf="showPersonalInfo"
          [class.active]="currentStep === 0"
          [class.completed]="isSectionComplete('Numéro de téléphone') && isSectionComplete('Adresse') && isSectionComplete('Date de naissance')"
          (click)="currentStep = 0">
          <mat-icon>person</mat-icon>
          <span>Informations personnelles</span>
        </button>
        <button
          *ngIf="showSkills"
          [class.active]="currentStep === 1"
          [class.completed]="isSectionComplete('Compétences')"
          (click)="currentStep = 1">
          <mat-icon>psychology</mat-icon>
          <span>Compétences</span>
        </button>
        <button
          *ngIf="showEducation"
          [class.active]="currentStep === 2"
          [class.completed]="isSectionComplete('Formations')"
          (click)="currentStep = 2">
          <mat-icon>school</mat-icon>
          <span>Formation</span>
        </button>
        <button
          *ngIf="showExperience"
          [class.active]="currentStep === 3"
          [class.completed]="isSectionComplete('Expériences')"
          (click)="currentStep = 3">
          <mat-icon>work</mat-icon>
          <span>Expérience</span>
        </button>
        <button
          *ngIf="showCertifications"
          [class.active]="currentStep === 4"
          [class.completed]="isSectionComplete('Certifications')"
          (click)="currentStep = 4">
          <mat-icon>verified</mat-icon>
          <span>Certifications</span>
        </button>
        <button
          *ngIf="showLinks"
          [class.active]="currentStep === 5"
          [class.completed]="isSectionComplete('Liens')"
          (click)="currentStep = 5">
          <mat-icon>link</mat-icon>
          <span>Liens</span>
        </button>
        <button
          *ngIf="showCV"
          [class.active]="currentStep === 6"
          [class.completed]="isSectionComplete('CV')"
          (click)="currentStep = 6">
          <mat-icon>description</mat-icon>
          <span>CV</span>
        </button>
      </div>

      <!-- Form content -->
      <div class="step-content">
        <form [formGroup]="profileForm">
          <!-- Messages -->
          <div class="message success" *ngIf="successMessage">
            <mat-icon>check_circle</mat-icon>
            <span>{{ successMessage }}</span>
          </div>
          <div class="message error" *ngIf="errorMessage">
            <mat-icon>error</mat-icon>
            <span>{{ errorMessage }}</span>
          </div>

          <!-- Step 1: Personal Information -->
          <div class="step-panel" *ngIf="currentStep === 0 && showPersonalInfo">
            <h3>Informations personnelles</h3>
            <p class="step-description">Complétez vos informations personnelles pour améliorer votre profil.</p>

            <div class="form-row">
              <mat-form-field>
                <mat-label>Prénom</mat-label>
                <input matInput formControlName="firstName" required>
                <mat-error *ngIf="profileForm.get('firstName')?.errors?.['required']">
                  Le prénom est requis
                </mat-error>
              </mat-form-field>

              <mat-form-field>
                <mat-label>Nom</mat-label>
                <input matInput formControlName="lastName" required>
                <mat-error *ngIf="profileForm.get('lastName')?.errors?.['required']">
                  Le nom est requis
                </mat-error>
              </mat-form-field>
            </div>

            <mat-form-field class="full-width">
              <mat-label>Email</mat-label>
              <input matInput formControlName="email" type="email" required>
              <mat-error *ngIf="profileForm.get('email')?.errors?.['required']">
                L'email est requis
              </mat-error>
              <mat-error *ngIf="profileForm.get('email')?.errors?.['email']">
                Veuillez entrer un email valide
              </mat-error>
            </mat-form-field>

            <mat-form-field class="full-width">
              <mat-label>Téléphone</mat-label>
              <input matInput formControlName="phoneNumber">
            </mat-form-field>

            <mat-form-field class="full-width">
              <mat-label>Adresse</mat-label>
              <input matInput formControlName="address">
            </mat-form-field>

            <mat-form-field class="full-width">
              <mat-label>Date de naissance</mat-label>
              <input matInput formControlName="dateOfBirth" type="date">
            </mat-form-field>

            <div class="form-actions">
              <button mat-button type="button" (click)="cancel()">Annuler</button>
              <button
                mat-raised-button
                color="primary"
                type="button"
                [disabled]="!profileForm.get('firstName')?.valid || !profileForm.get('lastName')?.valid || !profileForm.get('email')?.valid || isSaving"
                (click)="updatePersonalInfo()">
                <div class="button-content">
                  <span *ngIf="isSaving" class="spinner-container">
                    <mat-spinner diameter="20"></mat-spinner>
                  </span>
                  <span *ngIf="!isSaving">Enregistrer et continuer</span>
                </div>
              </button>
            </div>
          </div>

          <!-- Step 2: Skills -->
          <div class="step-panel" *ngIf="currentStep === 1 && showSkills">
            <h3>Compétences</h3>
            <p class="step-description">Ajoutez vos compétences pour mettre en valeur votre expertise.</p>

            <div formGroupName="newSkill">
              <div class="form-row">
                <mat-form-field class="full-width">
                  <mat-label>Nom de la compétence</mat-label>
                  <input matInput formControlName="name" placeholder="Ex: JavaScript, Management, Marketing...">
                </mat-form-field>
              </div>

              <div class="form-row">
                <mat-form-field class="full-width">
                  <mat-label>Niveau</mat-label>
                  <mat-select formControlName="level">
                    <mat-option value="Débutant">Débutant</mat-option>
                    <mat-option value="Intermédiaire">Intermédiaire</mat-option>
                    <mat-option value="Avancé">Avancé</mat-option>
                    <mat-option value="Expert">Expert</mat-option>
                  </mat-select>
                </mat-form-field>
              </div>
            </div>

            <div class="form-actions">
              <button mat-button type="button" (click)="prevStep()">Précédent</button>
              <button
                mat-raised-button
                color="primary"
                type="button"
                [disabled]="!profileForm.get('newSkill.name')?.value || isSaving"
                (click)="addSkill()">
                <div class="button-content">
                  <span *ngIf="isSaving" class="spinner-container">
                    <mat-spinner diameter="20"></mat-spinner>
                  </span>
                  <span *ngIf="!isSaving">Ajouter et continuer</span>
                </div>
              </button>
            </div>
          </div>

          <!-- Step 3: Education -->
          <div class="step-panel" *ngIf="currentStep === 2 && showEducation">
            <h3>Formation</h3>
            <p class="step-description">Ajoutez vos formations pour mettre en valeur votre parcours académique.</p>

            <div formGroupName="newEducation">
              <div class="form-row">
                <mat-form-field class="full-width">
                  <mat-label>École / Université</mat-label>
                  <input matInput formControlName="school">
                </mat-form-field>
              </div>

              <div class="form-row">
                <mat-form-field>
                  <mat-label>Diplôme</mat-label>
                  <input matInput formControlName="degree">
                </mat-form-field>

                <mat-form-field>
                  <mat-label>Domaine d'étude</mat-label>
                  <input matInput formControlName="fieldOfStudy">
                </mat-form-field>
              </div>

              <div class="form-row">
                <mat-form-field>
                  <mat-label>Date de début</mat-label>
                  <input matInput formControlName="startDate" type="date">
                </mat-form-field>

                <mat-form-field>
                  <mat-label>Date de fin</mat-label>
                  <input matInput formControlName="endDate" type="date">
                </mat-form-field>
              </div>

              <div class="form-row">
                <mat-form-field class="full-width">
                  <mat-label>Description</mat-label>
                  <textarea matInput formControlName="description" rows="3"></textarea>
                </mat-form-field>
              </div>
            </div>

            <div class="form-actions">
              <button mat-button type="button" (click)="prevStep()">Précédent</button>
              <button
                mat-raised-button
                color="primary"
                type="button"
                [disabled]="!profileForm.get('newEducation.school')?.value || !profileForm.get('newEducation.degree')?.value || isSaving"
                (click)="addEducation()">
                <div class="button-content">
                  <span *ngIf="isSaving" class="spinner-container">
                    <mat-spinner diameter="20"></mat-spinner>
                  </span>
                  <span *ngIf="!isSaving">Ajouter et continuer</span>
                </div>
              </button>
            </div>
          </div>

          <!-- Step 4: Experience -->
          <div class="step-panel" *ngIf="currentStep === 3 && showExperience">
            <h3>Expérience professionnelle</h3>
            <p class="step-description">Ajoutez vos expériences professionnelles pour mettre en valeur votre parcours.</p>

            <div formGroupName="newExperience">
              <div class="form-row">
                <mat-form-field class="full-width">
                  <mat-label>Titre du poste</mat-label>
                  <input matInput formControlName="title">
                </mat-form-field>
              </div>

              <div class="form-row">
                <mat-form-field>
                  <mat-label>Entreprise</mat-label>
                  <input matInput formControlName="company">
                </mat-form-field>

                <mat-form-field>
                  <mat-label>Lieu</mat-label>
                  <input matInput formControlName="location">
                </mat-form-field>
              </div>

              <div class="form-row">
                <mat-form-field>
                  <mat-label>Date de début</mat-label>
                  <input matInput formControlName="startDate" type="date">
                </mat-form-field>

                <mat-form-field>
                  <mat-label>Date de fin</mat-label>
                  <input matInput formControlName="endDate" type="date">
                </mat-form-field>
              </div>

              <div class="form-row">
                <mat-form-field class="full-width">
                  <mat-label>Description</mat-label>
                  <textarea matInput formControlName="description" rows="3"></textarea>
                </mat-form-field>
              </div>
            </div>

            <div class="form-actions">
              <button mat-button type="button" (click)="prevStep()">Précédent</button>
              <button
                mat-raised-button
                color="primary"
                type="button"
                [disabled]="!profileForm.get('newExperience.title')?.value || !profileForm.get('newExperience.company')?.value || isSaving"
                (click)="addExperience()">
                <div class="button-content">
                  <span *ngIf="isSaving" class="spinner-container">
                    <mat-spinner diameter="20"></mat-spinner>
                  </span>
                  <span *ngIf="!isSaving">Ajouter et continuer</span>
                </div>
              </button>
            </div>
          </div>

          <!-- Step 5: Certifications -->
          <div class="step-panel" *ngIf="currentStep === 4 && showCertifications">
            <h3>Certifications</h3>
            <p class="step-description">Ajoutez vos certifications pour mettre en valeur vos qualifications.</p>

            <div formGroupName="newCertification">
              <div class="form-row">
                <mat-form-field class="full-width">
                  <mat-label>Nom de la certification</mat-label>
                  <input matInput formControlName="name">
                </mat-form-field>
              </div>

              <div class="form-row">
                <mat-form-field class="full-width">
                  <mat-label>Organisation émettrice</mat-label>
                  <input matInput formControlName="issuingOrganization">
                </mat-form-field>
              </div>

              <div class="form-row">
                <mat-form-field>
                  <mat-label>Date d'obtention</mat-label>
                  <input matInput formControlName="issueDate" type="date">
                </mat-form-field>

                <mat-form-field>
                  <mat-label>Date d'expiration</mat-label>
                  <input matInput formControlName="expirationDate" type="date">
                </mat-form-field>
              </div>

              <div class="form-row">
                <mat-form-field class="full-width">
                  <mat-label>ID de la certification</mat-label>
                  <input matInput formControlName="credentialID">
                </mat-form-field>
              </div>
            </div>

            <div class="form-actions">
              <button mat-button type="button" (click)="prevStep()">Précédent</button>
              <button
                mat-raised-button
                color="primary"
                type="button"
                [disabled]="!profileForm.get('newCertification.name')?.value || !profileForm.get('newCertification.issuingOrganization')?.value || isSaving"
                (click)="addCertification()">
                <div class="button-content">
                  <span *ngIf="isSaving" class="spinner-container">
                    <mat-spinner diameter="20"></mat-spinner>
                  </span>
                  <span *ngIf="!isSaving">Ajouter et continuer</span>
                </div>
              </button>
            </div>
          </div>

          <!-- Step 6: Links -->
          <div class="step-panel" *ngIf="currentStep === 5 && showLinks">
            <h3>Liens</h3>
            <p class="step-description">Ajoutez des liens vers vos profils en ligne, portfolio, ou autres sites web pertinents.</p>

            <div formGroupName="newLink">
              <div class="form-row">
                <mat-form-field class="full-width">
                  <mat-label>URL</mat-label>
                  <input matInput formControlName="url" placeholder="https://...">
                  <mat-error *ngIf="profileForm.get('newLink.url')?.errors?.['pattern']">
                    Veuillez entrer une URL valide (commençant par http:// ou https://)
                  </mat-error>
                </mat-form-field>
              </div>
            </div>

            <div class="form-actions">
              <button mat-button type="button" (click)="prevStep()">Précédent</button>
              <button
                mat-raised-button
                color="primary"
                type="button"
                [disabled]="!profileForm.get('newLink.url')?.value || profileForm.get('newLink.url')?.errors?.['pattern'] || isSaving"
                (click)="addLink()">
                <div class="button-content">
                  <span *ngIf="isSaving" class="spinner-container">
                    <mat-spinner diameter="20"></mat-spinner>
                  </span>
                  <span *ngIf="!isSaving">Ajouter et continuer</span>
                </div>
              </button>
            </div>
          </div>

          <!-- Step 7: CV -->
          <div class="step-panel" *ngIf="currentStep === 6 && showCV">
            <h3>CV</h3>
            <p class="step-description">Téléchargez votre CV pour compléter votre profil.</p>

            <div class="cv-upload">
              <div class="upload-zone">
                <input type="file" id="cv-file" (change)="uploadCV($event)" accept=".pdf,.doc,.docx" hidden>
                <label for="cv-file" class="upload-label">
                  <mat-icon>cloud_upload</mat-icon>
                  <span>Cliquez pour télécharger votre CV</span>
                  <span class="upload-hint">Formats acceptés: PDF, DOC, DOCX</span>
                </label>
              </div>
            </div>

            <div class="form-actions">
              <button mat-button type="button" (click)="prevStep()">Précédent</button>
              <button
                mat-raised-button
                color="primary"
                type="button"
                [disabled]="isSaving"
                (click)="completeProfile()">
                <div class="button-content">
                  <span *ngIf="isSaving" class="spinner-container">
                    <mat-spinner diameter="20"></mat-spinner>
                  </span>
                  <span *ngIf="!isSaving">Terminer</span>
                </div>
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
