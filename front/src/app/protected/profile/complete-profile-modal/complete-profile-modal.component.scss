.complete-profile-modal {
  max-width: 800px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e0e0e0;

  h2 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #254779;
  }

  .close-button {
    color: #666666;

    &:hover {
      color: #254779;
    }
  }
}

.progress-container {
  height: 6px;
  background-color: #f0f0f0;
  width: 100%;
  overflow: hidden;

  .progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #0a66c2, #3a6eaf);
    transition: width 0.5s ease;
  }
}

.progress-text {
  display: flex;
  justify-content: space-between;
  padding: 8px 24px;
  font-size: 13px;
  color: #666666;
  background-color: #f9f9f9;
}

.modal-content {
  flex: 1;
  overflow: auto;
  padding: 0;
}

.steps-container {
  display: flex;
  height: 100%;

  .steps-navigation {
    width: 220px;
    background-color: #f5f7fa;
    padding: 20px 0;
    display: flex;
    flex-direction: column;
    gap: 5px;
    border-right: 1px solid #e0e0e0;

    button {
      display: flex;
      align-items: center;
      gap: 10px;
      padding: 12px 16px;
      background: transparent;
      border: none;
      text-align: left;
      font-size: 14px;
      color: #666666;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
        width: 3px;
        background-color: transparent;
        transition: background-color 0.3s ease;
      }

      mat-icon {
        font-size: 20px;
        color: #888888;
      }

      &:hover {
        background-color: rgba(10, 102, 194, 0.05);
        color: #254779;

        mat-icon {
          color: #254779;
        }
      }

      &.active {
        background-color: rgba(10, 102, 194, 0.1);
        color: #0a66c2;
        font-weight: 500;

        &::after {
          background-color: #0a66c2;
        }

        mat-icon {
          color: #0a66c2;
        }
      }

      &.completed {
        color: #4caf50;

        &::after {
          background-color: #4caf50;
        }

        mat-icon {
          color: #4caf50;
        }
      }
    }
  }

  .step-content {
    flex: 1;
    padding: 24px;
    overflow: auto;

    .step-panel {
      h3 {
        margin: 0 0 8px 0;
        font-size: 18px;
        font-weight: 600;
        color: #254779;
      }

      .step-description {
        margin: 0 0 24px 0;
        font-size: 14px;
        color: #666666;
      }
    }
  }
}

.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;

  mat-form-field {
    flex: 1;
  }
}

.full-width {
  width: 100%;
}

.form-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #e0e0e0;
}

.message {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border-radius: 4px;
  margin-bottom: 16px;

  mat-icon {
    font-size: 20px;
  }

  &.success {
    background-color: #e8f5e9;
    color: #2e7d32;

    mat-icon {
      color: #2e7d32;
    }
  }

  &.error {
    background-color: #ffebee;
    color: #c62828;

    mat-icon {
      color: #c62828;
    }
  }
}

.cv-upload {
  margin: 24px 0;

  .upload-zone {
    border: 2px dashed #a9c7ff;
    border-radius: 8px;
    padding: 40px 20px;
    text-align: center;
    background-color: #f0f7ff;
    transition: all 0.3s ease;

    &:hover {
      background-color: #e6f0ff;
      border-color: #0a66c2;
    }

    .upload-label {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 12px;
      cursor: pointer;

      mat-icon {
        font-size: 48px;
        color: #0a66c2;
      }

      span {
        font-size: 16px;
        color: #254779;
        font-weight: 500;
      }

      .upload-hint {
        font-size: 12px;
        color: #666666;
        font-weight: normal;
      }
    }
  }
}

// Styles pour le spinner dans les boutons
button {
  position: relative;

  .button-content {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 24px;
    position: relative;
  }

  .spinner-container {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
  }

  mat-spinner {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}

// Media queries pour la responsivité
@media (max-width: 768px) {
  .steps-container {
    flex-direction: column;

    .steps-navigation {
      width: 100%;
      flex-direction: row;
      overflow-x: auto;
      padding: 10px;

      button {
        flex-shrink: 0;
        padding: 8px 12px;

        &::after {
          width: 100%;
          height: 3px;
          bottom: 0;
          top: auto;
        }

        span {
          display: none;
        }
      }
    }
  }

  .form-row {
    flex-direction: column;
    gap: 0;
  }
}
