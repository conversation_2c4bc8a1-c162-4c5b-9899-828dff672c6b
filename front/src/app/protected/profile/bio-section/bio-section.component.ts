import { Component, Input, OnInit, Output, EventEmitter, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { FormsModule } from '@angular/forms';
import { trigger, state, style, animate, transition } from '@angular/animations';
import { MatFormField, MatLabel, MatHint } from '@angular/material/form-field';
import {UserService} from '../../../services/user/user.service';
// Import du modèle UserProfile pour le typage
import { UserProfile } from '../../../models/user-profile.model';

@Component({
  selector: 'app-bio-section',
  templateUrl: './bio-section.component.html',
  styleUrls: ['./bio-section.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatFormFieldModule,
    MatFormField,
    MatLabel,
    MatHint,
    FormsModule
  ],
  animations: [
    trigger('fadeInOut', [
      state('void', style({
        opacity: 0,
        transform: 'translateY(20px)'
      })),
      transition('void => *', animate('300ms ease-in-out')),
      transition('* => void', animate('200ms ease-in-out'))
    ]),
    trigger('expandCollapse', [
      state('collapsed', style({
        maxHeight: '100px',
        overflow: 'hidden'
      })),
      state('expanded', style({
        maxHeight: '1000px'
      })),
      transition('collapsed <=> expanded', animate('300ms ease-in-out'))
    ]),
    trigger('rotateIcon', [
      state('collapsed', style({
        transform: 'rotate(0deg)'
      })),
      state('expanded', style({
        transform: 'rotate(180deg)'
      })),
      transition('collapsed <=> expanded', animate('300ms ease-in-out'))
    ])
  ]
})
export class BioSectionComponent implements OnInit {
  @Input() bio: string = '';
  @Input() userId: string | null = null;
  @Output() bioUpdated = new EventEmitter<string>();

  isEditing: boolean = false;
  tempBio: string = '';
  isSaving: boolean = false;
  errorMessage: string | null = null;
  isExpanded: boolean = false;

  constructor(
    private userService: UserService,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.tempBio = this.bio || '';
  }

  toggleEdit(): void {
    if (this.isEditing) {
      // Cancel editing
      this.isEditing = false;
      this.tempBio = this.bio || '';
    } else {
      // Start editing
      this.isEditing = true;
      this.tempBio = this.bio || '';
    }
  }

  saveBio(): void {
    if (!this.tempBio || !this.tempBio.trim()) {
      this.errorMessage = 'La bio ne peut pas être vide';
      return;
    }

    this.isSaving = true;
    this.errorMessage = null;

    // Créer un objet avec les informations minimales nécessaires
    // Utiliser le type UserProfile pour éviter l'erreur TS7053
    const updatedProfile: Partial<UserProfile> = {
      bio: this.tempBio.trim()
    };

    // Si nous avons l'ID de l'utilisateur, l'ajouter à la requête
    if (this.userId) {
      updatedProfile.userId = this.userId;
    }

    console.log('Envoi de la mise à jour de la bio:', updatedProfile);

    this.userService.updateFullUserProfile(updatedProfile).subscribe({
      next: (response) => {
        console.log('Bio mise à jour avec succès:', response);
        this.isSaving = false;
        this.isEditing = false;
        this.bio = this.tempBio.trim();
        this.bioUpdated.emit(this.bio);
        this.cdr.detectChanges();
      },
      error: (err) => {
        console.error('Erreur lors de la mise à jour de la bio:', err);
        this.isSaving = false;
        this.errorMessage = 'Erreur lors de la mise à jour de la bio: ' + (err.message || 'Veuillez réessayer');
        this.cdr.detectChanges();
      }
    });
  }

  toggleExpand(): void {
    this.isExpanded = !this.isExpanded;
  }

  shouldShowExpandButton(): boolean {
    // Simplifier cette méthode pour éviter les erreurs DOM
    if (!this.bio) return false;

    // Si la bio contient plus de 150 caractères, on suppose qu'elle est longue
    return this.bio.length > 150;
  }
}
