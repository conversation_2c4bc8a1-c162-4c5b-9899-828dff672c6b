import { Component, Inject } from '@angular/core';
import {
  MatDialogRef,
  MAT_DIALOG_DATA,
  MatDialogTitle,
  MatDialogActions,
  MatDialogContent
} from '@angular/material/dialog';
import { FormsModule } from '@angular/forms';
import {<PERSON><PERSON><PERSON>r, MatFormField, <PERSON><PERSON>abe<PERSON>} from '@angular/material/form-field';
import { MatButton } from '@angular/material/button';
import { MatInput } from '@angular/material/input';
import { MatIcon } from '@angular/material/icon';
import { MatOption, MatSelect } from '@angular/material/select';
import { NgIf } from '@angular/common';

@Component({
  selector: 'app-add-skill-modal',
  templateUrl: './add-skill-modal.component.html',
  styleUrls: ['./add-skill-modal.component.css'],
  standalone: true,
  imports: [
    MatFormField,
    FormsModule,
    MatButton,
    MatInput,
    MatDialogTitle,
    MatLabel,
    MatIcon,
    MatSelect,
    MatO<PERSON>,
    <PERSON><PERSON><PERSON>ogA<PERSON>,
    <PERSON><PERSON><PERSON>og<PERSON>ontent,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>
  ]
})
export class AddSkillModalComponent {
  competence: { nom: string; niveau: string } = { nom: '', niveau: '' };
  levels: string[] = ['Débutant', 'Intermédiaire', 'Avancé', 'Expert']; // Options for the niveau dropdown

  constructor(
    public dialogRef: MatDialogRef<AddSkillModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    // If data is provided (edit mode), pre-fill the form with existing values
    if (this.data) {
      this.competence.nom = this.data.nom || this.data || ''; // Fallback to data if it's a string (current backend)
      this.competence.niveau = this.data.niveau || '';
    }
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  onAddCompetence(): void {
    if (this.competence.nom && this.competence.niveau) {
      this.dialogRef.close(this.competence);
    } else {
      alert("Veuillez remplir tous les champs (Nom de la compétence et Niveau).");
    }
  }
}
