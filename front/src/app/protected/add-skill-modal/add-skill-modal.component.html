<h1 mat-dialog-title class="dialog-title">
  <mat-icon class="icon-title">psychology</mat-icon>
  {{ data ? 'Modifier une compétence' : 'Ajouter une compétence' }}
</h1>

<div mat-dialog-content class="dialog-content dialog-form">
  <mat-form-field appearance="outline" class="full-width animate-field">
    <mat-label>Nom de la compétence</mat-label>
    <input matInput [(ngModel)]="competence.nom" required />
    <mat-error *ngIf="!competence.nom">Le nom de la compétence est requis</mat-error>
  </mat-form-field>

  <mat-form-field appearance="outline" class="full-width animate-field">
    <mat-label>Niveau</mat-label>
    <mat-select [(ngModel)]="competence.niveau" required>
      <mat-option *ngFor="let level of levels" [value]="level">{{ level }}</mat-option>
    </mat-select>
    <mat-error *ngIf="!competence.niveau">Le niveau est requis</mat-error>
  </mat-form-field>
</div>

<div mat-dialog-actions class="dialog-actions">
  <button mat-stroked-button color="warn" (click)="onCancel()" class="btn-cancel">
    <mat-icon>cancel</mat-icon> Annuler
  </button>
  <button mat-flat-button color="primary" (click)="onAddCompetence()"
          [disabled]="!competence.nom || !competence.niveau"
          class="btn-add">
    <mat-icon>{{ data ? 'edit' : 'add_circle' }}</mat-icon>
    {{ data ? 'Mettre à jour' : 'Ajouter' }}
  </button>
</div>
