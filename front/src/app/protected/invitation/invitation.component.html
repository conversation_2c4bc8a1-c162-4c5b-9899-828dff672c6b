<div class="invitation-container">
  <div class="invitation-header">
    <div class="header-content">
      <div class="header-icon">
        <mat-icon>mail_outline</mat-icon>
      </div>
      <h1 class="header-title">Inviter un utilisateur</h1>
      <p class="header-subtitle">Invitez un utilisateur à rejoindre votre espace de travail</p>
    </div>
  </div>

  <div class="invitation-card">
    <mat-horizontal-stepper [linear]="true" #stepper class="custom-stepper">
      <!-- Step 1: Choose User -->
      <mat-step [stepControl]="step1Form">
        <form [formGroup]="step1Form" class="step-form">
          <ng-template matStepLabel>Choisissez un utilisateur</ng-template>

          <div class="step-content">
            <div class="step-icon">
              <mat-icon>person_search</mat-icon>
            </div>
            <h2 class="step-title">Sélectionner un utilisateur</h2>
            <p class="step-description">Recherchez et sélectionnez l'utilisateur que vous souhaitez inviter</p>

            <div *ngIf="errorMessage" class="error-message">
              <mat-icon>error</mat-icon>
              <span>{{ errorMessage }}</span>
            </div>

            <mat-form-field appearance="outline" class="custom-form-field">
              <mat-label>Rechercher un utilisateur</mat-label>
              <mat-icon matPrefix class="search-icon">search</mat-icon>
              <input type="text"
                    placeholder="Nom ou email de l'utilisateur"
                    (keyup)="filterUsers($event)"
                    matInput
                    formControlName="user"
                    [matAutocomplete]="auto">
              <mat-autocomplete #auto="matAutocomplete" [displayWith]="displayUserFn" class="custom-autocomplete">
                @for (user of users; track user) {
                  <mat-option [value]="user.id" class="user-option">
                    <div class="user-option-content">
                      <div class="user-avatar">
                        <img [src]="getAvatarUrl(user.profilePicture)" alt="{{ user.name || 'User' }}" class="user-picture" (error)="onImageError($event)">
                      </div>
                      <div class="user-info">
                        <span class="user-name">{{ user.name || 'Utilisateur' }}</span>
                        <span class="user-email">{{ user.email || 'Email non disponible' }}</span>
                      </div>
                      <div class="user-select-indicator">
                        <mat-icon>arrow_forward_ios</mat-icon>
                      </div>
                    </div>
                  </mat-option>
                }
                @if (users.length === 0 && !loading) {
                  <mat-option disabled class="no-results">
                    <span>
                      <mat-icon>search_off</mat-icon>
                      Aucun utilisateur trouvé
                    </span>
                  </mat-option>
                }
                @if (loading) {
                  <mat-option disabled class="loading-results">
                    <span>
                      <mat-spinner diameter="20"></mat-spinner>
                      Chargement des utilisateurs...
                    </span>
                  </mat-option>
                }
              </mat-autocomplete>
              <mat-error *ngIf="step1Form.controls['user'].hasError('required')">
                Veuillez sélectionner un utilisateur
              </mat-error>
            </mat-form-field>
          </div>

          <div class="button-group">
            <button mat-flat-button matStepperNext [disabled]="step1Form.invalid" class="next-button">
              <span>Suivant</span>
              <mat-icon>arrow_forward</mat-icon>
            </button>
          </div>
        </form>
      </mat-step>

      <!-- Step 2: Message -->
      <mat-step [stepControl]="step2Form">
        <form [formGroup]="step2Form" class="step-form">
          <ng-template matStepLabel>Personnaliser l'invitation</ng-template>

          <div class="step-content">
            <div class="step-icon">
              <mat-icon>message</mat-icon>
            </div>
            <h2 class="step-title">Ajouter un message personnalisé</h2>
            <p class="step-description">Ajoutez un message pour personnaliser votre invitation</p>

            <div class="selected-user-card" *ngIf="selectedUser">
              <div class="user-avatar large">
                <img [src]="getAvatarUrl(selectedUser.profilePicture)" alt="{{ selectedUser.name || 'User' }}" class="user-picture" (error)="onImageError($event)">
              </div>
              <div class="user-details">
                <h3 class="user-name">{{ selectedUser.name || 'Utilisateur' }}</h3>
                <p class="user-email">{{ selectedUser.email || 'Email non disponible' }}</p>
                <div class="user-status">
                  <span class="status-indicator"></span>
                  <span class="status-text">Destinataire de l'invitation</span>
                </div>
              </div>
            </div>

            <!-- Hidden email field for form validation -->
            <input type="hidden" formControlName="email">

            <mat-form-field appearance="outline" class="custom-form-field message-field">
              <mat-label>Message d'invitation</mat-label>
              <textarea matInput
                        placeholder="Écrivez votre message ici..."
                        formControlName="message"
                        rows="4"></textarea>
              <mat-icon matSuffix>edit</mat-icon>
              <mat-hint>Ajoutez un message personnel pour expliquer la raison de l'invitation</mat-hint>
            </mat-form-field>
          </div>

          <div class="button-group">
            <button mat-stroked-button matStepperPrevious class="back-button">
              <mat-icon>arrow_back</mat-icon>
              <span>Retour</span>
            </button>
            <button mat-flat-button matStepperNext [disabled]="step2Form.invalid" class="next-button">
              <span>Suivant</span>
              <mat-icon>arrow_forward</mat-icon>
            </button>
          </div>
        </form>
      </mat-step>

      <!-- Step 3: Confirmation -->
      <mat-step>
        <ng-template matStepLabel>Confirmation</ng-template>

        <div class="step-content">
          <div class="step-icon">
            <mat-icon>check_circle</mat-icon>
          </div>
          <h2 class="step-title">Confirmer l'invitation</h2>
          <p class="step-description">Vérifiez les détails avant d'envoyer l'invitation</p>

          <div class="confirmation-card">
            <div class="confirmation-header">
              <mat-icon>mail</mat-icon>
              <h3>Détails de l'invitation</h3>
            </div>

            <div class="confirmation-content">
              <div class="confirmation-item" *ngIf="selectedUser">
                <span class="item-label">Destinataire:</span>
                <div class="item-value user-value">
                  <div class="user-avatar small">
                    <img [src]="getAvatarUrl(selectedUser.profilePicture)" alt="{{ selectedUser.name || 'User' }}" class="user-picture" (error)="onImageError($event)">
                  </div>
                  <div class="user-confirmation-info">
                    <div class="user-name">{{ selectedUser.name || 'Utilisateur' }}</div>
                    <div class="user-email">{{ selectedUser.email || 'Email non disponible' }}</div>
                  </div>
                  <div class="user-badge">
                    <mat-icon>person</mat-icon>
                    <span>Utilisateur</span>
                  </div>
                </div>
              </div>

              <div class="confirmation-item" *ngIf="step2Form.value.message">
                <span class="item-label">Message:</span>
                <div class="item-value message-value">
                  <p>{{ step2Form.value.message }}</p>
                </div>
              </div>
            </div>
          </div>

          <div class="success-animation" *ngIf="invitationSent">
            <div class="checkmark-circle">
              <div class="checkmark"></div>
            </div>
            <h3 class="success-message">Invitation envoyée avec succès!</h3>
          </div>
        </div>

        <div class="button-group">
          <button mat-stroked-button matStepperPrevious class="back-button" [disabled]="loading || invitationSent">
            <mat-icon>arrow_back</mat-icon>
            <span>Retour</span>
          </button>
          <button mat-flat-button color="primary" (click)="sendInvitation()" [disabled]="loading || invitationSent" class="send-button">
            <span *ngIf="!loading">Envoyer l'invitation</span>
            <mat-spinner *ngIf="loading" diameter="24" class="spinner"></mat-spinner>
            <mat-icon *ngIf="!loading">send</mat-icon>
          </button>
        </div>
      </mat-step>
    </mat-horizontal-stepper>
  </div>
</div>
