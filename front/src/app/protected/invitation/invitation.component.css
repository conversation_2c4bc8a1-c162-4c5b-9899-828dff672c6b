/* src/app/components/invitation/invitation.component.css */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 149, 0, 0.4);
  }
  70% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(255, 149, 0, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 149, 0, 0);
  }
}

@keyframes scaleDown {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.95);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes checkmarkDraw {
  0% {
    stroke-dashoffset: 100;
  }
  100% {
    stroke-dashoffset: 0;
  }
}

@keyframes circleFill {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Main Container */
.invitation-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
  font-family: 'Inter', sans-serif;
  padding: 40px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* Header */
.invitation-header {
  width: 100%;
  max-width: 800px;
  margin-bottom: 30px;
  text-align: center;
  animation: fadeIn 0.6s ease-out;
}

.header-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.header-icon {
  width: 70px;
  height: 70px;
  background: linear-gradient(135deg, #FF9500, #FF5722);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  box-shadow: 0 10px 20px rgba(255, 149, 0, 0.2);
  animation: pulse 2s infinite;
}

.header-icon mat-icon {
  font-size: 36px;
  height: 36px;
  width: 36px;
  color: white;
}

.header-title {
  font-size: 32px;
  font-weight: 700;
  color: #1F2937;
  margin: 0 0 8px 0;
}

.header-subtitle {
  font-size: 16px;
  color: #6B7280;
  margin: 0;
  max-width: 500px;
}

/* Card */
.invitation-card {
  width: 100%;
  max-width: 800px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  animation: fadeIn 0.8s ease-out;
}

/* Stepper */
.custom-stepper {
  width: 100%;
  padding: 0;
}

::ng-deep .mat-horizontal-stepper-header-container {
  padding: 24px 24px 0;
  background: #FFFFFF;
  border-bottom: 1px solid #F3F4F6;
}

::ng-deep .mat-horizontal-stepper-header {
  padding: 0 24px;
  height: 72px;
}

::ng-deep .mat-stepper-horizontal-line {
  margin: 0 8px;
  height: 1px;
  background-color: #E5E7EB;
}

::ng-deep .mat-step-header .mat-step-icon-selected,
::ng-deep .mat-step-header .mat-step-icon-state-done,
::ng-deep .mat-step-header .mat-step-icon-state-edit {
  background-color: #FF9500;
  color: white;
}

::ng-deep .mat-step-header .mat-step-icon-state-edit {
  background-color: #FF9500;
}

::ng-deep .mat-step-header .mat-step-icon {
  background-color: #E5E7EB;
  color: #9CA3AF;
}

::ng-deep .mat-step-header .mat-step-label.mat-step-label-active {
  color: #1F2937;
  font-weight: 600;
}

::ng-deep .mat-step-header .mat-step-label {
  color: #6B7280;
}

::ng-deep .mat-step-header:hover {
  background-color: rgba(249, 250, 251, 0.8);
}

::ng-deep .mat-step-header .mat-step-icon {
  transition: all 0.3s ease;
}

/* Step Content */
.step-form {
  padding: 32px 24px;
}

.step-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  margin-bottom: 32px;
  animation: fadeIn 0.5s ease-out;
}

.step-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #2563EB, #3B82F6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  box-shadow: 0 8px 16px rgba(37, 99, 235, 0.2);
}

.step-icon mat-icon {
  font-size: 28px;
  height: 28px;
  width: 28px;
  color: white;
}

.step-title {
  font-size: 24px;
  font-weight: 700;
  color: #1F2937;
  margin: 0 0 8px 0;
}

.step-description {
  font-size: 16px;
  color: #6B7280;
  margin: 0 0 24px 0;
  max-width: 500px;
}

/* Form Fields */
.custom-form-field {
  width: 100%;
  max-width: 500px;
  margin-bottom: 24px;
}

::ng-deep .custom-form-field .mat-form-field-wrapper {
  padding-bottom: 0;
}

::ng-deep .custom-form-field .mat-form-field-flex {
  background-color: #F9FAFB;
  border-radius: 8px;
  padding: 8px 16px !important;
  transition: all 0.3s ease;
}

::ng-deep .custom-form-field.mat-focused .mat-form-field-flex {
  background-color: #F3F4F6;
  box-shadow: 0 0 0 2px rgba(255, 149, 0, 0.3);
}

::ng-deep .custom-form-field .mat-form-field-outline {
  display: none;
}

::ng-deep .custom-form-field .mat-form-field-infix {
  padding: 12px 0;
  width: auto;
}

::ng-deep .custom-form-field .mat-form-field-label-wrapper {
  top: -0.75em;
}

::ng-deep .custom-form-field .mat-form-field-label {
  color: #6B7280;
  font-size: 14px;
}

::ng-deep .custom-form-field.mat-focused .mat-form-field-label {
  color: #FF9500;
}

::ng-deep .custom-form-field .mat-form-field-underline {
  display: none;
}

.search-icon {
  color: #9CA3AF;
  margin-right: 8px;
}

.message-field {
  margin-top: 16px;
}

/* User Selection */
::ng-deep .custom-autocomplete {
  border-radius: 12px;
  box-shadow: 0 12px 28px rgba(0, 0, 0, 0.12), 0 4px 8px rgba(0, 0, 0, 0.08);
  max-height: 320px;
  overflow-y: auto;
  padding: 8px;
  background-color: white;
  margin-top: 8px;
}

::ng-deep .custom-autocomplete::-webkit-scrollbar {
  width: 6px;
}

::ng-deep .custom-autocomplete::-webkit-scrollbar-track {
  background: #F3F4F6;
  border-radius: 10px;
}

::ng-deep .custom-autocomplete::-webkit-scrollbar-thumb {
  background: #D1D5DB;
  border-radius: 10px;
}

::ng-deep .custom-autocomplete::-webkit-scrollbar-thumb:hover {
  background: #9CA3AF;
}

::ng-deep .custom-autocomplete .mat-option {
  height: auto;
  line-height: 1.5;
  padding: 6px 12px;
  margin: 4px 0;
  border-radius: 8px;
  transition: all 0.2s ease;
}

::ng-deep .custom-autocomplete .mat-option:hover {
  background-color: #F3F4F6;
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

::ng-deep .custom-autocomplete .mat-option.mat-selected {
  background-color: rgba(255, 149, 0, 0.08);
}

::ng-deep .custom-autocomplete .mat-option.mat-active {
  background-color: rgba(255, 149, 0, 0.12);
}

.user-option-content {
  display: flex;
  align-items: center;
  padding: 8px 0;
  width: 100%;
}

.user-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: linear-gradient(135deg, #FF9500, #FF5722);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 18px;
  margin-right: 14px;
  flex-shrink: 0;
  box-shadow: 0 4px 8px rgba(255, 149, 0, 0.2);
  position: relative;
  overflow: hidden;
  border: 2px solid white;
  transition: all 0.3s ease;
}

.user-avatar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 60%);
  z-index: 1;
}

.user-picture {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 0;
}

::ng-deep .mat-option:hover .user-avatar {
  transform: scale(1.05);
  box-shadow: 0 6px 12px rgba(255, 149, 0, 0.3);
}

.user-select-indicator {
  margin-left: auto;
  opacity: 0;
  transform: translateX(-10px);
  transition: all 0.3s ease;
}

.user-select-indicator mat-icon {
  font-size: 16px;
  height: 16px;
  width: 16px;
  color: #FF9500;
}

::ng-deep .mat-option:hover .user-select-indicator {
  opacity: 1;
  transform: translateX(0);
}

.user-avatar.large {
  width: 64px;
  height: 64px;
  font-size: 26px;
  box-shadow: 0 6px 12px rgba(255, 149, 0, 0.25);
}

.user-avatar.small {
  width: 36px;
  height: 36px;
  font-size: 14px;
  box-shadow: 0 3px 6px rgba(255, 149, 0, 0.15);
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  text-align: left;
  flex-grow: 1;
  overflow: hidden;
}

.user-name {
  font-weight: 600;
  color: #1F2937;
  font-size: 15px;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}

.user-email {
  color: #6B7280;
  font-size: 13px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}

.no-results, .loading-results {
  color: #9CA3AF;
  font-style: italic;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
  text-align: center;
}

.no-results span, .loading-results span {
  display: flex;
  align-items: center;
  gap: 8px;
}

.no-results mat-icon {
  color: #9CA3AF;
  margin-right: 8px;
}

.loading-results mat-spinner {
  margin-right: 8px;
}

.error-message {
  display: flex;
  align-items: center;
  background-color: rgba(239, 68, 68, 0.1);
  color: #EF4444;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 16px;
  font-size: 14px;
  border-left: 4px solid #EF4444;
  animation: fadeIn 0.3s ease-out;
}

.error-message mat-icon {
  margin-right: 8px;
  font-size: 20px;
  height: 20px;
  width: 20px;
}

/* Selected User Card */
.selected-user-card {
  display: flex;
  align-items: center;
  background-color: #F9FAFB;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 24px;
  border: 1px solid #E5E7EB;
  animation: fadeIn 0.5s ease-out;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.selected-user-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(to bottom, #FF9500, #FF5722);
}

.user-details {
  margin-left: 20px;
  text-align: left;
}

.user-details h3 {
  margin: 0 0 6px 0;
  font-size: 20px;
  font-weight: 600;
  color: #1F2937;
}

.user-details p {
  margin: 0 0 10px 0;
  font-size: 15px;
  color: #6B7280;
}

.user-status {
  display: flex;
  align-items: center;
  margin-top: 8px;
}

.status-indicator {
  width: 8px;
  height: 8px;
  background-color: #10B981;
  border-radius: 50%;
  margin-right: 8px;
  position: relative;
}

.status-indicator::after {
  content: '';
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  background-color: rgba(16, 185, 129, 0.2);
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.status-text {
  font-size: 13px;
  color: #10B981;
  font-weight: 500;
}

/* Confirmation Card */
.confirmation-card {
  background-color: #F9FAFB;
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid #E5E7EB;
  margin-bottom: 24px;
  width: 100%;
  max-width: 500px;
  animation: fadeIn 0.5s ease-out;
}

.confirmation-header {
  background: linear-gradient(90deg, #2563EB, #3B82F6);
  padding: 16px;
  display: flex;
  align-items: center;
  color: white;
}

.confirmation-header mat-icon {
  margin-right: 12px;
}

.confirmation-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.confirmation-content {
  padding: 16px;
}

.confirmation-item {
  margin-bottom: 16px;
  display: flex;
  flex-direction: column;
  text-align: left;
}

.confirmation-item:last-child {
  margin-bottom: 0;
}

.item-label {
  font-weight: 600;
  color: #4B5563;
  font-size: 14px;
  margin-bottom: 8px;
}

.item-value {
  background-color: white;
  border-radius: 8px;
  padding: 12px;
  border: 1px solid #E5E7EB;
}

.user-value {
  display: flex;
  align-items: center;
  position: relative;
}

.user-confirmation-info {
  display: flex;
  flex-direction: column;
  margin-left: 12px;
}

.user-badge {
  display: flex;
  align-items: center;
  background-color: rgba(37, 99, 235, 0.1);
  border-radius: 16px;
  padding: 4px 10px;
  margin-left: auto;
  font-size: 12px;
  color: #2563EB;
  font-weight: 500;
}

.user-badge mat-icon {
  font-size: 14px;
  height: 14px;
  width: 14px;
  margin-right: 4px;
}

.message-value p {
  margin: 0;
  color: #1F2937;
  font-size: 14px;
  line-height: 1.5;
}

/* Success Animation */
.success-animation {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 24px 0;
  animation: fadeIn 0.5s ease-out;
}

.checkmark-circle {
  width: 80px;
  height: 80px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #10B981;
  border-radius: 50%;
  margin-bottom: 16px;
  animation: circleFill 0.5s ease-out;
}

.checkmark {
  width: 40px;
  height: 40px;
  position: relative;
}

.checkmark:before {
  content: '';
  position: absolute;
  width: 12px;
  height: 24px;
  border-right: 4px solid white;
  border-bottom: 4px solid white;
  top: 0;
  left: 14px;
  transform: rotate(45deg);
  animation: checkmarkDraw 0.5s ease-out forwards;
  animation-delay: 0.2s;
  opacity: 0;
}

.checkmark.draw:before {
  opacity: 1;
}

.success-message {
  font-size: 20px;
  font-weight: 600;
  color: #10B981;
  margin: 0;
}

/* Buttons */
.button-group {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  margin-top: 24px;
}

.next-button {
  background: linear-gradient(90deg, #2563EB, #3B82F6);
  color: white;
  padding: 8px 24px;
  border-radius: 8px;
  font-weight: 600;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.2);
  animation: slideInRight 0.5s ease-out;
}

.next-button:hover:not(:disabled) {
  background: linear-gradient(90deg, #1D4ED8, #2563EB);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(37, 99, 235, 0.3);
}

.next-button mat-icon {
  margin-left: 8px;
}

.back-button {
  color: #6B7280;
  padding: 8px 24px;
  border-radius: 8px;
  font-weight: 600;
  display: flex;
  align-items: center;
  border: 1px solid #E5E7EB;
  background-color: white;
  transition: all 0.3s ease;
  animation: slideInLeft 0.5s ease-out;
}

.back-button:hover:not(:disabled) {
  color: #1F2937;
  background-color: #F9FAFB;
  border-color: #D1D5DB;
}

.back-button mat-icon {
  margin-right: 8px;
}

.send-button {
  background: linear-gradient(90deg, #FF9500, #FF5722);
  color: white;
  padding: 8px 24px;
  border-radius: 8px;
  font-weight: 600;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(255, 149, 0, 0.2);
  animation: slideInRight 0.5s ease-out;
}

.send-button:hover:not(:disabled) {
  background: linear-gradient(90deg, #F59E0B, #EA580C);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(255, 149, 0, 0.3);
}

.send-button mat-icon {
  margin-left: 8px;
}

.spinner {
  margin: 0 8px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .invitation-container {
    padding: 32px 16px;
  }

  .header-icon {
    width: 60px;
    height: 60px;
  }

  .header-icon mat-icon {
    font-size: 30px;
    height: 30px;
    width: 30px;
  }

  .header-title {
    font-size: 28px;
  }

  .step-title {
    font-size: 22px;
  }

  .step-form {
    padding: 24px 20px;
  }

  .step-icon {
    width: 50px;
    height: 50px;
  }

  .step-icon mat-icon {
    font-size: 24px;
    height: 24px;
    width: 24px;
  }

  .user-avatar.large {
    width: 50px;
    height: 50px;
    font-size: 20px;
  }
}

@media (max-width: 480px) {
  .invitation-container {
    padding: 24px 12px;
  }

  .header-icon {
    width: 50px;
    height: 50px;
  }

  .header-icon mat-icon {
    font-size: 24px;
    height: 24px;
    width: 24px;
  }

  .header-title {
    font-size: 24px;
  }

  .header-subtitle {
    font-size: 14px;
  }

  .step-title {
    font-size: 20px;
  }

  .step-description {
    font-size: 14px;
  }

  .step-form {
    padding: 20px 16px;
  }

  .button-group {
    flex-direction: column-reverse;
    gap: 12px;
  }

  .next-button, .back-button, .send-button {
    width: 100%;
    justify-content: center;
  }

  .confirmation-header h3 {
    font-size: 16px;
  }

  .success-message {
    font-size: 18px;
  }
}
