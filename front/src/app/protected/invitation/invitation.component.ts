import {Component, OnInit, ViewChild} from '@angular/core';
import {HttpErrorResponse} from '@angular/common/http';
import {FormBuilder, FormGroup, ReactiveFormsModule, Validators} from '@angular/forms';
import {MatStepper, MatStepperModule} from '@angular/material/stepper';
import {MatFormFieldModule} from '@angular/material/form-field';
import {MatSelectModule} from '@angular/material/select';
import {MatInputModule} from '@angular/material/input';
import {NgIf, NgFor} from '@angular/common';
import {InvitationService} from '../../services/invitation/invitation.service';
import {UserService} from '../../services/user/user.service';
import {WorkspaceService} from '../../services/workspace/workspace.service';
import {MatAutocompleteModule} from '@angular/material/autocomplete';
import {MatIconModule} from '@angular/material/icon';
import {MatProgressSpinner, MatProgressSpinnerModule} from '@angular/material/progress-spinner';
import {MatButtonModule} from '@angular/material/button';
import {AvatarService} from '../../services/avatar/avatar.service';

@Component({
  selector: 'app-invitation',
  templateUrl: './invitation.component.html',
  styleUrls: ['./invitation.component.css'],
  standalone: true,
  imports: [
    MatFormFieldModule,
    MatStepperModule,
    ReactiveFormsModule,
    MatSelectModule,
    MatButtonModule,
    MatInputModule,
    NgIf,
    MatAutocompleteModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatProgressSpinner
  ]
})
export class InvitationComponent implements OnInit {

  users: any[] = [];
  receivedInvitations: any[] = [];
  selectedUser: any = null;
  loading = false;
  invitationSent = false;
  errorMessage = '';
  invitedUserIds: Set<string> = new Set<string>();
  step1Form: FormGroup;
  step2Form: FormGroup;
  data: any;
  @ViewChild('stepper') stepper!: MatStepper;

  // Avatar par défaut en SVG (cercle orange avec silhouette blanche)
  defaultAvatarUrl: string = 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48Y2lyY2xlIGN4PSI1MCIgY3k9IjUwIiByPSI1MCIgZmlsbD0iI0ZGOTUwMCIvPjxwYXRoIGQ9Ik01MCwyMCBDNTguMjg0MjcxLDIwIDY1LDI2LjcxNTcyODggNjUsMzUgQzY1LDQzLjI4NDI3MTIgNTguMjg0MjcxLDUwIDUwLDUwIEM0MS43MTU3Mjg4LDUwIDM1LDQzLjI4NDI3MTIgMzUsMzUgQzM1LDI2LjcxNTcyODggNDEuNzE1NzI4OCwyMCA1MCwyMCBaIE01MCw1NSBDNjYuNTY4NTQyNSw1NSA4MCw2MS43MTU3Mjg4IDgwLDcwIEw4MCw4MCBMMjAsODAgTDIwLDcwIEMyMCw2MS43MTU3Mjg4IDMzLjQzMTQ1NzUsNTUgNTAsNTUgWiIgZmlsbD0iI0ZGRkZGRiIvPjwvc3ZnPg==';

  constructor(private invitationService: InvitationService,
              private workspaceService: WorkspaceService,
              private userService: UserService,
              private fb: FormBuilder,
              private avatarService: AvatarService) {
    this.step1Form = this.fb.group({
      user: ['', Validators.required]
    });

    this.step2Form = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      message: ['', Validators.required]
    });
  }

  ngOnInit(): void {
    // Charger les invitations déjà envoyées depuis le localStorage
    this.loadSentInvitations();

    // Charger les utilisateurs et les invitations reçues
    this.loadUsers('');
    this.loadReceivedInvitations();

    this.step1Form.get('user')?.valueChanges.subscribe(userId => {
      if (userId) {
        // Trouver l'utilisateur sélectionné par son ID
        this.selectedUser = this.users.find(u => u.id === userId);
        if (this.selectedUser) {
          // Mettre à jour le champ email dans le formulaire de l'étape 2
          this.step2Form.patchValue({email: this.selectedUser.email || ''});
        }
      } else {
        // Réinitialiser l'utilisateur sélectionné si aucun ID n'est fourni
        this.selectedUser = null;
      }
    });
  }

  // Charger les invitations déjà envoyées depuis le localStorage
  loadSentInvitations(): void {
    try {
      const storedInvitations = localStorage.getItem('invitations');
      if (storedInvitations) {
        const invitations = JSON.parse(storedInvitations);
        // Ajouter les IDs des utilisateurs invités à notre ensemble
        invitations.forEach((invitation: any) => {
          if (invitation.user) {
            this.invitedUserIds.add(invitation.user);
          }
        });
      }
    } catch (error) {
      console.error('Erreur lors du chargement des invitations envoyées:', error);
    }
  }

  loadUsers(search: string): void {
    this.loading = true;
    this.errorMessage = '';
    this.userService.loadUsersForInvitations(search).subscribe({
      next: (data) => {
        console.log('Utilisateurs récupérés:', data);
        // Vérifier que les données sont valides et que chaque utilisateur a au moins un ID
        if (Array.isArray(data)) {
          // Filtrer les utilisateurs valides et ceux qui n'ont pas encore été invités
          this.users = data.filter(user =>
            user && user.id && !this.invitedUserIds.has(user.id)
          );
        } else {
          this.users = [];
        }
        this.loading = false;
      },
      error: (error) => {
        this.loading = false;
        this.users = [];
        this.handleHttpError(error);
      }
    });
  }

  loadReceivedInvitations(): void {
    this.invitationService.loadReceivedInvitations().subscribe({
      next: (data) => {
        console.log('Invitations reçues :', data);
        this.receivedInvitations = data.content || [];
      },
      error: (error) => {
        console.error('Erreur lors du chargement des invitations:', error);
        // Ne pas afficher d'erreur pour les invitations, ce n'est pas critique
      }
    });
  }

  sendInvitation(): void {
    this.loading = true;

    console.log("data", this.step2Form.value, this.step1Form.value);
    const data = {
      ...this.step2Form.value,
      ...this.step1Form.value,
    };

    // Stocker l'ID de l'utilisateur sélectionné
    const userId = this.step1Form.value.user;

    this.invitationService.sendInvitation(data)
      .subscribe({
        next: (response) => {
          console.log('Invitation envoyée avec succès:', response);
          this.loading = false;
          this.invitationSent = true;

          // Ajouter l'utilisateur à la liste des utilisateurs invités
          if (userId) {
            this.invitedUserIds.add(userId);
          }

          // Reset after 3 seconds to allow the user to see the success animation
          setTimeout(() => {
            this.invitationSent = false;
            this.stepper.reset(); // revenir à l'étape 1
            // Recharger la liste des utilisateurs pour exclure celui qui vient d'être invité
            this.loadUsers('');
          }, 3000);
        },
        error: (error) => {
          console.error('Erreur lors de l\'envoi de l\'invitation:', error);
          if (error.error) {
            console.error('Détails de l\'erreur:', error.error); // Check error details
          }
          this.loading = false;
          this.storeInvitation(data);

          // Show error message
          this.errorMessage = 'Une erreur est survenue lors de l\'envoi de l\'invitation. Veuillez réessayer.';

          // Reset after 3 seconds
          setTimeout(() => {
            this.errorMessage = '';
            this.stepper.reset(); // même si erreur, retour au début
          }, 3000);
        }
      });
  }

  storeInvitation(invitation: any): void {
    let invitations = JSON.parse(localStorage.getItem('invitations') || '[]');
    invitations.push({...invitation, status: 'pending'});
    localStorage.setItem('invitations', JSON.stringify(invitations));
    console.log("Invitation enregistrée:", invitations); // Ajoutez ce log
  }


  private handleHttpError(error: HttpErrorResponse): void {
    console.error('Erreur HTTP:', error);

    switch (error.status) {
      case 404:
        this.errorMessage = 'Le serveur ne trouve pas la ressource demandée.';
        break;
      case 401:
        this.errorMessage = 'Session expirée. Veuillez vous reconnecter.';
        localStorage.removeItem('access_token');
        window.location.href = '/login';
        break;
      case 403:
        this.errorMessage = 'Vous n\'avez pas les permissions requises pour cette action.';
        break;
      case 500:
        this.errorMessage = 'Erreur serveur. Veuillez réessayer plus tard.';
        break;
      case 0:
        this.errorMessage = 'Impossible de se connecter au serveur. Vérifiez votre connexion internet.';
        break;
      default:
        if (error.error && typeof error.error === 'string') {
          this.errorMessage = `Erreur: ${error.error}`;
        } else if (error.error && error.error.message) {
          this.errorMessage = `Erreur: ${error.error.message}`;
        } else {
          this.errorMessage = `Une erreur est survenue (${error.status}): ${error.message || 'Détails non disponibles'}`;
        }
    }
  }

  filterUsers(event: Event) {
    const searchValue = (event.target as HTMLInputElement).value;
    if (searchValue && searchValue.length >= 2) {
      this.loadUsers(searchValue);
    } else if (!searchValue) {
      this.loadUsers('');
    }
  }

  displayUserFn = (userId: string): string => {
    if (!userId) return '';
    const user = this.users.find(u => u.id === userId);
    if (!user) return '';
    return user.name ? `${user.name} (${user.email || ''})` : (user.email || '');
  }

  /**
   * Obtient l'URL de l'avatar d'un utilisateur
   * @param profilePicture URL de la photo de profil
   * @returns URL de l'avatar
   */
  getAvatarUrl(profilePicture: string | undefined | null): string {
    return this.avatarService.getAvatarUrl(profilePicture || null);
  }

  /**
   * Gère les erreurs de chargement d'image et affiche l'avatar par défaut
   */
  onImageError(event: any): void {
    console.log('Erreur de chargement de l\'image, utilisation de l\'avatar par défaut');
    event.target.src = this.defaultAvatarUrl;
  }
}
