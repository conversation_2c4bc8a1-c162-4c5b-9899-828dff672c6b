.sidenav-container {
  height: 100vh;
}
.sidenav {
  width: 250px;
  display: flex;
  flex-direction: column;
  padding: 0;
}

/* Styles pour le conteneur du workspace actif */
.active-workspace-container {
  padding: 20px 15px;
  margin: 10px;
  border-radius: 8px;
  background-color: rgba(0, 22, 96, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
  transition: background-color 0.3s ease;
}

.active-workspace-container:hover {
  background-color: rgba(0, 22, 96, 0.2);
}

.logo-container {
  display: flex;
  justify-content: center;
  padding: 20px 0;
}

.logo {
  width: 120px;
}

.mat-nav-list a {
  text-decoration: none;
  font-weight: bold;
}

.mat-nav-list a.mat-list-item {
  display: flex;
  align-items: center;
  padding: 10px 20px;
  transition: background 0.3s;
}
.mat-icon {
  margin-right: 10px;
}
.bottom-menu {
  margin-top: auto;
  padding-bottom: 20px;
}
.content {
  padding: 16px;
}
.bottom-menu {
  position: absolute;
  bottom: 10px;
  left: 0;
  width: 100%;
  display: flex;
  flex-direction: column;
}
.bottom-item {
  display: flex;
  align-items: center;
  padding: 10px 20px;
  text-decoration: none;
  font-weight: bold;
  transition: background 0.3s;
}

.bottom-item mat-icon {
  margin-right: 10px;
}
.badge {
  position: absolute;
  top: 0;
  right: 0;
  background-color: red;
  color: white;
  border-radius: 50%;
  padding: 4px 8px;
  font-size: 12px;
}
