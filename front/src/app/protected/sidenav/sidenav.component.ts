import {ChangeDete<PERSON><PERSON><PERSON>, <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild} from '@angular/core';
import {<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er<PERSON>ontainer, MatSidenavModule} from '@angular/material/sidenav';
import {Router, RouterLink, RouterOutlet} from '@angular/router';
import {<PERSON><PERSON><PERSON><PERSON><PERSON>, MatNavList} from '@angular/material/list';
import {MatIcon} from '@angular/material/icon';
import {TranslatePipe} from '@ngx-translate/core';
import {OidcSecurityService} from 'angular-auth-oidc-client';
import {MatDivider} from '@angular/material/divider';

import {MatBadge} from '@angular/material/badge';
import {CommonModule} from '@angular/common';
import {InvitationService} from '../../services/invitation/invitation.service';
import {WorkspaceService} from '../../services/workspace/workspace.service';
import {WorkspaceModalComponent} from '../workspace-modal/workspace-modal.component';
import {MatDialog} from '@angular/material/dialog';
import {ActiveWorkspaceDisplayComponent} from '../../shared/active-workspace-display/active-workspace-display.component';
import {Subscription} from 'rxjs';
import {UserRoleService} from '../../services/user-role/user-role.service';


@Component({
  selector: 'app-sidenav',
  standalone: true,
  templateUrl: './sidenav.component.html',
  styleUrls: ['./sidenav.component.scss'],
  imports: [
    CommonModule,  // Ajout de CommonModule ici
    MatDrawerContainer,
    MatSidenavModule,
    RouterLink,
    RouterOutlet,
    MatDrawer,
    MatListItem,
    MatIcon,
    MatNavList,
    TranslatePipe,
    MatDivider,
    MatBadge,
    ActiveWorkspaceDisplayComponent
  ]
})
export class SidenavComponent implements OnInit, OnDestroy {
  @ViewChild('drawer') drawer!: MatDrawer;

  unreadInvitationsCount: number = 0;
  workspaces: any[] = [];
  isRecruteur: boolean = false;
  subscriptions: Subscription[] = [];

  constructor(private oidcSecurityService: OidcSecurityService,
              private invitationService: InvitationService,
              private router: Router,
              private workspaceService: WorkspaceService,
              private dialog: MatDialog,
              private cdr: ChangeDetectorRef,
              private userRoleService: UserRoleService) {
    this.updateNotificationCount();
  }

  ngOnInit(): void {
    // S'abonner aux changements de rôle
    const roleSub = this.userRoleService.isRecruiter$.subscribe(isRecruiter => {
      console.log('Sidenav - Rôle changé:', isRecruiter ? 'Recruteur' : 'Candidat');
      this.isRecruteur = isRecruiter;
      this.cdr.detectChanges();
    });

    this.subscriptions.push(roleSub);
  }



  ngOnDestroy(): void {
    // Désabonner de tous les abonnements
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }
  public toggle() {
    this.drawer.toggle().then(r => console.log(r));
  }

  logout() {
    this.oidcSecurityService.logoff('oidc').subscribe(() => {
      // Nettoyer toutes les données de workspace
      localStorage.removeItem('Workspace-Id');
      sessionStorage.removeItem('Workspace-Id');
      localStorage.removeItem('Selected-Workspace-Id');

      // Notifier le service de workspace
      this.workspaceService.deleteActiveWorkspace();
      this.workspaceService.deleteSelectedWorkspace();

      // Mettre à jour l'interface
      this.isRecruteur = false;
      this.cdr.detectChanges();

      // Rediriger vers la page d'accueil
      this.router.navigate(['/']).then(r => console.log('Redirection vers la page d\'accueil après déconnexion:', r));
    });
  }

  updateNotificationCount(): void {
    this.invitationService.loadReceivedInvitations().subscribe(value => {

      this.unreadInvitationsCount = value.content.filter((invitation: any) => invitation.status === 'PENDING').length;
    });
  }
  openWorkspaceSwitcher(): void {
    this.dialog.open(WorkspaceModalComponent, {
      width: '500px',
      autoFocus: true,
      restoreFocus: false,
      data: { name: 'Workspace Modal' }
    });
  }



}
