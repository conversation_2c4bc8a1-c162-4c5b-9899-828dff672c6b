<mat-drawer-container class="sidenav-container">
  <mat-drawer #drawer mode="over" class="sidenav" [opened]="false">
    <!-- Affichage du workspace actif -->
    <div class="active-workspace-container">
      <app-active-workspace-display [size]="'medium'" [showName]="true" [showLogo]="true"></app-active-workspace-display>
    </div>

    <mat-divider></mat-divider>

    <mat-nav-list>
      <!-- Accueil - visible uniquement pour les utilisateurs normaux -->
      <a mat-list-item *ngIf="!isRecruteur" routerLink="/acceuil">
        <mat-icon>home</mat-icon>
        <span>{{ 'acceuil' | translate }}</span>
      </a>

      <!-- Profil - visible pour tous -->
      <a mat-list-item [routerLink]="isRecruteur ? '/recruiter-profile' : '/profile'">
        <mat-icon>person</mat-icon>
        <span>{{ 'nav-profile' | translate }}</span>
      </a>

      <!-- Posts - visible uniquement pour les recruteurs -->
      <a mat-list-item *ngIf="isRecruteur" routerLink="/acceuilposts">
        <mat-icon>work</mat-icon>
        <span>{{ 'JOBS' | translate }}</span>
      </a>

      <!-- Gestion des posts - visible uniquement pour les recruteurs -->
      <a mat-list-item *ngIf="isRecruteur" routerLink="/posts">
        <mat-icon>post_add</mat-icon>
        <span>{{ 'posts' | translate }}</span>
      </a>

      <!-- Invitations envoyées - visible uniquement pour les recruteurs -->
      <a mat-list-item *ngIf="isRecruteur" routerLink="/recruiter-invitations">
        <mat-icon>mail_outline</mat-icon>
        <span>Invitations envoyées</span>
      </a>

      <!-- Archive - visible uniquement pour les recruteurs -->
      <a mat-list-item *ngIf="isRecruteur" routerLink="/archive">
        <mat-icon>archive</mat-icon>
        <span>{{ 'archive' | translate }}</span>
      </a>

      <!-- Recrutement - visible pour tous -->
      <a mat-list-item routerLink="/recruitment">
        <mat-icon>business_center</mat-icon>
        <span>{{ 'recruteur' | translate }}</span>
      </a>

      <!-- Notifications - visible pour tous -->
      <mat-list-item routerLink="/notifications">
        <mat-icon matBadge="{{ unreadInvitationsCount }}" matBadgeColor="accent" matBadgePosition="after">
          notifications
        </mat-icon>
        <span>{{ 'invitation' | translate }}</span>
        <span class="badge" *ngIf="unreadInvitationsCount > 0">{{ unreadInvitationsCount }}</span>
      </mat-list-item>

      <!-- Dashboard - visible uniquement pour les recruteurs -->
      <a mat-list-item *ngIf="isRecruteur" routerLink="/dashboard">
        <mat-icon>dashboard</mat-icon>
        <span>{{ 'dashboard' | translate }}</span>
      </a>

      <!-- Profil du workspace - visible uniquement pour les recruteurs -->
      <a mat-list-item *ngIf="isRecruteur" [routerLink]="['/workspace-profile']">
        <mat-icon>group</mat-icon>
        <span>Workspace Profile</span>
      </a>

    </mat-nav-list>

    <mat-divider></mat-divider>

    <div class="bottom-menu">
      <mat-nav-list>
        <a mat-list-item class="bottom-item" (click)="openWorkspaceSwitcher()">
          <mat-icon>sync_alt</mat-icon>
          <span>{{ 'switch_workspace' | translate }}</span>
        </a>

        <a mat-list-item class="bottom-item" routerLink="/settings">
          <mat-icon>settings</mat-icon>
          <span>{{ 'settings' | translate }}</span>
        </a>

        <mat-list-item (click)="logout()" class="logout">
          <mat-icon>exit_to_app</mat-icon>
          <span>{{ 'LOGOUT' | translate }}</span>
        </mat-list-item>
      </mat-nav-list>
    </div>
  </mat-drawer>
  <mat-drawer-content><router-outlet></router-outlet></mat-drawer-content>

</mat-drawer-container>
