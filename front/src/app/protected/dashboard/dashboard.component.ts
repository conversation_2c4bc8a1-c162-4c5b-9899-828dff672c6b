import {Component} from '@angular/core';
import {<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ardTit<PERSON>} from '@angular/material/card';
import {MatIcon} from '@angular/material/icon';
import {MatButton} from '@angular/material/button';
import {Router, RouterOutlet} from '@angular/router';
import {CommonModule} from '@angular/common';
import {FormsModule} from '@angular/forms';
import {SidebarRecruiterComponent} from '../sidebar-recruiter/sidebar-recruiter.component';

@Component({
  selector: 'app-dashboard',
  imports: [
    CommonModule,
    FormsModule,
    MatCard,
    MatCardHeader,
    MatCardContent,
    MatIcon,
    MatButton,
    MatCardTitle,
    RouterOutlet,
    SidebarRecruiterComponent,

  ],
  templateUrl: './dashboard.component.html',
  standalone: true,
  styleUrl: './dashboard.component.css'
})
export class DashboardComponent {
  filteredCards: any[] = [];
  cards: any[] = [
    {title: 'Voir tous les candidats', description: '<PERSON><PERSON><PERSON> et gérez les candidatures.', route: '/candidatures'},
    {title: 'Voir votre workspace', description: 'Accédez à votre espace de travail.', route: '/workspace'},
    {title: 'Voir vos posts', description: 'Découvrez vos offres d\'emploi publiées.', route: '/posts'},
    {title: 'Voir vos messages', description: 'Consultez vos messages et notifications.', route: '/messages'},
    {title: 'Voir les statistiques', description: 'Accédez aux statistiques de performance.', route: '/stats'},
    {title: 'Gérer les paiements', description: 'Suivez l\'état des paiements et factures.', route: '/payments'},
    {title: 'Gérer les invitations', description: 'Invitez de nouveaux recruteurs.', route: '/invitations'},
    {title: 'Configurer votre compte', description: 'Modifiez les paramètres de votre profil.', route: '/settings'},
    {title: 'Publier une nouvelle offre', description: 'Publiez une offre d\'emploi.', route: '/create-post'},
    {title: 'Gérer les équipes', description: 'Gérez les membres et rôles de l\'équipe.', route: '/team-management'},
    {title: 'Voir les feedbacks', description: 'Consultez les avis des candidats et recruteurs.', route: '/feedback'}
  ];


  constructor(private router: Router) {
  }

  // goToCandidatures() {
  //   this.router.navigate(['/candidatures']);
  // }

  goToWorkspace() {
    this.router.navigate(['/workspaces']);
  }

  goToPosts() {
    this.router.navigate(['/acceuilposts']);
  }

  goToMessages() {
    this.router.navigate(['/messages']);
  }

  goToStats() {
    this.router.navigate(['/stats']);
  }

  goToPayments() {
    this.router.navigate(['/payments']);
  }

  goToInvitations() {
    this.router.navigate(['/invitations']);
  }

  goToSettings() {
    this.router.navigate(['/settings']);
  }

  goToCreatePost() {

    this.router.navigate(['/posts']);
  }


  goToTeamManagement() {
    this.router.navigate(['/manage-users']);
  }

  goToFeedback() {
    this.router.navigate(['/feedback']);
  }

  ngOnInit() {
    this.filteredCards = this.cards;
  }

  filterDashboard(event: Event) {
    const query = (event.target as HTMLInputElement).value.toLowerCase();
    this.filteredCards = this.cards.filter(card =>
      card.title.toLowerCase().includes(query) ||
      card.description.toLowerCase().includes(query)
    );
    console.log(query, this.filteredCards);
  }

  navigateTo(route: string) {
    this.router.navigate([route]);
  }

  goToTestResults() {
    this.router.navigate(['/results'])
      .then(success => {
        if (success) {
          console.log('Navigation vers /results réussie');
          // Optionnel : Ajouter ici d’autres opérations à effectuer après la navigation
        } else {
          console.error('Navigation vers /results a échoué');
          // Optionnel : Afficher un message d’erreur ou gérer l’échec de la navigation
        }
      });
  }


  prepareTest() {

  }
}
