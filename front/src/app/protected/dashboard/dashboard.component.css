/* Global container */
.dashboard {
  display: grid;
  grid-template-columns: 250px 1fr; /* Sidebar professionnelle et équilibrée */
  grid-template-areas:
    "sidebar main";
  min-height: 100vh;
  background: #f5f6f8; /* <PERSON><PERSON> très clair, neutre */
  font-family: 'Roboto', 'Open Sans', sans-serif; /* Police professionnelle */
  overflow-x: hidden;
}

/* Sidebar */
app-sidebar {
  grid-area: sidebar;
  background: #1a202c; /* Gris foncé sobre */
  color: #ffffff;
  height: 100%;
  padding: 24px;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1); /* Ombre discrète */
  position: sticky;
  top: 0;
  z-index: 100;
}

/* Main content */
.dashboard-main {
  grid-area: main;
  display: flex;
  flex-direction: column;
  background: #ffffff; /* Fond blanc pur */
}

/* Header */
.dashboard-header {
  display: flex;
  align-items: center;
  gap: 24px;
  padding: 20px 32px;
  background: #ffffff;
  border-bottom: 1px solid #e2e8f0; /* Bordure subtile */
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05); /* Ombre légère */
  z-index: 10;
}

/* Title */
.dashboard-title {
  font-size: 28px;
  font-weight: 500;
  color: #1a202c; /* Gris foncé élégant */
  margin: 0;
  flex-grow: 1;
}

/* Search container */
.search-container {
  display: flex;
  align-items: center;
  background: #f7fafc; /* Fond gris très clair */
  border: 1px solid #e2e8f0; /* Bordure fine */
  border-radius: 8px;
  padding: 8px 12px;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.search-container:hover {
  border-color: #a0aec0; /* Gris moyen au survol */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.search-bar {
  border: none;
  background: transparent;
  padding: 6px;
  width: 220px;
  font-size: 14px;
  color: #2d3748;
  outline: none;
}

.search-bar::placeholder {
  color: #a0aec0; /* Gris moyen */
}

.search-icon {
  color: #718096; /* Gris bleuté */
  font-size: 20px;
  transition: color 0.3s ease;
}

.search-container:hover .search-icon {
  color: #2b6cb0; /* Bleu professionnel */
}

/* Profile button */
.profile-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #2b6cb0; /* Bleu professionnel */
  color: #ffffff !important; /* Texte blanc, forcé */
  border-radius: 8px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 2px 6px rgba(43, 108, 176, 0.2);
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

.profile-button:hover {
  background: #1c528a; /* Bleu plus foncé */
  color: #ffffff !important; /* Texte blanc au survol */
  box-shadow: 0 4px 10px rgba(43, 108, 176, 0.3);
}

.profile-button mat-icon {
  font-size: 18px;
  width: 18px;
  height: 18px;
  color: #ffffff; /* Icône blanche pour cohérence */
}

/* Card container */
.card-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  padding: 32px;
}

/* Dashboard card */
.dashboard-card {
  padding: 20px;
  border-radius: 12px;
  background: #ffffff;
  border: 1px solid #edf2f7; /* Bordure très subtile */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: box-shadow 0.3s ease, transform 0.3s ease;
  cursor: pointer;
}

.dashboard-card:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px); /* Soulèvement léger */
}

.dashboard-card mat-card-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid #edf2f7; /* Séparateur discret */
}

.dashboard-card mat-card-title {
  font-size: 18px;
  font-weight: 500;
  color: #2d3748; /* Gris foncé doux */
}

.card-icon {
  color: #2b6cb0; /* Bleu professionnel */
  font-size: 24px;
  width: 24px;
  height: 24px;
  transition: color 0.3s ease;
}

.dashboard-card:hover .card-icon {
  color: #1c528a; /* Bleu plus foncé */
}

.dashboard-card p {
  font-size: 14px;
  color: #718096; /* Gris moyen */
  line-height: 1.5;
  margin: 12px 0 16px;
}

.card-button {
  background: #2b6cb0; /* Bleu assorti */
  color: #ffffff !important; /* Texte blanc, forcé */
  font-size: 13px;
  font-weight: 500;
  border-radius: 6px;
  padding: 6px 12px;
  box-shadow: 0 2px 4px rgba(43, 108, 176, 0.2);
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

.card-button:hover {
  background: #1c528a;
  color: #ffffff !important; /* Texte blanc au survol */
  box-shadow: 0 3px 6px rgba(43, 108, 176, 0.3);
}

/* Responsive */
@media (max-width: 1024px) {
  .dashboard {
    grid-template-columns: 220px 1fr;
  }

  .dashboard-header {
    padding: 16px 24px;
    gap: 16px;
  }

  .search-bar {
    width: 180px;
  }

  .card-container {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    padding: 24px;
  }
}

@media (max-width: 768px) {
  .dashboard {
    grid-template-columns: 1fr;
    grid-template-areas:
      "main";
  }

  app-sidebar {
    display: none; /* À remplacer par un menu burger si nécessaire */
  }

  .dashboard-main {
    border-radius: 0;
  }

  .dashboard-header {
    padding: 12px 16px;
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .dashboard-title {
    font-size: 24px;
  }

  .search-bar {
    width: 100%;
    max-width: 300px;
  }

  .profile-button {
    width: 100%;
    justify-content: center;
  }

  .card-container {
    grid-template-columns: 1fr;
    padding: 16px;
    gap: 16px;
  }

  .dashboard-card mat-card-title {
    font-size: 16px;
  }

  .card-icon {
    font-size: 22px;
    width: 22px;
    height: 22px;
  }
}
