mat-card {
  max-width: 400px;
  margin: 2rem auto;
  padding: 1rem;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

mat-card-header {
  justify-content: center;
  margin-bottom: 1rem;
}

mat-card-title {
  font-size: 1.5rem;
  font-weight: 500;
}

mat-form-field {
  width: 100%;
  margin-bottom: 1rem;
}

button {
  width: 100%;
  padding: 0.5rem;
  font-size: 1rem;
}

:host-context(.dark) {
  mat-card {
    background-color: #2a2a3a;
    color: #e0e0e0;
  }

  mat-form-field {
    .mat-mdc-text-field-wrapper {
      background-color: #333;
    }
    input {
      color: #e0e0e0;
    }
    mat-label {
      color: #b0b0b0;
    }
  }

  button {
    background-color: #ff4081;
    color: #ffffff;
  }
}
