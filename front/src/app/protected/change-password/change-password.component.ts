import { Component } from '@angular/core';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { FormsModule } from '@angular/forms';
import { UserService } from '../../services/user/user.service';
import { MatSnackBar } from '@angular/material/snack-bar';
import { OidcSecurityService } from 'angular-auth-oidc-client';
import { switchMap } from 'rxjs';

@Component({
  selector: 'app-change-password',
  standalone: true,
  imports: [
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    FormsModule
  ],
  templateUrl: './change-password.component.html',
  styleUrls: ['./change-password.component.css']
})
export class ChangePasswordComponent {
  user = {
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  };

  constructor(
    private userService: UserService,
    private snackBar: MatSnackBar,
    private oidcSecurityService: OidcSecurityService
  ) {
    this.oidcSecurityService.getUserData().subscribe({
      next: (userData) => console.log('OIDC User Data:', userData),
      error: (err) => console.error('Error fetching user data:', err)
    });
  }

  updatePassword() {
    if (!this.user.currentPassword || !this.user.newPassword || !this.user.confirmPassword) {
      this.snackBar.open('All fields are required.', 'Close', { duration: 3000 });
      return;
    }

    if (this.user.newPassword !== this.user.confirmPassword) {
      this.snackBar.open('New password and confirmation do not match.', 'Close', { duration: 3000 });
      return;
    }

    this.oidcSecurityService.getUserData().pipe(
      switchMap(userData => {
        if (!userData) {
          throw new Error('No user data available. Please log in again.');
        }
        const email = userData.email || userData.preferred_username || userData.sub;
        if (!email) {
          throw new Error('User email not found in token.');
        }
        const payload = {
          oldPassword: this.user.currentPassword,
          newPassword: this.user.newPassword
        };
        return this.userService.changePassword(email, payload);
      })
    ).subscribe({
      next: () => {
        this.snackBar.open('Password changed successfully!', 'Close', { duration: 3000 });
        this.user = { currentPassword: '', newPassword: '', confirmPassword: '' };
      },
      error: (error) => {
        console.error('Error changing password:', error);
        const errorMessage = error.error?.error || error.message || 'Failed to change password.';
        this.snackBar.open(errorMessage, 'Close', { duration: 5000 });
      }
    });
  }
}
