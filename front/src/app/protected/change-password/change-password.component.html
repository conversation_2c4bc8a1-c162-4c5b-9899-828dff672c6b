<mat-card>
  <mat-card-header>
    <mat-card-title>Change Password</mat-card-title>
  </mat-card-header>
  <mat-card-content>
    <form>
      <mat-form-field appearance="fill">
        <mat-label>Current Password</mat-label>
        <input matInput type="password" [(ngModel)]="user.currentPassword" name="currentPassword" required>
      </mat-form-field>
      <mat-form-field appearance="fill">
        <mat-label>New Password</mat-label>
        <input matInput type="password" [(ngModel)]="user.newPassword" name="newPassword" required>
      </mat-form-field>
      <mat-form-field appearance="fill">
        <mat-label>Confirm New Password</mat-label>
        <input matInput type="password" [(ngModel)]="user.confirmPassword" name="confirmPassword" required>
      </mat-form-field>
    </form>
  </mat-card-content>
  <mat-card-actions>
    <button mat-raised-button color="primary" (click)="updatePassword()">Change Password</button>
  </mat-card-actions>
</mat-card>
