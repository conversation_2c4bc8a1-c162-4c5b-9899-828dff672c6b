<!-- src/app/protected/manage-payments/manage-payments.component.html -->
<div class="manage-payments-container">
  <app-sidebar *ngIf="isSidebarVisible"></app-sidebar>
  <div class="content-container" [ngClass]="{'full-width': !isSidebarVisible}">
    <button mat-icon-button class="toggle-sidebar" (click)="toggleSidebar()">
      <mat-icon>{{ isSidebarVisible ? 'chevron_left' : 'chevron_right' }}</mat-icon>
    </button>
    <div class="header-section">
      <h1>Gérer les Paiements</h1>
      <div class="header-actions">
        <button mat-icon-button class="theme-toggle" (click)="toggleTheme()" matTooltip="Changer le thème">
          <mat-icon>{{ isDarkMode ? 'light_mode' : 'dark_mode' }}</mat-icon>
        </button>
        <button mat-raised-button color="primary" class="download-button" (click)="downloadReport()">
          <mat-icon>download</mat-icon> Télécharger Rapport
        </button>
        <button mat-raised-button color="accent" class="back-button" (click)="goBackToDashboard()">
          <mat-icon>arrow_back</mat-icon> Retour
        </button>
      </div>
    </div>
    <!-- Quick Stats Panel -->
    <div class="quick-stats-container">
      <div class="quick-stats-header" (click)="toggleQuickStats()">
        <h2>Statistiques Rapides</h2>
        <mat-icon class="toggle-icon">{{ isQuickStatsOpen ? 'expand_less' : 'expand_more' }}</mat-icon>
      </div>
      <div class="quick-stats-content" *ngIf="isQuickStatsOpen">
        <div class="stats-item">
          <mat-icon>attach_money</mat-icon>
          <div>
            <h3>Montant Moyen par Paiement</h3>
            <p>{{ quickStats.averagePayment ? (quickStats.averagePayment | currency:'EUR') : '0 €' }}</p>
          </div>
        </div>
        <div class="stats-item">
          <mat-icon>person</mat-icon>
          <div>
            <h3>Utilisateur le Plus Actif</h3>
            <p>{{ quickStats.mostActiveUser || 'N/A' }}</p>
          </div>
        </div>
        <div class="stats-item">
          <mat-icon>schedule</mat-icon>
          <div>
            <h3>Dernière Activité</h3>
            <p>{{ quickStats.lastActivity ? (quickStats.lastActivity | date:'medium') : 'N/A' }}</p>
          </div>
        </div>
      </div>
    </div>
    <!-- Summary Section -->
    <div class="summary-container">
      <mat-card class="summary-card">
        <mat-card-content>
          <div class="summary-item">
            <mat-icon class="summary-icon">payment</mat-icon>
            <div>
              <h3>Total des Paiements</h3>
              <p>{{ summary.totalPayments || 0 }}</p>
            </div>
          </div>
          <div class="summary-item">
            <mat-icon class="summary-icon">euro</mat-icon>
            <div>
              <h3>Montant Total</h3>
              <p>{{ summary.totalAmount ? (summary.totalAmount | currency:'EUR') : '0 €' }}</p>
            </div>
          </div>
          <div class="summary-item">
            <mat-icon class="summary-icon">hourglass_empty</mat-icon>
            <div>
              <h3>Paiements en Attente</h3>
              <p>{{ summary.pendingPayments || 0 }}</p>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
    <!-- Filter Section -->
    <div class="filter-container">
      <mat-form-field appearance="outline" class="status-filter">
        <mat-label>Filtrer par statut</mat-label>
        <mat-select (selectionChange)="filterByStatus($event.value)">
          <mat-option value="">Tous</mat-option>
          <mat-option value="completed">Complété</mat-option>
          <mat-option value="pending">En attente</mat-option>
          <mat-option value="failed">Échoué</mat-option>
        </mat-select>
      </mat-form-field>
      <mat-form-field appearance="outline" class="date-filter">
        <mat-label>Date de début</mat-label>
        <input matInput [matDatepicker]="startPicker" (dateChange)="filterByDateRange()" [(ngModel)]="startDate">
        <mat-datepicker-toggle matSuffix [for]="startPicker"></mat-datepicker-toggle>
        <mat-datepicker #startPicker></mat-datepicker>
      </mat-form-field>
      <mat-form-field appearance="outline" class="date-filter">
        <mat-label>Date de fin</mat-label>
        <input matInput [matDatepicker]="endPicker" (dateChange)="filterByDateRange()" [(ngModel)]="endDate">
        <mat-datepicker-toggle matSuffix [for]="endPicker"></mat-datepicker-toggle>
        <mat-datepicker #endPicker></mat-datepicker>
      </mat-form-field>
    </div>
    <!-- Table Section -->
    <div class="table-container">
      <mat-form-field appearance="outline" class="search-bar">
        <mat-label>Rechercher...</mat-label>
        <input matInput placeholder="Rechercher..." (input)="applyFilter($event)">
        <mat-icon matSuffix>search</mat-icon>
      </mat-form-field>
      <mat-spinner *ngIf="isLoading" class="spinner"></mat-spinner>
      <table mat-table [dataSource]="dataSource" matSort class="mat-elevation-z8" *ngIf="!isLoading">
        <!-- ID Column -->
        <ng-container matColumnDef="id">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>ID</th>
          <td mat-cell *matCellDef="let payment">{{ payment.id ?? 'N/A' }}</td>
        </ng-container>

        <!-- User Column -->
        <ng-container matColumnDef="user">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>Utilisateur</th>
          <td mat-cell *matCellDef="let payment">{{ payment.user ?? 'N/A' }}</td>
        </ng-container>

        <!-- Amount Column -->
        <ng-container matColumnDef="amount">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>Montant</th>
          <td mat-cell *matCellDef="let payment">{{ payment.amount ? (payment.amount | currency:'EUR') : 'N/A' }}</td>
        </ng-container>

        <!-- Date Column -->
        <ng-container matColumnDef="date">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>Date</th>
          <td mat-cell *matCellDef="let payment">{{ (payment.date | date:'mediumDate') ?? 'N/A' }}</td>
        </ng-container>

        <!-- Status Column -->
        <ng-container matColumnDef="status">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>Statut</th>
          <td mat-cell *matCellDef="let payment">
            <span [ngClass]="getStatusClass(payment.status)">{{ payment.status ?? 'N/A' }}</span>
          </td>
        </ng-container>

        <!-- Actions Column -->
        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef>Actions</th>
          <td mat-cell *matCellDef="let payment">
            <button mat-icon-button color="primary" (click)="viewPayment(payment)" matTooltip="Voir">
              <mat-icon>visibility</mat-icon>
            </button>
            <button mat-icon-button color="warn" (click)="refundPayment(payment)" matTooltip="Rembourser">
              <mat-icon>money_off</mat-icon>
            </button>
          </td>
        </ng-container>

        <!-- Table Header and Rows -->
        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
      </table>
      <mat-paginator
        [pageSizeOptions]="[5, 10, 20]"
        [pageSize]="pageSize"
        [pageIndex]="pageIndex"
        [length]="totalElements"
        (page)="onPageChange($event)"
        showFirstLastButtons
        *ngIf="!isLoading">
      </mat-paginator>
    </div>
    <button mat-fab color="primary" class="fab" (click)="refreshData()" matTooltip="Rafraîchir les données">
      <mat-icon>refresh</mat-icon>
    </button>
  </div>
</div>
