// src/app/protected/manage-payments/manage-payments.component.ts
import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatTableModule } from '@angular/material/table';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { PaymentService } from '../../../services/payment/payment.service';
import { HttpErrorResponse } from '@angular/common/http';
import { Router } from '@angular/router';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator, MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatCardModule } from '@angular/material/card';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { FormsModule } from '@angular/forms';
import { SidebarComponent } from '../../sidebar/sidebar.component';

@Component({
  selector: 'app-manage-payments',
  templateUrl: './manage-payments.component.html',
  styleUrls: ['./manage-payments.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    MatTableModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule,
    MatSnackBarModule,
    MatPaginatorModule,
    MatSortModule,
    MatProgressSpinnerModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatCardModule,
    MatDatepickerModule,
    MatNativeDateModule,
    FormsModule,
    SidebarComponent
  ]
})
export class ManagePaymentsComponent implements OnInit {
  displayedColumns: string[] = ['id', 'user', 'amount', 'date', 'status', 'actions'];
  dataSource = new MatTableDataSource<any>([]);
  isLoading = false;
  totalElements = 0;
  pageSize = 5;
  pageIndex = 0;
  sortField = 'id';
  sortDirection = 'asc';
  summary: any = {
    totalPayments: 0,
    totalAmount: 0,
    pendingPayments: 0
  };
  quickStats: any = {
    averagePayment: 0,
    mostActiveUser: null,
    lastActivity: null
  };
  startDate: Date | null = null;
  endDate: Date | null = null;
  isSidebarVisible = true;
  isDarkMode = false;
  isQuickStatsOpen = true;

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  constructor(
    private paymentService: PaymentService,
    private snackBar: MatSnackBar,
    private router: Router
  ) {}

  ngOnInit() {
    this.loadPayments();
  }

  ngAfterViewInit() {
    this.dataSource.sort = this.sort;
    this.sort.sortChange.subscribe(() => {
      this.sortField = this.sort.active;
      this.sortDirection = this.sort.direction || 'asc';
      this.pageIndex = 0;
      this.loadPayments();
    });
  }

  loadPayments() {
    this.isLoading = true;
    const sortParam = `${this.sortField},${this.sortDirection}`;
    this.paymentService.getAllPayments(this.pageIndex, this.pageSize, sortParam).subscribe(
      (response: { content: any[], totalElements: number }) => {
        this.dataSource.data = response.content;
        this.totalElements = response.totalElements;
        // Calculate summary data
        this.summary.totalPayments = response.totalElements;
        this.summary.totalAmount = response.content.reduce((sum: number, payment: any) => sum + (payment.amount || 0), 0);
        this.summary.pendingPayments = response.content.filter((payment: any) => payment.status?.toLowerCase() === 'pending').length;
        // Calculate quick stats
        this.calculateQuickStats(response.content);
        this.isLoading = false;
      },
      (error: HttpErrorResponse) => {
        console.error('Error fetching payments:', error.message);
        this.snackBar.open('Error loading payments: ' + error.message, 'Close', { duration: 3000 });
        this.isLoading = false;
      }
    );
  }

  calculateQuickStats(payments: any[]) {
    // Average payment amount
    const totalPayments = payments.length;
    const totalAmount = payments.reduce((sum: number, payment: any) => sum + (payment.amount || 0), 0);
    this.quickStats.averagePayment = totalPayments > 0 ? totalAmount / totalPayments : 0;

    // Most active user
    const userCounts: { [key: string]: number } = {};
    payments.forEach(payment => {
      const user = payment.user || 'Unknown';
      userCounts[user] = (userCounts[user] || 0) + 1;
    });
    const mostActive = Object.entries(userCounts).reduce((a, b) => (b[1] > a[1] ? b : a), ['N/A', 0]);
    this.quickStats.mostActiveUser = mostActive[0] !== 'N/A' ? mostActive[0] : 'N/A';

    // Last activity
    const sortedPayments = payments.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
    this.quickStats.lastActivity = sortedPayments.length > 0 ? sortedPayments[0].date : null;
  }

  onPageChange(event: PageEvent) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.loadPayments();
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  filterByStatus(status: string) {
    this.dataSource.filterPredicate = (data: any, filter: string) => {
      return !filter || data.status.toLowerCase() === filter.toLowerCase();
    };
    this.dataSource.filter = status;
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  filterByDateRange() {
    this.dataSource.filterPredicate = (data: any, filter: string) => {
      const paymentDate = new Date(data.date);
      const start = this.startDate ? new Date(this.startDate) : null;
      const end = this.endDate ? new Date(this.endDate) : null;
      return (!start || paymentDate >= start) && (!end || paymentDate <= end);
    };
    this.dataSource.filter = 'date'; // Trigger filter
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  getStatusClass(status: string): string {
    switch (status?.toLowerCase()) {
      case 'completed':
        return 'status-completed';
      case 'pending':
        return 'status-pending';
      case 'failed':
        return 'status-failed';
      default:
        return '';
    }
  }

  viewPayment(payment: any) {
    this.snackBar.open(`View payment: ${payment.id}`, 'Close', { duration: 3000 });
    // Add navigation to payment details page if implemented
  }

  refundPayment(payment: any) {
    if (confirm(`Are you sure you want to refund payment ${payment.id}?`)) {
      this.paymentService.refundPayment(payment.id).subscribe(
        (response: any) => {
          this.snackBar.open('Payment refunded successfully', 'Close', { duration: 3000 });
          this.loadPayments();
        },
        (error: HttpErrorResponse) => {
          console.error('Error refunding payment:', error.message);
          this.snackBar.open('Error refunding payment: ' + error.message, 'Close', { duration: 3000 });
        }
      );
    }
  }

  goBackToDashboard() {
    this.router.navigate(['/admin/dashboard']);
  }

  downloadReport() {
    const csvData = this.convertToCSV(this.dataSource.data);
    const blob = new Blob([csvData], { type: 'text/csv;charset=utf-8;' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', 'payment_report.csv');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  convertToCSV(data: any[]): string {
    const headers = this.displayedColumns.join(',');
    const rows = data.map(payment =>
      this.displayedColumns.map(col => {
        if (col === 'amount') return payment[col] ? payment[col].toFixed(2) : 'N/A';
        if (col === 'date') return payment[col] ? new Date(payment[col]).toLocaleDateString() : 'N/A';
        return payment[col] || 'N/A';
      }).join(',')
    );
    return `${headers}\n${rows.join('\n')}`;
  }

  refreshData() {
    this.pageIndex = 0;
    this.loadPayments();
    this.snackBar.open('Données rafraîchies', 'Fermer', { duration: 2000 });
  }

  toggleSidebar() {
    this.isSidebarVisible = !this.isSidebarVisible;
  }

  toggleTheme() {
    this.isDarkMode = !this.isDarkMode;
    document.body.classList.toggle('dark-mode', this.isDarkMode);
  }

  toggleQuickStats() {
    this.isQuickStatsOpen = !this.isQuickStatsOpen;
  }
}
