<!-- src/app/protected/manage-users/manage-users.component.html -->
<div class="manage-users-container">
  <app-sidebar></app-sidebar>

  <div class="content-container">
    <div class="header-section">
      <h1><PERSON><PERSON><PERSON> les Utilisateurs</h1>
      <button mat-raised-button color="accent" class="back-button" (click)="goBackToDashboard()">
        <mat-icon>arrow_back</mat-icon> Retour
      </button>
    </div>
    <div class="table-container">
      <mat-spinner *ngIf="isLoading" class="spinner"></mat-spinner>
      <table mat-table [dataSource]="dataSource" matSort class="mat-elevation-z8" *ngIf="!isLoading">
        <!-- ID Column -->
        <ng-container matColumnDef="id">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>ID</th>
          <td mat-cell *matCellDef="let user">{{ user.id ?? 'N/A' }}</td>
        </ng-container>

        <!-- First Name Column -->
        <ng-container matColumnDef="firstName">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>Prénom</th>
          <td mat-cell *matCellDef="let user">{{ user.firstName ?? 'N/A' }}</td>
        </ng-container>

        <!-- Last Name Column -->
        <ng-container matColumnDef="lastName">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>Nom</th>
          <td mat-cell *matCellDef="let user">{{ user.lastName ?? 'N/A' }}</td>
        </ng-container>

        <!-- Email Column -->
        <ng-container matColumnDef="email">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>Email</th>
          <td mat-cell *matCellDef="let user">{{ user.email ?? 'N/A' }}</td>
        </ng-container>

        <!-- Role Column -->
        <ng-container matColumnDef="role">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>Rôle</th>
          <td mat-cell *matCellDef="let user">{{ user.role ?? 'N/A' }}</td>
        </ng-container>

        <!-- Created At Column -->
        <ng-container matColumnDef="createdAt">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>Date de Création</th>
          <td mat-cell *matCellDef="let user">{{ (user.createdAt | date:'mediumDate') ?? 'N/A' }}</td>
        </ng-container>

        <!-- Actions Column -->
        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef>Actions</th>
          <td mat-cell *matCellDef="let user">
            <button mat-icon-button color="primary" (click)="editUser(user)" matTooltip="Modifier">
              <mat-icon>edit</mat-icon>
            </button>
            <button mat-icon-button color="warn" (click)="deleteUser(user)" matTooltip="Supprimer">
              <mat-icon>delete</mat-icon>
            </button>
          </td>
        </ng-container>


        <!-- Table Header and Rows -->
        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
      </table>
      <mat-paginator [pageSizeOptions]="[5, 10, 20]" showFirstLastButtons></mat-paginator>
    </div>
  </div>
</div>


