import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatTableModule } from '@angular/material/table';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { UserService } from '../../../services/user/user.service';
import { HttpErrorResponse } from '@angular/common/http';
import { Router } from '@angular/router';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { SidebarComponent } from '../../sidebar/sidebar.component';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import {EditUserDialogComponent} from '../edit-user-dialog/edit-user-dialog.component';

@Component({
  selector: 'app-manage-users',
  templateUrl: './manage-users.component.html',
  styleUrls: ['./manage-users.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    MatTableModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule,
    MatSnackBarModule,
    MatPaginatorModule,
    MatSortModule,
    MatProgressSpinnerModule,
    SidebarComponent,
    MatDialogModule
  ]
})
export class ManageUsersComponent implements OnInit {
  displayedColumns: string[] = ['id', 'firstName', 'lastName', 'email', 'role', 'createdAt', 'actions'];
  dataSource = new MatTableDataSource<any>([]);
  isLoading = false;

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  constructor(
    private userService: UserService,
    private snackBar: MatSnackBar,
    private router: Router,
    private dialog: MatDialog
  ) {}

  ngOnInit() {
    this.loadUsers();
  }

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  loadUsers() {
    this.isLoading = true;
    this.userService.getAllUsers().subscribe(
      (data: any[]) => {
        this.dataSource.data = data;
        this.isLoading = false;
      },
      (error: HttpErrorResponse) => {
        console.error('Error fetching users:', error.message);
        this.snackBar.open('Erreur lors du chargement des utilisateurs : ' + error.message, 'Fermer', { duration: 3000 });
        this.isLoading = false;
      }
    );
  }

  editUser(user: any) {
    const dialogRef = this.dialog.open(EditUserDialogComponent, {
      width: '500px',
      data: { ...user }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.isLoading = true;
        // Ensure the payload matches the backend's expected User model
        const updatedUser = {
          firstName: result.firstName,
          lastName: result.lastName,
          email: result.email,
          role: result.role
        };
        this.userService.updateUser(user.id.toString(), updatedUser).subscribe({
          next: () => {
            this.snackBar.open('Utilisateur mis à jour avec succès', 'Fermer', { duration: 3000 });
            this.loadUsers();
            this.isLoading = false;
          },
          error: (error: HttpErrorResponse) => {
            console.error('Erreur lors de la mise à jour de l\'utilisateur:', error.message);
            this.snackBar.open('Erreur lors de la mise à jour de l\'utilisateur : ' + error.message, 'Fermer', { duration: 3000 });
            this.isLoading = false;
          }
        });
      }
    });
  }

  deleteUser(user: any) {
    if (confirm(`Êtes-vous sûr de vouloir supprimer ${user.firstName} ${user.lastName} ?`)) {
      this.isLoading = true;
      this.userService.deleteUser(user.id.toString()).subscribe({
        next: (response) => {
          if (response.deleted) {
            this.snackBar.open('Utilisateur supprimé avec succès', 'Fermer', { duration: 3000 });
            this.loadUsers();
          } else {
            this.snackBar.open('Échec de la suppression de l\'utilisateur', 'Fermer', { duration: 3000 });
          }
          this.isLoading = false;
        },
        error: (error: HttpErrorResponse) => {
          console.error('Erreur lors de la suppression de l\'utilisateur:', error.message);
          this.snackBar.open('Erreur lors de la suppression de l\'utilisateur : ' + error.message, 'Fermer', { duration: 3000 });
          this.isLoading = false;
        }
      });
    }
  }

  goBackToDashboard() {
    this.router.navigate(['/admin/dashboard']);
  }
}
