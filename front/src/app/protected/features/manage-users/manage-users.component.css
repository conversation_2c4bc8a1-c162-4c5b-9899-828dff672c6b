/* src/app/protected/manage-users/manage-users.component.css */
.manage-users-container {
  display: flex;
  min-height: 100vh;
}

app-sidebar {
  width: 250px;
  background-color: #f5f7fa;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
}

.content-container {
  margin-left: 250px;
  flex: 1;
  padding: 20px;
  max-width: 1200px;
  margin-right: auto;
  margin-left: 270px;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

h1 {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.back-button {
  background-color: #ff4081;
  color: white;
  padding: 8px 16px;
}

.back-button mat-icon {
  margin-right: 8px;
}

.table-container {
  width: 100%;
  overflow-x: auto;
}

table {
  width: 100%;
  border-collapse: collapse;
}

th, td {
  padding: 12px 16px;
  text-align: left;
}

th {
  background-color: #f5f7fa;
  font-weight: 600;
  color: #333;
}

td {
  border-bottom: 1px solid #e0e0e0;
  color: #555;
}

tr:hover {
  background-color: #f9f9f9;
}

button[mat-icon-button] {
  margin: 0 4px;
}

button[color="primary"] {
  color: #1a73e8;
}

button[color="warn"] {
  color: #d32f2f;
}

.spinner {
  margin: 20px auto;
}
