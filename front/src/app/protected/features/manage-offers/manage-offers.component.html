<div class="manage-offers-container">
  <!-- Sidebar -->
  <app-sidebar></app-sidebar>

  <!-- Main Content -->
  <div class="content-container">
    <!-- Header -->
    <header class="header-section">
      <h1>Gérer les Offres</h1>
      <div class="header-actions">
        <button mat-raised-button class="back-button" (click)="goBack()">
          <mat-icon>arrow_back</mat-icon> Retour
        </button>
      </div>
    </header>

    <!-- Error Message -->
    <div class="error-message" *ngIf="errorMessage">
      <mat-icon color="warn">error</mat-icon>
      <span>{{ errorMessage }}</span>
    </div>

    <!-- Quick Stats -->
    <section class="quick-stats-container">
      <div class="quick-stats-content">
        <div class="stats-item">
          <mat-icon>work</mat-icon>
          <div>
            <h3>Total des Offres</h3>
            <p>{{ totalPosts }}</p>
          </div>
        </div>
        <div class="stats-item">
          <mat-icon>schedule</mat-icon>
          <div>
            <h3>Offres Actives</h3>
            <p>0</p> <!-- Set to 0 since status is missing -->
          </div>
        </div>
        <div class="stats-item">
          <mat-icon>hourglass_empty</mat-icon>
          <div>
            <h3>Offres en Attente</h3>
            <p>0</p> <!-- Set to 0 since status is missing -->
          </div>
        </div>
      </div>
    </section>

    <!-- Table -->
    <div class="table-container">
      <mat-spinner *ngIf="isLoading" class="spinner"></mat-spinner>
      <table mat-table [dataSource]="posts" *ngIf="!isLoading && !errorMessage">
        <!-- ID Column -->
        <ng-container matColumnDef="id">
          <th mat-header-cell *matHeaderCellDef> ID </th>
          <td mat-cell *matCellDef="let post"> {{ post.id }} </td>
        </ng-container>

        <!-- Title Column -->
        <ng-container matColumnDef="title">
          <th mat-header-cell *matHeaderCellDef> Titre </th>
          <td mat-cell *matCellDef="let post"> {{ post.title }} </td>
        </ng-container>

        <!-- Company Column -->
        <ng-container matColumnDef="company">
          <th mat-header-cell *matHeaderCellDef> Entreprise </th>
          <td mat-cell *matCellDef="let post"> {{ post.company }} </td>
        </ng-container>

        <!-- Location Column -->
        <ng-container matColumnDef="location">
          <th mat-header-cell *matHeaderCellDef> Lieu </th>
          <td mat-cell *matCellDef="let post"> {{ post.location }} </td>
        </ng-container>

        <!-- Created At Column -->
        <ng-container matColumnDef="createdAt">
          <th mat-header-cell *matHeaderCellDef> Date de Création </th>
          <td mat-cell *matCellDef="let post"> {{ post.createdAt }} </td>
        </ng-container>

        <!-- Status Column -->
        <ng-container matColumnDef="status">
          <th mat-header-cell *matHeaderCellDef> Statut </th>
          <td mat-cell *matCellDef="let post">
            <span [ngClass]="{
              'status-active': post.status === 'active',
              'status-pending': post.status === 'pending',
              'status-closed': post.status === 'closed'
            }">
              {{ post.status | titlecase }}
            </span>
          </td>
        </ng-container>

        <!-- Actions Column -->
        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef> Actions </th>
          <td mat-cell *matCellDef="let post">
            <button mat-icon-button color="primary" matTooltip="Modifier">
              <mat-icon>edit</mat-icon>
            </button>
            <button mat-icon-button color="warn" matTooltip="Supprimer">
              <mat-icon>delete</mat-icon>
            </button>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
      </table>

      <!-- Pagination Controls -->
      <div class="pagination" *ngIf="!isLoading && !errorMessage">
        <button mat-button [disabled]="page === 0" (click)="previousPage()">
          <mat-icon>chevron_left</mat-icon> Précédent
        </button>
        <span>Page {{ page + 1 }}</span>
        <button mat-button [disabled]="posts.length < size" (click)="nextPage()">
          Suivant <mat-icon>chevron_right</mat-icon>
        </button>
      </div>
    </div>

    <!-- Floating Action Button -->
    <button mat-fab class="fab" color="primary" matTooltip="Rafraîchir" (click)="refresh()">
      <mat-icon>refresh</mat-icon>
    </button>
  </div>
</div>
