@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  --primary-color: #1e3a8a;
  --primary-light: #2a4caf;
  --primary-dark: #152a62;
  --accent-color: #ff6b00;
  --accent-light: #ff8c3f;
  --accent-dark: #e05e00;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --text-tertiary: #94a3b8;
  --background-primary: #ffffff;
  --background-secondary: #f8fafc;
  --background-tertiary: #f1f5f9;
  --border-color: #e2e8f0;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
}

/* Modal Overlay with solid background */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7); /* Plus opaque, sans effet de flou */
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

/* Modal Content with Modern Design */
.modal-content {
  background: #ffffff; /* Blanc pur au lieu de la variable */
  border-radius: var(--radius-lg);
  box-shadow:
    0 10px 25px rgba(0, 0, 0, 0.15),
    0 6px 12px rgba(0, 0, 0, 0.1),
    0 1px 4px rgba(0, 0, 0, 0.05);
  width: 100%;
  max-width: 550px;
  max-height: 85vh;
  overflow: hidden;
  font-family: 'Inter', sans-serif;
  display: flex;
  flex-direction: column;
  border-top: 5px solid #ff6b00; /* Bordure orange plus visible en haut */
  border-bottom: 5px solid #ff6b00; /* Bordure orange plus visible en bas */
  animation: slideUp 0.4s cubic-bezier(0.16, 1, 0.3, 1);
}

/* Modal Header with Flex Layout */
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 2px solid #ff6b00; /* Bordure orange en bas de l'en-tête */
  background: #ffffff; /* Fond blanc pur */
}

.header-content {
  display: flex;
  align-items: center;
}

.header-icon {
  width: 40px;
  height: 40px;
  background-color: #021c6c; /* Fond orange */
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  box-shadow: 0 2px 8px rgba(255, 107, 0, 0.3); /* Ombre orange */
}

.header-icon mat-icon {
  color: white;
  font-size: 24px;
}

.header-text {
  display: flex;
  flex-direction: column;
}

.modal-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 4px 0;
  line-height: 1.2;
}

.modal-subtitle {
  font-size: 14px;
  color: var(--text-secondary);
  margin: 0;
}

.close-icon-btn {
  color: var(--text-secondary);
  transition: all var(--transition-normal);
}

.close-icon-btn:hover {
  color: #648afd; /* Couleur orange */
  background-color: rgba(255, 107, 0, 0.1); /* Fond légèrement orange */
  transform: rotate(90deg);
}

/* Search Container */
.search-container {
  display: flex;
  align-items: center;
  margin: 16px 24px 0;
  background-color: var(--background-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: 8px 12px;
  transition: all var(--transition-normal);
}

.search-container:focus-within {
  border-color: #ff6b00; /* Bordure orange */
  box-shadow: 0 0 0 2px rgba(255, 107, 0, 0.1); /* Ombre orange */
}

.search-icon {
  color: var(--text-secondary);
  margin-right: 8px;
  transition: color 0.3s ease;
}

.search-container:focus-within .search-icon {
  color: #ff6b00; /* Couleur orange quand la barre de recherche est active */
}

.search-input {
  flex: 1;
  border: none;
  background: transparent;
  font-size: 14px;
  color: var(--text-primary);
  outline: none;
  padding: 4px 0;
}

.search-input::placeholder {
  color: var(--text-tertiary);
}

/* Dialog Content */
mat-dialog-content {
  padding: 16px 24px;
  overflow-y: auto;
  max-height: 50vh;
}

/* Workspaces Container */
.workspaces-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* Workspace Card */
.workspace-card {
  display: flex;
  align-items: center;
  padding: 16px;
  border-radius: var(--radius-lg);
  background-color: var(--background-primary);
  border: 1px solid var(--border-color);
  transition: all var(--transition-normal);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  animation: fadeIn 0.5s ease;
}

.workspace-card::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 4px;
  background-color: transparent;
  transition: all var(--transition-normal);
}

.workspace-card:hover {
  border-color: var(--border-color);
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.workspace-card:hover::before {
  background-color: #ff6b00; /* Orange pur */
}

.workspace-card.active {
  border-color: #ff6b00; /* Bordure orange */
  background-color: rgba(255, 107, 0, 0.05); /* Fond légèrement orange */
}

.workspace-card.active::before {
  background-color: #ff6b00; /* Indicateur orange */
}

/* Style pour le workspace sélectionné mais pas actif */
.workspace-card.selected {
  border-color: #021c6c; /* Bordure bleue */
  background-color: rgba(2, 28, 108, 0.05); /* Fond légèrement bleu */
}

.workspace-card.selected::before {
  background-color: #021c6c; /* Indicateur bleu */
}

.workspace-card.selected .select-btn {
  color: #021c6c; /* Couleur bleue */
}

/* Workspace Logo */
.workspace-logo {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-md);
  overflow: hidden;
  margin-right: 16px;
  flex-shrink: 0;
  background-color: var(--background-tertiary);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--border-color);
}

.workspace-logo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.default-logo {
  width: 50%;
  height: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #05278f; /* Fond orange */
  color: white;
  font-size: 10px;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(255, 107, 0, 0.3); /* Ombre orange */
}

/* Workspace Details */
.workspace-details {
  flex: 1;
  min-width: 0;
}

.workspace-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.workspace-description {
  font-size: 13px;
  color: var(--text-secondary);
  margin: 0 0 8px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.workspace-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.workspace-location, .workspace-role {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: var(--text-secondary);
}

.workspace-location mat-icon, .workspace-role mat-icon {
  font-size: 14px;
  height: 14px;
  width: 14px;
  margin-right: 4px;
}

/* Workspace Actions */
.workspace-actions {
  margin-left: 12px;
}

.select-btn {
  color: #021c6c; /* Couleur orange */
  transition: all 0.3s ease;
}

.select-btn:hover {
  background-color: rgba(255, 107, 0, 0.1); /* Fond légèrement orange */
  transform: translateX(2px);
}

.workspace-card.active .select-btn {
  color: #ff6b00; /* Couleur orange */
}

/* No Workspaces */
.no-workspaces {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px 16px;
  text-align: center;
  color: var(--text-secondary);
}

.empty-icon {
  font-size: 48px;
  color: #ff6b00; /* Couleur orange */
  margin-bottom: 16px;
}

.no-workspaces p {
  font-size: 16px;
  margin-bottom: 20px;
}

.create-workspace-btn {
  margin-top: 8px;
  background-color: #648afd; /* Fond orange */
  color: white;
  border: none;
}

.create-workspace-btn:hover {
  background-color: #021c6c; /* Orange plus foncé au survol */
  box-shadow: 0 2px 8px rgba(255, 107, 0, 0.3); /* Ombre orange */
}

/* Dialog Actions */
mat-dialog-actions {
  padding: 16px 24px;
  border-top: 1px solid #e2e8f0; /* Bordure gris clair */
  display: flex;
  justify-content: space-between;
  background-color: #ffffff; /* Fond blanc pur */
}

.cancel-btn {
  color: #64748b; /* Gris foncé */
  font-weight: 500;
  transition: color 0.3s ease;
}

.cancel-btn:hover {
  color: #021c6c; /* Couleur orange au survol */
}

.create-btn {
  background-color: #021c6c; /* Fond orange */
  color: white;
  border-radius: 8px;
  padding: 0 16px;
  height: 40px;
  font-weight: 500;
  display: flex;
  align-items: center;
  border: none;
}

.create-btn mat-icon {
  margin-right: 8px;
}

.create-btn:hover {
  background-color: #648afd; /* Orange plus foncé au survol */
  box-shadow: 0 2px 8px rgba(255, 107, 0, 0.3); /* Ombre orange */
  transform: translateY(-2px);
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Animation for staggered cards */
.workspace-card[data-index="0"] { animation-delay: 0.1s; }
.workspace-card[data-index="1"] { animation-delay: 0.15s; }
.workspace-card[data-index="2"] { animation-delay: 0.2s; }
.workspace-card[data-index="3"] { animation-delay: 0.25s; }
.workspace-card[data-index="4"] { animation-delay: 0.3s; }
.workspace-card[data-index="5"] { animation-delay: 0.35s; }

/* Responsive Design */
@media (max-width: 768px) {
  .modal-content {
    max-width: 90%;
    max-height: 80vh;
  }

  .modal-header {
    padding: 16px 20px;
  }

  .header-icon {
    width: 36px;
    height: 36px;
  }

  .header-icon mat-icon {
    font-size: 20px;
  }

  .modal-title {
    font-size: 18px;
  }

  .modal-subtitle {
    font-size: 13px;
  }

  .search-container {
    margin: 12px 20px 0;
  }

  mat-dialog-content {
    padding: 12px 20px;
  }

  .workspace-card {
    padding: 12px;
  }

  .workspace-logo {
    width: 40px;
    height: 40px;
    margin-right: 12px;
  }

  .workspace-name {
    font-size: 15px;
  }

  .workspace-description {
    font-size: 12px;
  }

  mat-dialog-actions {
    padding: 12px 20px;
  }
}

@media (max-width: 480px) {
  .modal-content {
    max-width: 95%;
    max-height: 85vh;
  }

  .modal-header {
    padding: 14px 16px;
  }

  .header-icon {
    width: 32px;
    height: 32px;
    margin-right: 12px;
  }

  .header-icon mat-icon {
    font-size: 18px;
  }

  .modal-title {
    font-size: 16px;
  }

  .modal-subtitle {
    font-size: 12px;
  }

  .search-container {
    margin: 10px 16px 0;
    padding: 6px 10px;
  }

  .search-input {
    font-size: 13px;
  }

  mat-dialog-content {
    padding: 10px 16px;
  }

  .workspace-card {
    padding: 10px;
  }

  .workspace-logo {
    width: 36px;
    height: 36px;
    margin-right: 10px;
  }

  .workspace-name {
    font-size: 14px;
  }

  .workspace-description {
    font-size: 11px;
    margin-bottom: 6px;
  }

  .workspace-meta {
    gap: 8px;
  }

  .workspace-location, .workspace-role {
    font-size: 11px;
  }

  mat-dialog-actions {
    padding: 10px 16px;
    flex-direction: column-reverse;
    gap: 8px;
  }

  .cancel-btn, .create-btn {
    width: 100%;
  }
}
