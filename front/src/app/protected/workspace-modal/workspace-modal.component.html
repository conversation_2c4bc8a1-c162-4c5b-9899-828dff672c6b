<div class="modal-overlay">
  <div class="modal-content">
    <div class="modal-header">
      <div class="header-content">
        <div class="header-icon">
          <mat-icon>business</mat-icon>
        </div>
        <div class="header-text">
          <h2 mat-dialog-title class="modal-title">Choisir un espace de travail</h2>
          <p class="modal-subtitle">Sélectionnez l'espace de travail auquel vous souhaitez accéder</p>
        </div>
      </div>
      <button mat-icon-button class="close-icon-btn" (click)="closeModal()" aria-label="Fermer">
        <mat-icon>close</mat-icon>
      </button>
    </div>

    <div class="search-container" *ngIf="workspaces.length > 3">
      <mat-icon class="search-icon">search</mat-icon>
      <input type="text" class="search-input" placeholder="Rechercher un espace..." (input)="filterWorkspaces($event)">
    </div>

    <mat-dialog-content>
      <div
        class="workspace-card"
        [class.active]="false"
        (click)="switchToWorkspace('candi')"
        [attr.data-index]="-1"
      >
        <div class="workspace-details">
          <h4 class="workspace-name">Switch as candidate</h4>
        </div>
        <div class="workspace-actions">
          <button mat-icon-button class="select-btn" matTooltip="Sélectionner">
            <mat-icon>arrow_forward</mat-icon>
          </button>
        </div>
      </div>
      <div *ngIf="filteredWorkspaces.length > 0; else noWorkspaces" class="workspaces-container">
        <div
          *ngFor="let ws of filteredWorkspaces; let i = index"
          class="workspace-card"
          [class.active]="isActiveWorkspace(ws.id)"
          [class.selected]="activeWorkspaceId === ws.id"
          (click)="switchToWorkspace(ws.id)"
          [attr.data-index]="i"
        >
          <div class="workspace-logo">
            <img *ngIf="ws.logoUrl; else defaultLogo" [src]="assetsUrl + ws.logoUrl" alt="{{ ws.name }} logo">
            <ng-template #defaultLogo>
              <div class="default-logo">{{ ws.name.charAt(0) }}</div>
            </ng-template>
          </div>
          <div class="workspace-details">
            <h4 class="workspace-name">{{ ws.name }}</h4>
            <p class="workspace-description">{{ ws.description || 'Aucune description' }}</p>
            <div class="workspace-meta">
              <span class="workspace-location" *ngIf="ws.location">
                <mat-icon>location_on</mat-icon> {{ ws.location }}
              </span>
              <span class="workspace-role" *ngIf="ws.role">
                <mat-icon>badge</mat-icon> {{ ws.role }}
              </span>
            </div>
          </div>
          <div class="workspace-actions">
            <button mat-icon-button class="select-btn" matTooltip="Sélectionner">
              <mat-icon>arrow_forward</mat-icon>
            </button>
          </div>
        </div>
      </div>
      <ng-template #noWorkspaces>
        <div class="no-workspaces">
          <mat-icon class="empty-icon">business_off</mat-icon>
          <p>Aucun espace de travail trouvé</p>
          <button mat-stroked-button color="primary" class="create-workspace-btn" (click)="createWorkspace()">
            <mat-icon>add_business</mat-icon>
            Créer un espace de travail
          </button>
        </div>
      </ng-template>
    </mat-dialog-content>

    <mat-dialog-actions>
      <button mat-button (click)="closeModal()" class="cancel-btn">Annuler</button>
      <button mat-flat-button color="primary" class="create-btn" (click)="createWorkspace()">
        <mat-icon>add</mat-icon> Nouvel espace
      </button>
    </mat-dialog-actions>
  </div>
</div>
