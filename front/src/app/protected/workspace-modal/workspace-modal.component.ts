import {Component, Inject, OnInit} from '@angular/core';
import {
  MAT_DIALOG_DATA,
  Mat<PERSON><PERSON>ogA<PERSON>,
  Mat<PERSON>ialogContent,
  MatDialogRef,
  MatDialogTitle
} from '@angular/material/dialog';
import {Router} from '@angular/router';
import {WorkspaceService} from '../../services/workspace/workspace.service';
import {MatIcon} from '@angular/material/icon';
import {NgForOf, NgIf} from '@angular/common';
import {MatButton, MatIconButton} from '@angular/material/button';
import {MatTooltip} from '@angular/material/tooltip';
import {environment} from '../../../environments/environment';
import {UserRoleService} from '../../services/user-role/user-role.service';

@Component({
  selector: 'app-workspace-modal',
  templateUrl: './workspace-modal.component.html',
  standalone: true,
  imports: [

    MatIcon,
    NgIf,
    NgForOf,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>on,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>nt,
    Mat<PERSON><PERSON>ogTitle,
    Mat<PERSON>ooltip
  ],
  styleUrls: ['./workspace-modal.component.css']
})
export class WorkspaceModalComponent implements OnInit {
  workspaces: any[] = [];
  filteredWorkspaces: any[] = [];
  activeWorkspaceId: string | null = null;
  assetsUrl = environment.assetsUrl

  constructor(
    private dialogRef: MatDialogRef<WorkspaceModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private workspaceService: WorkspaceService,
    private router: Router,
    private userRoleService: UserRoleService
  ) {
  }

  ngOnInit(): void {
    // Récupérer l'ID du workspace actif
    this.activeWorkspaceId = this.workspaceService.getActiveWorkspaceId();

    // Si aucun workspace n'est actif, utiliser le workspace sélectionné pour l'affichage
    if (!this.activeWorkspaceId) {
      this.activeWorkspaceId = this.workspaceService.getSelectedWorkspaceId();
      console.log('WorkspaceModal - Aucun workspace actif, utilisation du workspace sélectionné:', this.activeWorkspaceId);
    }

    // Charger les workspaces
    this.loadWorkspaces();
  }

  /**
   * Charge les workspaces de l'utilisateur
   */
  loadWorkspaces(): void {
    this.workspaceService.getWorkspacesForUser().subscribe({
      next: (res) => {
        // Ajouter des informations supplémentaires aux workspaces
        this.workspaces = res.map((ws: any) => ({
          ...ws,
          role: ws.role || 'Membre',
          description: ws.description || 'Aucune description disponible'
        }));

        // Initialiser les workspaces filtrés
        this.filteredWorkspaces = [...this.workspaces];

        // Sélectionner automatiquement le premier workspace (visuellement seulement)
        if (this.workspaces.length > 0 && !this.activeWorkspaceId) {
          const firstWorkspaceId = this.workspaces[0].id;
          console.log('WorkspaceModal - Sélection automatique du premier workspace:', firstWorkspaceId);
          this.workspaceService.setSelectedWorkspace(firstWorkspaceId);
          this.activeWorkspaceId = firstWorkspaceId; // Pour l'affichage visuel seulement
        }
      },
      error: (err) => {
        console.error('Erreur lors du chargement des workspaces', err);
        this.workspaces = [];
        this.filteredWorkspaces = [];
      }
    });
  }

  /**
   * Filtre les workspaces en fonction de la recherche
   * @param event Événement d'input
   */
  filterWorkspaces(event: Event): void {
    const searchTerm = (event.target as HTMLInputElement).value.toLowerCase();

    if (!searchTerm) {
      this.filteredWorkspaces = [...this.workspaces];
      return;
    }

    this.filteredWorkspaces = this.workspaces.filter(ws =>
      ws.name.toLowerCase().includes(searchTerm) ||
      (ws.description && ws.description.toLowerCase().includes(searchTerm)) ||
      (ws.location && ws.location.toLowerCase().includes(searchTerm))
    );
  }

  /**
   * Vérifie si un workspace est actif ou sélectionné
   * @param workspaceId ID du workspace
   * @returns true si le workspace est actif ou sélectionné
   */
  isActiveWorkspace(workspaceId: string): boolean {
    // Vérifier si le workspace est actif ou sélectionné
    const isActive = this.activeWorkspaceId === workspaceId;
    const isSelected = this.workspaceService.getSelectedWorkspaceId() === workspaceId;

    // Retourner true si le workspace est actif ou sélectionné
    return isActive || isSelected;
  }

  /**
   * Change de workspace
   * @param workspaceId ID du workspace
   */
  switchToWorkspace(workspaceId: string): void {
    if (!workspaceId) return;

    if (workspaceId === 'candi') {
      console.log('Switching to candidate profile');

      // Fermer le modal avant de changer de mode
      this.dialogRef.close();

      // Passer en mode candidat via le UserRoleService
      this.userRoleService.forceUserAsCandidate();

      // Attendre un court instant pour s'assurer que le changement est appliqué
      setTimeout(() => {
        console.log('Navigating to profile page after switching to candidate mode');
        // Use Angular router instead of window.location.href
        this.router.navigate(['/profile']);
      }, 100);

      return;
    }

    // Si c'est déjà le workspace actif, ne rien faire
    if (this.workspaceService.getActiveWorkspaceId() === workspaceId) {
      console.log('Workspace already active:', workspaceId);
      this.closeModal();
      return;
    }

    // Changer de workspace
    console.log('Switching to workspace:', workspaceId);

    // Fermer le modal avant de changer de workspace
    this.dialogRef.close();

    // Passer en mode recruteur via le UserRoleService
    this.userRoleService.forceUserAsRecruiter(workspaceId);

    // Attendre un court instant pour s'assurer que le changement est appliqué
    setTimeout(() => {
      console.log('Navigating to recruiter profile after switching workspace');
      // Use Angular router instead of window.location.href
      this.router.navigate(['/recruiter-profile']);
    }, 100);
  }

  createWorkspace(): void {
    this.dialogRef.close('create');
    this.router.navigate(['/workspace']);
  }

  closeModal(): void {
    this.dialogRef.close();
  }
}
