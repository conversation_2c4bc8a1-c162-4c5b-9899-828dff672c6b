<mat-grid-list [cols]="4" rowHeight="100px" role="grid">
  <!-- Colonne 1 : Espace réservé pour la carte de profil fixe -->
  <mat-grid-tile [colspan]="1" [rowspan]=23 role="gridcell">
    <!-- Carte de profil fixe (en dehors de la grille pour rester fixe) -->
  </mat-grid-tile>

  <!-- <PERSON>te de profil fixe (placée en dehors de la grille) -->
  <mat-card class="profile-card">
    <!-- En-tête avec photo de couverture et avatar -->
    <div class="profile-header">
      <!-- Fond simple et élégant -->
      <div class="profile-cover"></div>

      <!-- Avatar professionnel -->
      <div class="profile-avatar-container">
        <div class="profile-avatar">
          <mat-icon class="avatar-icon">person</mat-icon>
        </div>

      </div>

      <!-- Informations principales -->
      <div class="profile-main-info">
        <h2 class="profile-name">{{ user?.firstName || 'John' }} {{ user?.lastName || 'Doe' }}</h2>

      </div>
    </div>

    <!-- Corps de la carte avec informations de contact -->
    <div class="profile-body">
      <div class="info-list">
        <div class="info-item">
          <mat-icon>email</mat-icon>
          <span class="info-text">{{ user?.email || '<EMAIL>' }}</span>
        </div>
        <div class="info-item">
          <mat-icon>phone</mat-icon>
          <span class="info-text">{{ user?.phoneNumber || '+33 6 12 34 56 78' }}</span>
        </div>
        <div class="info-item">
          <mat-icon>location_on</mat-icon>
          <span class="info-text">{{ user?.address || 'Paris, France' }}</span>
        </div>

      </div>

      <div class="actions-list">
        <div class="action-item" (click)="navigateToSavedItems()">
          <mat-icon>bookmark</mat-icon>
          <span>Éléments enregistrés</span>
        </div>
        <div class="action-item" (click)="navigateToWorkspaces()">
          <mat-icon>groups</mat-icon>
          <span>Workspaces</span>
        </div>
        <div class="action-item" (click)="navigateToSettings()">
          <mat-icon>settings</mat-icon>
          <span>Paramètres</span>
        </div>
      </div>
    </div>
  </mat-card>


  <!-- Colonne 4 : Widget "À la une" -->
  <!--  <mat-grid-tile [colspan]="1" [rowspan]="3" role="gridcell">-->
  <!--    <mat-card class="widget-card">-->
  <!--      <mat-card-content class="widget-info">-->
  <!--        <div class="widget-header">-->
  <!--          <mat-card-title class="widget-title">À la une</mat-card-title>-->
  <!--        </div>-->
  <!--        <mat-divider class="widget-divider"></mat-divider>-->
  <!--        <mat-list class="widget-list" role="list">-->
  <!--          <mat-list-item class="widget-list-item" role="listitem">-->
  <!--            <mat-icon class="widget-icon" aria-hidden="true">event</mat-icon>-->
  <!--            <span class="widget-text">Événements</span>-->
  <!--          </mat-list-item>-->
  <!--          <mat-list-item class="widget-list-item" role="listitem">-->
  <!--            <mat-icon class="widget-icon" aria-hidden="true">article</mat-icon>-->
  <!--            <span class="widget-text">Publications</span>-->
  <!--          </mat-list-item>-->
  <!--          <mat-list-item class="widget-list-item" role="listitem">-->
  <!--            <mat-icon class="widget-icon" aria-hidden="true">work</mat-icon>-->
  <!--            <span class="widget-text">Offres d’emploi</span>-->
  <!--          </mat-list-item>-->
  <!--        </mat-list>-->
  <!--      </mat-card-content>-->
  <!--    </mat-card>-->
  <!--  </mat-grid-tile>-->

  <!-- Colonne 2-3 : Barre de recherche et Publications -->
  <mat-grid-tile [colspan]="2" [rowspan]="23" role="gridcell">
    <div class="post-box">
      <!-- Barre de recherche intégrée -->
      <div class="search-container">
        <div class="search-bar-wrapper">
          <div class="search-icon-wrapper">
            <mat-icon class="search-icon">search</mat-icon>
          </div>
          <input
            type="text"
            class="search-input"
            placeholder="Rechercher des publications, personnes ou entreprises..."
            [(ngModel)]="searchTerm"
            (input)="searchPosts()"
            aria-label="Rechercher"
          />
          <button *ngIf="searchTerm" class="clear-search-button" (click)="clearSearch()"
                  aria-label="Effacer la recherche">
            <mat-icon>close</mat-icon>
          </button>
        </div>
      </div>

      <!-- Séparateur -->
      <div class="search-posts-separator"></div>
      <!-- Display total posts loaded -->
      <mat-card *ngFor="let post of filteredPosts" class="post-card linkedin-style">
        <mat-card-header class="post-card-header">
          <!-- Workspace Logo -->
          <div class="post-author-info">
            <div class="avatar-container">
              <img *ngIf="post.workspace?.logoUrl" [src]="getLogoUrl(post.workspace.logoUrl)" alt="Workspace Logo"
                   class="avatar-icon" (error)="handleImageError($event, 'account_circle')"/>

              <mat-icon *ngIf="!post.workspace?.logoUrl" class="avatar-icon">account_circle</mat-icon>
            </div>
            <div class="author-details">
              <h3 class="post-title">{{ post.titre }}</h3>
              <div class="post-meta">
                <!-- Afficher le nom du workspace associé au post s'il existe -->
                <span class="workspace-name" *ngIf="post.workspace?.name">{{ post.workspace.name }}</span>
                <span class="post-author">Publié par {{ post.userId || 'Utilisateur inconnu' }}</span>
                <span class="post-date">{{ post.datePublication | date: 'medium' }}</span>
              </div>
            </div>
          </div>
        </mat-card-header>
        <mat-card-content class="post-content">
          <p class="description">{{ post.description }}</p>
        </mat-card-content>
        <mat-card-actions class="post-actions">
          <div class="reaction-bar">
            <button
              mat-button
              [matMenuTriggerFor]="reactionMenu"
              class="reaction-btn"
              aria-label="React to post"
            >
              <span class="reaction-label">{{ post.userReactionType ? post.userReactionType : 'Travopulse' }}</span>
              <img
                *ngIf="!post.userReactionType || post.userReactionType === 'like'"
                src="assets/images/trav.png"
                alt="Travopulse Icon"
                class="action-icon"
              />
              <mat-icon
                *ngIf="post.userReactionType === 'love'"
                class="action-icon love-icon"
                aria-hidden="true"
              >
                favorite
              </mat-icon>
              <mat-icon
                *ngIf="post.userReactionType === 'haha'"
                class="action-icon haha-icon"
                aria-hidden="true"
              >
                sentiment_very_satisfied
              </mat-icon>
              <span class="reaction-count">{{ post.reactionCounts?.total || 0 }}</span>
            </button>
            <mat-menu #reactionMenu="matMenu" class="reaction-menu">
              <div class="reaction-container">
                <button
                  mat-menu-item
                  (click)="react(post, 'like')"
                  class="reaction-option"
                  [ngClass]="{'selected-reaction': post.userReactionType === 'like'}"
                >
                  <img src="assets/images/trav.png" alt="Travopulse Icon" class="reaction-icon"/>
                  <span class="reaction-count">{{ post.reactionCounts?.like || 0 }}</span>
                </button>
                <button
                  mat-menu-item
                  (click)="react(post, 'love')"
                  class="reaction-option"
                  [ngClass]="{'selected-reaction': post.userReactionType === 'love'}"
                  aria-label="Love post"
                >
                  <mat-icon class="reaction-icon love-icon" aria-hidden="true">favorite</mat-icon>
                  <span class="reaction-count">{{ post.reactionCounts?.love || 0 }}</span>
                </button>
                <button
                  mat-menu-item
                  (click)="react(post, 'haha')"
                  class="reaction-option"
                  [ngClass]="{'selected-reaction': post.userReactionType === 'haha'}"
                  aria-label="Haha reaction"
                >
                  <mat-icon
                    class="reaction-icon haha-icon"
                    aria-hidden="true"
                  >
                    sentiment_very_satisfied
                  </mat-icon>
                  <span class="reaction-count">{{ post.reactionCounts?.haha || 0 }}</span>
                </button>
              </div>
            </mat-menu>
            <button
              mat-button
              class="action-btn apply-btn"
              (click)="navigateToApplication(post.id, post.titre)"
              aria-label="Apply to job"
            >
              <mat-icon class="action-icon" aria-hidden="true">send</mat-icon>
              <span>Postuler</span>
            </button>
            <button
              mat-button
              class="action-btn details-btn"
              (click)="navigateToPostDetail(post.id, post.titre)"
              aria-label="View post details"
            >
              <mat-icon class="action-icon" aria-hidden="true">visibility</mat-icon>
              <span>Voir détails</span>
            </button>
            <button
              mat-button
              class="action-btn save-btn"
              (click)="toggleSavePost(post)"
              [ngClass]="{'saved': isPostSaved(post.id)}"
              aria-label="Enregistrer cette publication"
            >
              <mat-icon class="action-icon"
                        aria-hidden="true">{{ isPostSaved(post.id) ? 'bookmark' : 'bookmark_border' }}
              </mat-icon>
              <span>{{ isPostSaved(post.id) ? 'Enregistré' : 'Enregistrer' }}</span>
            </button>
          </div>
        </mat-card-actions>
      </mat-card>
      <!-- Indicateur de chargement initial -->
      <div class="loading-initial" *ngIf="isLoading && posts.length === 0" role="alert">
        <mat-spinner diameter="50" aria-label="Chargement des publications"></mat-spinner>
        <span>Chargement des publications...</span>
      </div>

      <!-- Indicateur de chargement pendant le défilement -->
      <div class="loading-more" *ngIf="isLoading && posts.length > 0" role="alert">
        <mat-spinner diameter="30" aria-label="Chargement de plus de publications"></mat-spinner>
        <span>Chargement...</span>
      </div>

      <!-- Message quand il n'y a plus de publications -->
      <div
        class="no-more-posts"
        *ngIf="!isLoading && !hasMorePosts && filteredPosts.length > 0"
        role="alert">
        <mat-icon>check_circle</mat-icon>
        <span>Vous avez vu toutes les publications</span>
      </div>

      <!-- Bouton pour charger plus manuellement (alternative au défilement) -->
      <div class="see-more" *ngIf="!isLoading && hasMorePosts && filteredPosts.length > 0">
        <button
          mat-raised-button
          color="primary"
          (click)="loadMorePosts()"
          aria-label="Charger plus de publications">
          <mat-icon>expand_more</mat-icon>
          Voir plus
        </button>
      </div>
    </div>
  </mat-grid-tile>

  <!-- Colonne 4 : Offres d'emploi et Footer -->
  <mat-grid-tile [colspan]="1" [rowspan]="9" role="gridcell">
    <!-- Espace réservé pour le contenu fixe -->
  </mat-grid-tile>

  <!-- Contenu fixe des offres d'emploi et du footer (placé en dehors de la grille) -->
  <div class="posts-footer-wrapper fixed-column">
    <div class="companies-container">
      <h3 class="companies-title">Entreprises qui recrutent</h3>

      <!-- Grille d'entreprises partenaires -->
      <div class="companies-grid">
        <!-- Indicateur de chargement -->
        <div class="loading-workspaces" *ngIf="isLoadingWorkspaces">
          <mat-spinner diameter="30" aria-label="Chargement des entreprises"></mat-spinner>
          <span>Chargement des entreprises...</span>
        </div>

        <!-- Affichage dynamique des workspaces (limité à 4 initialement) -->
        <div class="company-card" *ngFor="let workspace of displayedWorkspaces"
             (click)="navigateToCompany(workspace.id)">
          <div class="company-logo-container">
            <img *ngIf="workspace.logoUrl" [src]="assetsUrl + workspace.logoUrl" alt="{{ workspace.name }}"
                 class="company-logo" onerror="this.src='assets/images/placeholder-logo.png'">
            <mat-icon *ngIf="!workspace.logoUrl" class="company-icon">business</mat-icon>
          </div>
          <div class="company-name">{{ workspace.name }}</div>
        </div>

        <!-- Affichage d'un message si aucun workspace n'est disponible -->
        <div class="no-workspaces" *ngIf="!isLoadingWorkspaces && workspaces.length === 0">
          <mat-icon>info</mat-icon>
          <span>Aucune entreprise disponible pour le moment</span>
        </div>
      </div>

      <!-- Bouton pour voir plus d'entreprises -->
      <button mat-button class="view-more-companies" (click)="viewMoreCompanies()">
        <mat-icon aria-hidden="true">business</mat-icon>
        <span *ngIf="!showAllWorkspaces && workspaces.length > 4">Voir toutes les entreprises qui recrutent</span>
        <span *ngIf="showAllWorkspaces || workspaces.length <= 4">Voir la page des entreprises</span>
      </button>
    </div>


    <div class="footer-section" role="contentinfo">
      <div class="footer-links">
        <a
          href="#"
          class="footer-link dropdown"
          [matMenuTriggerFor]="conditionsMenu"
          aria-haspopup="true"
          aria-label="Conditions générales et confidentialité menu"
        >
          Conditions générales et confidentialité
          <mat-icon class="dropdown-icon" aria-hidden="true">arrow_drop_down</mat-icon>
        </a>
        <mat-menu #conditionsMenu="matMenu">
          <button mat-menu-item aria-label="View privacy policy">
            Politique de confidentialité
          </button>
          <button mat-menu-item aria-label="View terms of use">
            Conditions d'utilisation
          </button>
        </mat-menu>
      </div>
      <div class="footer-branding">
        <span class="footer-logo">MyApp</span>
        <span class="footer-copyright">
            <span class="copyright-symbol">©</span> 2025 MyApp Corporation
          </span>
      </div>
    </div>
  </div>
</mat-grid-list>
