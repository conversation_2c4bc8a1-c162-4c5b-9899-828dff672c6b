import { Compo<PERSON>, OnInit, ChangeDetectorRef, HostListener } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatGridList, MatGridTile } from '@angular/material/grid-list';
import { Mat<PERSON>ard, MatCardModule } from '@angular/material/card';
import {MatButton, MatButtonModule, MatIconButton} from '@angular/material/button';
import {MatIcon, MatIconModule} from '@angular/material/icon';
import { MatInput, MatInputModule } from '@angular/material/input';
import { CommonModule, NgClass, NgForOf } from '@angular/common';
import { MatList, MatListItem } from '@angular/material/list';
import { MatDivider } from '@angular/material/divider';
import { PostsService } from '../../services/posts/posts.service';
import { UserService } from '../../services/user/user.service';
import { AddPostComponent } from '../add-post/add-post.component';
import { Mat<PERSON>enu, MatMenuItem, MatMenuTrigger } from '@angular/material/menu';
import { Router } from '@angular/router';
import { ReactionService } from '../../services/reaction/reaction.service';
import { MatFormFieldModule } from '@angular/material/form-field';
import { OidcSecurityService } from 'angular-auth-oidc-client';
import { MatProgressSpinner } from '@angular/material/progress-spinner';
import { FormsModule } from '@angular/forms';
import { SavedItemsService } from '../../services/saved-items/saved-items.service';
import { WorkspaceService } from '../../services/workspace/workspace.service';
import { environment } from '../../../environments/environment';
import { UserRoleService } from '../../services/user-role/user-role.service';

type Post = {
  id: string;
  titre: string;
  description: string;
  entreprise: string;
  userId: string | null;
  profileRequest: string;
  contractType: string;
  datePublication: string;
  archived: boolean;
  createdBy?: string; // Nom de l'utilisateur qui a créé le post
  userEmail?: string; // Email de l'utilisateur
  userProfilePicture?: string; // URL de la photo de profil
  workspace: { name: string, logoUrl?: string }; // Assurez-vous que workspace est toujours défini
};

type Workspace = {
  name: string;
  logoUrl?: string;
};

type ExtendedPost = Post & {
  reactionCounts?: {
    like?: number;
    love?: number;
    haha?: number;
    total?: number;
  };
  userReactionType?: 'like' | 'love' | 'haha';
  workspace?: Workspace;
};

@Component({
  selector: 'app-acceuil',
  standalone: true,
  imports: [
    MatGridList,
    MatGridTile,
    MatCard,
    MatCardModule,
    MatButton,
    MatIcon,
    NgForOf,
    MatMenuTrigger,
    MatMenu,
    MatMenuItem,
    MatInputModule,
    MatFormFieldModule,
    NgClass,
    CommonModule,
    MatProgressSpinner,
    MatButtonModule,
    MatIconModule,
    FormsModule,
  ],
  templateUrl: './acceuil.component.html',
  styleUrls: ['./acceuil.component.scss'],
})
export class AcceuilComponent implements OnInit {
  posts: ExtendedPost[] = [];
  filteredPosts: ExtendedPost[] = [];
  emploiPosts: ExtendedPost[] = [];
  searchTerm: string = '';
  isSearching: boolean = false;
  assetsUrl = environment.assetsUrl;
  workspaces: any[] = []; // Liste complète des workspaces pour la section "Entreprises qui recrutent"
  displayedWorkspaces: any[] = []; // Liste des workspaces affichés (limités à 4 initialement)
  isLoadingWorkspaces: boolean = false; // Indicateur de chargement des workspaces
  showAllWorkspaces: boolean = false; // Indicateur pour afficher tous les workspaces

  activeWorkspace: any = null;

  limitedEmploiPosts = this.emploiPosts.slice(0, 2);
  user: any = {
    firstName: 'Utilisateur',
    lastName: '',
    email: 'Non renseigné',
    phoneNumber: 'Non renseigné',
    address: 'Non renseigné',
  };

  userId = 1;
  isLoading: any;
  hasMorePosts: boolean = false;

  constructor(
    private postsService: PostsService,
    private userService: UserService,
    public dialog: MatDialog,
    private router: Router,
    private reactionService: ReactionService,
    private oidcSecurityService: OidcSecurityService,
    private cdr: ChangeDetectorRef,
    private savedItemsService: SavedItemsService,
    private workspaceService: WorkspaceService,
    private userRoleService: UserRoleService
  ) {
    console.log('ReactionService instance:', reactionService);
  }

  ngOnInit(): void {
    // Forcer le mode candidat sur la page d'accueil
    console.log('AcceuilComponent - Initializing, forcing candidate mode');
    this.userRoleService.forceUserAsCandidate();

    this.oidcSecurityService.checkAuth().subscribe(({ isAuthenticated, userData }) => {
      if (!isAuthenticated) {
        console.error('User not authenticated. Redirecting to login.');
        this.oidcSecurityService.authorize();
        return;
      }

      console.log('User authenticated, raw userData:', JSON.stringify(userData));
      this.userId = userData?.sub && !isNaN(Number(userData.sub)) ? Number(userData.sub) : 1;
      console.log('Assigned userId from OIDC:', this.userId);

      // Charger le profil utilisateur
      this.loadUserProfile();

      // Charger le workspace actif (qui devrait être null maintenant)
      this.loadActiveWorkspace();

      // Charger les premiers posts avec le défilement infini
      this.loadMorePosts();

      // Initialiser les posts pour la colonne de droite (emplois limités)
      this.loadLimitedEmploiPosts();

      // Charger les workspaces pour la section "Entreprises qui recrutent"
      this.loadWorkspaces();
    });
  }

  // Méthode pour charger les posts limités pour la colonne de droite
  loadLimitedEmploiPosts(): void {
    this.postsService.getPosts(0,2).subscribe(
      (data: any) => {
        const posts = data.content || [];
        this.emploiPosts = posts;
        this.limitedEmploiPosts = posts.slice(0, 3);
      },
      (error) => {
        console.error('Erreur chargement des posts limités:', error);
      }
    );
  }

  // Détection du défilement pour charger plus de posts
  @HostListener('window:scroll', [])
  onWindowScroll(): void {
    // Vérifier si l'utilisateur est proche du bas de la page (200px avant la fin)
    const scrollPosition = window.scrollY || document.documentElement.scrollTop;
    const windowHeight = window.innerHeight;
    const documentHeight = document.documentElement.scrollHeight;

    // Si on est proche du bas de la page (200px avant la fin)
    if (scrollPosition + windowHeight >= documentHeight - 200) {
      // Charger plus de posts si on n'est pas déjà en train de charger et s'il y a plus de posts
      if (!this.isLoading && this.hasMorePosts) {
        console.log('Proche du bas de la page, chargement de plus de posts...');
        this.loadMorePosts();
      }
    }
  }

  private loadUserProfile(): void {
    this.userService.getUserProfile().subscribe(
      (data: any) => {
        console.log('Profile retrieved, raw data:', JSON.stringify(data));
        if (data && data.user && data.user.email) {
          this.user = {
            firstName: data.user.firstName || data.user.given_name || 'Utilisateur',
            lastName: data.user.lastName || data.user.family_name || '',
            email: data.user.email || 'Non renseigné',
            phoneNumber: data.user.phoneNumber || data.user.phone_number || 'Non renseigné',
            address: data.user.address || 'Non renseigné',
          };
          this.userId = data.user.id && !isNaN(Number(data.user.id)) ? Number(data.user.id) : this.userId;
          console.log('Normalized user from profile:', this.user);
        } else {
          console.error('No valid user data found in response:', data);
        }
        this.cdr.detectChanges();
      },
      (error) => {
        console.error('Failed to load user profile:', error);
        if (error.status === 401) {
          this.oidcSecurityService.authorize();
        }
      }
    );
  }

  // La méthode loadPosts a été remplacée par loadMorePosts avec défilement infini

  /**
   * Charge les détails du workspace actif (celui qui a été switché)
   */
  private loadActiveWorkspace(): void {
    console.log('Début de loadActiveWorkspace()');

    // Vérifier le localStorage et sessionStorage directement
    const localStorageId = localStorage.getItem('Workspace-Id');
    const sessionStorageId = sessionStorage.getItem('Workspace-Id');
    console.log('Workspace-Id dans localStorage:', localStorageId);
    console.log('Workspace-Id dans sessionStorage:', sessionStorageId);

    // Récupérer l'ID du workspace actif via le service
    const activeWorkspaceId = this.workspaceService.getActiveWorkspaceId();
    console.log('ID du workspace actif depuis le service:', activeWorkspaceId);

    // Si aucun workspace actif n'est trouvé, essayer de charger les workspaces de l'utilisateur
    if (!activeWorkspaceId) {
      console.log('Aucun workspace actif trouvé, chargement des workspaces de l\'utilisateur...');
      this.workspaceService.getWorkspacesForUser().subscribe({
        next: (workspaces) => {
          console.log('Workspaces de l\'utilisateur:', workspaces);
          if (workspaces && workspaces.length > 0) {
            // Utiliser le premier workspace comme workspace actif
            const firstWorkspace = workspaces[0];
            console.log('Utilisation du premier workspace comme workspace actif:', firstWorkspace);
            this.workspaceService.switchWorkspace(firstWorkspace.id);
            this.activeWorkspace = firstWorkspace;
            this.cdr.detectChanges();
          } else {
            console.log('Aucun workspace trouvé pour l\'utilisateur');
          }
        },
        error: (error) => {
          console.error('Erreur lors du chargement des workspaces de l\'utilisateur:', error);
        }
      });
      return;
    }

    // Si un workspace actif est trouvé, récupérer ses détails
    console.log('Tentative de récupération du workspace avec ID:', activeWorkspaceId);

    // Essayer d'abord getWorkspaceProfile
    this.workspaceService.getWorkspaceProfile(activeWorkspaceId).subscribe({
      next: (workspace) => {
        console.log('Réponse de getWorkspaceProfile:', workspace);
        if (workspace) {
          console.log('Workspace actif chargé avec getWorkspaceProfile:', workspace);
          this.activeWorkspace = workspace;
          this.cdr.detectChanges();
        } else {
          console.log('Aucun détail trouvé avec getWorkspaceProfile, essai avec getWorkspaceById...');
          // Si getWorkspaceProfile échoue, essayer getWorkspaceById
          this.workspaceService.getWorkspaceById(activeWorkspaceId).subscribe({
            next: (workspace) => {
              console.log('Réponse de getWorkspaceById:', workspace);
              if (workspace) {
                console.log('Workspace actif chargé avec getWorkspaceById:', workspace);
                this.activeWorkspace = workspace;
                this.cdr.detectChanges();
              } else {
                console.log('Aucun détail trouvé avec getWorkspaceById');
              }
            },
            error: (error) => {
              console.error('Erreur lors du chargement du workspace avec getWorkspaceById:', error);
            }
          });
        }
      },
      error: (error) => {
        console.error('Erreur lors du chargement du workspace avec getWorkspaceProfile:', error);
        console.log('Essai avec getWorkspaceById...');

        // Si getWorkspaceProfile échoue, essayer getWorkspaceById
        this.workspaceService.getWorkspaceById(activeWorkspaceId).subscribe({
          next: (workspace) => {
            console.log('Réponse de getWorkspaceById après échec de getWorkspaceProfile:', workspace);
            if (workspace) {
              console.log('Workspace actif chargé avec getWorkspaceById:', workspace);
              this.activeWorkspace = workspace;
              this.cdr.detectChanges();
            } else {
              console.log('Aucun détail trouvé avec getWorkspaceById');
              // Essayer avec getPublicWorkspaceInfo en dernier recours
              this.workspaceService.getPublicWorkspaceInfo(activeWorkspaceId).subscribe({
                next: (publicInfo) => {
                  console.log('Réponse de getPublicWorkspaceInfo:', publicInfo);
                  if (publicInfo) {
                    console.log('Informations publiques du workspace chargées:', publicInfo);
                    this.activeWorkspace = publicInfo;
                    this.cdr.detectChanges();
                  } else {
                    console.log('Aucune information publique trouvée pour le workspace');
                  }
                },
                error: (publicError) => {
                  console.error('Erreur lors du chargement des informations publiques du workspace:', publicError);
                }
              });
            }
          },
          error: (error) => {
            console.error('Erreur lors du chargement du workspace avec getWorkspaceById:', error);
            // Essayer avec getPublicWorkspaceInfo en dernier recours
            this.workspaceService.getPublicWorkspaceInfo(activeWorkspaceId).subscribe({
              next: (publicInfo) => {
                console.log('Réponse de getPublicWorkspaceInfo après échec de getWorkspaceById:', publicInfo);
                if (publicInfo) {
                  console.log('Informations publiques du workspace chargées:', publicInfo);
                  this.activeWorkspace = publicInfo;
                  this.cdr.detectChanges();
                } else {
                  console.log('Aucune information publique trouvée pour le workspace');
                }
              },
              error: (publicError) => {
                console.error('Erreur lors du chargement des informations publiques du workspace:', publicError);
              }
            });
          }
        });
      }
    });
  }

  openAddPostDialog(): void {
    const dialogRef = this.dialog.open(AddPostComponent, { width: '600px' });
    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        // Réinitialiser et recharger les posts après ajout d'un nouveau post
        this.currentPage = 0;
        this.initialLoad = true;
        this.posts = [];
        this.filteredPosts = [];
        this.loadMorePosts();
        this.loadLimitedEmploiPosts();
      }
    });
  }

  react(post: any, reactionType: string) {
    if (!post?.id) {
      console.error('Post ID is missing');
      return;
    }
    if (!this.userId || isNaN(Number(this.userId))) {
      console.error('Invalid userId detected:', { userId: this.userId, postId: post.id, reactionType });
      this.oidcSecurityService.authorize();
      return;
    }

    const postId = post.id;
    const userId = Number(this.userId);

    if (post.hasReacted) {
      if (post.userReactionType === reactionType) {
        console.log('User already reacted with this type:', reactionType);
        return;
      } else {
        this.reactionService.addReaction(postId, userId, reactionType).subscribe(
          (response) => {
            console.log('Reaction updated:', response);
            post.userReactionType = reactionType;
            this.reactionService.getReactionCounts(postId).subscribe(
              (counts) => (post.reactionCounts = counts),
              (error) => console.error(`Failed to update reaction counts for post ${postId}:`, error)
            );
          },
          (error) => {
            console.error('Failed to update reaction:', error);
            if (error.status === 401) {
              this.oidcSecurityService.authorize();
            }
          }
        );
      }
    } else {
      this.reactionService.addReaction(postId, userId, reactionType).subscribe(
        (response) => {
          console.log('Réaction enregistrée :', response);
          post.hasReacted = true;
          post.userReactionType = reactionType;
          this.reactionService.getReactionCounts(postId).subscribe(
            (counts) => (post.reactionCounts = counts),
            (error) => console.error(`Failed to update reaction counts for post ${postId}:`, error)
          );
        },
        (error) => {
          console.error('Erreur lors de l\'enregistrement :', error);
          if (error.status === 401) {
            this.oidcSecurityService.authorize();
          }
        }
      );
    }
  }

  /**
   * Méthode utilitaire pour construire l'URL complète du logo
   * @param logoPath Chemin du logo
   * @returns URL complète du logo ou image par défaut si le chemin est vide
   */
  getLogoUrl(logoPath: string | undefined): string {
    if (!logoPath) return 'assets/images/placeholder-logo.png';

    // Vérifier si le chemin est une URL complète
    if (logoPath.startsWith('http://') || logoPath.startsWith('https://')) {
      return logoPath;
    }

    // Construire l'URL avec le chemin du serveur
    const fullPath = this.assetsUrl + (logoPath.startsWith('/') ? '' : '/') + logoPath;
    console.log('URL complète construite:', fullPath);
    return fullPath;
  }

  /**
   * Méthode pour gérer les erreurs de chargement d'image
   * @param event Événement d'erreur
   * @param fallbackIcon Icône de secours à afficher
   */
  handleImageError(event: any, fallbackIcon: string = 'business') {
    console.log('Erreur de chargement d\'image:', event);
    // Remplacer l'image par une icône mat-icon
    const imgElement = event.target;
    const parent = imgElement.parentNode;

    // Créer une icône mat-icon comme fallback
    const iconElement = document.createElement('mat-icon');
    iconElement.className = 'avatar-icon';
    iconElement.textContent = fallbackIcon;

    // Remplacer l'image par l'icône
    if (parent) {
      parent.replaceChild(iconElement, imgElement);
    }
  }

  navigateToApplication(postId: string, postTitle: string) {
    const title = encodeURIComponent(postTitle || 'Unknown Job Post');
    console.log('Navigating with postId:', postId, 'postTitle:', postTitle);
    this.router.navigate([`/job-application/${postId}/${title}`]);
  }

  // Navigation vers la page de l'entreprise (workspace)
  navigateToCompany(workspaceId: string) {
    console.log(`Navigating to workspace page: ${workspaceId}`);
    // Navigation vers la page du workspace
    this.router.navigate(['/profile-workspace', workspaceId]);
  }

  // Méthode pour charger tous les workspaces (entreprises) depuis le backend
  loadWorkspaces(): void {
    // Activer l'indicateur de chargement
    this.isLoadingWorkspaces = true;
    this.cdr.detectChanges();

    this.workspaceService.getAllWorkspaces().subscribe({
      next: (workspaces) => {
        console.log('Toutes les entreprises chargées:', workspaces);
        this.workspaces = workspaces;
        // Initialiser les workspaces affichés (limités à 4)
        this.updateDisplayedWorkspaces();
        // Désactiver l'indicateur de chargement
        this.isLoadingWorkspaces = false;
        this.cdr.detectChanges();
      },
      error: (error) => {
        console.error('Erreur lors du chargement des entreprises:', error);
        // En cas d'erreur, essayer de charger les workspaces de l'utilisateur comme fallback
        this.workspaceService.getWorkspacesForUser().subscribe({
          next: (userWorkspaces) => {
            console.log('Workspaces de l\'utilisateur chargés comme fallback:', userWorkspaces);
            this.workspaces = userWorkspaces;
            // Initialiser les workspaces affichés (limités à 4)
            this.updateDisplayedWorkspaces();
            // Désactiver l'indicateur de chargement
            this.isLoadingWorkspaces = false;
            this.cdr.detectChanges();
          },
          error: (userError) => {
            console.error('Erreur lors du chargement des workspaces de l\'utilisateur:', userError);
            // En cas d'erreur, on initialise avec un tableau vide
            this.workspaces = [];
            this.displayedWorkspaces = [];
            // Désactiver l'indicateur de chargement
            this.isLoadingWorkspaces = false;
            this.cdr.detectChanges();
          }
        });
      }
    });
  }

  // Méthode pour mettre à jour les workspaces affichés
  updateDisplayedWorkspaces(): void {
    if (this.showAllWorkspaces) {
      // Afficher tous les workspaces
      this.displayedWorkspaces = [...this.workspaces];
    } else {
      // Afficher seulement les 4 premiers workspaces
      this.displayedWorkspaces = this.workspaces.slice(0, 4);
    }
  }

  // Afficher toutes les entreprises qui recrutent
  viewMoreCompanies() {
    if (this.workspaces.length <= 4 || this.showAllWorkspaces) {
      // S'il y a 4 entreprises ou moins, ou si on affiche déjà toutes les entreprises,
      // naviguer vers la page des workspaces
      console.log('Navigation vers la page des workspaces');
      this.router.navigate(['/workspaces']);
    } else {
      // Sinon, basculer l'affichage pour montrer toutes les entreprises
      this.showAllWorkspaces = true;
      this.updateDisplayedWorkspaces();
      console.log('Affichage de toutes les entreprises dans la section actuelle');
      this.cdr.detectChanges();
    }
  }

  navigateToWorkspaces(): void {
    console.log('Navigating to Workspaces');
    this.router.navigate(['/workspaces']);
  }

  navigateToSettings(): void {
    console.log('Navigating to Settings');
    this.router.navigate(['/settings']);
  }

  navigateToSavedItems(): void {
    console.log('Navigating to Saved Items');
    this.router.navigate(['/saved-items']);
  }

  navigateToPostDetail(id: any, titre: any) {
    this.router.navigate(['/posts', id]);
    console.log('Navigating to post detail with ID:', id);
    console.log('Post title:', titre);
    this.cdr.detectChanges();
  }

  currentPage: number = 0;
  pageSize: number = 4; // Afficher 4 posts initialement
  initialLoad: boolean = true;

  loadMorePosts(): void {
    // Éviter les chargements multiples simultanés
    if (this.isLoading) return;

    this.isLoading = true;
    console.log(`Chargement des posts: page ${this.currentPage}, taille ${this.pageSize}`);

    this.postsService.getPosts(this.currentPage, this.pageSize).subscribe(
      (data: any) => {
        const newPosts = data.content || [];
        this.hasMorePosts = !data.last;
        console.log(`Reçu ${newPosts.length} nouveaux posts, plus de posts: ${this.hasMorePosts}`);

        // Ajouter les nouvelles publications
        if (this.initialLoad) {
          // Premier chargement, remplacer les posts
          this.posts = [...newPosts];
          this.initialLoad = false;
        } else {
          // Chargements suivants, ajouter aux posts existants
          this.posts = [...this.posts, ...newPosts];
        }

        this.filteredPosts = [...this.posts];

        // Récupérer les réactions pour les nouveaux posts
        newPosts.forEach((post: ExtendedPost) => {
          this.reactionService.getReactionCounts(post.id).subscribe(
            (counts) => {
              post.reactionCounts = counts;
              post.reactionCounts = counts;
              post.userReactionType = counts.me
            },
            (error) => {
              console.error(`Erreur reaction pour ${post.id}:`, error);
              post.reactionCounts = { like: 0, love: 0, haha: 0, total: 0 };
            }
          );
        });

        this.currentPage++;
        this.isLoading = false;
        this.cdr.detectChanges(); // Forcer la mise à jour de la vue
      },
      (error) => {
        console.error('Erreur chargement des posts:', error);
        this.isLoading = false;
        this.cdr.detectChanges();
      }
    );
  }

  toggleSavePost(post: Post & {
    reactionCounts?: { like?: number; love?: number; haha?: number; total?: number };
    userReactionType?: "like" | "love" | "haha";
    workspace?: Workspace
  }) {
    console.log('Toggling save post:', post);
    if (!post || !post.id) {
      console.error('Invalid post object:', post);
      return;
    }

    this.savedItemsService.toggleSavedItem(post).subscribe(
      (result) => {
        console.log('Toggle save post result:', result);
        // Force la mise à jour de la vue
        this.cdr.detectChanges();
      },
      (error) => {
        console.error('Error toggling save post:', error);
      }
    );
  }

  isPostSaved(id: string) {
    if (!id) {
      console.error('Invalid post ID:', id);
      return false;
    }
    return this.savedItemsService.isItemSaved(id);
  }

  searchPosts() {
    this.isSearching = true;
    this.filteredPosts = this.posts.filter(post => post.titre.toLowerCase().includes(this.searchTerm.toLowerCase()));
    this.isSearching = false;
    this.cdr.detectChanges();
    console.log('Recherche effectuée:', this.searchTerm, 'Résultats:', this.filteredPosts.length);
    this.cdr.detectChanges();

  }

  clearSearch() {
    this.searchTerm = '';
    this.filteredPosts = this.posts;
    this.cdr.detectChanges();
  }
}
