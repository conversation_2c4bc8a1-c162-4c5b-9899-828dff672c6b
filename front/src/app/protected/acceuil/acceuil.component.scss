/* ==========================================================================
   Styles globaux - Design Premium
   ========================================================================== */

/* ==========================================================================
   Styles pour afficher une seule barre de défilement verticale et supprimer l'horizontale
   ========================================================================== */
/* Style pour tous les éléments - Suppression complète des barres de défilement */
* {
  scrollbar-width: none !important; /* Supprime les barres de défilement pour Firefox */
  -ms-overflow-style: none !important; /* Supprime les barres de défilement pour IE et Edge */
  overflow-x: hidden !important; /* Supprime tous les défilements horizontaux */
  box-sizing: border-box; /* Assure que les dimensions incluent padding et bordure */
}

/* Suppression du défilement pour la colonne des publications */
mat-grid-tile[colspan="2"][rowspan="20"] {
  overflow: visible !important;
  height: auto !important;
}

mat-grid-tile[colspan="2"][rowspan="20"] .mat-grid-tile-content {
  overflow: visible !important;
  height: auto !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: auto !important;
  position: relative !important;
  display: block !important;
}



/* Suppression du défilement pour la colonne des publications */
.post-box {
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
  overflow: visible !important;
  height: auto !important;
}

/* Configuration du conteneur principal */
.app-protected-container {
  overflow-y: auto !important;
  overflow-x: hidden !important;
  height: 100vh;
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
}

/* Suppression complète des barres de défilement pour Chrome, Safari, Opera */
::-webkit-scrollbar {
  width: 0 !important;
  height: 0 !important;
  display: none !important;
}

/* Suppression de la barre de défilement dans la section des publications */
.post-box::-webkit-scrollbar,
.post-content::-webkit-scrollbar,
.post-card::-webkit-scrollbar,
.post-card-content::-webkit-scrollbar,
.description::-webkit-scrollbar {
  width: 0 !important;
  height: 0 !important;
  display: none !important;
}



body, html {
  overflow-x: hidden !important; /* Force la suppression du défilement horizontal */
  overflow-y: auto; /* Permet le défilement vertical */
  scroll-behavior: smooth;
  font-family: 'Inter', 'Segoe UI', Roboto, -apple-system, BlinkMacSystemFont, sans-serif;
  color: #333333;
  margin: 0;
  padding: 0;
  height: 100%;
  scrollbar-width: none; /* Supprime les barres de défilement pour Firefox */
  -ms-overflow-style: none; /* Supprime les barres de défilement pour IE et Edge */
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-feature-settings: 'liga', 'kern';
}

/* ==========================================================================
   Layout principal - Fond simple et élégant
   ========================================================================== */
mat-grid-list {
  flex: 1 1 auto;
  width: 100%;
  height: calc(100vh - 64px); /* Hauteur fixe en soustrayant la hauteur de l'en-tête */
  background: #f0f4f8; /* Fond bleu très clair */
  padding: 24px;
  overflow-y: auto; /* Permet le défilement vertical */
  overflow-x: hidden !important; /* Force la suppression du défilement horizontal */
  position: relative;
  max-width: 100vw; /* Empêche le débordement horizontal */
  font-family: 'Inter', 'Segoe UI', Roboto, -apple-system, BlinkMacSystemFont, sans-serif;
  letter-spacing: 0.01em;
}

/* ==========================================================================
   Conteneur de publications - Centrage et espacement optimisés
   ========================================================================== */
.post-box {
  max-width: 680px;
  width: 100%;
  padding: 20px;
  margin: 0 auto;
  background-color: transparent;
  position: relative;
  overflow-y: auto !important; /* Permet le défilement vertical */
  overflow-x: hidden !important; /* Force la suppression du défilement horizontal */
  height: 100% !important; /* Hauteur complète pour permettre le défilement */
  box-sizing: border-box; /* Inclut padding dans la largeur */
  scrollbar-width: none !important; /* Supprime la barre de défilement pour Firefox */
  -ms-overflow-style: none !important; /* Supprime la barre de défilement pour IE et Edge */
}

/* Séparateur entre la barre de recherche et les publications */
.search-posts-separator {
  height: 1px;
  background: #001660; /* Bleu marine */
  margin: 20px 0;
  width: 100%;
  opacity: 0.7;
}

/* Conteneur de la barre de recherche */
.search-container {
  width: 100%;
  margin-bottom: 10px;
}

/* ==========================================================================
   Carte de publication - Design épuré et professionnel
   ========================================================================== */
.post-card {
  width: 100%;
  border-radius: 8px;
  padding: 24px;
  background: #FFFFFF;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  transition: all 0.3s ease;
  border-left: 3px solid #F59E0B; /* Bordure gauche orange */
  border-top: 1px solid #E2E8F0;
  border-right: 1px solid #E2E8F0;
  border-bottom: 1px solid #E2E8F0;
  position: relative;
  overflow: visible !important; /* Permet au contenu de s'afficher sans défilement */
  height: auto !important; /* Hauteur automatique pour afficher tout le contenu */
}

.post-card:hover {
  box-shadow: 0 6px 12px rgba(245, 158, 11, 0.15);
  border-left: 3px solid #D97706; /* Bordure gauche orange foncé */
  border-top: 1px solid #F59E0B;
  border-right: 1px solid #F59E0B;
  border-bottom: 1px solid #F59E0B;
  transform: translateY(-2px);
}



.post-card.linkedin-style {
  width: 100%;
  border-radius: 8px;
  padding: 0;
  background: #FFFFFF;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  transition: all 0.3s ease;
  overflow: visible !important;
  border-top: 1px solid #E2E8F0;
  border-right: 1px solid #E2E8F0;
  border-bottom: 1px solid #E2E8F0;
  border-left: 3px solid #F59E0B; /* Bordure gauche orange */
}

.post-card.linkedin-style:hover {
  box-shadow: 0 4px 8px rgba(0, 22, 96, 0.2);
  border-color: #001660; /* Bleu marine */
}

/* ==========================================================================
   En-tête de la carte - Mise en page simple et élégante
   ========================================================================== */
.post-card-header {
  display: flex;
  align-items: center;
  padding-bottom: 16px;
  position: relative;
  border-bottom: 1px solid #F59E0B; /* Bordure orange */
  margin-bottom: 16px;
}

/* Ajouter un accent orange à l'en-tête */
.post-card-header::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 40px;
  height: 3px;
  background: #F59E0B; /* Accent orange */
  border-radius: 1.5px;
}

.post-author-info {
  display: flex;
  width: 100%;
  align-items: center;
}

.avatar-container {
  margin-right: 20px;
  position: relative;
}

.avatar-icon {
  font-size: 40px;
  width: 44px;
  height: 44px;
  color: #001660; /* Bleu marine */
  margin-right: 12px;
  border-radius: 50%;
  background: #e6ecf5; /* Fond bleu très clair */
  padding: 4px;
  border: 1px solid #001660; /* Bordure bleu marine */
  display: flex;
  align-items: center;
  justify-content: center;
}

.author-details {
  flex: 1;
}

.post-title {
  font-size: 16px;
  font-weight: 600;
  color: #001660; /* Bleu marine */
  margin-bottom: 4px;
  line-height: 1.4;
  position: relative;
  display: inline-block;
}

/* Effet de soulignement orange au survol */
.post-title::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background-color: #F59E0B; /* Orange */
  transition: width 0.3s ease;
}

.post-card:hover .post-title::after {
  width: 100%;
}

.post-meta {
  display: flex;
  flex-direction: column;
}

.workspace-name {
  font-weight: 600;
  margin-bottom: 3px;
  color: #001660;
  font-size: 14px;
}

.post-time, .post-author, .post-date {
  font-size: 12px;
  color: #748ffc; /* Bleu moyen */
  font-weight: 400;
  margin-bottom: 2px;
}

/* ==========================================================================
   Contenu de la publication - Typographie optimisée pour la lisibilité
   ========================================================================== */
.post-content {
  padding: 15px 0 24px; /* Augmente l'espace en haut */
  overflow: visible !important;
  height: auto !important;
}

.description {
  margin: 0;
  padding: 0 20px; /* Ajoute de l'espace à gauche et à droite */
  font-size: 14px;
  color: #343a40; /* Gris foncé */
  line-height: 1.6;
  word-break: break-word;
  text-align: justify; /* Justification du texte */
}

/* ==========================================================================
   Barre de recherche - Design élégant avec effets interactifs
   ========================================================================== */
.search-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  max-width: 900px;
  padding: 0 16px;
  position: relative;
  z-index: 10;
}

.search-bar-wrapper {
  display: flex;
  align-items: center;
  width: 100%;
  height: 46px;
  border-radius: 6px;
  background: #FFFFFF;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
  border: 1px solid #dbe4ff; /* Bordure bleu très clair */
}

.search-bar-wrapper:hover {
  border-color: #F59E0B; /* Orange */
  box-shadow: 0 2px 5px rgba(245, 158, 11, 0.2); /* Ombre orangée */
}

.search-bar-wrapper:focus-within {
  border-color: #4263eb; /* Bleu vif */
  box-shadow: 0 0 0 2px rgba(66, 99, 235, 0.2);
}

.search-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  position: relative;
  z-index: 2;
}

.search-icon {
  color: #001660; /* Bleu marine */
  opacity: 0.7;
  font-size: 20px;
}

.search-bar-wrapper:focus-within .search-icon {
  color: #001660; /* Bleu marine */
}

.search-input {
  flex: 1;
  height: 100%;
  border: none;
  outline: none;
  background: transparent;
  font-size: 14px;
  color: #343a40; /* Gris foncé */
  font-weight: 400;
  padding: 0;
  margin-right: 16px;
  caret-color: #001660; /* Curseur bleu marine */
}

.search-input::placeholder {
  color: #001660; /* Bleu marine */
  opacity: 0.5;
  font-weight: 400;
}

.search-input:focus::placeholder {
  opacity: 0.5;
}

.clear-search-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  border: none;
  background: transparent;
  cursor: pointer;
  border-radius: 50%;
  transition: background-color 0.3s ease;
  margin-right: 8px;
}

.clear-search-button:hover {
  background-color: #edf2ff; /* Fond bleu très clair */
}

.clear-search-button mat-icon {
  color: #001660; /* Bleu marine */
  opacity: 0.7;
  font-size: 18px;
}

.clear-search-button:hover mat-icon {
  color: #001660; /* Bleu marine */
}

/* ==========================================================================
   Actions de la publication - Boutons élégants avec effets interactifs
   ========================================================================== */
.post-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 18px;
  border-top: 1px solid rgba(226, 232, 240, 0.8);
  position: relative;
}

/* Effet de séparateur élégant */
.post-actions::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(to right, rgba(0, 22, 96, 0) 0%, rgba(0, 22, 96, 0.1) 50%, rgba(0, 22, 96, 0) 100%);
  pointer-events: none;
}

.reaction-bar {
  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: center;
}

/* Boutons d'action */
.action-btn, .btn-details {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  border-radius: 4px;
  font-weight: 500;
  font-size: 13px;
  color: #001660; /* Bleu marine */
  background: #e6ecf5; /* Fond bleu très clair */
  transition: all 0.3s ease;
  border: 1px solid #001660; /* Bordure bleu marine */
}

.action-btn:hover, .btn-details:hover {
  background: #001660; /* Bleu marine */
  color: #FFFFFF;
  border-color: #001660; /* Bleu marine */
}

/* Effet de vague au clic */
.action-btn::after, .btn-details::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  background: rgba(255, 255, 255, 0.3);
  opacity: 0;
  border-radius: 100%;
  transform: scale(1) translate(-50%, -50%);
  transform-origin: 50% 50%;
}

.action-btn:active::after, .btn-details:active::after {
  animation: ripple 0.6s ease-out;
}

@keyframes ripple {
  0% {
    transform: scale(0) translate(-50%, -50%);
    opacity: 0.5;
  }
  100% {
    transform: scale(20) translate(-50%, -50%);
    opacity: 0;
  }
}

.btn-details {
  background: linear-gradient(135deg, #EDF2F7 0%, #E2E8F0 100%);
  color: #001660;
  box-shadow: 0 4px 12px rgba(0, 22, 96, 0.08), 0 1px 3px rgba(0, 22, 96, 0.03);
}

.apply-btn {
  background: #001660; /* Bleu marine */
  color: #FFFFFF;
  border-color: #001660; /* Bleu marine */
  font-weight: 500;
  box-shadow: 0 2px 6px rgba(0, 22, 96, 0.2);
}

.apply-btn:hover {
  background: #00124d; /* Bleu marine plus foncé */
  border-color: #00124d; /* Bleu marine plus foncé */
  box-shadow: 0 4px 10px rgba(0, 22, 96, 0.3);
  transform: translateY(-2px);
}

/* Bouton Enregistrer */
.save-btn {
  color: #748ffc; /* Bleu moyen */
  background: #edf2ff; /* Fond bleu très clair */
  transition: all 0.3s ease;
  border: 1px solid #dbe4ff; /* Bordure bleu clair */
}

.save-btn:hover {
  color: #FFFFFF;
  background: #4263eb; /* Bleu vif */
  border-color: #4263eb; /* Bleu vif */
}

.save-btn.saved {
  color: #001660; /* Bleu marine */
  background: #e6ecf5; /* Fond bleu très clair */
  font-weight: 500;
  border-color: #001660; /* Bleu marine */
}

.save-btn.saved:hover {
  color: #FFFFFF;
  background: #001660; /* Bleu marine */
  border-color: #001660; /* Bleu marine */
}

.save-btn.saved .action-icon {
  color: #001660; /* Bleu marine */
}

.save-btn.saved:hover .action-icon {
  color: #FFFFFF;
}

/* Icônes dans les boutons */
.action-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
  margin-right: 8px;
  transition: all 0.3s ease;
  color: #001660; /* Bleu marine */
}

/* Icône blanche pour le bouton Postuler */
.apply-btn .action-icon {
  color: #FFFFFF; /* Blanc */
}

/* Maintenir l'icône blanche au survol */
.apply-btn:hover .action-icon {
  color: #FFFFFF; /* Blanc */
}

.action-btn:hover .action-icon, .btn-details:hover .action-icon {
  color: #FFFFFF;
}

/* ==========================================================================
   Menu de réactions - Design élégant avec animations fluides
   ========================================================================== */
.reaction-menu {
  padding: 0;
  background: transparent;
  position: relative;
  z-index: 10;
}

.reaction-container {
  display: flex;
  align-items: center;
  background: #FFFFFF;
  border-radius: 25px;
  padding: 8px 10px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12), 0 2px 8px rgba(0, 0, 0, 0.08);
  border: none;
  animation: popIn 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.selected-reaction {
  background-color: rgba(0, 22, 96, 0.08);
  border-radius: 50%;
  padding: 4px;
  transform: scale(1.1);
  transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.reaction-option {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  margin: 0 4px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
  position: relative;
}

.reaction-option:hover {
  transform: scale(1.3) translateY(-4px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1;
}

.reaction-icon {
  font-size: 24px;
  width: 24px;
  height: 24px;
  transition: transform 0.3s ease;
}

.reaction-count {
  font-size: 13px;
  color: #64748B;
  font-weight: 500;
  margin-left: 4px;
}

/* Animation d'entrée */
@keyframes popIn {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* ==========================================================================
   Indicateurs de chargement - Design élégant et informatif
   ========================================================================== */
.loading-initial {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 0;
  width: 100%;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 22, 96, 0.06), 0 1px 3px rgba(0, 22, 96, 0.04);
  margin: 20px 0;
  border: 1px solid rgba(226, 232, 240, 0.8);
}

.loading-initial span {
  margin-top: 16px;
  font-size: 16px;
  color: #4B5563;
  font-weight: 500;
  letter-spacing: 0.01em;
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24px 0;
  width: 100%;
  gap: 12px;
  background-color: transparent;
  margin: 10px 0;
}

.loading-more span {
  font-size: 14px;
  color: #64748B;
  font-weight: 500;
}

.no-more-posts {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24px 0;
  width: 100%;
  gap: 10px;
  color: #64748B;
  font-size: 14px;
  font-weight: 500;
  border-top: 1px solid rgba(226, 232, 240, 0.8);
  margin: 20px 0;
  letter-spacing: 0.01em;
}

.no-more-posts mat-icon {
  color: #001660;
  font-size: 20px;
}

.see-more {
  display: flex;
  justify-content: center;
  padding: 16px 0 32px;
  width: 100%;
}

.see-more button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 4px;
  background: #001660; /* Bleu marine */
  color: white;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
}

.see-more button:hover {
  background: #001660; /* Bleu marine */
  opacity: 0.9;
}

.see-more button mat-icon {
  font-size: 20px;
  transition: transform 0.3s ease;
}

.see-more button:hover mat-icon {
  transform: translateY(2px);
}

/* ==========================================================================
   Carte de profil - Design professionnel et épuré
   ========================================================================== */
.profile-card {
  width: calc(25% - 48px); /* Largeur fixe basée sur la grille avec marge */
  max-width: 300px;
  height: auto;
  border-radius: 4px;
  overflow: visible; /* Permet au contenu de déborder si nécessaire */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  background: #FFFFFF;
  position: fixed; /* Fixe la carte pendant le défilement */
  top: 88px; /* Position depuis le haut (ajustez selon votre en-tête) */
  left: 24px; /* Position depuis la gauche */
  transition: box-shadow 0.3s ease;
  border: 1px solid #001660; /* Bordure bleu marine */
  display: flex;
  flex-direction: column;
  z-index: 100; /* S'assure que la carte reste au-dessus des autres éléments */
}

.profile-card:hover {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

/* En-tête de profil simple et élégant */
.profile-header {
  position: relative;
  padding: 30px 20px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow: hidden;
}

/* Fond de couverture avec dégradé bleu-orange */
.profile-cover {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #001660 0%, #F59E0B 100%); /* Dégradé bleu-orange */
  z-index: 1;
}

/* Conteneur d'avatar avec positionnement du bouton d'édition */
.profile-avatar-container {
  position: relative;
  margin-bottom: 20px;
  z-index: 3;
  display: flex;
  justify-content: center; /* Centre horizontalement */
  align-items: center; /* Centre verticalement */
  width: 100%;
}

/* Avatar professionnel et élégant */
.profile-avatar {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  color: #001660;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 3;
  overflow: hidden; /* Assure que tout reste dans le cercle */
}

/* Bordure orange autour de l'avatar */
.profile-avatar::after {
  content: '';
  position: absolute;
  top: -6px;
  left: -6px;
  right: -6px;
  bottom: -6px;
  border-radius: 50%;
  border: 2px solid #F59E0B; /* Bordure orange */
  opacity: 0.7;
  z-index: -1;
}

/* Icône d'avatar améliorée */
.avatar-icon {
  font-size: 46px; /* Taille légèrement réduite pour un meilleur centrage */
  width: 70px;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #001660; /* Bleu marine */
  background: rgb(255, 255, 255); /* Fond blanc semi-transparent */
  border-radius: 50%;
  box-shadow: 0 2px 6px rgba(0, 22, 96, 0.1); /* Ombre légère */
  transition: all 0.3s ease;
}

/* Effet de survol subtil pour l'avatar */
.profile-avatar:hover .avatar-icon {
  color: #001660; /* Bleu marine plus intense */
  background: rgba(255, 255, 255, 0.9); /* Fond blanc plus opaque */
  box-shadow: 0 4px 8px rgba(0, 22, 96, 0.2); /* Ombre plus prononcée */
  transform: scale(1.05); /* Légère augmentation de taille */
}

/* Bouton d'édition discret */
.edit-avatar-btn {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 32px;
  height: 32px;
  background: #FFFFFF;
  color: #001660; /* Bleu marine */
  box-shadow: 0 2px 6px rgba(0, 22, 96, 0.2); /* Ombre bleu marine */
  border: 1px solid #001660; /* Bordure bleu marine */
  transition: background-color 0.3s ease, color 0.3s ease;
  z-index: 4;
}

.edit-avatar-btn:hover {
  background: #F59E0B; /* Fond orange */
  color: #FFFFFF;
}

.edit-avatar-btn mat-icon {
  font-size: 16px;
}

/* Conteneur d'informations principales */
.profile-main-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 15px;
  position: relative;
  z-index: 3;
  width: 100%;
}

/* Nom et titre avec typographie claire */
.profile-name {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 6px 0;
  text-align: center;
  color: #FFFFFF;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  position: relative;
  display: inline-block;
}

/* Accent orange sous le nom */
.profile-name::after {
  content: '';
  position: absolute;
  bottom: -3px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 2px;
  background-color: #F59E0B; /* Ligne orange */
  border-radius: 1px;
}

.profile-title {
  font-size: 14px;
  font-weight: 500;
  margin: 0 0 8px 0;
  text-align: center;
  color: #F59E0B; /* Couleur orange */
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
}

/* Localisation avec icône */
.profile-location {
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.8);
  font-size: 13px;
}

.profile-location mat-icon {
  font-size: 14px;
  margin-right: 4px;
  opacity: 0.9;
}

/* Statistiques avec mise en page moderne et icônes */
.profile-stats {
  display: flex;
  justify-content: space-around;
  width: 100%;
  margin-top: 20px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  border-radius: 15px;
  padding: 15px 10px;
  position: relative;
  z-index: 3;
  border: 1px solid rgba(255, 255, 255, 0.18);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 12px;
  position: relative;
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-5px);
}

.stat-icon-container {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: rgba(0, 22, 96, 0.7); /* Fond bleu marine semi-transparent */
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 22, 96, 0.8); /* Bordure bleu marine */
  transition: all 0.3s ease;
}

.stat-item:hover .stat-icon-container {
  background: rgba(0, 22, 96, 0.9); /* Fond bleu marine plus intense au survol */
  transform: scale(1.1);
  box-shadow: 0 6px 12px rgba(0, 22, 96, 0.3); /* Ombre bleu marine */
}

.stat-icon {
  font-size: 18px;
  color: #FFFFFF;
  opacity: 0.9;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
}

.stat-value {
  font-size: 22px;
  font-weight: 700;
  margin: 4px 0;
  color: #FFFFFF;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.stat-item:hover .stat-value {
  transform: scale(1.1);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.stat-label {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.8);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-weight: 500;
  transition: all 0.3s ease;
}

/* Corps de la carte avec informations de contact */
.profile-body {
  padding: 20px;
  background: #FFFFFF;
  position: relative;
  z-index: 3;
}

/* Liste d'informations avec séparateurs subtils */
.info-list {
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
}

.info-item {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #4B5563;
  padding: 12px 8px;
  border-bottom: 1px solid rgba(245, 158, 11, 0.2); /* Bordure orange très légère */
  transition: color 0.3s ease, background-color 0.3s ease;
  cursor: default;
  border-radius: 4px;
}

.info-item:last-child {
  border-bottom: none;
}

.info-item:hover {
  color: #001660;
  background-color: rgba(245, 158, 11, 0.05); /* Fond orange très léger au survol */
}

.info-item mat-icon {
  color: #001660; /* Bleu marine */
  margin-right: 12px;
  font-size: 18px;
  transition: transform 0.3s ease, color 0.3s ease;
}

.info-item:hover mat-icon {
  transform: scale(1.1);
}

.info-text {
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 180px;
}

/* Actions avec style professionnel */
.actions-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: 20px;
}

.action-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-radius: 6px;
  background: #F9FAFB;
  color: #4B5563;
  font-weight: 500;
  font-size: 14px;
  transition: all 0.3s ease;
  border: 1px solid #E5E7EB;
  border-left: 3px solid #F59E0B; /* Bordure gauche orange */
  box-shadow: 0 1px 3px rgba(245, 158, 11, 0.1); /* Ombre légèrement orangée */
  cursor: pointer;
}

.action-item:hover {
  background: #001660;
  color: #FFFFFF;
  border-color: #001660;
  border-left: 3px solid #D97706; /* Bordure gauche orange foncé */
  box-shadow: 0 3px 6px rgba(245, 158, 11, 0.2); /* Ombre orangée plus visible */
  transform: translateY(-2px);
}

.action-item mat-icon {
  margin-right: 12px;
  font-size: 18px;
  transition: transform 0.3s ease, color 0.3s ease;
  color: #001660; /* Bleu marine */
}

.action-item:hover mat-icon {
  transform: scale(1.1);
  color: #FFFFFF; /* Blanc au survol */
}

/* ==========================================================================
   Colonne des offres d'emploi - Design élégant et professionnel
   ========================================================================== */
.posts-footer-wrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  padding: 20px;
  gap: 24px;
  position: relative;
}

/* Style pour la colonne fixe des offres d'emploi et du footer */
.fixed-column {
  position: fixed;
  top: 88px; /* Position depuis le haut (ajustez selon votre en-tête) */
  right: 24px; /* Position depuis la droite */
  width: calc(25% - 48px); /* Largeur fixe basée sur la grille avec marge */
  max-width: 300px;
  height: auto;
  z-index: 100; /* S'assure que la colonne reste au-dessus des autres éléments */
}

/* Container des entreprises partenaires */
.companies-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow: visible;
  padding-right: 0;
  position: relative;
}

/* Titre de la section des entreprises */
.companies-title {
  font-size: 18px;
  font-weight: 600;
  color: #001660;
  margin-bottom: 16px;
  padding-bottom: 10px;
  border-bottom: 2px solid rgba(0, 22, 96, 0.1);
  position: relative;
  letter-spacing: 0.02em;
  text-shadow: 0 1px 1px rgba(255, 255, 255, 0.8);
  display: inline-block;
}

/* Accent orange sous le titre */
.companies-title::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%; /* Ligne orange sur toute la largeur */
  height: 2px;
  background: linear-gradient(90deg, #F59E0B 0%, rgba(245, 158, 11, 0.4) 100%);
  border-radius: 1px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* Accent orange à côté du titre */
.companies-title::before {
  content: '';
  position: absolute;
  left: -12px;
  top: 0;
  bottom: 0;
  width: 4px;
  background: #F59E0B;
  border-radius: 2px;
  box-shadow: 0 1px 3px rgba(245, 158, 11, 0.3);
}

/* Grille des entreprises */
.companies-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  margin-bottom: 16px;
  position: relative;
  min-height: 100px; /* Hauteur minimale pour afficher correctement le chargement */
}

/* Indicateur de chargement des workspaces */
.loading-workspaces {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 10;
  border-radius: 8px;
  padding: 20px;
}

.loading-workspaces span {
  margin-top: 10px;
  font-size: 14px;
  color: #001660; /* Bleu marine */
  font-weight: 500;
}

/* Message quand aucun workspace n'est disponible */
.no-workspaces {
  grid-column: 1 / -1; /* Prend toute la largeur de la grille */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px dashed #e9ecef;
  color: #6c757d;
  text-align: center;
}

.no-workspaces mat-icon {
  font-size: 24px;
  color: #001660; /* Bleu marine */
  margin-bottom: 8px;
}



/* Carte d'entreprise */
.company-card {
  background: #FFFFFF;
  border-radius: 10px;
  padding: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 22, 96, 0.06);
  border: 1px solid #e9ecef;
  border-bottom: 2px solid #F59E0B; /* Bordure orange en bas */
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;

  /* Effet de brillance */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.8) 50%, rgba(255, 255, 255, 0) 100%);
    z-index: 1;
  }

  /* Indicateur orange dans le coin supérieur droit */
  &::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 8px;
    height: 8px;
    background: #F59E0B;
    border-radius: 0 0 0 8px;
    z-index: 2;
  }
}

/* Effet de survol de la carte d'entreprise */
.company-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 16px rgba(245, 158, 11, 0.15), 0 4px 8px rgba(245, 158, 11, 0.08); /* Ombre orange */
  border-color: rgba(245, 158, 11, 0.3);
  border-bottom: 2px solid #F59E0B; /* Bordure orange en bas */
  background: linear-gradient(to bottom, #FFFFFF, #FEF9EE); /* Léger dégradé vers orange très clair */

  /* Animation de l'effet de brillance au survol */
  &::before {
    animation: shine 1.5s ease-in-out;
  }

  /* Effet sur le logo au survol */
  .company-logo {
    transform: scale(1.05);
  }

  /* Effet sur le conteneur de logo au survol */
  .company-logo-container::after {
    width: 40px;
    opacity: 1;
    background: linear-gradient(90deg, rgba(245, 158, 11, 0.3) 0%, rgba(245, 158, 11, 1) 50%, rgba(245, 158, 11, 0.3) 100%);
  }

  /* Effet sur le nom de l'entreprise au survol */
  .company-name {
    color: #F59E0B; /* Texte orange au survol */
    font-weight: 600;
  }

  /* Agrandissement de l'indicateur orange */
  &::after {
    width: 12px;
    height: 12px;
    transition: all 0.3s ease;
  }
}

/* Conteneur du logo de l'entreprise */
.company-logo-container {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  border-radius: 8px;
  background: #f8f9fa;
  padding: 8px;
  border: 1px solid #e9ecef;
  overflow: hidden;
  position: relative;

  /* Bordure orange subtile */
  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 20px;
    height: 2px;
    background: linear-gradient(90deg, rgba(245, 158, 11, 0.2) 0%, rgba(245, 158, 11, 0.8) 50%, rgba(245, 158, 11, 0.2) 100%);
    border-radius: 1px;
    opacity: 0.7;
    transition: all 0.3s ease;
  }
}

/* Logo de l'entreprise */
.company-logo {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  transition: transform 0.3s ease;
}

/* Icône de l'entreprise (fallback quand pas de logo) */
.company-icon {
  font-size: 30px;
  width: 30px;
  height: 30px;
  color: #001660; /* Bleu marine */
  background: #e6ecf5; /* Fond bleu très clair */
  padding: 10px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.company-card:hover .company-icon {
  color: #FFFFFF;
  background: #001660; /* Bleu marine */
  transform: scale(1.05);
}

/* Nom de l'entreprise */
.company-name {
  font-size: 14px;
  font-weight: 500;
  color: #495057;
  text-align: center;
  margin-top: 4px;
  transition: color 0.3s ease;
}

/* Bouton pour voir plus d'entreprises */
.view-more-companies {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-top: 12px;
  padding: 8px 16px;
  background: linear-gradient(135deg, #001660 0%, #0F2B76 100%); /* Dégradé bleu marine */
  color: #FFFFFF;
  border-radius: 4px;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(0, 22, 96, 0.15);
  border: none;
  border-bottom: 2px solid #F59E0B; /* Bordure orange en bas */
  width: 100%;
  position: relative;
  overflow: hidden;

  /* Effet de brillance */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent 0%, rgba(245, 158, 11, 0.2) 50%, transparent 100%);
    transition: all 0.5s ease;
  }

  /* Icône orange */
  mat-icon {
    color: #F59E0B;
    transition: all 0.3s ease;
  }
}

/* Effet de survol du bouton */
.view-more-companies:hover {
  background: linear-gradient(135deg, #0F2B76 0%, #1A3A8A 100%); /* Dégradé bleu marine plus foncé */
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 22, 96, 0.2), 0 2px 4px rgba(245, 158, 11, 0.1); /* Ombre avec touche orange */
  border-bottom: 2px solid #D97706; /* Bordure orange foncé en bas */

  /* Animation de l'effet de brillance */
  &::before {
    left: 100%;
  }

  /* Animation de l'icône */
  mat-icon {
    transform: translateX(3px);
    color: #FFFFFF;
  }
}

/* Animation de brillance */
@keyframes shine {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  20% {
    opacity: 0.5;
  }
  80% {
    opacity: 0.5;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}











.footer-section {
  margin-top: auto;
  padding-top: 20px;
  position: relative;
}

/* Titre du footer - supprimé */
.footer-section::before {
  content: none;
}

/* Accent orange supprimé */
.footer-section::after {
  content: none;
}

.footer-links {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  justify-content: center; /* Centre les liens horizontalement */
  margin-bottom: 16px;
  text-align: center; /* Centre le texte dans les liens */
}

.footer-link {
  color: #495057;
  font-size: 13px;
  text-decoration: none;
  transition: all 0.3s ease;
  padding: 8px 14px;
  border-radius: 20px; /* Coins plus arrondis pour un look plus moderne */
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  display: inline-block; /* Permet au texte d'être centré */
  text-align: center; /* Centre le texte */
  min-width: 80px; /* Largeur minimale pour uniformité */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05); /* Légère ombre */
}

.footer-link:hover {
  color: #FFFFFF; /* Texte blanc au survol */
  text-decoration: none;
  background-color: #001660; /* Fond bleu marine au survol */
  border-color: #001660; /* Bordure bleu marine */
  transform: translateY(-2px) scale(1.05); /* Légère élévation et agrandissement */
  box-shadow: 0 4px 8px rgba(0, 22, 96, 0.15); /* Ombre plus prononcée */
}

/* Style moderne pour le footer branding */
.footer-branding {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 24px;
  position: relative;
  padding-top: 20px;

  /* Effet de séparation subtil */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80%;
    height: 1px;
    background: linear-gradient(90deg, rgba(233, 236, 239, 0) 0%, rgba(233, 236, 239, 1) 50%, rgba(233, 236, 239, 0) 100%);
  }
}

/* Logo de l'application */
.footer-logo {
  font-size: 18px;
  font-weight: 700;
  color: #001660; /* Bleu marine */
  letter-spacing: 0.05em;
  margin-bottom: 6px;
  background: linear-gradient(135deg, #001660 0%, #0F2B76 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
  text-shadow: 0 1px 1px rgba(255, 255, 255, 0.1);
  position: relative;

  /* Effet de brillance */
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 50%);
    mix-blend-mode: overlay;
  }
}

/* Copyright moderne */
.footer-copyright {
  text-align: center;
  font-size: 11px;
  font-weight: 500;
  color: #748ffc; /* Bleu moyen */
  letter-spacing: 0.05em; /* Espacement des lettres plus prononcé */
  position: relative;
  padding: 4px 12px;
  border-radius: 12px;
  background: rgba(237, 242, 255, 0.5); /* Fond bleu très léger */
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  box-shadow:
    0 1px 2px rgba(0, 22, 96, 0.05),
    0 0 0 1px rgba(255, 255, 255, 0.7) inset;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 4px;

  /* Style du symbole copyright */
  .copyright-symbol {
    font-size: 14px;
    color: #001660; /* Bleu marine */
    margin-right: 2px;
    font-weight: 700;
  }

  /* Effet de brillance */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.8) 50%, rgba(255, 255, 255, 0) 100%);
  }

  /* Effet au survol */
  &:hover {
    background: rgba(219, 228, 255, 0.7); /* Fond bleu léger au survol */
    color: #001660; /* Bleu marine au survol */
    transform: translateY(-1px);
    box-shadow:
      0 2px 4px rgba(0, 22, 96, 0.08),
      0 0 0 1px rgba(255, 255, 255, 0.8) inset;
  }
}

/* ==========================================================================
   Responsive - Optimisations pour différentes tailles d'écran
   ========================================================================== */
@media (max-width: 768px) {
  mat-grid-list {
    padding: 16px;
  }

  .post-box {
    padding: 16px;
  }

  .post-card {
    padding: 24px;
  }

  .avatar-icon {
    font-size: 40px;
    width: 44px;
    height: 44px;
    margin-right: 12px;
  }

  .post-title {
    font-size: 16px;
  }

  .description {
    font-size: 14px;
  }

  .action-btn, .btn-details {
    padding: 8px 16px;
    font-size: 14px;
  }

  .action-icon {
    font-size: 18px;
    width: 18px;
    height: 18px;
    margin-right: 6px;
  }
}

@media (max-width: 480px) {
  .search-bar-wrapper {
    height: 48px;
  }

  .search-icon {
    font-size: 20px;
  }

  .search-input {
    font-size: 14px;
  }

  .post-actions {
    flex-wrap: wrap;
  }

  .reaction-bar {
    flex-wrap: wrap;
    justify-content: space-around;
    gap: 8px;
  }

  .action-btn, .btn-details {
    margin-bottom: 8px;
    width: 100%;
    justify-content: center;
  }
}
.see-more {
  text-align: center;
  margin: 20px 0;
}

button[mat-raised-button] {
  background-color: #1976d2; /* Primary color */
  color: white;
  padding: 10px 20px;
  font-size: 16px;
  border-radius: 5px;
}

button[mat-raised-button]:hover {
  background-color: #1565c0; /* Slightly darker on hover */
}
.workspace-name {
  font-size: 14px;
  color: #666;
  display: block;
  margin-top: 4px;
}
