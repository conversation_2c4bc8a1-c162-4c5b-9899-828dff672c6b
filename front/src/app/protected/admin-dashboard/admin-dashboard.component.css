/* Dashboard Container */
.dashboard {
  display: flex;
  min-height: 100vh;
  background-color: #f5f5f5;
  font-family: 'Roboto', sans-serif;
}

.dashboard-container {
  flex: 1;
  padding: 30px;
  margin-left: 250px; /* Adjust based on sidebar width */
}

/* Header */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  background-color: #ffffff; /* Dark blue for admin theme */
  color: #021c6c;
  padding: 20px 30px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dashboard-header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 500;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 15px;
}

.search-container {
  position: relative;
  display: flex;
  align-items: center;
}

.search-bar {
  width: 250px;
}

.search-bar mat-form-field {
  width: 100%;
}

.search-bar .mat-form-field-wrapper {
  padding-bottom: 0;
}

.search-bar .mat-form-field-outline {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 20px;
}

.search-bar .mat-form-field-outline-thick {
  background-color: #fff;
}

.search-icon {
  color: #666;
}

.notification-button {
  position: relative;
  color: #90d9f4;
}

.notification-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background-color: #ff4081;
  color: #90d9f4;
  border-radius: 50%;
  padding: 2px 6px;
  font-size: 12px;
}

.profile-button {
  background-color: #fff;
  color: #1e3a8a;
  font-weight: 500;
  border-radius: 20px;
  padding: 5px 15px;
  transition: background-color 0.3s ease;
}

.profile-button mat-icon {
  margin-right: 5px;
}

.profile-button:hover {
  background-color: #f0f0f0;
}

/* Cards Section */
.card-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.dashboard-card {
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  cursor: pointer;
}

.dashboard-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

mat-card-header {
  padding: 20px;
  border-bottom: 1px solid #e0e0e0;
}

mat-card-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

mat-card-title mat-icon {
  margin-right: 10px;
  font-size: 24px;
}

mat-card-content {
  padding: 20px;
}

mat-card-content p {
  margin: 0 0 15px;
  font-size: 14px;
  color: #666;
}

mat-card-content button {
  border-radius: 20px;
  font-weight: 500;
}

mat-table {
  width: 100%;
}

mat-header-cell,
mat-cell {
  padding: 12px;
  font-size: 14px;
  color: #333;
}

mat-header-cell {
  font-weight: 600;
  background-color: #f5f5f5;
}

mat-row:hover {
  background-color: #f9f9f9;
}

mat-paginator {
  border-top: 1px solid #e0e0e0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-container {
    margin-left: 0;
    padding: 15px;
  }

  .dashboard-header {
    flex-direction: column;
    gap: 15px;
  }

  .header-actions {
    width: 100%;
    justify-content: space-between;
  }

  .search-bar {
    width: 100%;
  }
}
