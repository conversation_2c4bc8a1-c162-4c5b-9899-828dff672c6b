import { Component } from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { SidebarComponent } from '../sidebar/sidebar.component';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatTooltipModule } from '@angular/material/tooltip';
import {Router} from '@angular/router';

@Component({
  selector: 'app-admin-dashboard',
  templateUrl: './admin-dashboard.component.html',
  styleUrls: ['./admin-dashboard.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    SidebarComponent,
    MatCardModule,
    MatIconModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatTableModule,
    MatPaginatorModule,
    MatTooltipModule
  ]
})
export class AdminDashboardComponent {
  displayedColumns: string[] = ['id', 'user', 'action', 'timestamp'];
  dataSource = new MatTableDataSource<any>([
    { id: 1, user: 'Admin John', action: 'Updated user role', timestamp: '2025-04-10 10:30' },
    { id: 2, user: 'Admin Sarah', action: 'Deleted post', timestamp: '2025-04-09 15:45' },
    { id: 3, user: 'System', action: 'Scheduled maintenance', timestamp: '2025-04-08 09:00' },
    { id: 4, user: 'Admin Mike', action: 'Added new user', timestamp: '2025-04-07 14:20' }
  ]);
  constructor(private router: Router) {}

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();
  }

  goToManageUsers() {
    this.router.navigate(['/manage-users']);
  }
  goToManagePayments(): void {
    this.router.navigate(['/manage-payments']);
  }

  goToManageOffers(): void {
    this.router.navigate(['/manage-offers']);
  }

  goToManageInvitations(): void {
    this.router.navigate(['/manage-invitations']); // New method for navigating to Manage Invitations
  }
}
