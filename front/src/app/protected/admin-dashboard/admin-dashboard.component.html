<div class="dashboard">
  <app-sidebar></app-sidebar>
  <!-- Main Content -->
  <div class="dashboard-container">
    <!-- Header -->
    <header class="dashboard-header">
      <h1>Tableau de Bord Admin</h1>
      <div class="header-actions">
        <div class="search-container">
          <mat-form-field appearance="outline" class="search-bar">
            <mat-label>Rechercher...</mat-label>
            <input matInput placeholder="Rechercher...">
            <mat-icon matSuffix class="search-icon">search</mat-icon>
          </mat-form-field>
        </div>
        <button mat-icon-button matTooltip="Notifications" class="notification-button">
          <mat-icon>notifications</mat-icon>
          <span class="notification-badge">3</span>
        </button>
        <button mat-button color="accent" class="profile-button">
          <mat-icon>account_circle</mat-icon> Admin Profil
        </button>
      </div>
    </header>

    <!-- Cards Section -->
    <div class="card-container">
      <mat-card class="dashboard-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon style="color: #3f51b5;">people</mat-icon>
            Gérer les utilisateurs
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <p>Ajoutez, modifiez ou supprimez des utilisateurs.</p>
          <button mat-button color="primary" (click)="goToManageUsers()">Accéder</button>
        </mat-card-content>
      </mat-card>

      <mat-card class="dashboard-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon style="color: #f44336;">security</mat-icon>
            Gérer les rôles
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <p>Configurez les permissions et rôles des utilisateurs.</p>
          <button mat-button color="primary">Accéder</button>
        </mat-card-content>
      </mat-card>

      <mat-card class="dashboard-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon style="color: #ff9800;">work</mat-icon>
            Gérer les offres
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <p>Supervisez toutes les offres d'emploi publiées.</p>
          <button mat-button color="primary" (click)="goToManageOffers()">Accéder</button>
        </mat-card-content>
      </mat-card>

      <mat-card class="dashboard-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon style="color: #2196f3;">payment</mat-icon>
            Gérer les paiements
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <p>Suivez les paiements et gérez les factures.</p>
          <button mat-button color="primary" (click)="goToManagePayments()">Accéder</button>
        </mat-card-content>
      </mat-card>

      <mat-card class="dashboard-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon style="color: #ff5722;">notifications</mat-icon>
            Gérer les invitations
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <p>Configurez et gérez les invitations système.</p>
          <button mat-button color="primary" (click)="goToManageInvitations()">Accéder</button>
        </mat-card-content>
      </mat-card>
    </div>
  </div>
</div>
