<form [formGroup]="workspaceForm" class="workspace-form" (ngSubmit)="addWorkspace()" novalidate>
  <mat-card class="workspace-card animated-card" role="region" aria-labelledby="workspace-title">
    <mat-card-header class="header-center">
      <mat-card-title id="workspace-title">
        <mat-icon class="workspace-icon" aria-hidden="true">apartment</mat-icon>
        Create Workspace
      </mat-card-title>
    </mat-card-header>

    <mat-card-content class="card-content">
      <div class="field-group">
        <!-- Workspace Name -->
        <mat-form-field appearance="outline" class="form-field animate-field">
          <mat-label>Workspace Name</mat-label>
          <input
            matInput
            formControlName="name"
            placeholder="e.g., TechHub"
            required
            aria-required="true"
            id="workspace-name"
          >
          <mat-icon matSuffix aria-hidden="true">business</mat-icon>
          <mat-error *ngIf="workspaceForm.get('name')?.hasError('required')">
            Workspace name is required
          </mat-error>
        </mat-form-field>

        <!-- Description -->
        <mat-form-field appearance="outline" class="form-field animate-field">
          <mat-label>Workspace Description</mat-label>
          <textarea
            matInput
            rows="3"
            formControlName="description"
            placeholder="Describe your workspace"
            id="workspace-description"
          ></textarea>
          <mat-hint>Optional - Highlight your mission</mat-hint>
        </mat-form-field>
      </div>

      <div class="field-group">
        <!-- Location -->
        <mat-form-field appearance="outline" class="form-field animate-field">
          <mat-label>Location</mat-label>
          <input
            matInput
            formControlName="location"
            placeholder="e.g., New York, USA"
            id="workspace-location"
          >
          <mat-icon matSuffix aria-hidden="true">location_on</mat-icon>
        </mat-form-field>

        <!-- Phone -->
        <mat-form-field appearance="outline" class="form-field animate-field">
          <mat-label>Contact Number</mat-label>
          <input
            matInput
            formControlName="phoneNumber"
            placeholder="+216 12 345 678"
            type="tel"
            id="workspace-phone"
          >
          <mat-icon matSuffix aria-hidden="true">call</mat-icon>
          <mat-error *ngIf="workspaceForm.get('phoneNumber')?.hasError('pattern')">
            Please enter a valid Tunisian phone number (e.g., +216 12 345 678)
          </mat-error>
          <mat-error *ngIf="workspaceForm.get('phoneNumber')?.hasError('required')">
            Contact number is required
          </mat-error>
        </mat-form-field>
      </div>

      <div class="field-group">
        <!-- Email -->
        <mat-form-field appearance="outline" class="form-field animate-field">
          <mat-label>Workspace Email</mat-label>
          <input
            matInput
            formControlName="email"
            placeholder="<EMAIL>"
            type="email"
            id="workspace-email"
          >
          <mat-icon matSuffix aria-hidden="true">mail</mat-icon>
          <mat-error *ngIf="workspaceForm.get('email')?.hasError('email')">
            Please enter a valid email address
          </mat-error>
        </mat-form-field>

        <!-- Logo Upload -->
        <div class="logo-upload-field animate-field">
          <label class="upload-label" for="logo-upload">Upload Workspace Logo</label>
          <input
            type="file"
            id="logo-upload"
            (change)="onLogoSelected($event)"
            accept="image/*"
            aria-describedby="logo-hint"
          >
          <mat-hint id="logo-hint" *ngIf="selectedLogoName">
            Selected: {{ selectedLogoName }}
          </mat-hint>
          <!-- Logo Preview -->
          <div class="logo-preview" *ngIf="logoPreviewUrl">
            <img [src]="logoPreviewUrl" alt="Workspace Logo Preview" class="preview-image">
            <button
              mat-icon-button
              color="warn"
              (click)="removeLogo()"
              class="remove-logo-btn"
              aria-label="Remove uploaded logo"
            >
              <mat-icon>delete</mat-icon>
            </button>
          </div>
        </div>
      </div>
    </mat-card-content>

    <mat-card-actions class="center-actions">
      <button
        mat-stroked-button
        type="button"
        class="cancel-btn"
        (click)="cancel()"
        [disabled]="isLoading"
        aria-label="Cancel"
      >
        Cancel
      </button>
      <button
        mat-raised-button
        color="primary"
        type="submit"
        class="add-btn"
        [disabled]="workspaceForm.invalid || isLoading"
        aria-label="Add workspace"
      >
        <mat-icon *ngIf="!isLoading">add_circle_outline</mat-icon>
        <mat-spinner *ngIf="isLoading" diameter="20"></mat-spinner>
        <span>Add Workspace</span>
      </button>
    </mat-card-actions>
  </mat-card>
</form>
