import {Component, EventEmitter, Output, ViewChild} from '@angular/core';
import {SidenavComponent} from '../sidenav/sidenav.component';
import {FormBuilder, FormGroup, ReactiveFormsModule, Validators} from '@angular/forms';
import {WorkspaceService} from '../../services/workspace/workspace.service';
import {PaymentService} from '../../services/payment/payment.service';
import {Router} from '@angular/router';
import {MatFormFieldModule} from '@angular/material/form-field';
import {MatIcon} from '@angular/material/icon';
import {MatInput} from '@angular/material/input';
import {MatButton} from '@angular/material/button';
import {MatCardModule} from '@angular/material/card';
import {MatProgressSpinner} from '@angular/material/progress-spinner';
import {CommonModule} from '@angular/common';

@Component({
  selector: 'app-workspace',
  templateUrl: './workspace.component.html',
  imports: [
    MatFormFieldModule,
    MatIcon,
    MatInput,
    MatButton,
    MatCardModule,
    ReactiveFormsModule,
    MatProgressSpinner,
    CommonModule, // For *ngIf and other common directives
    ReactiveFormsModule,
  ],
  standalone: true,
  styleUrls: ['./workspace.component.css']
})
export class WorkspaceComponent {
  @ViewChild(SidenavComponent) sidenav!: SidenavComponent;
  @Output() menuClicked = new EventEmitter<void>();
  workspaceForm: FormGroup;
  selectedFile: File | null = null;
  selectedLogoFile: File | null = null;
  selectedLogoName: string = '';
  isLoading: boolean = false; // Fixed the type
  logoPreviewUrl: string | null = null;

  constructor(
    private fb: FormBuilder,
    private workspaceService: WorkspaceService,
    private paymentService: PaymentService,
    private router: Router
  ) {
    this.workspaceForm = this.fb.group({
      name: ['', Validators.required],
      description: [''],
      location: [''],
      phoneNumber: [
        '',
        [Validators.required, Validators.pattern(/^\+216\s?\d{2}\s?\d{3}\s?\d{3}$/)]
      ],
      email: ['', [Validators.email]],
    });
  }

  onFileSelected(event: any): void {
    this.selectedFile = event.target.files[0];
  }

  onLogoSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      const file = input.files[0];

      // Vérifier la taille du fichier (max 2 Mo)
      if (file.size > 2 * 1024 * 1024) {
        alert('Le fichier est trop volumineux. La taille maximale est de 2 Mo.');
        this.removeLogo();
        return;
      }

      // Vérifier le type de fichier (images uniquement)
      if (!file.type.startsWith('image/')) {
        alert('Seules les images sont acceptées (JPG, PNG, GIF, etc.).');
        this.removeLogo();
        return;
      }

      console.log('Logo sélectionné:', file.name, 'Type:', file.type, 'Taille:', file.size);

      // Vérifier les dimensions de l'image et redimensionner si nécessaire
      const img = new Image();
      img.onload = () => {
        // Vérifier si l'image est trop grande
        if (img.width > 800 || img.height > 800) {
          console.log('L\'image est trop grande, redimensionnement en cours...');

          // Redimensionner l'image
          this.resizeImage(file, 800, 800)
            .then(resizedFile => {
              console.log('Image redimensionnée:', resizedFile.name, 'Nouvelle taille:', resizedFile.size);
              this.selectedLogoFile = resizedFile;
              this.selectedLogoName = resizedFile.name;
              this.logoPreviewUrl = URL.createObjectURL(resizedFile);
            })
            .catch(error => {
              console.error('Erreur lors du redimensionnement de l\'image:', error);
              // Utiliser l'image originale en cas d'erreur
              this.selectedLogoFile = file;
              this.selectedLogoName = file.name;
              this.logoPreviewUrl = URL.createObjectURL(file);
            });
        } else {
          // Utiliser l'image originale si elle est déjà de la bonne taille
          this.selectedLogoFile = file;
          this.selectedLogoName = file.name;
          this.logoPreviewUrl = URL.createObjectURL(file);
        }
      };
      img.onerror = () => {
        console.error('Erreur lors du chargement de l\'image');
        // Utiliser l'image originale en cas d'erreur
        this.selectedLogoFile = file;
        this.selectedLogoName = file.name;
        this.logoPreviewUrl = URL.createObjectURL(file);
      };
      img.src = URL.createObjectURL(file);
    }
  }

  removeLogo(): void {
    this.selectedLogoFile = null;
    this.selectedLogoName = '';
    this.logoPreviewUrl = null;
    const input = document.getElementById('logo-upload') as HTMLInputElement;
    if (input) input.value = '';
  }

  // Méthode pour redimensionner une image
  resizeImage(file: File, maxWidth: number, maxHeight: number): Promise<File> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        // Calculer les nouvelles dimensions
        let width = img.width;
        let height = img.height;

        if (width > maxWidth) {
          height = Math.round(height * (maxWidth / width));
          width = maxWidth;
        }

        if (height > maxHeight) {
          width = Math.round(width * (maxHeight / height));
          height = maxHeight;
        }

        // Créer un canvas pour redimensionner l'image
        const canvas = document.createElement('canvas');
        canvas.width = width;
        canvas.height = height;

        // Dessiner l'image redimensionnée sur le canvas
        const ctx = canvas.getContext('2d');
        if (!ctx) {
          reject(new Error('Impossible de créer le contexte 2D'));
          return;
        }

        ctx.drawImage(img, 0, 0, width, height);

        // Convertir le canvas en blob
        canvas.toBlob((blob) => {
          if (!blob) {
            reject(new Error('Impossible de convertir le canvas en blob'));
            return;
          }

          // Créer un nouveau fichier à partir du blob
          const resizedFile = new File([blob], file.name, {
            type: file.type,
            lastModified: file.lastModified
          });

          resolve(resizedFile);
        }, file.type, 0.9); // Qualité de 90%
      };

      img.onerror = () => {
        reject(new Error('Erreur lors du chargement de l\'image'));
      };

      img.src = URL.createObjectURL(file);
    });
  }

  addWorkspace(): void {
    if (this.workspaceForm.valid) {
      console.log('Données envoyées :', this.workspaceForm.value);

      this.isLoading = true;
      const workspaceData = {
        ...this.workspaceForm.value
      };

      console.log('Données du workspace à envoyer:', workspaceData);
      // Fonction pour continuer l'ajout du workspace
      const continueAddWorkspace = () => {
        this.workspaceService.createWorkspace(workspaceData).subscribe({
          next: (response) => {
            console.log('Workspace ajouté avec succès !', response);

            // Stocker l'ID du workspace dans le service de paiement
            if (response && response.id) {
              // Convertir l'ID en string si nécessaire
              const workspaceId = response.id.toString();
              console.log('Storing workspace ID for payment:', workspaceId);
              this.paymentService.setWorkspaceId(workspaceId);

              // Afficher un message de succès plus convivial
              const successMessage = document.createElement('div');
              successMessage.className = 'workspace-success-message';
              successMessage.innerHTML = '<div class="icon"><i class="material-icons">check_circle</i></div><div class="message">Workspace créé avec succès !</div>';
              document.body.appendChild(successMessage);

              // Supprimer le message après 2 secondes et rediriger vers la page de paiement
              setTimeout(() => {
                if (document.body.contains(successMessage)) {
                  document.body.removeChild(successMessage);
                }

                // Réinitialiser le formulaire
                this.workspaceForm.reset();
                this.removeLogo();

                // Rediriger automatiquement vers la page de paiement
                console.log('Redirecting to payment plans page...');
                this.router.navigate(['/protected/payment/plans']);
              }, 2000);
            } else {
              console.warn('Workspace créé mais aucun ID retourné:', response);
              alert('Workspace créé mais aucun ID retourné. Veuillez contacter le support.');
              this.workspaceForm.reset();
              this.removeLogo();
            }
            this.isLoading = false;
          },
          error: (error) => {
            console.error('Erreur lors de l\'ajout du workspace', error);

            // Gérer les différents types d'erreurs
            if (error.message === 'No access token available' ||
              error.message === 'User not authenticated' ||
              error.message === 'Failed to refresh access token') {
              alert('Vous devez être connecté pour créer un workspace. Redirection vers la page de connexion...');
              // Rediriger vers la page de connexion
              setTimeout(() => {
                this.router.navigate(['/auth/sign-in']);
              }, 1000);
            } else if (error.status === 500) {
              // Erreur 500 (Internal Server Error)
              console.error('Détails de l\'erreur 500:', error);

              // Essayer d'extraire plus d'informations sur l'erreur
              let errorMessage = 'Erreur serveur lors de la création du workspace.';

              if (error.error && typeof error.error === 'object') {
                // Si l'erreur contient un objet JSON
                if (error.error.message) {
                  errorMessage += ' Message: ' + error.error.message;
                } else if (error.error.error) {
                  errorMessage += ' Message: ' + error.error.error;
                }
              } else if (typeof error.error === 'string') {
                // Si l'erreur est une chaîne de caractères
                errorMessage += ' Message: ' + error.error;
              }

              alert(errorMessage + '\n\nVeuillez vérifier les informations saisies et réessayer.');
            } else if (error.status === 400) {
              // Erreur 400 (Bad Request)
              alert('Les informations saisies sont incorrectes. Veuillez vérifier et réessayer.');
            } else if (error.status === 409) {
              // Erreur 409 (Conflict) - Nom de workspace déjà utilisé
              alert('Un workspace avec ce nom existe déjà. Veuillez choisir un autre nom.');
            } else {
              alert('Erreur lors de l\'ajout du workspace: ' + (error.message || 'Veuillez réessayer'));
            }

            this.isLoading = false;
          }
        });
      };

      if (this.selectedLogoFile) {
        console.log('Logo sélectionné:', this.selectedLogoFile.name, 'Taille:', this.selectedLogoFile.size, 'Type:', this.selectedLogoFile.type);

        // Vérifier si le logo est une image
        if (!this.selectedLogoFile.type.startsWith('image/')) {
          alert('Le logo doit être une image (JPG, PNG, etc.). Veuillez sélectionner un autre fichier.');
          this.removeLogo();

          this.isLoading = false;
          return;
        }

        // Vérifier la taille du logo (max 2 Mo)
        if (this.selectedLogoFile.size > 2 * 1024 * 1024) {
          // Essayer de redimensionner l'image si elle est trop grande
          this.resizeImage(this.selectedLogoFile, 800, 800)
            .then(resizedFile => {
              console.log('Image redimensionnée pour réduire la taille:', resizedFile.size);
              workspaceData.logo = resizedFile;
              continueAddWorkspace();
            })
            .catch(error => {
              console.error('Erreur lors du redimensionnement:', error);
              alert('Le logo est trop volumineux. La taille maximale est de 2 Mo.');
              this.removeLogo();
              this.isLoading = false;
            });
          return;
        }
      }

      // Si pas de logo ou logo déjà valide, continuer
      continueAddWorkspace();

      // Fin de la méthode addWorkspace
    } else {
      alert('Veuillez remplir tous les champs obligatoires');
    }
  }

  // Méthode dédiée pour télécharger le logo et finaliser
  uploadLogoAndFinalize(workspaceId: string): void {
    if (!this.selectedLogoFile) {
      this.finalizeWorkspaceCreation(workspaceId);
      return;
    }

    // Afficher un message de chargement
    this.isLoading = true;

    // Télécharger le logo avec gestion d'erreur améliorée
    this.workspaceService.uploadLogo(workspaceId, this.selectedLogoFile).subscribe({
      next: (logoResponse) => {
        console.log('Logo ajouté avec succès !', logoResponse);
        this.finalizeWorkspaceCreation(workspaceId, true);
      },
      error: (logoError) => {
        console.error('Erreur lors de l\'ajout du logo', logoError);

        // Message d'erreur plus détaillé
        let errorMessage = 'Erreur serveur lors de l\'enregistrement du logo. Veuillez réessayer ou contacter l\'administrateur.';

        if (logoError.message) {
          errorMessage = logoError.message;
        }

        alert(errorMessage);
        this.finalizeWorkspaceCreation(workspaceId, false);
      }
    });
  }

  finalizeWorkspaceCreation(workspaceId: string, logoSuccess: boolean = false): void {
    // Afficher un message de succès plus détaillé
    if (this.selectedLogoFile && logoSuccess) {
      alert('Workspace ajouté avec succès avec le logo!');
    } else if (this.selectedLogoFile && !logoSuccess) {
      // Le message d'erreur spécifique a déjà été affiché, donc pas besoin d'un autre alert ici
    } else {
      alert('Workspace ajouté avec succès!');
    }

    // Réinitialiser le formulaire et l'interface
    this.workspaceForm.reset();
    this.removeLogo();

    // Rediriger vers le nouveau workspace
    this.workspaceService.switchWorkspace(workspaceId);
    this.isLoading = false;
  }

  cancel() {

  }
}
