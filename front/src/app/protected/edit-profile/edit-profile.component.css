/* Conteneur principal */
.edit-profile-container {
  padding: 24px;
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  max-width: 700px;
  margin: 20px auto;
  font-family: 'Roboto', sans-serif;
}

/* En-tête du modal */
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.modal-header h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 500;
  color: #1a3c34;
}

.close-button {
  color: #666;
}

.close-button:hover {
  color: #333;
}

/* Style pour le chargement */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

.loading-container mat-spinner {
  margin-bottom: 16px;
}

.loading-container p {
  color: #666;
  font-size: 14px;
}

/* Style des onglets */
mat-tab-group {
  background-color: transparent;
}

::ng-deep .mat-tab-label {
  font-size: 16px;
  font-weight: 500;
  color: #666;
  padding: 0 24px;
  min-width: 100px;
  opacity: 0.7;
  transition: all 0.3s ease;
}

::ng-deep .mat-tab-label:hover {
  opacity: 1;
  background-color: #f5f5f5;
}

::ng-deep .mat-tab-label.mat-tab-label-active {
  color: #1a3c34;
  opacity: 1;
  font-weight: 600;
}

::ng-deep .mat-ink-bar {
  background-color: #1a3c34 !important;
}

/* Contenu des onglets */
.tab-content {
  padding: 24px;
  background-color: #fafafa;
  border-radius: 8px;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* Disposition des champs en grille */
.form-row {
  display: flex;
  gap: 16px;
}

.full-width {
  width: 100%;
}

/* Style des champs de formulaire */
mat-form-field {
  margin-bottom: 16px;
}

::ng-deep .mat-form-field-appearance-outline .mat-form-field-outline {
  color: #d3d3d3;
}

::ng-deep .mat-form-field-appearance-outline .mat-form-field-outline-thick {
  color: #1a3c34 !important;
}

mat-label {
  font-size: 14px;
  color: #444;
}

input {
  font-size: 16px;
  color: #333;
}

mat-error {
  font-size: 12px;
  color: #d32f2f;
}

/* Message "Coming Soon" */
.coming-soon {
  text-align: center;
  color: #666;
  font-style: italic;
  margin: 24px 0;
}

/* Boutons d'action */
.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
}

.cancel-button {
  color: #666;
  border-color: #d3d3d3;
  padding: 8px 16px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.cancel-button:hover {
  background-color: #f5f5f5;
  border-color: #bbb;
}

button[mat-raised-button] {
  background-color: #1a3c34;
  color: #ffffff;
  padding: 8px 24px;
  font-weight: 500;
  border-radius: 6px;
  transition: all 0.3s ease;
}

button[mat-raised-button]:hover {
  background-color: #15332d;
  transform: translateY(-1px);
}

button[mat-raised-button][disabled] {
  background-color: #ccc;
  color: #999;
  cursor: not-allowed;
}

mat-spinner {
  display: inline-block;
  vertical-align: middle;
  margin-right: 8px;
}

/* Responsive Design */
@media (max-width: 600px) {
  .edit-profile-container {
    margin: 10px;
    padding: 16px;
  }

  .form-row {
    flex-direction: column;
    gap: 8px;
  }

  .modal-header h2 {
    font-size: 20px;
  }

  ::ng-deep .mat-tab-label {
    font-size: 14px;
    padding: 0 16px;
    min-width: 80px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 8px;
  }

  .action-buttons button {
    width: 100%;
  }
}
