import { Component, OnInit, ChangeDetectorRef } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { UserService } from '../../services/user/user.service';
import { MatDialogRef } from '@angular/material/dialog';
import { MatTabGroup, MatTab } from '@angular/material/tabs';
import { NgIf } from '@angular/common';
import { Mat<PERSON>rror, MatFormField, MatLabel } from '@angular/material/form-field';
import { MatInput } from '@angular/material/input';
import { MatButton, MatIconButton } from '@angular/material/button';
import { MatIcon } from '@angular/material/icon';
import {MatProgressSpinner, MatSpinner} from '@angular/material/progress-spinner';

export interface UserProfile {
  id?: number;
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber?: string;
  address?: string;
}

@Component({
  selector: 'app-edit-profile',
  templateUrl: './edit-profile.component.html',
  standalone: true,
  imports: [
    MatTabGroup,
    ReactiveFormsModule,
    MatTab,
    NgIf,
    MatFormField,
    MatInput,
    MatLabel,
    MatButton,
    MatIcon,
    MatIconButton,
    MatError,
    MatProgressSpinner,
  ],
  styleUrls: ['./edit-profile.component.css']
})
export class EditProfileComponent implements OnInit {
  userProfileForm: FormGroup;
  isLoading = true;
  isSaving = false;
  userId: string | null = null; // Store the userId
  profileId: string | null = null; // Store the profileId

  constructor(
    private fb: FormBuilder,
    private userService: UserService,
    public dialogRef: MatDialogRef<EditProfileComponent>,
    private cdr: ChangeDetectorRef
  ) {
    this.userProfileForm = this.fb.group({
      firstName: ['', Validators.required],
      lastName: ['', Validators.required],
      email: ['', [Validators.required, Validators.email]],
      phoneNumber: [''],
      address: [''],
      dateOfBirth: [''],
      bio: ['']
    });
  }

  ngOnInit(): void {
    this.fetchUserProfile();
  }

  fetchUserProfile(): void {
    this.isLoading = true;
    this.userService.getUserProfile().subscribe({
      next: (data: any) => {
        console.log('User Profile Fetched:', data);
        // Extract the 'user' property from the fetched data
        const userProfile = data.user || {};
        // Patch the form with the user data
        this.userProfileForm.patchValue({
          firstName: userProfile.firstName || '',
          lastName: userProfile.lastName || '',
          email: userProfile.email || '',
          phoneNumber: userProfile.phoneNumber || '',
          address: userProfile.address || '',
          dateOfBirth: userProfile.dateOfBirth || '',
          bio: userProfile.bio || ''
        });
        console.log('Form Values After Patching:', this.userProfileForm.value);
        this.isLoading = false;
        this.cdr.detectChanges();
      },
      error: (err) => {
        console.error('Erreur lors de la récupération du profil:', err);
        this.isLoading = false;
        alert('Une erreur est survenue lors de la récupération des données.');
        this.cdr.detectChanges();
      }
    });
  }
  onSubmit(): void {
    if (this.userProfileForm.valid) {
      this.isSaving = true;
      const updatedProfile: UserProfile = {
        ...this.userProfileForm.value
      };

      console.log('Updated Profile:', updatedProfile);

      this.userService.updateFullUserProfile(updatedProfile).subscribe({
        next: (response) => {
          console.log('Profil mis à jour avec succès:', response);
          this.isSaving = false;
          this.dialogRef.close('updated');
        },
        error: (err) => {
          console.error('Erreur lors de la mise à jour du profil:', err);
          this.isSaving = false;
          alert('Une erreur est survenue lors de la mise à jour du profil.');
        }
      });
    } else {
      console.log('Formulaire invalide:', this.userProfileForm.errors);
    }
  }
}
