import {ChangeDetector<PERSON><PERSON>, Component, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roy, OnInit, Output} from '@angular/core';
import {MatIcon} from '@angular/material/icon';
import {MatAnchor, MatIconButton} from '@angular/material/button';
import {MatToolbar} from '@angular/material/toolbar';
import {NavigationEnd, Router, RouterLink} from '@angular/router';
import {FormsModule} from '@angular/forms';
import {NgForOf, NgIf} from '@angular/common';
import {MatMenu, MatMenuItem, MatMenuTrigger} from '@angular/material/menu';
import {TranslatePipe, TranslateService} from '@ngx-translate/core';
import {MatBadge} from '@angular/material/badge';
import {
  ActiveWorkspaceDisplayComponent
} from '../../shared/active-workspace-display/active-workspace-display.component';
import {WorkspaceService} from '../../services/workspace/workspace.service';
import {Subscription} from 'rxjs';
import {UserRoleService} from '../../services/user-role/user-role.service';
import {ThemeService} from '../../services/theme/theme.service';
import {ThemeToggleComponent} from '../../shared/theme-toggle/theme-toggle.component';

@Component({
  selector: 'app-nav',
  standalone: true,
  templateUrl: './app-nav.component.html',
  styleUrls: ['./app-nav.component.scss'],
  imports: [
    MatIcon,
    MatMenu,
    MatIconButton,
    FormsModule,
    MatMenuItem,
    MatMenuTrigger,
    MatAnchor,
    RouterLink,
    MatToolbar,
    TranslatePipe,
    NgForOf,
    MatBadge,
    ActiveWorkspaceDisplayComponent,
    NgIf,
    ThemeToggleComponent
  ]
})
export class AppNavComponent implements OnInit, OnDestroy {
  showSearchBar = false;
  searchQuery = '';
  selectedLanguage = 'Français';
  isSettingsPage = false;
  unreadCount: number = 0; // Initialize as number, default to 0
  isDarkMode = false; // Propriété pour le mode sombre
  isRecruteur: boolean = false;
  subscriptions: Subscription[] = [];

  @Output() menuClicked = new EventEmitter<void>();

  languages = [
    {code: 'fr', name: 'Français', icon: '🇫🇷'},
    {code: 'en', name: 'English', icon: '🇬🇧'},
    {code: 'de', name: 'Deutsch', icon: '🇩🇪'},
    {code: 'ar', name: 'العربية', icon: '🇸🇦'},
    {code: 'it', name: 'Italien', icon: '🇮🇹'}
  ];

  constructor(
    private translateService: TranslateService,
    private router: Router,
    private workspaceService: WorkspaceService,
    private cdr: ChangeDetectorRef,
    private userRoleService: UserRoleService,
    private themeService: ThemeService
  ) {
    this.translateService.setDefaultLang('en');
    this.translateService.use('en');

    // Initialisation du mode sombre gérée par le ThemeService

    const routerSub = this.router.events.subscribe(event => {
      if (event instanceof NavigationEnd) {
        this.isSettingsPage = this.router.url.includes('/settings');

        // Vérifier le statut de recruteur à chaque changement de route
        this.checkIfUserIsRecruiter();
      }
    });

    // Ajouter l'abonnement à la liste des abonnements
    this.subscriptions.push(routerSub);
    // S'abonner au workspace actif
    const activeWorkspaceSub = workspaceService.activeWorkspace$.subscribe(workspaceId => {
      // Ajouter l'abonnement à la liste des abonnements
      this.subscriptions.push(activeWorkspaceSub);
      console.log('AppNav - Workspace ID actif:', workspaceId);

      // Vérifier si l'utilisateur est un recruteur
      this.checkIfUserIsRecruiter();
    });
  }

  ngOnInit(): void {
    console.log('AppNav - Initializing component');

    // S'abonner aux changements de rôle
    const roleSub = this.userRoleService.isRecruiter$.subscribe(isRecruiter => {
      console.log('AppNav - Rôle changé:', isRecruiter ? 'Recruteur' : 'Candidat');
      this.isRecruteur = isRecruiter;
      this.cdr.detectChanges();
    });

    // S'abonner aux changements de thème
    const themeSub = this.themeService.isDarkTheme$.subscribe(isDark => {
      console.log('AppNav - Thème changé:', isDark ? 'Sombre' : 'Clair');
      this.isDarkMode = isDark;
      this.cdr.detectChanges();
    });

    this.subscriptions.push(roleSub, themeSub);

    // Écouter les changements de workspace
    this.listenToWorkspaceChanges();

    // Subscribe to router events to check workspace status on each navigation
    const routerSub = this.router.events.subscribe(event => {
      if (event instanceof NavigationEnd) {
        console.log('AppNav - Navigation detected to:', event.url);
        // Check workspace status on each navigation
        this.checkIfUserIsRecruiter();
      }
    });

    this.subscriptions.push(routerSub);
  }

  /**
   * Écoute les changements de workspace pour mettre à jour l'interface
   */
  private listenToWorkspaceChanges(): void {
    const workspaceChangedSub = this.workspaceService.workspaceChanged$.subscribe(workspaceId => {
      console.log('AppNav - Changement de workspace détecté:', workspaceId);

      // Traitement spécial pour la page d'accueil
      if (this.router.url === '/acceuil' || this.router.url.startsWith('/acceuil')) {
        console.log('AppNav - Sur la page d\'accueil lors d\'un changement de workspace, forçage du mode candidat');
        this.isRecruteur = false;
        this.cdr.detectChanges();
        return;
      }

      // Force a check of the current workspace ID from storage
      const currentId = this.workspaceService.getActiveWorkspaceId();
      console.log('AppNav - Current workspace ID after change event:', currentId);

      // Mettre à jour le statut de recruteur
      this.checkIfUserIsRecruiter();

      // Force UI update
      this.cdr.detectChanges();
    });

    // Ajouter l'abonnement à la liste des abonnements
    this.subscriptions.push(workspaceChangedSub);
  }

  ngOnDestroy(): void {
    // Désabonner de tous les abonnements
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  /**
   * Vérifie directement si l'utilisateur actuel est un recruteur
   */
  private checkIfUserIsRecruiter(): void {
    // Traitement spécial pour la page d'accueil
    if (this.router.url === '/acceuil' || this.router.url.startsWith('/acceuil')) {
      console.log('AppNav - Sur la page d\'accueil, forçage du mode candidat');
      this.isRecruteur = false;
      this.cdr.detectChanges();
      return;
    }

    // Vérifier d'abord dans sessionStorage et localStorage
    const sessionWorkspaceId = sessionStorage.getItem('Workspace-Id');
    const localWorkspaceId = localStorage.getItem('Workspace-Id');

    // Get the workspace ID from the service
    const serviceWorkspaceId = this.workspaceService.getActiveWorkspaceId();

    console.log('AppNav - Workspace ID check:', {
      sessionStorage: sessionWorkspaceId,
      localStorage: localWorkspaceId,
      serviceWorkspaceId: serviceWorkspaceId,
      currentUrl: this.router.url
    });

    // Si un workspace est actif dans le service ou dans le stockage, l'utilisateur est un recruteur
    const isRecruiter = (
      (!!serviceWorkspaceId && serviceWorkspaceId.length > 0) ||
      (!!sessionWorkspaceId && sessionWorkspaceId.length > 0) ||
      (!!localWorkspaceId && localWorkspaceId.length > 0)
    );

    console.log('AppNav - Est recruteur:', isRecruiter);

    // Always update the interface to ensure consistency
    this.isRecruteur = isRecruiter;
    console.log('AppNav - isRecruteur mis à jour:', this.isRecruteur);
    // Forcer la détection des changements pour mettre à jour l'interface
    this.cdr.detectChanges();
  }

  // La fonction toggleDarkMode a été remplacée par le composant ThemeToggleComponent

  logout() {
    localStorage.removeItem('user');
    localStorage.removeItem('Workspace-Id');
    sessionStorage.removeItem('Workspace-Id');
    this.router.navigate(['/']);
  }

  toggleSearchBar() {
    this.showSearchBar = !this.showSearchBar;
    this.searchQuery = '';
  }

  clearSearch() {
    this.searchQuery = '';
  }

  changeLanguage(langCode: string) {
    this.translateService.use(langCode);
    this.selectedLanguage = this.languages.find(lang => lang.code === langCode)?.name || 'Français';
    localStorage.setItem('language', langCode);
  }

  viewNotifications() {
    this.router.navigate(['/notifications']);
  }
}
