<mat-toolbar class="app-bar" color="secondary-container">
  <div class="left-section">
    <button mat-icon-button (click)="menuClicked.emit()" aria-label="Ouvrir le menu">
      <mat-icon>menu</mat-icon>
    </button>
    <a [routerLink]="isRecruteur ? '/dashboard' : '/acceuil'" class="logo-container">
      <img src="/assets/images/min_logo.png" alt="logo" class="logo">
    </a>
  </div>
  <span class="spacer"></span>
  <nav>
    <!-- Lien Accueil - visible uniquement pour les utilisateurs normaux -->
    <a mat-button routerLink="/acceuil" *ngIf="!isRecruteur">
      <mat-icon class="nav-icon">home</mat-icon>
      {{ 'ACCUEIL' | translate }}
    </a>

    <!-- Lien Profil - visible pour tous -->
    <a mat-button [routerLink]="isRecruteur ? '/recruiter-profile' : '/profile'">
      <mat-icon class="nav-icon">person</mat-icon>
      {{ 'PROFIL' | translate }}
    </a>

    <!-- Lien Jobs - visible uniquement pour les recruteurs -->
    <a mat-button routerLink="/acceuilposts" *ngIf="isRecruteur">
      <mat-icon class="nav-icon">work</mat-icon>
      {{ 'JOBS' | translate }}
    </a>

    <!-- Lien Posts - visible uniquement pour les recruteurs -->
    <a mat-button routerLink="/posts" *ngIf="isRecruteur">
      <mat-icon class="nav-icon">post_add</mat-icon>
      {{ 'POSTS' | translate }}
    </a>

    <!-- Lien Invitations - visible uniquement pour les recruteurs -->
    <a mat-button routerLink="/recruiter-invitations" *ngIf="isRecruteur">
      <mat-icon class="nav-icon">mail_outline</mat-icon>
      {{ 'invitation' | translate }}
    </a>
  </nav>
  <span class="spacer"></span>

  <!-- Affichage du workspace actif -->
  <div class="active-workspace-container">
    <app-active-workspace-display [size]="'small'"></app-active-workspace-display>
  </div>

  <app-theme-toggle></app-theme-toggle>
  <!-- Add Notification Button -->
  <button mat-icon-button (click)="viewNotifications()" aria-label="Voir les notifications" [matBadge]="unreadCount > 0 ? unreadCount : null" matBadgeColor="warn">
    <mat-icon>notifications</mat-icon>
  </button>
  <button mat-icon-button [matMenuTriggerFor]="langMenu" aria-label="Langue">
    <mat-icon>language</mat-icon>
  </button>
<!--  <div class="search-bar" *ngIf="showSearchBar">-->
<!--    <mat-form-field appearance="outline" label="Rechercher...">-->
<!--      <input matInput [(ngModel)]="searchQuery" />-->
<!--      <button mat-icon-button matSuffix (click)="clearSearch()" aria-label="Effacer la recherche">-->
<!--        <mat-icon>close</mat-icon>-->
<!--      </button>-->
<!--    </mat-form-field>-->
<!--  </div>-->
  <mat-menu #langMenu="matMenu">
    <ng-container *ngFor="let lang of languages">
      <button mat-menu-item (click)="changeLanguage(lang.code)">
        <span>{{ lang.icon }}</span> {{ lang.name }}
      </button>
    </ng-container>
  </mat-menu>
</mat-toolbar>
