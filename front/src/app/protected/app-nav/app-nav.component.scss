.app-bar {
  background-color: var(--mat-sys-color-primary);
  color: var(--mat-sys-color-on-primary);
  display: flex;
  align-items: center;
  padding: 8px 16px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  border-bottom: 2px solid #ddd;
  justify-content: space-between;
}
.nav-icon {
  margin-right: 6px;
  vertical-align: middle;
}
.left-section {
  display: flex;
  align-items: center;
}
.logo-container {
  display: flex;
  align-items: center;
}

.logo {
  height: 60px;
  width: 100px;
  object-fit: contain;
  transition: transform 0.3s ease;
}

.logo:hover {
  transform: scale(1.1);
}

.spacer {
  flex: 1;
}

nav .nav-links a {
  text-decoration: none;
  font-weight: 500;
  margin: 0 20px;
  font-size: 1rem;
  color: inherit;
  transition: color 0.3s ease;
}

nav .nav-links a:hover {
  color: var(--mat-sys-color-secondary);
}

.toolbar-actions button.mat-icon-button {
  margin: 0 8px;
  transition: transform 0.3s ease, background-color 0.3s ease;
}

.toolbar-actions button.mat-icon-button:hover {
  transform: scale(1.1);
  background-color: rgba(0, 0, 0, 0.1);
}

.toolbar-actions button.mat-icon-button:focus {
  outline: none;
}

.search-bar {
  position: absolute;
  right: 20px;
  top: 56px;
  width: 250px;
  display: flex;
}

mat-form-field {
  width: 100%;
  background-color: var(--mat-sys-color-surface);
}

mat-form-field input {
  padding: 8px;
}

mat-menu button {
  font-size: 1rem;
  background-color: var(--mat-sys-color-surface);
  transition: background-color 0.3s ease;
}

mat-menu button:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

mat-menu {
  padding: 8px;
}

nav .nav-links a {
  font-weight: 600;
  color: var(--mat-sys-color-on-primary);
  padding: 8px 16px;
  border-radius: 5px;
  transition: background-color 0.3s ease, color 0.3s ease;
}

nav .nav-links a:hover {
  background-color: rgba(0, 0, 0, 0.1);
  color: var(--mat-sys-color-secondary);
}

@media (max-width: 768px) {
  .logo {
    height: 50px;
    width: 90px;
  }

  .spacer {
    flex: 0;
  }

  .search-bar {
    right: 16px;
    top: 56px;
  }
}
.nav-links a {
  text-decoration: none;
  font-weight: 600;
  margin: 0 20px;
  font-size: 1rem;
  color: var(--mat-sys-color-on-primary);
  padding: 8px 16px;
  border-radius: 5px;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.nav-links a:hover {
  background-color: rgba(0, 0, 0, 0.1);
  color: var(--mat-sys-color-secondary);
}

.nav-links a.active-link {
  background-color: var(--mat-sys-color-secondary);
  color: rgba(174, 174, 174, 0.71);
  font-weight: bold;
  border-radius: 5px;
}
.toolbar-actions button.mat-icon-button mat-icon,
button.mat-icon-button mat-icon {
  color: var(--mat-sys-color-on-primary);
  transition: color 0.3s ease, transform 0.3s ease;
}

.toolbar-actions button.mat-icon-button:hover mat-icon,
button.mat-icon-button:hover mat-icon {
  color: var(--mat-sys-color-secondary);
  transform: scale(1.1);
}

.toolbar-actions button.mat-icon-button:active mat-icon,
button.mat-icon-button:active mat-icon {
  color: var(--mat-sys-color-secondary);
  transform: scale(1.2);
}
.search-container {
  display: flex;
  align-items: center;
  position: relative;
}

.search-bar {
  position: absolute;
  right: 50px;
  top: 50%;
  transform: translateY(-50%);
  border-radius: 5px;
  padding: 5px;
  display: flex;
  align-items: center;
  transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
}

.search-bar.hidden {
  opacity: 0;
  transform: translateY(-50%) scale(0.9);
  pointer-events: none;
}

.search-bar input {
  border: none;
  outline: none;
  padding: 5px;
  width: 50px;
}

.search-bar button {
  background: transparent;
  border: none;
  cursor: pointer;
}

.search-icon {
  position: relative;
}
.app-bar {
  background-color: var(--mat-sys-color-primary);
  color: var(--mat-sys-color-on-primary);
  display: flex;
  align-items: center;
  padding: 8px 16px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  border-bottom: 2px solid #ddd;
  justify-content: space-between;
}

.left-section {
  display: flex;
  align-items: center;
}

.logo-container {
  display: flex;
  align-items: center;
}

.logo {
  height: 60px;
  width: 100px;
  object-fit: contain;
  transition: transform 0.3s ease;
}

.logo:hover {
  transform: scale(1.1);
}

.spacer {
  flex: 1;
}

nav .nav-links a {
  text-decoration: none;
  font-weight: 500;
  margin: 0 20px;
  font-size: 1rem;
  color: inherit;
  transition: color 0.3s ease;
}

nav .nav-links a:hover {
  color: var(--mat-sys-color-secondary);
}

.toolbar-actions button.mat-icon-button {
  margin: 0 8px;
  transition: transform 0.3s ease, background-color 0.3s ease;
}

.toolbar-actions button.mat-icon-button:hover {
  transform: scale(1.1);
  background-color: rgba(0, 0, 0, 0.1);
}

.toolbar-actions button.mat-icon-button:focus {
  outline: none;
}

.search-bar {
  position: absolute;
  right: 20px;
  top: 56px;
  width: 250px;
  display: flex;
}

mat-form-field {
  width: 100%;
  background-color: var(--mat-sys-color-surface);
}

mat-form-field input {
  padding: 8px;
}

mat-menu button {
  font-size: 1rem;
  background-color: var(--mat-sys-color-surface);
  transition: background-color 0.3s ease;
}

mat-menu button:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

mat-menu {
  padding: 8px;
}

nav .nav-links a {
  font-weight: 600;
  color: var(--mat-sys-color-on-primary);
  padding: 8px 16px;
  border-radius: 5px;
  transition: background-color 0.3s ease, color 0.3s ease;
}

nav .nav-links a:hover {
  background-color: rgba(0, 0, 0, 0.1);
  color: var(--mat-sys-color-secondary);
}

@media (max-width: 768px) {
  .logo {
    height: 50px;
    width: 90px;
  }

  .spacer {
    flex: 0;
  }

  .search-bar {
    right: 16px;
    top: 56px;
  }
}
.nav-links a {
  text-decoration: none;
  font-weight: 600;
  margin: 0 20px;
  font-size: 1rem;
  color: var(--mat-sys-color-on-primary);
  padding: 8px 16px;
  border-radius: 5px;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.nav-links a:hover {
  background-color: rgba(0, 0, 0, 0.1);
  color: var(--mat-sys-color-secondary);
}

.nav-links a.active-link {
  background-color: var(--mat-sys-color-secondary);
  color: rgba(174, 174, 174, 0.71);
  font-weight: bold;
  border-radius: 5px;
}
.toolbar-actions button.mat-icon-button mat-icon,
button.mat-icon-button mat-icon {
  color: var(--mat-sys-color-on-primary);
  transition: color 0.3s ease, transform 0.3s ease;
}

.toolbar-actions button.mat-icon-button:hover mat-icon,
button.mat-icon-button:hover mat-icon {
  color: var(--mat-sys-color-secondary);
  transform: scale(1.1);
}

.toolbar-actions button.mat-icon-button:active mat-icon,
button.mat-icon-button:active mat-icon {
  color: var(--mat-sys-color-secondary);
  transform: scale(1.2);
}
.search-container {
  display: flex;
  align-items: center;
  position: relative;
}

.search-bar {
  position: absolute;
  right: 50px;
  top: 50%;
  transform: translateY(-50%);
  border-radius: 5px;
  padding: 5px;
  display: flex;
  align-items: center;
  transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
}

.search-bar.hidden {
  opacity: 0;
  transform: translateY(-50%) scale(0.9);
  pointer-events: none;
}

.search-bar input {
  border: none;
  outline: none;
  padding: 5px;
  width: 50px;
}

.search-bar button {
  background: transparent;
  border: none;
  cursor: pointer;
}

.search-icon {
  position: relative;
}

/* Styles pour le conteneur du workspace actif */
.active-workspace-container {
  display: flex;
  align-items: center;
  padding: 0 15px;
  margin-right: 10px;
  height: 40px;
  border-radius: 20px;
  background-color: rgba(255, 255, 255, 0.1);
  transition: background-color 0.3s ease;
}

.active-workspace-container:hover {
  background-color: rgba(255, 255, 255, 0.2);
}