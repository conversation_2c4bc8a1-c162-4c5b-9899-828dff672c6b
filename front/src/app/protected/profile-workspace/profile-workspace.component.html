<div class="profile-container">
  <!-- Header Section -->
  <header class="header-section">
    <div class="wave-effect"></div>
    <div class="header-content">
      <div class="logo-container" (click)="triggerLogoUpload()" tabindex="0" role="button" aria-label="Cliquez pour ajouter un logo">
        <mat-icon class="logo" *ngIf="!workspaceData?.logoUrl">business</mat-icon>
        <img *ngIf="workspaceData?.logoUrl" [src]="assetsUrl + workspaceData.logoUrl" alt="Logo" class="workspace-logo">
        <div class="logo-upload-hint">Cliquez pour modifier</div>
        <input type="file" #logoFileInput style="display: none;" (change)="onLogoSelected($event)" accept="image/*">
      </div>
      <div class="header-info">
        <h1 class="workspace-name">{{ workspaceData?.name }}</h1>
        <div class="contact-info-section">
          <div class="contact-info">
            <mat-icon>email</mat-icon> {{ workspaceData?.email }}
          </div>
          <div class="contact-info">
            <mat-icon>phone</mat-icon> {{ workspaceData?.phoneNumber }}
          </div>
          <div class="contact-info">
            <mat-icon>location_on</mat-icon> {{ workspaceData?.location }}
          </div>

        </div>
      </div>
    </div>
  </header>

  <!-- Description Section -->
  <div class="workspace-info">
    <div class="elegant-description-container">
      <div class="elegant-description">
        <div class="description-left">
          <div class="company-logo-display">
            <img *ngIf="workspaceData?.logoUrl" [src]="assetsUrl + workspaceData.logoUrl" alt="Logo" class="company-logo-image">
            <div *ngIf="!workspaceData?.logoUrl" class="company-logo-placeholder">
              <mat-icon>business</mat-icon>
            </div>
          </div>
          <div class="vertical-line"></div>
        </div>

        <div class="description-right">
          <div class="description-header">
            <h2 class="elegant-title">{{ workspaceData?.name || 'Notre Entreprise' }}</h2>
            <div class="title-underline"></div>
            <button mat-icon-button class="edit-button" (click)="toggleEditMode('description')" aria-label="Modifier la description">
              <mat-icon>edit</mat-icon>
            </button>
          </div>

          <div class="description-content" *ngIf="!isEditingDescription">
            <p class="elegant-text">
              {{ workspaceData?.description || 'Notre entreprise est dédiée à fournir des solutions innovantes et de haute qualité pour répondre aux besoins de nos clients. Avec une équipe d\'experts passionnés et des années d\'expérience dans le domaine, nous nous engageons à offrir un service exceptionnel et des résultats qui dépassent les attentes.' }}
            </p>
          </div>

          <div class="description-edit-form" *ngIf="isEditingDescription">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Description de l'entreprise</mat-label>
              <textarea matInput [(ngModel)]="editWorkspaceData.description" rows="6" placeholder="Décrivez votre entreprise..."></textarea>
            </mat-form-field>

            <div class="form-actions">
              <button mat-button color="warn" (click)="cancelEdit('description')">
                Annuler
              </button>
              <button mat-raised-button color="primary" (click)="saveWorkspaceDetails('description')">
                Enregistrer
              </button>
            </div>
          </div>

            <div class="key-values">
              <div class="value-item">
                <div class="value-icon">
                  <mat-icon>trending_up</mat-icon>
                </div>
                <div class="value-text">Innovation</div>
              </div>

              <div class="value-item">
                <div class="value-icon">
                  <mat-icon>verified</mat-icon>
                </div>
                <div class="value-text">Qualité</div>
              </div>

              <div class="value-item">
                <div class="value-icon">
                  <mat-icon>star</mat-icon>
                </div>
                <div class="value-text">Excellence</div>
              </div>
            </div>

            <div class="signature-container">
              <div class="signature-line"></div>
              <div class="signature-text">Direction Générale</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="statements-section">
    <div class="statements-background">
      <div class="bg-shape shape-1"></div>
      <div class="bg-shape shape-2"></div>
      <div class="bg-shape shape-3"></div>
    </div>

    <div class="section-header">
      <div class="section-title-container">
        <div class="title-accent"></div>
        <h2 class="section-title">Notre Engagement</h2>
        <div class="title-accent"></div>
        <button mat-icon-button class="edit-button" (click)="toggleEditMode('statements')" aria-label="Modifier les engagements">
          <mat-icon>edit</mat-icon>
        </button>
      </div>
      <p class="section-subtitle">Les piliers qui guident notre entreprise vers l'excellence</p>
    </div>

    <!-- Affichage des statements -->
    <div class="statements-container" *ngIf="!isEditingStatements">
      <div class="statement-item vision-item">
        <div class="statement-icon-container">
          <div class="icon-background"></div>
          <mat-icon>visibility</mat-icon>
        </div>
        <div class="statement-content">
          <h3>Vision</h3>
          <div class="statement-divider"></div>
          <p>{{ workspaceData?.vision || 'Devenir le leader incontesté de notre secteur en offrant des solutions innovantes qui transforment positivement notre industrie.' }}</p>
        </div>
        <div class="statement-footer">
          <div class="statement-number">01</div>
        </div>
      </div>

      <div class="statement-item mission-item">
        <div class="statement-icon-container">
          <div class="icon-background"></div>
          <mat-icon>flag</mat-icon>
        </div>
        <div class="statement-content">
          <h3>Mission</h3>
          <div class="statement-divider"></div>
          <p>{{ workspaceData?.mission || 'Fournir des solutions de haute qualité qui répondent aux besoins de nos clients tout en maintenant les plus hauts standards d\'excellence et d\'intégrité.' }}</p>
        </div>
        <div class="statement-footer">
          <div class="statement-number">02</div>
        </div>
      </div>

      <div class="statement-item values-item">
        <div class="statement-icon-container">
          <div class="icon-background"></div>
          <mat-icon>verified_user</mat-icon>
        </div>
        <div class="statement-content">
          <h3>Valeurs</h3>
          <div class="statement-divider"></div>
          <div class="values-list">
            <div class="value-tag">Innovation</div>
            <div class="value-tag">Collaboration</div>
            <div class="value-tag">Excellence</div>
            <div class="value-tag">Intégrité</div>
          </div>
        </div>
        <div class="statement-footer">
          <div class="statement-number">03</div>
        </div>
      </div>
    </div>

    <!-- Formulaire d'édition des statements -->
    <div class="statements-edit-form" *ngIf="isEditingStatements">
      <div class="form-container">
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Notre Vision</mat-label>
          <textarea matInput [(ngModel)]="editWorkspaceData.vision" rows="4" placeholder="Décrivez la vision de votre entreprise..."></textarea>
          <mat-hint>Décrivez où vous voyez votre entreprise dans le futur</mat-hint>
        </mat-form-field>

        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Notre Mission</mat-label>
          <textarea matInput [(ngModel)]="editWorkspaceData.mission" rows="4" placeholder="Décrivez la mission de votre entreprise..."></textarea>
          <mat-hint>Expliquez ce que fait votre entreprise et pourquoi</mat-hint>
        </mat-form-field>

        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Nos Valeurs</mat-label>
          <textarea matInput [(ngModel)]="editWorkspaceData.values" rows="4" placeholder="Décrivez les valeurs de votre entreprise..."></textarea>
          <mat-hint>Listez les principes qui guident votre entreprise</mat-hint>
        </mat-form-field>

        <div class="form-actions">
          <button mat-button color="warn" (click)="cancelEdit('statements')">
            Annuler
          </button>
          <button mat-raised-button color="primary" (click)="saveWorkspaceStatements()">
            Enregistrer
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Services Section -->
  <div class="services-section">
    <div class="services-background">
      <div class="bg-shape shape-1"></div>
      <div class="bg-shape shape-2"></div>
      <div class="bg-shape shape-3"></div>
      <div class="bg-lines"></div>
    </div>

    <div class="section-header">
      <div class="section-title-container">
        <div class="title-accent"></div>
        <h2 class="section-title">Nos Services</h2>
        <div class="title-accent"></div>
      </div>
      <p class="section-subtitle">Des solutions innovantes pour répondre à vos besoins</p>
    </div>

    <div class="services-container">
      <div class="service-card">
        <div class="service-card-inner">
          <div class="service-card-front">
            <div class="service-icon-wrapper">
              <div class="service-icon-bg"></div>
              <mat-icon class="service-icon">code</mat-icon>
            </div>
            <h3 class="service-title">Développement</h3>
            <div class="service-divider"></div>
            <p class="service-short-desc">Solutions logicielles sur mesure</p>
          </div>

          <div class="service-card-back">
            <h3 class="service-title">Développement</h3>
            <div class="service-divider"></div>
            <p class="service-description">Solutions logicielles sur mesure pour répondre à vos besoins spécifiques, développées par nos experts en utilisant les dernières technologies.</p>
            <ul class="service-features">
              <li><mat-icon>check_circle</mat-icon> Applications web</li>
              <li><mat-icon>check_circle</mat-icon> Applications mobiles</li>
              <li><mat-icon>check_circle</mat-icon> Logiciels d'entreprise</li>
            </ul>
            <button mat-flat-button class="service-button">En savoir plus</button>
          </div>
        </div>
      </div>

      <div class="service-card">
        <div class="service-card-inner">
          <div class="service-card-front">
            <div class="service-icon-wrapper">
              <div class="service-icon-bg"></div>
              <mat-icon class="service-icon">cloud</mat-icon>
            </div>
            <h3 class="service-title">Cloud Computing</h3>
            <div class="service-divider"></div>
            <p class="service-short-desc">Infrastructure cloud sécurisée</p>
          </div>

          <div class="service-card-back">
            <h3 class="service-title">Cloud Computing</h3>
            <div class="service-divider"></div>
            <p class="service-description">Infrastructure cloud sécurisée et évolutive pour votre entreprise, optimisée pour la performance et la fiabilité.</p>
            <ul class="service-features">
              <li><mat-icon>check_circle</mat-icon> Serveurs virtuels</li>
              <li><mat-icon>check_circle</mat-icon> Stockage évolutif</li>
              <li><mat-icon>check_circle</mat-icon> Solutions SaaS</li>
            </ul>
            <button mat-flat-button class="service-button">En savoir plus</button>
          </div>
        </div>
      </div>

      <div class="service-card">
        <div class="service-card-inner">
          <div class="service-card-front">
            <div class="service-icon-wrapper">
              <div class="service-icon-bg"></div>
              <mat-icon class="service-icon">security</mat-icon>
            </div>
            <h3 class="service-title">Cybersécurité</h3>
            <div class="service-divider"></div>
            <p class="service-short-desc">Protection de vos données</p>
          </div>

          <div class="service-card-back">
            <h3 class="service-title">Cybersécurité</h3>
            <div class="service-divider"></div>
            <p class="service-description">Protection de vos données et systèmes contre les menaces informatiques avec des solutions de sécurité avancées.</p>
            <ul class="service-features">
              <li><mat-icon>check_circle</mat-icon> Audit de sécurité</li>
              <li><mat-icon>check_circle</mat-icon> Protection des données</li>
              <li><mat-icon>check_circle</mat-icon> Surveillance continue</li>
            </ul>
            <button mat-flat-button class="service-button">En savoir plus</button>
          </div>
        </div>
      </div>
    </div>

    <div class="services-cta">
      <p>Découvrez comment nos services peuvent transformer votre entreprise</p>
      <button mat-flat-button class="cta-button">
        <mat-icon>phone</mat-icon> Contactez-nous pour une consultation
      </button>
    </div>
  </div>

  <!-- Company Posts Section - Design Élégant -->
  <div class="company-posts-section">
    <!-- Arrière-plan élégant avec effets visuels -->
    <div class="posts-background">
      <div class="bg-pattern"></div>
      <div class="bg-gradient-overlay"></div>
      <div class="floating-shapes">
        <div class="floating-shape shape-1"></div>
        <div class="floating-shape shape-2"></div>
        <div class="floating-shape shape-3"></div>
      </div>
    </div>

    <!-- En-tête de section élégant -->
    <div class="section-header">
      <div class="section-badge">
        <span class="badge-text">Notre Actualité</span>
        <div class="badge-shine"></div>
      </div>

      <div class="section-title-container">
        <div class="title-accent"></div>
        <h2 class="section-title">Publications</h2>
        <div class="title-accent"></div>
      </div>

      <p class="section-subtitle">Découvrez nos dernières actualités et articles</p>
    </div>

    <!-- Conteneur des publications avec design élégant -->
    <div class="company-posts-container">
      <div *ngIf="workspacePosts && workspacePosts.length > 0; else noPosts" class="posts-grid">
        <div class="company-post-card" *ngFor="let post of workspacePosts; let i = index" [ngClass]="{'featured-post': i === 0}">
          <div class="post-card-inner">
            <!-- Effet de bordure brillante -->
            <div class="card-border"></div>

            <!-- Image de couverture avec effet de profondeur -->
            <div class="post-cover">
              <div class="cover-overlay"></div>
              <div class="post-category">Actualité</div>
              <div class="post-date-badge">
                <div class="date-day">{{ post.createdAt | date:'dd' }}</div>
                <div class="date-month">{{ post.createdAt | date:'MMM' }}</div>
              </div>
            </div>

            <div class="post-content">
              <div class="post-meta">
                <div class="post-author">
                  <div class="author-avatar">
                    <mat-icon>business</mat-icon>
                  </div>
                  <span>{{ workspaceData?.name }}</span>
                </div>
                <div class="post-time">
                  <mat-icon>access_time</mat-icon>
                  <span>{{ post.createdAt | date:'HH:mm' }}</span>
                </div>
              </div>

              <h3 class="post-title">{{ post.title }}</h3>
              <div class="title-underline"></div>

              <p class="post-excerpt">{{ post.description }}</p>

              <div class="post-footer">
                <a class="read-more-btn" [routerLink]="['/post', post.id]">
                  <span class="btn-text">Lire l'article</span>
                  <div class="btn-icon-container">
                    <mat-icon>arrow_forward</mat-icon>
                  </div>
                </a>

                <div class="post-stats">
                  <div class="stat-item">
                    <mat-icon>visibility</mat-icon>
                    <span>{{ 120 + i * 45 }}</span>
                  </div>
                  <div class="stat-item">
                    <mat-icon>thumb_up</mat-icon>
                    <span>{{ 18 + i * 7 }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Message "Aucune publication" élégant -->
      <ng-template #noPosts>
        <div class="no-posts-container">
          <div class="no-posts-message">
            <div class="message-decoration top-left"></div>
            <div class="message-decoration top-right"></div>
            <div class="message-decoration bottom-left"></div>
            <div class="message-decoration bottom-right"></div>

            <div class="message-icon-container">
              <div class="icon-ring"></div>
              <div class="message-icon">
                <mat-icon>article</mat-icon>
              </div>
            </div>

            <h3>Aucune publication disponible</h3>
            <div class="message-divider">
              <div class="divider-line"></div>
              <div class="divider-circle"></div>
              <div class="divider-line"></div>
            </div>

            <p>Les publications de l'entreprise apparaîtront ici dès qu'elles seront publiées.</p>

            <button mat-flat-button class="create-post-btn">
              <div class="btn-content">
                <mat-icon>add</mat-icon>
                <span>Créer une publication</span>
              </div>
            </button>
          </div>
        </div>
      </ng-template>
    </div>

    <!-- Pagination élégante -->
    <div class="posts-pagination" *ngIf="workspacePosts && workspacePosts.length > 3">
      <div class="pagination-container">
        <button mat-mini-fab class="pagination-btn prev-btn">
          <mat-icon>chevron_left</mat-icon>
        </button>

        <div class="pagination-numbers">
          <div class="page-number active">
            <span>1</span>
          </div>
          <div class="page-number">
            <span>2</span>
          </div>
          <div class="page-number">
            <span>3</span>
          </div>
          <div class="page-dots">...</div>
        </div>

        <button mat-mini-fab class="pagination-btn next-btn">
          <mat-icon>chevron_right</mat-icon>
        </button>
      </div>
    </div>
  </div>

  <!-- Contact Section - Design Ultra-Professionnel -->
  <div class="contact-section">
    <div class="contact-background">
      <div class="bg-gradient"></div>
      <div class="bg-pattern"></div>
      <div class="bg-shapes">
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
        <div class="shape shape-3"></div>
        <div class="shape shape-4"></div>
      </div>
      <div class="animated-lines">
        <div class="line line-1"></div>
        <div class="line line-2"></div>
        <div class="line line-3"></div>
      </div>
      <div class="particles-container">
        <div class="particle particle-1"></div>
        <div class="particle particle-2"></div>
        <div class="particle particle-3"></div>
        <div class="particle particle-4"></div>
        <div class="particle particle-5"></div>
        <div class="particle particle-6"></div>
      </div>
    </div>

    <div class="contact-container">
      <div class="contact-content">
        <div class="contact-header">
          <div class="header-badge">Restons en contact</div>
          <h2 class="contact-title">Prêt à transformer votre entreprise?</h2>
          <div class="title-underline">
            <div class="underline-dot"></div>
          </div>
          <p class="contact-subtitle">Discutons de vos besoins et découvrez comment notre expertise peut vous aider à atteindre vos objectifs.</p>
        </div>

        <div class="contact-methods">
          <div class="contact-method">
            <div class="method-icon-container">
              <div class="method-icon-bg"></div>
              <mat-icon>email</mat-icon>
            </div>
            <h3>Email</h3>
            <p>{{ workspaceData?.email || '<EMAIL>' }}</p>
            <a href="mailto:{{ workspaceData?.email || '<EMAIL>' }}" class="method-link">
              <span>Envoyer un email</span>
              <mat-icon>arrow_forward</mat-icon>
            </a>
          </div>

          <div class="contact-method">
            <div class="method-icon-container">
              <div class="method-icon-bg"></div>
              <mat-icon>phone</mat-icon>
            </div>
            <h3>Téléphone</h3>
            <p>{{ workspaceData?.phoneNumber || '+33 1 23 45 67 89' }}</p>
            <a href="tel:{{ workspaceData?.phoneNumber || '+33123456789' }}" class="method-link">
              <span>Appeler maintenant</span>
              <mat-icon>arrow_forward</mat-icon>
            </a>
          </div>

          <div class="contact-method">
            <div class="method-icon-container">
              <div class="method-icon-bg"></div>
              <mat-icon>location_on</mat-icon>
            </div>
            <h3>Adresse</h3>
            <p>{{ workspaceData?.location || '123 Avenue des Champs-Élysées, Paris' }}</p>
            <a href="https://maps.google.com/?q={{ workspaceData?.location || 'Paris' }}" target="_blank" class="method-link">
              <span>Voir sur la carte</span>
              <mat-icon>arrow_forward</mat-icon>
            </a>
          </div>
        </div>

        <div class="contact-cta">
          <button mat-flat-button class="cta-button">
            <span>Prendre rendez-vous</span>
            <div class="button-icon">
              <mat-icon>event</mat-icon>
            </div>
          </button>

          <div class="social-links">
            <a href="#" class="social-link linkedin">
              <mat-icon>linkedin</mat-icon>
            </a>
            <a href="#" class="social-link twitter">
              <mat-icon>twitter</mat-icon>
            </a>
            <a href="#" class="social-link facebook">
              <mat-icon>facebook</mat-icon>
            </a>
          </div>
        </div>
      </div>


    </div>
  </div>

  <!-- Footer Section - Design Simple et Élégant -->
  <div class="footer-section">
    <div class="footer-content">
      <div class="footer-divider"></div>

      <div class="footer-main">
        <div class="copyright">
          <span class="copyright-text">© {{ currentYear }} {{ workspaceData?.name || 'Votre Espace de Travail' }}. Tous droits réservés.</span>
        </div>

        <div class="footer-social-links">
          <a href="{{ workspaceData?.linkedinUrl || '#' }}" class="footer-social-link" target="_blank" *ngIf="workspaceData?.linkedinUrl">
            <mat-icon>linkedin</mat-icon>
          </a>
          <a href="{{ workspaceData?.twitterUrl || '#' }}" class="footer-social-link" target="_blank" *ngIf="workspaceData?.twitterUrl">
            <mat-icon>twitter</mat-icon>
          </a>
          <a href="{{ workspaceData?.facebookUrl || '#' }}" class="footer-social-link" target="_blank" *ngIf="workspaceData?.facebookUrl">
            <mat-icon>facebook</mat-icon>
          </a>
          <a href="{{ workspaceData?.instagramUrl || '#' }}" class="footer-social-link" target="_blank" *ngIf="workspaceData?.instagramUrl">
            <mat-icon>photo_camera</mat-icon>
          </a>
        </div>

        <div class="footer-legal-links">
          <a href="#" class="legal-link">Conditions générales</a>
          <span class="link-separator">|</span>
          <a href="#" class="legal-link">Confidentialité</a>
        </div>
      </div>
    </div>
  </div>
