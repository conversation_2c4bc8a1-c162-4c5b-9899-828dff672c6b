/* ==========================================================================
   Profile Workspace - Professional Corporate Design
   ========================================================================== */

/* Import Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap');

/* Mixins for reusable styles */
@mixin card-shadow {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  transition: box-shadow 0.2s ease, transform 0.2s ease;

  &:hover {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06);
  }
}

@mixin section-container {
  padding: 50px 20px;
  position: relative;
}

@mixin content-width {
  max-width: 1200px;
  margin: 0 auto;
}

/* Variables SCSS */
$primary-blue: #001660;
$primary-blue-light: #2a3f8a;
$primary-blue-dark: #00104d;
$accent-orange: #F59E0B;
$accent-orange-light: #FBBF24;
$accent-orange-dark: #D97706;
$text-primary: #2D3748;
$text-secondary: #4A5568;
$text-light: #FFFFFF;
$bg-primary: #FFFFFF;
$bg-secondary: #F7FAFC;
$bg-tertiary: #EDF2F7;
$bg-card: #FFFFFF;
$border-color: #E2E8F0;
$border-focus: #001660;
$shadow-sm: 0 2px 4px rgba(0, 22, 96, 0.06), 0 1px 2px rgba(0, 22, 96, 0.04);
$shadow-md: 0 4px 8px rgba(0, 22, 96, 0.08), 0 2px 4px rgba(0, 22, 96, 0.06);
$shadow-lg: 0 10px 20px rgba(0, 22, 96, 0.1), 0 4px 8px rgba(0, 22, 96, 0.08);
$shadow-xl: 0 20px 25px rgba(0, 22, 96, 0.12), 0 10px 10px rgba(0, 22, 96, 0.08);
$radius-sm: 4px;
$radius-md: 8px;
$radius-lg: 12px;
$radius-xl: 20px;
$transition: 0.3s ease;

/* General Container */
.profile-container {
  width: 100%;
  min-height: 80vh;
  margin: 0;
  background: $bg-primary;
  font-family: 'Inter', sans-serif;
  overflow-x: hidden;
  color: $text-primary;
  position: relative;
  line-height: 1.6;
  letter-spacing: 0.01em;

  /* Ajouter un effet de fond subtil */
  background-image:
    linear-gradient(to bottom, rgba(247, 250, 252, 0.8) 0%, rgba(255, 255, 255, 1) 100%),
    url('data:image/svg+xml,%3Csvg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"%3E%3Cpath d="M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z" fill="%23001660" fill-opacity="0.02" fill-rule="evenodd"/%3E%3C/svg%3E');
}

/* Header Section */
.header-section {
  position: relative;
  padding: 80px 40px 80px;
  background: linear-gradient(135deg, $primary-blue 0%, $primary-blue-dark 100%);
  color: $text-light;
  box-shadow: $shadow-lg;
  z-index: 10;
  overflow: hidden;

  /* Effet de motif en arrière-plan */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('data:image/svg+xml,%3Csvg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"%3E%3Cpath d="M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5z" fill="%23FFFFFF" fill-opacity="0.05" fill-rule="evenodd"/%3E%3C/svg%3E');
    opacity: 0.5;
  }

  /* Accent orange en bas */
  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, $accent-orange-dark 0%, $accent-orange 50%, $accent-orange-light 100%);
    box-shadow: 0 -1px 10px rgba(245, 158, 11, 0.3);
  }

  /* Effet de vague en bas */
  .wave-effect {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 70px;
    background-image: url('data:image/svg+xml;utf8,<svg viewBox="0 0 1200 120" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"><path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z" opacity=".05" fill="%23FFFFFF"/><path d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z" opacity=".1" fill="%23FFFFFF"/><path d="M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z" opacity=".2" fill="%23FFFFFF"/></svg>');
    background-size: cover;
    background-position: center top;
    z-index: 1;
    pointer-events: none;
    transform: translateY(1px);
  }

  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    @include content-width;
    position: relative;
    z-index: 2;
    max-width: 1200px;
    margin: 0 auto;
  }

  .logo-container {
    background: $primary-blue;
    border-radius: 50%;
    width: 120px; /* Réduit de 140px à 120px */
    height: 120px; /* Réduit de 140px à 120px */
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: $shadow-lg;
    position: relative;
    overflow: hidden;
    border: 3px solid $accent-orange; /* Réduit de 4px à 3px */
    cursor: pointer;
    transition: $transition;
    margin-right: 30px; /* Réduit de 40px à 30px */
    z-index: 5;

    /* Effet de brillance */
    &::after {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0) 100%);
      transform: rotate(45deg);
      pointer-events: none;
      z-index: 2;
      animation: shine 3s infinite ease-in-out;
    }

    /* Effet de halo */
    &::before {
      content: '';
      position: absolute;
      top: -10px;
      left: -10px;
      right: -10px;
      bottom: -10px;
      border-radius: 50%;
      background: radial-gradient(circle, rgba(245, 158, 11, 0.2) 0%, rgba(245, 158, 11, 0) 70%);
      z-index: -1;
      animation: pulse 3s infinite ease-in-out;
    }

    @keyframes shine {
      0% {
        transform: rotate(45deg) translateX(-120%);
      }
      50% {
        transform: rotate(45deg) translateX(100%);
      }
      100% {
        transform: rotate(45deg) translateX(100%);
      }
    }

    @keyframes pulse {
      0% {
        opacity: 0.5;
        transform: scale(1);
      }
      50% {
        opacity: 0.7;
        transform: scale(1.05);
      }
      100% {
        opacity: 0.5;
        transform: scale(1);
      }
    }

    &:hover {
      box-shadow: $shadow-xl;
      transform: scale(1.05);
      border-color: $accent-orange-light;

      .logo-upload-hint {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .workspace-logo {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: $transition;
    }

    .logo-upload-hint {
      position: absolute;
      bottom: -40px;
      left: 50%;
      transform: translateX(-50%) translateY(5px);
      font-size: 13px;
      white-space: nowrap;
      color: $accent-orange;
      background: rgba(0, 22, 96, 0.9);
      padding: 6px 12px;
      border-radius: $radius-md;
      opacity: 0;
      transition: $transition;
      pointer-events: none;
      font-weight: 500;
      box-shadow: $shadow-sm;
      letter-spacing: 0.02em;
    }
  }


  .logo-container mat-icon {
    font-size: 50px; /* Réduit de 60px à 50px */
    color: $text-light;
    z-index: 2;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
    transition: $transition;
    opacity: 0.9;

    &:hover {
      color: $accent-orange-light;
      transform: scale(1.1);
      opacity: 1;
    }
  }

  .header-info {
    flex: 1;
    margin: 0 40px;
    position: relative;
    z-index: 3;
  }

  .workspace-name {
    font-family: 'Montserrat', sans-serif;
    font-size: 36px; /* Réduit de 42px à 36px */
    font-weight: 700;
    margin-bottom: 15px; /* Réduit de 20px à 15px */
    color: $text-light;
    position: relative;
    display: inline-block;
    letter-spacing: 0.02em;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    transition: $transition;
    background: linear-gradient(to right, $text-light, rgba(255, 255, 255, 0.85));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;

    &:hover {
      transform: translateY(-2px);
      text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
      background: linear-gradient(to right, $accent-orange-light, $text-light);
      -webkit-background-clip: text;
      background-clip: text;
    }
  }

  /* Orange accent under workspace name */
  .workspace-name::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 100px;
    height: 4px;
    background: linear-gradient(90deg, $accent-orange-dark, $accent-orange, $accent-orange-light);
    border-radius: $radius-sm;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: $transition;
    opacity: 0.8;
  }

  .workspace-name:hover::after {
    width: 100%;
    background: linear-gradient(90deg, $accent-orange-light, $accent-orange, $accent-orange-dark);
    opacity: 1;
    box-shadow: 0 4px 8px rgba(245, 158, 11, 0.3);
  }

  .contact-info-section {
    display: flex;
    flex-wrap: wrap;
    gap: 15px; /* Réduit de 20px à 15px */
    margin-top: 20px; /* Réduit de 30px à 20px */
    position: relative;
    z-index: 5;
  }

  .contact-info {
    display: flex;
    align-items: center;
    font-size: 15px;
    font-weight: 500;
    color: $text-light;
    background: rgba(0, 0, 0, 0.15);
    padding: 12px 20px;
    border-radius: $radius-md;
    margin-right: 12px;
    margin-bottom: 8px;
    transition: $transition;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border-left: 3px solid $accent-orange;
    backdrop-filter: blur(5px);
    letter-spacing: 0.02em;
    position: relative;
    overflow: hidden;

    /* Effet de brillance au survol */
    &::after {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.1) 50%, rgba(255, 255, 255, 0) 100%);
      transform: rotate(45deg);
      pointer-events: none;
      z-index: 2;
      opacity: 0;
      transition: opacity 0.5s ease;
    }

    &:hover {
      background: rgba(0, 0, 0, 0.25);
      transform: translateY(-3px);
      box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
      border-left: 3px solid $accent-orange-light;

      &::after {
        opacity: 1;
        animation: contactShine 1.5s ease-in-out;
      }
    }

    @keyframes contactShine {
      0% {
        transform: rotate(45deg) translateX(-120%);
      }
      100% {
        transform: rotate(45deg) translateX(120%);
      }
    }
  }

  .contact-info mat-icon {
    font-size: 18px; /* Réduit de 20px à 18px */
    margin-right: 10px; /* Réduit de 12px à 10px */
    color: $accent-orange;
    transition: $transition;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
  }

  .contact-info:hover mat-icon {
    color: $accent-orange-light;
    transform: scale(1.1);
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
  }

  .workspace-info {
    margin: 0 auto;
    padding: 60px 40px;
    max-width: 1200px;
    position: relative;
    z-index: 10;
    transform: translateY(-60px);
  }

  /* Container pour la description élégante */
  .elegant-description-container {
    position: relative;
    margin: 0 auto;

    /* Animation d'apparition au chargement */
    animation: fadeIn 1s ease-out forwards;

    @keyframes fadeIn {
      from {
        opacity: 0;
      }
      to {
        opacity: 1;
      }
    }
  }
}
  /* Style principal de la description élégante */
  .elegant-description {
    display: flex;
    background: $bg-primary;
    border-radius: $radius-lg;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 22, 96, 0.08), 0 5px 15px rgba(0, 22, 96, 0.04);
    position: relative;
    transition: all 0.5s cubic-bezier(0.25, 0.8, 0.25, 1);
    border: 1px solid rgba(0, 22, 96, 0.05);

    /* Effet de bordure subtile */
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border: 1px solid transparent;
      border-radius: $radius-lg;
      background: linear-gradient(135deg, $primary-blue, $accent-orange);
      -webkit-mask:
        linear-gradient(#fff 0 0) padding-box,
        linear-gradient(#fff 0 0) border-box;
      -webkit-mask-composite: xor;
      mask-composite: exclude;
      opacity: 0;
      transition: opacity 0.5s ease;
    }

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 15px 35px rgba(0, 22, 96, 0.12), 0 8px 20px rgba(0, 22, 96, 0.08);

      &::before {
        opacity: 1;
      }

      .vertical-line {
        height: 80%;
      }

      .title-underline {
        width: 100px;
      }

      .value-icon {
        transform: translateY(-5px);
        box-shadow: 0 8px 16px rgba(0, 22, 96, 0.15);
      }

      .signature-line {
        width: 120px;
      }
    }
  }

  /* Partie gauche avec logo */
  .description-left {
    width: 30%;
    background: linear-gradient(135deg, rgba(0, 22, 96, 0.02), rgba(0, 22, 96, 0.05));
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    position: relative;
  }

  /* Affichage du logo de l'entreprise */
  .company-logo-display {
    width: 180px;
    height: 180px;
    border-radius: 50%;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8px 20px rgba(0, 22, 96, 0.1);
    margin-bottom: 30px;
    border: 1px solid rgba(0, 22, 96, 0.05);
    overflow: hidden;
    transition: all 0.5s ease;

    &:hover {
      transform: scale(1.05);
      box-shadow: 0 12px 25px rgba(0, 22, 96, 0.15);
    }

    .company-logo-image {
      width: 100%;
      height: 100%;
      object-fit: contain;
      padding: 20px;
    }

    .company-logo-placeholder {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, $primary-blue-light 0%, $primary-blue 100%);

      mat-icon {
        font-size: 80px;
        color: white;
      }
    }
  }

  /* Ligne verticale décorative */
  .vertical-line {
    width: 2px;
    height: 60%;
    background: linear-gradient(to bottom, $primary-blue 0%, $accent-orange 100%);
    margin-top: 20px;
    transition: height 0.5s ease;
  }

  /* Partie droite avec contenu */
  .description-right {
    width: 70%;
    padding: 50px;
    display: flex;
    flex-direction: column;
  }

  /* En-tête de la description */
  .description-header {
    margin-bottom: 30px;

    .elegant-title {
      font-family: 'Montserrat', sans-serif;
      font-size: 32px;
      font-weight: 600;
      color: $primary-blue;
      margin: 0 0 15px 0;
      letter-spacing: 0.02em;
      position: relative;
    }

    .title-underline {
      width: 60px;
      height: 3px;
      background: linear-gradient(to right, $primary-blue 0%, $accent-orange 100%);
      transition: width 0.5s ease;
    }
  }

  /* Contenu de la description */
  .description-content {
    flex: 1;
    display: flex;
    flex-direction: column;

    .elegant-text {
      font-family: 'Inter', sans-serif;
      font-size: 16px;
      line-height: 1.8;
      color: $text-secondary;
      margin-bottom: 30px;
      text-align: justify;
      position: relative;

      &::first-letter {
        font-size: 2.8em;
        font-weight: 600;
        color: $primary-blue;
        float: left;
        line-height: 0.8;
        margin-right: 8px;
        margin-top: 8px;
      }
    }
  }

  /* Valeurs clés */
  .key-values {
    display: flex;
    justify-content: space-between;
    margin: 20px 0 40px;

    .value-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 30%;

      .value-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, $primary-blue 0%, $primary-blue-dark 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 15px;
        box-shadow: 0 5px 15px rgba(0, 22, 96, 0.1);
        transition: all 0.3s ease;

        mat-icon {
          font-size: 28px;
          color: white;
        }
      }

      &:nth-child(2) .value-icon {
        background: linear-gradient(135deg, $primary-blue-light 0%, $primary-blue 100%);
      }

      &:nth-child(3) .value-icon {
        background: linear-gradient(135deg, $accent-orange 0%, $accent-orange-dark 100%);
      }

      .value-text {
        font-size: 15px;
        font-weight: 600;
        color: $primary-blue;
        text-transform: uppercase;
        letter-spacing: 0.05em;
      }
    }
  }

  /* Signature */
  .signature-container {
    margin-top: auto;
    align-self: flex-end;
    display: flex;
    flex-direction: column;
    align-items: flex-end;

    .signature-line {
      width: 80px;
      height: 2px;
      background: $accent-orange;
      margin-bottom: 8px;
      transition: width 0.3s ease;
    }

    .signature-text {
      font-size: 14px;
      font-style: italic;
      color: $text-secondary;
    }
  }


  /* Workspace Description Section */
  .workspace-info-section {
    padding: 80px 60px;
    background: linear-gradient(to bottom, $bg-secondary, $bg-primary);
    text-align: center;
    position: relative;
    overflow: hidden;

    /* Effet de motif en arrière-plan */
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image: url('data:image/svg+xml,%3Csvg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"%3E%3Cpath d="M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5z" fill="%23001660" fill-opacity="0.02" fill-rule="evenodd"/%3E%3C/svg%3E');
      opacity: 0.5;
      z-index: 1;
    }

    .workspace-info {
      max-width: 1400px;
      margin: 0 auto;
      position: relative;
      z-index: 2;

      .workspace-description {
        font-size: 18px;
        color: $text-primary;
        max-width: 900px;
        margin: 0 auto;
        padding: 30px 40px;
        background: $bg-primary;
        border-radius: $radius-lg;
        box-shadow: $shadow-lg;
        border-bottom: 4px solid $accent-orange;
        text-align: justify;
        line-height: 1.8;
        letter-spacing: 0.01em;
        transition: $transition;

        &:hover {
          transform: translateY(-5px);
          box-shadow: $shadow-xl;
        }

        p:first-of-type::first-letter {
          font-size: 2em;
          font-weight: 600;
          color: $primary-blue;
          float: left;
          padding-right: 8px;
          line-height: 0.8;
        }
      }
    }
  }

  /* Section Title */
  .section-title {
    font-family: 'Montserrat', sans-serif;
    font-size: 32px;
    font-weight: 700;
    color: $primary-blue;
    text-align: center;
    margin: 60px 0 50px;
    position: relative;
    display: inline-block;
    letter-spacing: 0.02em;
    text-transform: uppercase;
    transition: $transition;
    padding-bottom: 12px;
    background: linear-gradient(to right, $primary-blue, $primary-blue-light);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

    &:hover {
      transform: translateY(-3px);
      background: linear-gradient(to right, $primary-blue-dark, $accent-orange);
      -webkit-background-clip: text;
      background-clip: text;

      &::after {
        width: 120px;
        background: linear-gradient(90deg, $accent-orange-light, $accent-orange, $accent-orange-dark);
        box-shadow: 0 2px 6px rgba(245, 158, 11, 0.3);
      }
    }
  }

  /* Decorative element for section title */
  .section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, $accent-orange-dark, $accent-orange, $accent-orange-light);
    border-radius: $radius-sm;
    transition: $transition;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    opacity: 0.9;
  }

  /* Section title container */
  .section-title-container {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 60px 0 40px;

    &::before, &::after {
      content: '';
      position: absolute;
      top: 50%;
      width: 80px;
      height: 1px;
      background: linear-gradient(90deg, transparent, $border-color);
    }

    &::before {
      left: -100px;
    }

    &::after {
      right: -100px;
      transform: rotate(180deg);
    }
  }

  @keyframes pulse {
    0% {
      opacity: 0.6;
      width: 30px;
    }
    50% {
      opacity: 1;
      width: 50px;
    }
    100% {
      opacity: 0.6;
      width: 30px;
    }
  }

  /* Statements Section */
  .statements-section {
    @include section-container;
    background: linear-gradient(135deg, rgba(0, 22, 96, 0.02) 0%, rgba(0, 22, 96, 0.05) 100%);
    padding: 100px 0 120px;
    position: relative;
    overflow: hidden;
    border-top: 1px solid rgba(0, 22, 96, 0.05);
    border-bottom: 1px solid rgba(0, 22, 96, 0.05);
  }

  /* Formes d'arrière-plan */
  .statements-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    overflow: hidden;

    .bg-shape {
      position: absolute;
      border-radius: 50%;
      opacity: 0.03;
      background: $primary-blue;

      &.shape-1 {
        width: 500px;
        height: 500px;
        top: -250px;
        left: -100px;
        animation: float-slow 20s ease-in-out infinite;
      }

      &.shape-2 {
        width: 300px;
        height: 300px;
        bottom: -150px;
        right: 10%;
        background: $accent-orange;
        animation: float-slow 15s ease-in-out infinite 2s;
      }

      &.shape-3 {
        width: 200px;
        height: 200px;
        top: 30%;
        right: -100px;
        background: $primary-blue-light;
        animation: float-slow 18s ease-in-out infinite 1s;
      }
    }

    @keyframes float-slow {
      0%, 100% {
        transform: translate(0, 0);
      }
      25% {
        transform: translate(-20px, 20px);
      }
      50% {
        transform: translate(10px, -15px);
      }
      75% {
        transform: translate(15px, 10px);
      }
    }
  }

  /* En-tête de section */
  .section-header {
    text-align: center;
    margin-bottom: 70px;
    position: relative;
    z-index: 2;
  }

  .section-title-container {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;

    .title-accent {
      width: 60px;
      height: 2px;
      background: linear-gradient(90deg, transparent, $primary-blue, transparent);
      margin: 0 20px;
    }
  }

  .section-title {
    font-family: 'Montserrat', sans-serif;
    font-size: 36px;
    font-weight: 700;
    color: $primary-blue;
    margin: 0;
    position: relative;
    display: inline-block;
    letter-spacing: 0.02em;
    text-transform: uppercase;
    background: linear-gradient(135deg, $primary-blue, $primary-blue-light);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .section-subtitle {
    font-size: 18px;
    color: $text-secondary;
    max-width: 600px;
    margin: 0 auto;
    font-style: italic;
  }

  /* Container des éléments */
  .statements-container {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 50px;
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
  }

  /* Style des éléments */
  .statement-item {
    background: $bg-card;
    width: 340px;
    border-radius: $radius-lg;
    overflow: hidden;
    transition: all 0.5s cubic-bezier(0.25, 0.8, 0.25, 1);
    box-shadow: 0 15px 35px rgba(0, 22, 96, 0.08), 0 5px 15px rgba(0, 22, 96, 0.04);
    position: relative;
    display: flex;
    flex-direction: column;

    /* Effet de bordure */
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border: 2px solid transparent;
      border-radius: $radius-lg;
      background: linear-gradient(135deg, $primary-blue, $accent-orange) border-box;
      -webkit-mask:
        linear-gradient(#fff 0 0) padding-box,
        linear-gradient(#fff 0 0) border-box;
      -webkit-mask-composite: xor;
      mask-composite: exclude;
      opacity: 0;
      transition: opacity 0.5s ease;
      pointer-events: none;
      z-index: 3;
    }

    &:hover {
      transform: translateY(-15px);
      box-shadow: 0 30px 60px rgba(0, 22, 96, 0.12), 0 15px 30px rgba(0, 22, 96, 0.08);

      &::before {
        opacity: 1;
      }

      .statement-icon-container {
        transform: translateY(-5px);

        .icon-background {
          transform: scale(1.2);
          opacity: 1;
        }

        mat-icon {
          transform: scale(1.1);
        }
      }

      .statement-divider {
        width: 80px;
        background: $accent-orange;
      }

      .statement-number {
        color: $accent-orange;
        transform: translateY(-5px);
      }

      .value-tag {
        transform: translateY(-3px);
        box-shadow: 0 5px 10px rgba(0, 22, 96, 0.1);
      }
    }

    &.vision-item .icon-background {
      background: linear-gradient(135deg, $primary-blue, $primary-blue-light);
    }

    &.mission-item .icon-background {
      background: linear-gradient(135deg, $primary-blue-light, $primary-blue);
    }

    &.values-item .icon-background {
      background: linear-gradient(135deg, $accent-orange, $accent-orange-dark);
    }
  }

  /* Conteneur d'icône */
  .statement-icon-container {
    width: 100px;
    height: 100px;
    margin: -50px auto 20px;
    position: relative;
    z-index: 2;
    transition: all 0.5s ease;

    .icon-background {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border-radius: 50%;
      opacity: 0.9;
      transition: all 0.5s ease;
      box-shadow: 0 10px 20px rgba(0, 22, 96, 0.15);
    }

    mat-icon {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-size: 40px;
      color: white;
      z-index: 2;
      transition: all 0.5s ease;
    }
  }

  /* Contenu */
  .statement-content {
    padding: 20px 30px 30px;
    flex: 1;

    h3 {
      font-family: 'Montserrat', sans-serif;
      font-size: 24px;
      font-weight: 600;
      color: $primary-blue;
      margin: 0 0 15px;
      letter-spacing: 0.02em;
    }

    .statement-divider {
      width: 50px;
      height: 3px;
      background: $primary-blue;
      margin: 0 auto 20px;
      transition: all 0.5s ease;
    }

    p {
      font-size: 16px;
      line-height: 1.7;
      color: $text-secondary;
      margin: 0;
      text-align: center;
    }

    .values-list {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      gap: 10px;
      margin-top: 5px;
    }

    .value-tag {
      background: rgba(0, 22, 96, 0.05);
      color: $primary-blue;
      padding: 8px 15px;
      border-radius: 20px;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.3s ease;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);

      &:nth-child(2) {
        background: rgba(42, 63, 138, 0.05);
        transition-delay: 0.1s;
      }

      &:nth-child(3) {
        background: rgba(245, 158, 11, 0.05);
        transition-delay: 0.2s;
      }

      &:nth-child(4) {
        background: rgba(0, 16, 77, 0.05);
        transition-delay: 0.3s;
      }
    }
  }

  /* Pied de page */
  .statement-footer {
    padding: 15px;
    border-top: 1px solid rgba(0, 22, 96, 0.05);
    background: rgba(0, 22, 96, 0.02);

    .statement-number {
      font-family: 'Montserrat', sans-serif;
      font-size: 24px;
      font-weight: 700;
      color: $primary-blue;
      opacity: 0.5;
      text-align: center;
      transition: all 0.3s ease;
    }
  }

  @keyframes shine {
    0% {
      transform: rotate(45deg) translateX(-120%);
    }
    100% {
      transform: rotate(45deg) translateX(120%);
    }
  }

  .statement-item mat-icon {
    font-size: 56px;
    color: $primary-blue;
    margin-bottom: 25px;
    transition: $transition;
    filter: drop-shadow(0 2px 4px rgba(0, 22, 96, 0.1));
    opacity: 0.9;
    background: linear-gradient(135deg, $primary-blue, $primary-blue-light);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .statement-item:hover mat-icon {
    color: $accent-orange;
    background: linear-gradient(135deg, $accent-orange, $accent-orange-dark);
    -webkit-background-clip: text;
    background-clip: text;
    opacity: 1;
  }

  .statement-item h3 {
    font-family: 'Montserrat', sans-serif;
    font-size: 22px;
    margin-bottom: 18px;
    color: $primary-blue;
    font-weight: 600;
    letter-spacing: 0.01em;
    transition: $transition;
    position: relative;
    display: inline-block;
    padding-bottom: 12px;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 40px;
      height: 3px;
      background: $primary-blue;
      transition: $transition;
      border-radius: $radius-sm;
      opacity: 0.8;
    }
  }

  .statement-item:hover h3 {
    color: $accent-orange;
    transform: translateY(-2px);

    &::after {
      width: 70px;
      background: $accent-orange;
      opacity: 1;
      box-shadow: 0 1px 3px rgba(245, 158, 11, 0.2);
    }
  }

  .statement-item p {
    font-size: 16px;
    color: $text-secondary;
    line-height: 1.7;
    margin-bottom: 0;
    transition: $transition;
    position: relative;
    z-index: 3;
  }

  /* Services Section */
  .services-section {
    @include section-container;
    background: linear-gradient(to bottom, $bg-primary, $bg-secondary);
    border-bottom: 1px solid $border-color;
    padding: 80px 0;
    position: relative;
    overflow: hidden;

    /* Effet de motif en arrière-plan */
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image: url('data:image/svg+xml,%3Csvg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"%3E%3Cpath d="M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5z" fill="%23001660" fill-opacity="0.02" fill-rule="evenodd"/%3E%3C/svg%3E');
      opacity: 0.5;
      z-index: 1;
    }
  }


  .services-container {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 40px;
    @include content-width;
    position: relative;
    z-index: 2;
    max-width: 1200px;
    margin: 0 auto;
  }

  .service-item {
    background: $bg-card;
    width: 340px;
    padding: 35px;
    border-radius: $radius-lg;
    text-align: center;
    transition: $transition;
    box-shadow: $shadow-md;
    position: relative;
    border: 1px solid $border-color;
    border-top: 5px solid $primary-blue;
    overflow: hidden;
    transform: translateZ(0); /* Force hardware acceleration */

    /* Effet de brillance */
    &::after {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.1) 50%, rgba(255, 255, 255, 0) 100%);
      transform: rotate(45deg);
      pointer-events: none;
      z-index: 2;
      transition: $transition;
      opacity: 0;
    }

    /* Effet de halo */
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: radial-gradient(circle at center, rgba(245, 158, 11, 0.02) 0%, rgba(245, 158, 11, 0) 70%);
      opacity: 0;
      transition: opacity 0.5s ease;
      z-index: 1;
    }

    &:hover {
      transform: translateY(-12px);
      box-shadow: $shadow-xl;
      border-top-color: $accent-orange;

      &::after {
        opacity: 1;
        animation: shine 1.5s ease-in-out;
      }

      &::before {
        opacity: 1;
      }

      mat-icon {
        color: $accent-orange;
        transform: scale(1.1);
        filter: drop-shadow(0 4px 8px rgba(245, 158, 11, 0.3));
      }
    }
  }


  .service-item mat-icon {
    font-size: 56px;
    color: $primary-blue;
    margin-bottom: 25px;
    transition: $transition;
    filter: drop-shadow(0 2px 4px rgba(0, 22, 96, 0.1));
    opacity: 0.9;
    background: linear-gradient(135deg, $primary-blue, $primary-blue-light);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .service-item:hover mat-icon {
    color: $accent-orange;
    background: linear-gradient(135deg, $accent-orange, $accent-orange-dark);
    -webkit-background-clip: text;
    background-clip: text;
    opacity: 1;
    filter: drop-shadow(0 4px 8px rgba(245, 158, 11, 0.3));
  }

  .service-item h3 {
    font-family: 'Montserrat', sans-serif;
    font-size: 22px;
    margin-bottom: 18px;
    color: $primary-blue;
    font-weight: 600;
    letter-spacing: 0.01em;
    transition: $transition;
    position: relative;
    display: inline-block;
    padding-bottom: 12px;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 40px;
      height: 3px;
      background: $primary-blue;
      transition: $transition;
      border-radius: $radius-sm;
      opacity: 0.8;
    }
  }

  .service-item:hover h3 {
    color: $accent-orange;
    transform: translateY(-2px);

    &::after {
      width: 70px;
      background: $accent-orange;
      opacity: 1;
      box-shadow: 0 1px 3px rgba(245, 158, 11, 0.2);
    }
  }

  .service-item p {
    font-size: 15px;
    color: $text-secondary;
    line-height: 1.6;
    margin-bottom: 20px;
    transition: $transition;
    text-align: justify;
  }

  /* Company Posts Section - Style Élégant */
  .company-posts-section {
    @include section-container;
    background: linear-gradient(135deg, rgba(0, 22, 96, 0.01) 0%, rgba(0, 22, 96, 0.03) 100%);
    padding: 100px 20px;
    position: relative;
    overflow: hidden;
    border-top: 1px solid rgba(0, 22, 96, 0.05);
    border-bottom: 1px solid rgba(0, 22, 96, 0.05);
  }

  /* Arrière-plan avec motif, dégradé et formes flottantes */
  .posts-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    overflow: hidden;

    .bg-pattern {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      opacity: 0.02;
      background-image:
        radial-gradient(circle at 25% 25%, $primary-blue 1px, transparent 1px),
        radial-gradient(circle at 75% 75%, $primary-blue 1px, transparent 1px);
      background-size: 40px 40px;
    }

    .bg-gradient-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: radial-gradient(circle at 10% 90%, rgba(0, 22, 96, 0.03) 0%, transparent 60%);
    }

    .floating-shapes {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;

      .floating-shape {
        position: absolute;
        border-radius: 50%;
        opacity: 0.03;
        filter: blur(2px);

        &.shape-1 {
          width: 300px;
          height: 300px;
          top: -150px;
          left: 10%;
          background: $primary-blue;
          animation: float-slow 25s ease-in-out infinite;
        }

        &.shape-2 {
          width: 200px;
          height: 200px;
          bottom: -100px;
          right: 15%;
          background: $accent-orange;
          animation: float-slow 20s ease-in-out infinite 2s;
        }

        &.shape-3 {
          width: 150px;
          height: 150px;
          top: 30%;
          right: 5%;
          background: $primary-blue-light;
          animation: float-slow 15s ease-in-out infinite 1s;
        }
      }
    }
  }

  /* Animation de flottement lent */
  @keyframes float-slow {
    0%, 100% {
      transform: translate(0, 0);
    }
    25% {
      transform: translate(-20px, 20px);
    }
    50% {
      transform: translate(10px, -15px);
    }
    75% {
      transform: translate(15px, 10px);
    }
  }

  /* Animation de brillance */
  @keyframes shine {
    0% {
      left: -100%;
      opacity: 0;
    }
    20% {
      opacity: 0.1;
    }
    100% {
      left: 100%;
      opacity: 0;
    }
  }

  /* En-tête de la section */
  .company-posts-section .section-header {
    text-align: center;
    margin-bottom: 70px;
    position: relative;
    z-index: 2;

    .section-badge {
      display: inline-block;
      background: linear-gradient(135deg, $primary-blue-light, $primary-blue);
      color: white;
      font-size: 12px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.1em;
      padding: 6px 15px;
      border-radius: 20px;
      margin-bottom: 20px;
      box-shadow: 0 5px 15px rgba(0, 22, 96, 0.15);
      position: relative;
      overflow: hidden;

      .badge-shine {
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0) 100%);
        transform: rotate(45deg);
        animation: shine 3s infinite;
      }
    }

    .section-title-container {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 15px;

      .title-accent {
        width: 60px;
        height: 2px;
        background: linear-gradient(90deg, transparent, $accent-orange, transparent);
        margin: 0 20px;
      }
    }

    .section-title {
      font-family: 'Montserrat', sans-serif;
      font-size: 36px;
      font-weight: 700;
      color: $primary-blue;
      margin: 0;
      position: relative;
      display: inline-block;
      letter-spacing: 0.02em;
      text-transform: uppercase;
      background: linear-gradient(135deg, $primary-blue, $primary-blue-light);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .section-subtitle {
      font-size: 18px;
      color: $text-secondary;
      max-width: 600px;
      margin: 0 auto;
      font-style: italic;
    }
  }


  /* Conteneur des publications */
  .company-posts-container {
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
  }

  /* Grille de publications */
  .posts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
    margin-bottom: 50px;
  }

  /* Publication mise en avant (première publication) */
  .featured-post {
    grid-column: 1 / -1;

    .post-card-inner {
      display: flex;
      flex-direction: row;
      height: 300px;
    }

    .post-cover {
      width: 40%;
      height: 100%;
    }

    .post-content {
      width: 60%;
      padding: 30px;
    }

    .post-title {
      font-size: 28px;
      margin-bottom: 20px;
    }

    .post-excerpt {
      font-size: 16px;
      margin-bottom: 25px;
      max-height: 80px;
    }
  }

  /* Carte de publication */
  .company-post-card {
    height: 450px;
    perspective: 1000px;
    position: relative;

    &:hover .post-card-inner {
      transform: translateY(-10px);
      box-shadow: 0 20px 40px rgba(0, 22, 96, 0.12), 0 10px 20px rgba(0, 22, 96, 0.08);
    }

    &:hover .card-border {
      opacity: 1;
    }
  }

  .post-card-inner {
    position: relative;
    width: 100%;
    height: 100%;
    text-align: left;
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    display: flex;
    flex-direction: column;
    background: white;
    border-radius: $radius-lg;
    overflow: hidden;
    box-shadow: 0 15px 35px rgba(0, 22, 96, 0.08), 0 5px 15px rgba(0, 22, 96, 0.05);
    border: 1px solid rgba(0, 22, 96, 0.05);
    z-index: 1;
  }

  /* Effet de bordure brillante */
  .card-border {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: $radius-lg;
    padding: 1px;
    background: linear-gradient(135deg, $primary-blue, $accent-orange, $primary-blue-light, $accent-orange-light);
    background-size: 300% 300%;
    animation: gradient-shift 8s ease infinite;
    opacity: 0;
    transition: opacity 0.5s ease;
    z-index: 0;
  }

  @keyframes gradient-shift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
  }

  /* Image de couverture */
  .post-cover {
    position: relative;
    height: 200px;
    background: linear-gradient(135deg, $primary-blue-light, $primary-blue);
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect x="0" y="0" width="50" height="50" fill="%23FFFFFF" fill-opacity="0.05"/><rect x="50" y="50" width="50" height="50" fill="%23FFFFFF" fill-opacity="0.05"/></svg>');
      background-size: 20px 20px;
      opacity: 0.3;
    }

    .cover-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: radial-gradient(circle at center, rgba(0, 22, 96, 0) 0%, rgba(0, 22, 96, 0.3) 100%);
      z-index: 1;
    }
  }

  /* Catégorie de publication */
  .post-category {
    position: absolute;
    top: 15px;
    left: 15px;
    background: linear-gradient(135deg, $accent-orange, $accent-orange-dark);
    color: white;
    padding: 6px 14px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.08em;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    z-index: 2;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 15px rgba(0, 0, 0, 0.25);
    }
  }

  /* Badge de date */
  .post-date-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 2;
    text-align: center;
    width: 55px;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px) scale(1.05);
      box-shadow: 0 6px 15px rgba(0, 0, 0, 0.2);
    }

    .date-day {
      font-size: 20px;
      font-weight: 700;
      color: $primary-blue;
      padding: 6px 0 2px;
      font-family: 'Montserrat', sans-serif;
    }

    .date-month {
      font-size: 11px;
      font-weight: 600;
      color: white;
      background: linear-gradient(90deg, $primary-blue, $primary-blue-dark);
      padding: 3px 0;
      text-transform: uppercase;
      letter-spacing: 0.05em;
    }
  }

  /* Contenu de la publication */
  .post-content {
    flex: 1;
    padding: 25px;
    display: flex;
    flex-direction: column;
  }

  /* Méta-informations */
  .post-meta {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    font-size: 13px;
    color: $text-secondary;

    .post-author, .post-time {
      display: flex;
      align-items: center;

      mat-icon {
        font-size: 16px;
        margin-right: 5px;
        color: $primary-blue;
      }
    }

    .author-avatar {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background: rgba(0, 22, 96, 0.05);
      margin-right: 8px;

      mat-icon {
        font-size: 14px;
        margin-right: 0;
      }
    }
  }

  /* Titre de la publication */
  .post-title {
    font-family: 'Montserrat', sans-serif;
    font-size: 20px;
    font-weight: 600;
    line-height: 1.4;
    color: $primary-blue;
    margin: 0 0 15px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    transition: color 0.3s ease;

    &:hover {
      color: $accent-orange;
    }
  }

  .title-underline {
    width: 40px;
    height: 3px;
    background: $accent-orange;
    margin-bottom: 15px;
    transition: width 0.3s ease;
  }

  .post-card-inner:hover .title-underline {
    width: 60px;
  }

  /* Extrait de la publication */
  .post-excerpt {
    font-size: 15px;
    line-height: 1.6;
    color: $text-secondary;
    margin-bottom: 20px;
    flex: 1;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-align: justify;
  }

  /* Pied de page de la publication */
  .post-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
    padding-top: 15px;
    border-top: 1px solid rgba(0, 22, 96, 0.05);
  }

  /* Bouton "Lire plus" */
  .read-more-btn {
    display: flex;
    align-items: center;
    color: $primary-blue;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 14px;

    .btn-icon-container {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background: rgba(0, 22, 96, 0.05);
      margin-left: 8px;
      transition: all 0.3s ease;

      mat-icon {
        font-size: 16px;
        transition: all 0.3s ease;
      }
    }

    &:hover {
      color: $accent-orange;

      .btn-icon-container {
        background: $accent-orange;
        transform: translateX(3px);

        mat-icon {
          color: white;
        }
      }
    }
  }

  /* Statistiques de la publication */
  .post-stats {
    display: flex;
    gap: 15px;

    .stat-item {
      display: flex;
      align-items: center;
      color: $text-secondary;
      font-size: 13px;

      mat-icon {
        font-size: 16px;
        margin-right: 4px;
        color: $primary-blue-light;
      }
    }
  }

  /* Message "Aucune publication" */
  .no-posts-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
  }

  .no-posts-message {
    background: white;
    border-radius: $radius-lg;
    padding: 50px;
    text-align: center;
    box-shadow: 0 15px 35px rgba(0, 22, 96, 0.08), 0 5px 15px rgba(0, 22, 96, 0.05);
    max-width: 500px;
    border: 1px solid rgba(0, 22, 96, 0.05);
    position: relative;

    .message-decoration {
      position: absolute;
      width: 30px;
      height: 30px;
      border: 2px solid rgba(0, 22, 96, 0.05);

      &.top-left {
        top: 15px;
        left: 15px;
        border-right: none;
        border-bottom: none;
      }

      &.top-right {
        top: 15px;
        right: 15px;
        border-left: none;
        border-bottom: none;
      }

      &.bottom-left {
        bottom: 15px;
        left: 15px;
        border-right: none;
        border-top: none;
      }

      &.bottom-right {
        bottom: 15px;
        right: 15px;
        border-left: none;
        border-top: none;
      }
    }

    .message-icon-container {
      position: relative;
      width: 80px;
      height: 80px;
      margin: 0 auto 25px;

      .icon-ring {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        border: 2px dashed rgba(0, 22, 96, 0.1);
        animation: spin 20s linear infinite;
      }

      .message-icon {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: rgba(0, 22, 96, 0.05);
        display: flex;
        align-items: center;
        justify-content: center;

        mat-icon {
          font-size: 30px;
          color: $primary-blue;
        }
      }
    }

    h3 {
      font-family: 'Montserrat', sans-serif;
      font-size: 22px;
      font-weight: 600;
      color: $primary-blue;
      margin: 0 0 15px;
    }

    .message-divider {
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 15px 0;

      .divider-line {
        width: 40px;
        height: 1px;
        background: rgba(0, 22, 96, 0.1);
      }

      .divider-circle {
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background: $accent-orange;
        margin: 0 10px;
      }
    }

    p {
      font-size: 16px;
      line-height: 1.6;
      color: $text-secondary;
      margin: 0 0 25px;
    }

    .create-post-btn {
      background: $primary-blue;
      color: white;
      padding: 10px 20px;
      border-radius: 30px;
      font-weight: 500;
      transition: all 0.3s ease;
      border: none;
      position: relative;
      overflow: hidden;

      .btn-content {
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        z-index: 1;

        mat-icon {
          margin-right: 8px;
        }
      }

      &:hover {
        background: $accent-orange;
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
      }
    }
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  /* Pagination */
  .posts-pagination {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 40px;
    position: relative;
    z-index: 2;

    .pagination-container {
      display: flex;
      align-items: center;
      background: white;
      border-radius: 40px;
      padding: 5px;
      box-shadow: 0 10px 25px rgba(0, 22, 96, 0.08);
      position: relative;
    }

    .pagination-btn {
      width: 36px;
      height: 36px;
      background: transparent;
      color: $primary-blue;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;

      &:hover {
        background: $primary-blue;
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 22, 96, 0.2);
      }

      mat-icon {
        font-size: 20px;
        position: relative;
        z-index: 1;
      }
    }

    .pagination-numbers {
      display: flex;
      align-items: center;
      margin: 0 10px;

      .page-number {
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        font-weight: 500;
        color: $text-secondary;
        border-radius: 50%;
        margin: 0 5px;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;

        span {
          position: relative;
          z-index: 1;
        }

        &:hover {
          color: $primary-blue;

          &::before {
            opacity: 0.1;
            transform: scale(1);
          }
        }

        &.active {
          color: white;

          &::before {
            opacity: 1;
            transform: scale(1);
            background: $primary-blue;
          }
        }

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          border-radius: 50%;
          background: $primary-blue;
          opacity: 0;
          transform: scale(0.5);
          transition: all 0.3s ease;
        }
      }

      .page-dots {
        margin: 0 5px;
        color: $text-secondary;
      }
    }
  }

  .company-post-item {
    width: 360px;
    background: $bg-card;
    border-radius: $radius-md;
    overflow: hidden;
    @include card-shadow;
    border: 1px solid $border-color;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 2px;
      background: $primary-blue;
      transition: all $transition;
    }

    &:hover::after {
      background: $accent-orange;
    }
  }

  .company-post-item .company-logo {
    width: 100%;
    height: 160px;
    object-fit: contain;
    padding: 20px;
    background: $bg-secondary;
    border-bottom: 1px solid $border-color;
  }

  .company-post-item .post-content {
    padding: 20px;
  }

  .company-post-item .company-name {
    font-family: 'Roboto', sans-serif;
    font-size: 16px;
    color: $primary-blue;
    font-weight: 500;
    margin-bottom: 8px;
  }

  .company-post-item .post-title {
    font-size: 15px;
    font-weight: 500;
    margin-bottom: 10px;
    color: $text-primary;
  }

  .company-post-item .post-excerpt {
    font-size: 14px;
    color: $text-secondary;
    line-height: 1.5;
    margin-bottom: 15px;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .company-post-item .post-date {
    font-size: 12px;
    color: $text-secondary;
    display: flex;
    align-items: center;
  }

  .company-post-item .post-date mat-icon {
    font-size: 14px;
    margin-right: 4px;
    color: $accent-orange;
  }

  .company-post-item .read-more {
    display: inline-flex;
    align-items: center;
    color: $primary-blue;
    font-size: 14px;
    font-weight: 500;
    margin-top: 10px;
    transition: all $transition;
  }

  .company-post-item .read-more mat-icon {
    font-size: 16px;
    margin-left: 4px;
    transition: transform $transition;
  }

  .company-post-item:hover .read-more {
    color: $accent-orange;
  }

  .company-post-item:hover .read-more mat-icon {
    transform: translateX(3px);
  }

  /* Testimonials Section */
  .testimonials-section {
    @include section-container;
    background: $bg-secondary;
    border-top: 1px solid $border-color;
    border-bottom: 1px solid $border-color;
  }


  .testimonials-container {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 30px;
    @include content-width;
  }

  .testimonial-item {
    background: $bg-card;
    padding: 25px;
    border-radius: $radius-md;
    max-width: 400px;
    text-align: center;
    @include card-shadow;
    border: 1px solid $border-color;
    position: relative;
  }


  .testimonial-item p {
    font-style: italic;
    color: $text-primary;
    margin-bottom: 15px;
    font-size: 14px;
    line-height: 1.6;
  }

  .testimonial-author {
    font-weight: 500;
    color: $accent-orange;
    font-size: 14px;
    position: relative;
    display: inline-block;
    padding-top: 8px;
  }

  .testimonial-author::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 2px;
    background: $primary-blue;
  }

  /* Contact Section */
  .contact-section {
    @include section-container;
    background: $bg-primary;
    text-align: center;
    border-top: 1px solid $border-color;
  }


  .contact-section p {
    font-size: 16px;
    color: $text-primary;
    margin-bottom: 25px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
  }

  .contact-section button {
    background: $primary-blue;
    color: $text-light;
    padding: 12px 30px;
    font-size: 15px;
    border: none;
    border-radius: $radius-sm;
    cursor: pointer;
    font-weight: 500;
    transition: all $transition;
    box-shadow: $shadow-sm;
    position: relative;
  }


  .contact-section button:hover {
    background: $primary-blue-light;
    box-shadow: $shadow-md;
  }

  /* Footer Section */
  .footer-section {
    background: $primary-blue;
    color: $text-light;
    text-align: center;
    padding: 25px;
    font-size: 14px;
    position: relative;
  }

  .footer-section::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at top right, rgba(245, 158, 11, 0.05) 0%, transparent 50%),
    radial-gradient(circle at bottom left, rgba(245, 158, 11, 0.05) 0%, transparent 50%);
    pointer-events: none;
  }

  /* Animations */
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes popIn {
    0% {
      transform: scale(0.7);
      opacity: 0;
    }
    70% {
      transform: scale(1.1);
      opacity: 1;
    }
    100% {
      transform: scale(1);
      opacity: 1;
    }
  }

  @keyframes pulse {
    0% {
      opacity: 0.6;
      transform: scale(1);
    }
    50% {
      opacity: 1;
      transform: scale(1.05);
    }
    100% {
      opacity: 0.6;
      transform: scale(1);
    }
  }

  @keyframes shine {
    0% {
      transform: translateX(-100%);
      opacity: 0;
    }
    20% {
      opacity: 0.5;
    }
    80% {
      opacity: 0.5;
    }
    100% {
      transform: translateX(100%);
      opacity: 0;
    }
  }

  @keyframes float {
    0% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
    100% {
      transform: translateY(0px);
    }
  }

  /* Responsive Design */
  @media (max-width: 1024px) {
    .header-section {
      padding: 120px 30px 40px;

      .header-content {
        flex-direction: column;
        align-items: center;
        text-align: center;

        .header-info {
          margin: 20px 0;

          .workspace-name {
            font-size: 28px;
          }

          .contact-info-section {
            justify-content: center;
          }
        }

        .join-button {
          margin-top: 10px;
        }
      }
    }

    .workspace-info-section {
      padding: 40px 30px;
    }

    .statements-container,
    .services-container,
    .projects-container,
    .testimonials-container {
      gap: 30px;
    }

    .statement-item,
    .service-item,
    .project-item,
    .testimonial-item {
      width: 100%;
      max-width: 400px;
    }
  }

  @media (max-width: 768px) {
    .header-section {
      padding: 100px 20px 30px;

      .header-content {
        .logo-container {
          width: 60px;
          height: 60px;

          mat-icon {
            font-size: 32px;
          }
        }

        .header-info {
          .workspace-name {
            font-size: 24px;
          }

          .contact-info-section {
            gap: 15px;

            .contact-info {
              font-size: 12px;

              mat-icon {
                font-size: 18px;
              }
            }
          }
        }

        .join-button button {
          padding: 8px 20px;
          font-size: 14px;
        }
      }
    }

    .workspace-info-section {
      padding: 30px 20px;

      .workspace-description {
        font-size: 16px;
      }
    }

    .section-title {
      font-size: 28px;
    }

    .statement-item,
    .service-item,
    .project-item,
    .testimonial-item {
      max-width: 90%;
    }
  }

  /* Animations */
  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  /* Loading overlay */
  .loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
  }

  .loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: $accent-orange;
    animation: spin 1s ease-in-out infinite;
  }

  .loading-text {
    color: white;
    margin-top: 20px;
    font-size: 16px;
    font-weight: 500;
  }

  /* Message quand il n'y a pas de posts */
  .no-posts-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    background-color: rgba(0, 0, 0, 0.03);
    border-radius: $radius-md;
    margin: 20px 0;
    text-align: center;

    mat-icon {
      font-size: 48px;
      color: $primary-blue;
      margin-bottom: 15px;
      opacity: 0.7;
    }

    p {
      font-size: 16px;
      color: $text-secondary;
      max-width: 400px;
      line-height: 1.5;
    }
  }

  /* Nouveau style pour la section des services */
  /* Services Section - Style élégant */
  .services-section {
    @include section-container;
    background: linear-gradient(135deg, rgba(0, 22, 96, 0.02) 0%, rgba(0, 22, 96, 0.05) 100%);
    padding: 100px 20px;
    position: relative;
    overflow: hidden;
    border-top: 1px solid rgba(0, 22, 96, 0.05);
    border-bottom: 1px solid rgba(0, 22, 96, 0.05);
  }

  /* Arrière-plan avec formes et lignes */
  .services-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    overflow: hidden;

    .bg-shape {
      position: absolute;
      border-radius: 50%;
      opacity: 0.03;

      &.shape-1 {
        width: 400px;
        height: 400px;
        top: -150px;
        right: -100px;
        background: $primary-blue;
        animation: float-slow 25s ease-in-out infinite;
      }

      &.shape-2 {
        width: 300px;
        height: 300px;
        bottom: -100px;
        left: -50px;
        background: $accent-orange;
        animation: float-slow 20s ease-in-out infinite 2s;
      }

      &.shape-3 {
        width: 200px;
        height: 200px;
        top: 40%;
        left: 15%;
        background: $primary-blue-light;
        animation: float-slow 15s ease-in-out infinite 1s;
      }
    }

    .bg-lines {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      opacity: 0.02;
      background-image:
        linear-gradient(0deg, transparent 24%, $primary-blue 25%, $primary-blue 26%, transparent 27%, transparent 74%, $primary-blue 75%, $primary-blue 76%, transparent 77%, transparent),
        linear-gradient(90deg, transparent 24%, $primary-blue 25%, $primary-blue 26%, transparent 27%, transparent 74%, $primary-blue 75%, $primary-blue 76%, transparent 77%, transparent);
      background-size: 50px 50px;
    }
  }

  /* En-tête de la section */
  .services-section .section-header {
    text-align: center;
    margin-bottom: 70px;
    position: relative;
    z-index: 2;
  }

  /* Conteneur des cartes de service */
  .services-container {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 40px;
    max-width: 1200px;
    margin: 0 auto 60px;
    position: relative;
    z-index: 2;
  }

  /* Carte de service avec effet de retournement */
  .service-card {
    width: 340px;
    height: 380px;
    perspective: 1000px;

    &:hover .service-card-inner {
      transform: rotateY(180deg);
    }
  }

  .service-card-inner {
    position: relative;
    width: 100%;
    height: 100%;
    text-align: center;
    transition: transform 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    transform-style: preserve-3d;
  }

  .service-card-front, .service-card-back {
    position: absolute;
    width: 100%;
    height: 100%;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    border-radius: $radius-lg;
    overflow: hidden;
    box-shadow: 0 15px 35px rgba(0, 22, 96, 0.1), 0 5px 15px rgba(0, 22, 96, 0.05);
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 30px;
    background: white;
    border: 1px solid rgba(0, 22, 96, 0.05);
  }

  /* Face avant de la carte */
  .service-card-front {
    background: linear-gradient(to bottom, white, rgba(0, 22, 96, 0.02));
    justify-content: center;

    .service-icon-wrapper {
      position: relative;
      width: 100px;
      height: 100px;
      margin-bottom: 25px;
    }

    .service-icon-bg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border-radius: 50%;
      background: linear-gradient(135deg, $primary-blue, $primary-blue-light);
      opacity: 0.9;
      box-shadow: 0 10px 20px rgba(0, 22, 96, 0.15);
      animation: pulse 2s infinite;
    }

    .service-icon {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-size: 40px;
      color: white;
      z-index: 1;
    }

    .service-title {
      font-family: 'Montserrat', sans-serif;
      font-size: 24px;
      font-weight: 600;
      color: $primary-blue;
      margin: 0 0 15px;
      letter-spacing: 0.02em;
    }

    .service-divider {
      width: 50px;
      height: 3px;
      background: $accent-orange;
      margin: 0 auto 20px;
      transition: width 0.3s ease;
    }

    .service-short-desc {
      font-size: 16px;
      color: $text-secondary;
      margin: 0;
    }
  }

  /* Face arrière de la carte */
  .service-card-back {
    background: linear-gradient(135deg, $primary-blue, $primary-blue-dark);
    color: white;
    transform: rotateY(180deg);
    justify-content: space-between;

    .service-title {
      font-family: 'Montserrat', sans-serif;
      font-size: 22px;
      font-weight: 600;
      margin: 0 0 10px;
      letter-spacing: 0.02em;
    }

    .service-divider {
      width: 50px;
      height: 3px;
      background: $accent-orange;
      margin: 0 auto 15px;
    }

    .service-description {
      font-size: 15px;
      line-height: 1.6;
      margin-bottom: 20px;
      text-align: center;
    }

    .service-features {
      list-style: none;
      padding: 0;
      margin: 0 0 20px;
      text-align: left;
      width: 100%;

      li {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        font-size: 14px;

        mat-icon {
          color: $accent-orange;
          font-size: 18px;
          margin-right: 8px;
        }
      }
    }

    .service-button {
      background: $accent-orange;
      color: white;
      padding: 8px 20px;
      border-radius: 30px;
      font-weight: 500;
      letter-spacing: 0.02em;
      transition: all 0.3s ease;
      border: none;

      &:hover {
        background: $accent-orange-light;
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
      }
    }
  }

  /* Section d'appel à l'action */
  .services-cta {
    text-align: center;
    margin-top: 40px;
    padding: 30px;
    background: rgba(0, 22, 96, 0.03);
    border-radius: $radius-lg;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
    position: relative;
    z-index: 2;

    p {
      font-size: 18px;
      color: $primary-blue;
      margin-bottom: 20px;
      font-weight: 500;
    }

    .cta-button {
      background: linear-gradient(135deg, $primary-blue, $primary-blue-dark);
      color: white;
      padding: 12px 25px;
      border-radius: 30px;
      font-weight: 500;
      letter-spacing: 0.02em;
      transition: all 0.3s ease;
      border: none;
      box-shadow: 0 5px 15px rgba(0, 22, 96, 0.2);

      mat-icon {
        margin-right: 8px;
      }

      &:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 20px rgba(0, 22, 96, 0.3);
      }
    }
  }

  /* Animation de pulsation */
  @keyframes pulse {
    0% {
      transform: scale(1);
      opacity: 0.9;
    }
    50% {
      transform: scale(1.05);
      opacity: 1;
    }
    100% {
      transform: scale(1);
      opacity: 0.9;
    }
  }

  /* Animation de flottement lent */
  @keyframes float-slow {
    0%, 100% {
      transform: translate(0, 0);
    }
    25% {
      transform: translate(-20px, 20px);
    }
    50% {
      transform: translate(10px, -15px);
    }
    75% {
      transform: translate(15px, 10px);
    }
  }

  /* Contact Section - Style Élégant */
  .contact-section {
    @include section-container;
    position: relative;
    padding: 100px 0;
    overflow: hidden;
    background: linear-gradient(135deg, $primary-blue-dark, $primary-blue);
    color: white;
  }

  /* Arrière-plan avec effets visuels */
  .contact-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;

    .bg-gradient {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: radial-gradient(circle at 70% 30%, rgba(255, 255, 255, 0.1) 0%, transparent 60%);
    }

    .bg-pattern {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image: url('data:image/svg+xml;utf8,<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"><path d="M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5z" fill="%23FFFFFF" fill-opacity="0.05" fill-rule="evenodd"/></svg>');
      opacity: 0.4;
    }

    .bg-shapes {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;

      .shape {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.03);

        &.shape-1 {
          width: 300px;
          height: 300px;
          top: -100px;
          left: -50px;
          animation: float-slow 20s ease-in-out infinite;
        }

        &.shape-2 {
          width: 200px;
          height: 200px;
          bottom: -50px;
          right: 10%;
          animation: float-slow 15s ease-in-out infinite 2s;
        }

        &.shape-3 {
          width: 150px;
          height: 150px;
          top: 20%;
          right: -50px;
          animation: float-slow 18s ease-in-out infinite 1s;
        }

        &.shape-4 {
          width: 100px;
          height: 100px;
          bottom: 30%;
          left: 10%;
          animation: float-slow 12s ease-in-out infinite 3s;
        }
      }
    }

    .animated-lines {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      overflow: hidden;
      opacity: 0.1;

      .line {
        position: absolute;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
        height: 1px;
        width: 50%;

        &.line-1 {
          top: 20%;
          left: -50%;
          animation: line-move 15s linear infinite;
        }

        &.line-2 {
          top: 50%;
          left: -50%;
          animation: line-move 20s linear infinite 2s;
        }

        &.line-3 {
          top: 80%;
          left: -50%;
          animation: line-move 18s linear infinite 1s;
        }
      }
    }

    .particles-container {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      overflow: hidden;

      .particle {
        position: absolute;
        width: 4px;
        height: 4px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.2);

        &.particle-1 {
          top: 20%;
          left: 10%;
          animation: particle-pulse 4s ease-in-out infinite;
        }

        &.particle-2 {
          top: 70%;
          left: 20%;
          animation: particle-pulse 5s ease-in-out infinite 1s;
        }

        &.particle-3 {
          top: 40%;
          left: 70%;
          animation: particle-pulse 6s ease-in-out infinite 2s;
        }

        &.particle-4 {
          top: 80%;
          left: 80%;
          animation: particle-pulse 4.5s ease-in-out infinite 0.5s;
        }

        &.particle-5 {
          top: 30%;
          left: 40%;
          animation: particle-pulse 5.5s ease-in-out infinite 1.5s;
        }

        &.particle-6 {
          top: 60%;
          left: 60%;
          animation: particle-pulse 4s ease-in-out infinite 2.5s;
        }
      }
    }
  }

  /* Conteneur principal */
  .contact-container {
    display: flex;
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
    padding: 0 20px;

    @media (max-width: 992px) {
      flex-direction: column;
      gap: 50px;
    }
  }

  /* Contenu de contact (gauche) */
  .contact-content {
    flex: 1;
    padding-right: 50px;

    @media (max-width: 992px) {
      padding-right: 0;
    }
  }

  /* En-tête de contact */
  .contact-header {
    margin-bottom: 50px;

    .header-badge {
      display: inline-block;
      background: $accent-orange;
      color: white;
      font-size: 12px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.1em;
      padding: 6px 15px;
      border-radius: 20px;
      margin-bottom: 20px;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
      position: relative;
      overflow: hidden;

      &::after {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0) 100%);
        transform: rotate(45deg);
        animation: shine 3s infinite;
      }
    }

    .contact-title {
      font-family: 'Montserrat', sans-serif;
      font-size: 36px;
      font-weight: 700;
      margin: 0 0 20px;
      line-height: 1.2;
    }

    .title-underline {
      width: 80px;
      height: 3px;
      background: $accent-orange;
      margin-bottom: 20px;
      position: relative;

      .underline-dot {
        position: absolute;
        top: 50%;
        right: 0;
        transform: translateY(-50%);
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: $accent-orange;
      }
    }

    .contact-subtitle {
      font-size: 18px;
      line-height: 1.6;
      margin: 0;
      color: rgba(255, 255, 255, 0.95);
      max-width: 500px;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      font-weight: 300;
    }
  }

  /* Méthodes de contact */
  .contact-methods {
    display: flex;
    gap: 30px;
    margin-bottom: 50px;

    @media (max-width: 768px) {
      flex-direction: column;
    }
  }

  .contact-method {
    flex: 1;
    background: rgba(255, 255, 255, 0.05);
    border-radius: $radius-lg;
    padding: 25px;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);

    &:hover {
      transform: translateY(-10px);
      background: rgba(255, 255, 255, 0.1);
      box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);

      .method-icon-bg {
        transform: scale(1.1);
        opacity: 1;
      }
    }

    .method-icon-container {
      position: relative;
      width: 60px;
      height: 60px;
      margin-bottom: 20px;
    }

    .method-icon-bg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border-radius: 50%;
      background: $accent-orange;
      opacity: 0.9;
      transition: all 0.3s ease;
    }

    mat-icon {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-size: 28px;
      color: white;
      z-index: 1;
    }

    h3 {
      font-size: 20px;
      font-weight: 600;
      margin: 0 0 10px;
    }

    p {
      font-size: 16px;
      margin: 0 0 20px;
      color: rgba(255, 255, 255, 0.85);
      letter-spacing: 0.02em;
      line-height: 1.5;
    }

    .method-link {
      display: flex;
      align-items: center;
      color: rgba(255, 255, 255, 0.9);
      text-decoration: none;
      font-weight: 500;
      font-size: 14px;
      transition: all 0.3s ease;
      background: rgba(255, 255, 255, 0.1);
      padding: 8px 15px;
      border-radius: 20px;
      margin-top: 5px;
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);

      span {
        margin-right: 5px;
      }

      mat-icon {
        position: static;
        transform: none;
        font-size: 16px;
        transition: transform 0.3s ease;
        color: $accent-orange;
      }

      &:hover {
        color: white;
        background: rgba(255, 255, 255, 0.15);
        box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
        transform: translateY(-2px);

        mat-icon {
          transform: translateX(3px);
          color: white;
        }
      }
    }
  }

  /* CTA et liens sociaux */
  .contact-cta {
    display: flex;
    justify-content: space-between;
    align-items: center;

    @media (max-width: 576px) {
      flex-direction: column;
      gap: 20px;
      align-items: flex-start;
    }

    .cta-button {
      background: white;
      color: $primary-blue;
      border: none;
      border-radius: 30px;
      padding: 0;
      font-weight: 600;
      font-size: 16px;
      display: flex;
      align-items: center;
      overflow: hidden;
      transition: all 0.3s ease;
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);

      span {
        padding: 12px 20px;
      }

      .button-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        background: $accent-orange;
        color: white;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        margin-right: 5px;
        transition: all 0.3s ease;
      }

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 12px 25px rgba(0, 0, 0, 0.3);

        .button-icon {
          transform: rotate(360deg);
        }
      }
    }

    .social-links {
      display: flex;
      gap: 15px;
    }

    .social-link {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.1);
      color: white;
      transition: all 0.3s ease;
      text-decoration: none;

      &:hover {
        transform: translateY(-5px);

        &.linkedin {
          background: #0077B5;
          box-shadow: 0 5px 15px rgba(0, 119, 181, 0.4);
        }

        &.twitter {
          background: #1DA1F2;
          box-shadow: 0 5px 15px rgba(29, 161, 242, 0.4);
        }

        &.facebook {
          background: #4267B2;
          box-shadow: 0 5px 15px rgba(66, 103, 178, 0.4);
        }
      }
    }
  }


  /* Footer Section - Design Simple et Élégant */
  .footer-section {
    position: relative;
    background: white;
    color: $text-secondary;
    padding: 20px 0;
    margin-top: 60px;
  }

  /* Contenu principal du footer */
  .footer-content {
    position: relative;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  /* Ligne de séparation */
  .footer-divider {
    height: 1px;
    background: linear-gradient(to right, transparent, $primary-blue-light, transparent);
    margin-bottom: 20px;
    opacity: 0.3;
  }

  /* Section principale du footer */
  .footer-main {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;

    @media (max-width: 768px) {
      flex-direction: column;
      text-align: center;
    }
  }

  /* Copyright */
  .copyright-text {
    font-size: 14px;
    color: $text-secondary;
  }

  /* Liens sociaux */
  .footer-social-links {
    display: flex;
    gap: 15px;

    @media (max-width: 768px) {
      justify-content: center;
    }
  }

  .footer-social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: $primary-blue;
    color: white;
    transition: all 0.3s ease;
    text-decoration: none;

    mat-icon {
      font-size: 16px;
    }

    &:hover {
      transform: translateY(-3px);
      background: $accent-orange;
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    }
  }

  /* Liens légaux */
  .footer-legal-links {
    display: flex;
    align-items: center;
    gap: 10px;

    @media (max-width: 768px) {
      justify-content: center;
    }
  }

  .legal-link {
    font-size: 14px;
    color: $text-secondary;
    text-decoration: none;
    transition: all 0.3s ease;

    &:hover {
      color: $primary-blue;
    }
  }

  .link-separator {
    color: $text-secondary;
    opacity: 0.5;
    font-size: 14px;
  }

  /* Styles pour les boutons d'édition */
  .edit-button {
    position: absolute;
    top: 0;
    right: 0;
    color: $primary-blue;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    opacity: 0.7;
    z-index: 10;

    &:hover {
      background: white;
      color: $accent-orange;
      opacity: 1;
      transform: scale(1.1);
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
    }

    mat-icon {
      font-size: 20px;
    }
  }

  /* Styles pour les formulaires d'édition */
  .description-edit-form,
  .statements-edit-form {
    background: white;
    border-radius: $radius-lg;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;

    .full-width {
      width: 100%;
      margin-bottom: 20px;
    }

    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: 15px;
      margin-top: 20px;
    }
  }

  .form-container {
    max-width: 800px;
    margin: 0 auto;
  }

  /* Positionnement des boutons d'édition dans les différentes sections */
  .description-header {
    position: relative;
  }

  .section-title-container {
    position: relative;
  }