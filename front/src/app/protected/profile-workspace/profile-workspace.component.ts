import {Component, ElementRef, OnInit, ViewChild} from '@angular/core';
import {MatIcon} from '@angular/material/icon';
import {DatePipe, NgClass, NgForOf, NgIf} from '@angular/common';
import {Mat<PERSON>utton, MatMiniFabButton} from '@angular/material/button';
import {ActivatedRoute, RouterLink} from '@angular/router';
import {UserService} from '../../services/user/user.service';
import {WorkspaceService} from '../../services/workspace/workspace.service';
import {environment} from '../../../environments/environment';
import {MatFormFieldModule, MatLabel} from '@angular/material/form-field';
import {MatInputModule} from '@angular/material/input';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import {MatCardModule} from '@angular/material/card';
import {MatOptionModule, MatRippleModule} from '@angular/material/core';
import {MatSelectModule} from '@angular/material/select';

@Component({
  selector: 'app-profile-workspace',
  templateUrl: './profile-workspace.component.html',
  standalone: true,
  imports: [
    MatIcon,
    DatePipe,
    MatButton,
    RouterLink,
    NgIf,
    NgForOf,
    MatMiniFabButton,
    NgClass,
    MatFormFieldModule,
    MatInputModule,
    FormsModule,
    ReactiveFormsModule,
    MatCardModule,
    MatRippleModule,
    MatLabel,
    MatOptionModule,
    MatSelectModule
  ],
  styleUrls: ['./profile-workspace.component.scss']
})
export class ProfileWorkspaceComponent implements OnInit {
  @ViewChild('logoFileInput') logoFileInput!: ElementRef;

  workspaceData: any = null;
  selectedLogoFile: File | null = null;
  workspacePosts: any[] = [];
  currentYear: number = new Date().getFullYear();
  assetsUrl = environment.assetsUrl; // URL de base pour les assets (logos, etc.)

  // Variables pour l'édition des informations du workspace
  isEditingGeneral: boolean = false;
  isEditingContact: boolean = false;
  isEditingSocial: boolean = false;
  isEditingDescription: boolean = false;
  isEditingStatements: boolean = false;
  isEditingValues: boolean = false;
  isEditingServices: boolean = false;

  // Données d'édition
  editWorkspaceData: any = {
    name: '',
    sector: '',
    foundedYear: '',
    companySize: '',
    location: '',
    email: '',
    phoneNumber: '',
    website: '',
    linkedinUrl: '',
    twitterUrl: '',
    facebookUrl: '',
    instagramUrl: '',
    description: '',
    vision: '',
    mission: '',
    values: ''
  };

  // Valeurs de l'entreprise (pour l'affichage dynamique)
  companyValues: any[] = [
    {icon: 'trending_up', text: 'Innovation'},
    {icon: 'verified', text: 'Qualité'},
    {icon: 'star', text: 'Excellence'}
  ];

  // Services de l'entreprise (pour l'affichage dynamique)
  companyServices: any[] = [
    {
      icon: 'code',
      title: 'Développement',
      shortDesc: 'Solutions logicielles sur mesure',
      description: 'Solutions logicielles sur mesure pour répondre à vos besoins spécifiques, développées par nos experts en utilisant les dernières technologies.',
      features: ['Applications web', 'Applications mobiles', 'Logiciels d\'entreprise']
    },
    {
      icon: 'cloud',
      title: 'Cloud Computing',
      shortDesc: 'Infrastructure cloud sécurisée',
      description: 'Infrastructure cloud sécurisée et évolutive pour votre entreprise, optimisée pour la performance et la fiabilité.',
      features: ['Serveurs virtuels', 'Stockage évolutif', 'Solutions SaaS']
    },
    {
      icon: 'security',
      title: 'Cybersécurité',
      shortDesc: 'Protection des données et systèmes',
      description: 'Solutions de sécurité complètes pour protéger vos données et systèmes contre les menaces modernes.',
      features: ['Audit de sécurité', 'Protection des données', 'Surveillance continue']
    }
  ];

  // Service en cours d'édition
  editingServiceIndex: number = -1;
  editingService: any = {
    icon: '',
    title: '',
    shortDesc: '',
    description: '',
    features: ['', '', '']
  };


  constructor(
    private userService: UserService,
    private workspaceService: WorkspaceService,
    private route: ActivatedRoute
  ) {
  }

  ngOnInit(): void {
    // Récupérer l'ID du workspace depuis l'URL
    this.route.paramMap.subscribe(params => {
      const workspaceId = params.get('id');
      console.log('Workspace ID from URL:', workspaceId);

      if (workspaceId) {
        // Récupérer les données du workspace depuis l'API
        this.workspaceService.getWorkspaceProfile(workspaceId).subscribe({
          next: (workspace) => {
            console.log('Workspace data loaded from API:', workspace);
            this.workspaceData = workspace;

            // Récupérer les posts du workspace
            this.loadWorkspacePosts(workspaceId);
          },
          error: (error) => {
            console.error('Error loading workspace data:', error);
            // En cas d'erreur, essayer de récupérer le workspace depuis le profil utilisateur
            this.loadWorkspaceFromUserProfile();
          }
        });
      } else {
        // Si aucun ID n'est fourni dans l'URL, récupérer le workspace depuis le profil utilisateur
        this.loadWorkspaceFromUserProfile();
      }
    });
  }

  /**
   * Charge les données du workspace depuis le profil utilisateur
   */
  loadWorkspaceFromUserProfile(): void {
    this.userService.getUserProfile().subscribe({
      next: (value) => {
        if (value && value.workspace) {
          this.workspaceData = value.workspace;
          console.log('Workspace data loaded from user profile:', this.workspaceData);

          // Récupérer les posts du workspace si l'ID est disponible
          if (this.workspaceData && this.workspaceData.id) {
            this.loadWorkspacePosts(this.workspaceData.id);
          }
        } else {
          console.warn('No workspace data found in user profile');
        }
      },
      error: (error) => {
        console.error('Error loading user profile:', error);
      }
    });
  }

  /**
   * Charge les posts du workspace
   */
  loadWorkspacePosts(workspaceId: string): void {
    this.workspaceService.getWorkspacePosts(workspaceId).subscribe({
      next: (posts) => {
        console.log('Workspace posts loaded:', posts);
        // Stocker les posts pour les afficher dans le template
        this.workspacePosts = posts;
      },
      error: (error) => {
        console.error('Error loading workspace posts:', error);
      }
    });
  }

  /**
   * Déclenche le dialogue de sélection de fichier pour le logo
   */
  triggerLogoUpload(): void {
    this.logoFileInput.nativeElement.click();
  }

  /**
   * Gère la sélection d'un logo
   */
  onLogoSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      const file = input.files[0];

      // Vérifier que le fichier est bien une image
      if (!file.type.startsWith('image/')) {
        alert('Le fichier sélectionné n\'est pas une image valide.');
        return;
      }

      // Vérifier que la taille du fichier est raisonnable (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        alert('La taille du logo ne doit pas dépasser 5MB.');
        return;
      }

      this.selectedLogoFile = file;
      this.uploadLogo();
    }
  }

  /**
   * Télécharge le logo sélectionné
   */
  uploadLogo(): void {
    console.log('Début de la méthode uploadLogo');

    if (!this.selectedLogoFile) {
      console.error('Aucun fichier logo sélectionné');
      return;
    }

    if (!this.workspaceData?.id) {
      console.error('ID du workspace non disponible');
      return;
    }

    console.log('ID du workspace:', this.workspaceData.id);
    console.log('Fichier logo sélectionné:', this.selectedLogoFile.name, this.selectedLogoFile.type, this.selectedLogoFile.size);

    // Créer un élément de chargement visuel
    const loadingElement = document.createElement('div');
    loadingElement.className = 'loading-overlay';
    loadingElement.innerHTML = '<div class="loading-spinner"></div><div class="loading-text">Téléchargement du logo...</div>';
    document.body.appendChild(loadingElement);

    console.log('Appel de workspaceService.uploadLogo');
    this.workspaceService.uploadLogo(this.workspaceData.id, this.selectedLogoFile).subscribe({
      next: (response) => {
        console.log('Logo ajouté avec succès !', response);

        // Mettre à jour le workspace avec le nouveau logo
        if (response && response.logoUrl) {
          console.log('Nouvelle URL du logo:', response.logoUrl);
          this.workspaceData.logoUrl = response.logoUrl;

          // Forcer le rafraîchissement de l'image en ajoutant un timestamp
          const timestamp = new Date().getTime();
          // S'assurer que l'URL du logo ne contient pas déjà l'URL de base
          if (this.workspaceData.logoUrl.startsWith('http')) {
            // Si l'URL est déjà complète, on la garde telle quelle
            this.workspaceData.logoUrl = this.workspaceData.logoUrl + '?t=' + timestamp;
          } else {
            // Sinon, on s'assure que l'URL est correctement formatée pour l'affichage
            // (mais sans ajouter l'URL de base ici, car elle sera ajoutée dans le template)
            this.workspaceData.logoUrl = this.workspaceData.logoUrl + '?t=' + timestamp;
          }
        } else {
          console.warn('Aucune URL de logo dans la réponse');
        }

        // Réinitialiser le fichier sélectionné
        this.selectedLogoFile = null;

        // Supprimer l'élément de chargement
        document.body.removeChild(loadingElement);

        // Afficher un message de succès
        alert('Logo ajouté avec succès !');
      },
      error: (error) => {
        console.error('Erreur lors de l\'ajout du logo', error);
        console.error('Détails de l\'erreur:', error.status, error.statusText, error.message);

        if (error.error) {
          console.error('Erreur du serveur:', error.error);
        }

        // Message d'erreur plus détaillé
        let errorMessage = 'Erreur lors de l\'ajout du logo.';

        if (error.message) {
          errorMessage = error.message;
        }

        // Supprimer l'élément de chargement
        document.body.removeChild(loadingElement);

        alert(errorMessage);

        // Réinitialiser le fichier sélectionné
        this.selectedLogoFile = null;
      }
    });
  }

  /**
   * Gère l'édition des informations du workspace
   */
  toggleEditMode(section: string) {
    // Réinitialiser les données d'édition avec les valeurs actuelles
    this.editWorkspaceData = {
      ...this.editWorkspaceData,
      name: this.workspaceData?.name || '',
      sector: this.workspaceData?.sector || '',
      foundedYear: this.workspaceData?.foundedYear || '',
      companySize: this.workspaceData?.companySize || '',
      location: this.workspaceData?.location || '',
      email: this.workspaceData?.email || '',
      phoneNumber: this.workspaceData?.phoneNumber || '',
      website: this.workspaceData?.website || '',
      linkedinUrl: this.workspaceData?.linkedinUrl || '',
      twitterUrl: this.workspaceData?.twitterUrl || '',
      facebookUrl: this.workspaceData?.facebookUrl || '',
      instagramUrl: this.workspaceData?.instagramUrl || '',
      description: this.workspaceData?.description || '',
      vision: this.workspaceData?.vision || '',
      mission: this.workspaceData?.mission || '',
      values: this.workspaceData?.values || ''
    };

    // Activer le mode d'édition pour la section spécifiée
    if (section === 'general') {
      this.isEditingGeneral = true;
    } else if (section === 'contact') {
      this.isEditingContact = true;
    } else if (section === 'social') {
      this.isEditingSocial = true;
    } else if (section === 'description') {
      this.isEditingDescription = true;
    } else if (section === 'statements') {
      this.isEditingStatements = true;
    } else if (section === 'values') {
      this.isEditingValues = true;
    }
  }

  /**
   * Annule l'édition d'une section
   */
  cancelEdit(section: string) {
    // Désactiver le mode d'édition pour la section spécifiée
    if (section === 'general') {
      this.isEditingGeneral = false;
    } else if (section === 'contact') {
      this.isEditingContact = false;
    } else if (section === 'social') {
      this.isEditingSocial = false;
    } else if (section === 'description') {
      this.isEditingDescription = false;
    } else if (section === 'statements') {
      this.isEditingStatements = false;
    } else if (section === 'values') {
      this.isEditingValues = false;
    }
  }

  /**
   * Enregistre les modifications des informations du workspace
   */
  saveWorkspaceDetails(section: string) {
    if (!this.workspaceData || !this.workspaceData.id) {
      console.error('Données du workspace non disponibles');
      return;
    }

    // Préparer les données à mettre à jour en fonction de la section
    let updateData: any = {};

    if (section === 'general') {
      updateData = {
        name: this.editWorkspaceData.name,
        sector: this.editWorkspaceData.sector,
        foundedYear: this.editWorkspaceData.foundedYear,
        companySize: this.editWorkspaceData.companySize
      };
    } else if (section === 'contact') {
      updateData = {
        location: this.editWorkspaceData.location,
        email: this.editWorkspaceData.email,
        phoneNumber: this.editWorkspaceData.phoneNumber,
        website: this.editWorkspaceData.website
      };
    } else if (section === 'social') {
      updateData = {
        linkedinUrl: this.editWorkspaceData.linkedinUrl,
        twitterUrl: this.editWorkspaceData.twitterUrl,
        facebookUrl: this.editWorkspaceData.facebookUrl,
        instagramUrl: this.editWorkspaceData.instagramUrl
      };
    } else if (section === 'description') {
      updateData = {
        description: this.editWorkspaceData.description
      };
    }

    // Mettre à jour les données du workspace
    this.workspaceService.updateWorkspace(this.workspaceData.id, updateData)
      .subscribe({
        next: (response) => {
          console.log('Informations mises à jour avec succès:', response);

          // Mettre à jour les données locales
          this.workspaceData = {
            ...this.workspaceData,
            ...updateData
          };

          // Désactiver le mode d'édition
          this.cancelEdit(section);
        },
        error: (error) => {
          console.error('Erreur lors de la mise à jour des informations:', error);
        }
      });
  }

  /**
   * Enregistre les modifications des statements (vision, mission, valeurs)
   */
  saveWorkspaceStatements() {
    if (!this.workspaceData || !this.workspaceData.id) {
      console.error('Données du workspace non disponibles');
      return;
    }

    const statementsData = {
      vision: this.editWorkspaceData.vision,
      mission: this.editWorkspaceData.mission,
      values: this.editWorkspaceData.values
    };

    this.workspaceService.updateWorkspaceStatements(this.workspaceData.id, statementsData)
      .subscribe({
        next: (response) => {
          console.log('Statements mis à jour avec succès:', response);

          // Mettre à jour les données locales
          this.workspaceData = {
            ...this.workspaceData,
            ...statementsData
          };

          // Désactiver le mode d'édition
          this.cancelEdit('statements');
        },
        error: (error) => {
          console.error('Erreur lors de la mise à jour des statements:', error);
        }
      });
  }

  /**
   * Enregistre les modifications des valeurs de l'entreprise
   */
  saveWorkspaceValues() {
    if (!this.workspaceData || !this.workspaceData.id) {
      console.error('Données du workspace non disponibles');
      return;
    }

    // Dans une implémentation réelle, vous pourriez avoir un tableau de valeurs à envoyer
    // Pour cet exemple, nous utilisons simplement le champ values comme une chaîne
    const valuesData = {
      values: this.editWorkspaceData.values
    };

    this.workspaceService.updateWorkspace(this.workspaceData.id, valuesData)
      .subscribe({
        next: (response) => {
          console.log('Valeurs mises à jour avec succès:', response);

          // Mettre à jour les données locales
          this.workspaceData = {
            ...this.workspaceData,
            ...valuesData
          };

          // Désactiver le mode d'édition
          this.cancelEdit('values');
        },
        error: (error) => {
          console.error('Erreur lors de la mise à jour des valeurs:', error);
        }
      });
  }


}
