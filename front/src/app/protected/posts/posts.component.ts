import { Component, EventEmitter, Output, OnInit, ChangeDetectorRef } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { provideNativeDateAdapter, MatNativeDateModule } from '@angular/material/core';
import { MatSelectModule } from '@angular/material/select';
import { OidcSecurityService } from 'angular-auth-oidc-client';
import { PostsService } from '../../services/posts/posts.service';
import { WorkspaceService } from '../../services/workspace/workspace.service';
import { environment } from '../../../environments/environment';

@Component({
  selector: 'app-posts',
  templateUrl: './posts.component.html',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatButtonModule,
    MatCardModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatSnackBarModule
  ],
  providers: [provideNativeDateAdapter()],
  styleUrls: ['./posts.component.scss']
})
export class PostsComponent implements OnInit {
  @Output() postAdded = new EventEmitter<void>();
  postForm: FormGroup;
  activeWorkspace: any = null;
  assetsUrl = environment.assetsUrl;

  constructor(
    private fb: FormBuilder,
    private postsService: PostsService,
    private snackBar: MatSnackBar,
    private router: Router,
    private oidcSecurityService: OidcSecurityService,
    private workspaceService: WorkspaceService,
    private cdr: ChangeDetectorRef
  ) {
    this.postForm = this.fb.group({
      jobTitle: ['', Validators.required],
      profileRequest: ['', Validators.required],
      contractType: ['', Validators.required],
      location: ['', Validators.required],
      description: ['', Validators.required],
      postedDate: [new Date(), Validators.required]  // Valeur par défaut: Date actuelle
    });
  }

  ngOnInit(): void {
    // Charger le workspace actif
    this.loadActiveWorkspace();
  }

  /**
   * Charge les détails du workspace actif (celui qui a été switché)
   */
  private loadActiveWorkspace(): void {
    // Récupérer l'ID du workspace actif
    const activeWorkspaceId = this.workspaceService.getActiveWorkspaceId();

    if (activeWorkspaceId) {
      console.log('ID du workspace actif:', activeWorkspaceId);

      // Récupérer les détails du workspace actif
      this.workspaceService.getWorkspaceProfile(activeWorkspaceId).subscribe({
        next: (workspace) => {
          if (workspace) {
            console.log('Workspace actif chargé:', workspace);
            this.activeWorkspace = workspace;
            this.cdr.detectChanges();
          } else {
            console.log('Aucun détail trouvé pour le workspace actif');
            this.tryAlternativeWorkspaceLoading(activeWorkspaceId);
          }
        },
        error: (error) => {
          console.error('Erreur lors du chargement du workspace actif:', error);
          this.tryAlternativeWorkspaceLoading(activeWorkspaceId);
        }
      });
    } else {
      console.log('Aucun workspace actif trouvé, chargement des workspaces de l\'utilisateur...');
      this.workspaceService.getWorkspacesForUser().subscribe({
        next: (workspaces) => {
          console.log('Workspaces de l\'utilisateur:', workspaces);
          if (workspaces && workspaces.length > 0) {
            // Utiliser le premier workspace comme workspace actif
            const firstWorkspace = workspaces[0];
            console.log('Utilisation du premier workspace comme workspace actif:', firstWorkspace);
            this.workspaceService.switchWorkspace(firstWorkspace.id);
            this.activeWorkspace = firstWorkspace;
            this.cdr.detectChanges();
          } else {
            console.log('Aucun workspace trouvé pour l\'utilisateur');
            this.showNoWorkspaceWarning();
          }
        },
        error: (error) => {
          console.error('Erreur lors du chargement des workspaces de l\'utilisateur:', error);
          this.showNoWorkspaceWarning();
        }
      });
    }
  }

  /**
   * Essaie de charger le workspace avec des méthodes alternatives
   */
  private tryAlternativeWorkspaceLoading(workspaceId: string): void {
    // Essayer avec getWorkspaceById
    this.workspaceService.getWorkspaceById(workspaceId).subscribe({
      next: (workspace) => {
        console.log('Workspace chargé avec getWorkspaceById:', workspace);
        if (workspace) {
          this.activeWorkspace = workspace;
          this.cdr.detectChanges();
        } else {
          // Essayer avec getPublicWorkspaceInfo
          this.workspaceService.getPublicWorkspaceInfo(workspaceId).subscribe({
            next: (publicInfo) => {
              console.log('Informations publiques du workspace chargées:', publicInfo);
              if (publicInfo) {
                this.activeWorkspace = publicInfo;
                this.cdr.detectChanges();
              } else {
                this.showNoWorkspaceWarning();
              }
            },
            error: (publicError) => {
              console.error('Erreur lors du chargement des informations publiques du workspace:', publicError);
              this.showNoWorkspaceWarning();
            }
          });
        }
      },
      error: (error) => {
        console.error('Erreur lors du chargement du workspace avec getWorkspaceById:', error);
        this.showNoWorkspaceWarning();
      }
    });
  }

  /**
   * Affiche un avertissement si aucun workspace n'est trouvé
   */
  private showNoWorkspaceWarning(): void {
    this.snackBar.open('Aucun workspace actif trouvé. Veuillez sélectionner un workspace avant de créer un post.', 'OK', {
      duration: 5000,
      panelClass: ['warning-snackbar']
    });
  }

  /**
   * Construit l'URL complète du logo
   */
  getLogoUrl(logoPath: string | undefined): string {
    if (!logoPath) return 'assets/images/placeholder-logo.png';

    // Vérifier si le chemin est une URL complète
    if (logoPath.startsWith('http://') || logoPath.startsWith('https://')) {
      return logoPath;
    }

    // Construire l'URL avec le chemin du serveur
    const fullPath = this.assetsUrl + (logoPath.startsWith('/') ? '' : '/') + logoPath;
    return fullPath;
  }

  addPost() {
    if (this.postForm.valid) {
      // Vérifier si un workspace actif est sélectionné
      if (!this.activeWorkspace) {
        this.showNoWorkspaceWarning();
        return;
      }

      this.oidcSecurityService.getUserData().subscribe({
        next: (userData) => {
          // Générer un ID basé sur le timestamp et l'ID utilisateur pour assurer l'unicité
          const generatedId = `post-${userData?.sub}-${new Date().getTime()}`;

          // Créer le post avec les données du formulaire
          const postData = {
            id: generatedId,  // Ajouter l'ID généré
            titre: this.postForm.value.jobTitle,
            description: this.postForm.value.description,
            entreprise: this.postForm.value.location,
            profileRequest: this.postForm.value.profileRequest,
            contractType: this.postForm.value.contractType,
            userId: userData?.sub,  // L'ID de l'utilisateur connecté
            datePublication: new Date().toISOString(),  // Date actuelle
            archived: false,  // Par défaut, l'archivage est à false
            // Ajouter les informations du workspace
            workspace: {
              id: this.activeWorkspace.id,
              name: this.activeWorkspace.name,
              logoUrl: this.activeWorkspace.logoUrl
            }
          };

          console.log('Creating post with data:', postData); // Pour déboguer

          // Appeler le service pour créer le poste
          this.postsService.createPost(postData).subscribe({
            next: (response) => {
              console.log('Post created successfully:', response);
              this.router.navigate(['/acceuilposts']);
            },
            error: (error) => {
              console.error('Error creating post:', error);
            }
          });
        }
      });
    }
  }

  closeDialog() {
    // Logic to close the dialog can be added here
  }
}
