<div class="modal-container">
  <form [formGroup]="postForm" (ngSubmit)="addPost()">
    <mat-card class="workspace-card">
      <mat-card-header class="header-center">
        <mat-card-title>Add a New Position</mat-card-title>
      </mat-card-header>

      <!-- Affichage du workspace de l'utilisateur -->
      <div class="workspace-info" *ngIf="activeWorkspace">
        <div class="workspace-header">
          <div class="workspace-logo">
            <img *ngIf="activeWorkspace.logoUrl" [src]="getLogoUrl(activeWorkspace.logoUrl)" alt="{{ activeWorkspace.name }} Logo" class="workspace-logo-img">
            <mat-icon *ngIf="!activeWorkspace.logoUrl" class="workspace-icon">business</mat-icon>
          </div>
          <div class="workspace-details">
            <h3 class="workspace-name">{{ activeWorkspace.name }}</h3>
            <p class="workspace-note">This post will be published on behalf of this workspace</p>
          </div>
        </div>
      </div>

      <!-- Message si aucun workspace n'est actif -->
      <div class="no-workspace-warning" *ngIf="!activeWorkspace">
        <mat-icon color="warn">warning</mat-icon>
        <p>No active workspace found. Please select a workspace before creating a post.</p>
      </div>
      <mat-card-content>
        <div class="form-row">
          <mat-form-field appearance="outline" class="form-field">
            <mat-label>Job Title</mat-label>
            <input matInput placeholder="Job Title" formControlName="jobTitle" required>
            <mat-icon matSuffix>content_paste</mat-icon>
            <mat-error *ngIf="postForm.get('jobTitle')?.hasError('required')">
              Job Title is required
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="form-field">
            <mat-label>Profile Request</mat-label>
            <input matInput placeholder="Profile Request" formControlName="profileRequest">
            <mat-icon matSuffix>person</mat-icon>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline" class="form-field">
            <mat-label>Contract Type</mat-label>
            <input matInput placeholder="Contract Type" formControlName="contractType">
            <mat-icon matSuffix>layers</mat-icon>
          </mat-form-field>

          <mat-form-field appearance="outline" class="form-field">
            <mat-label>Location</mat-label>
            <input matInput placeholder="Location" formControlName="location" required>
            <mat-icon matSuffix>place</mat-icon>
            <mat-error *ngIf="postForm.get('location')?.hasError('required')">
              Location is required
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline" class="form-field">
            <mat-label>Posted Date</mat-label>
            <input matInput [matDatepicker]="picker" placeholder="Posted Date" formControlName="postedDate" required>
            <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
            <mat-datepicker #picker></mat-datepicker>
            <mat-error *ngIf="postForm.get('postedDate')?.hasError('required')">
              Posted Date is required
            </mat-error>
          </mat-form-field>
        </div>

        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Description</mat-label>
          <textarea matInput rows="4" placeholder="Description" formControlName="description"></textarea>
        </mat-form-field>
      </mat-card-content>
      <mat-card-actions class="center-actions">
        <button mat-stroked-button type="button" class="cancel-button" (click)="closeDialog()">
          Cancel
        </button>
        <button mat-raised-button color="accent" class="add-btn" type="submit" [disabled]="postForm.invalid">
          <mat-icon>add</mat-icon> Add
        </button>
      </mat-card-actions>
    </mat-card>
  </form>
</div>
