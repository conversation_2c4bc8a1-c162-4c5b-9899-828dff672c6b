/* src/app/protected/posts/posts.component.scss */
.modal-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f7fa;
  padding: 20px;
}

.workspace-card {
  width: 100%;
  max-width: 600px;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
}
.success-snackbar {
  background-color: #4caf50; /* Vert pour succès */
  color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.error-snackbar {
  background-color: #f44336; /* Rouge pour erreur */
  color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.mat-snack-bar-action {
  color: #ffffff !important;
  font-weight: 500;
}
.header-center {
  justify-content: center;
  margin-bottom: 20px;
}

mat-card-title {
  font-size: 20px;
  font-weight: 600;
  color: #001660;
  text-transform: uppercase;
}

mat-card-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-row {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.form-field {
  flex: 1;
  min-width: 200px;
}

.full-width {
  width: 100%;
}

mat-form-field {
  width: 100%;
}

mat-form-field .mat-form-field-wrapper {
  padding-bottom: 0;
}

mat-form-field mat-icon {
  color: #666;
}

mat-form-field textarea {
  resize: vertical;
  min-height: 80px;
}

.center-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 20px;
}

.cancel-button {
  color: #666;
  border-color: #ccc;
}

.cancel-button:hover {
  background-color: #f0f0f0;
}

.add-btn {
  background-color: #001660;
  color: #ffffff;
  padding: 8px 24px;
  font-weight: 500;
}

.add-btn:hover {
  background-color: #000d33;
}

.add-btn mat-icon {
  margin-right: 8px;
}

mat-error {
  font-size: 12px;
  color: #d32f2f;
}

/* Styles pour l'affichage du workspace */
.workspace-info {
  margin: 0 0 20px 0;
  padding: 15px;
  background-color: #f0f7ff;
  border-radius: 8px;
  border: 1px solid #d0e1f9;
}

.workspace-header {
  display: flex;
  align-items: center;
  gap: 15px;
}

.workspace-logo {
  flex: 0 0 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #001660;
  color: white;
}

.workspace-logo-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.workspace-icon {
  font-size: 30px;
  width: 30px;
  height: 30px;
  color: white;
}

.workspace-details {
  flex: 1;
}

.workspace-name {
  font-size: 18px;
  font-weight: 600;
  color: #001660;
  margin: 0 0 5px 0;
}

.workspace-note {
  font-size: 14px;
  color: #666;
  margin: 0;
}

/* Message d'avertissement si aucun workspace n'est actif */
.no-workspace-warning {
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 0 0 20px 0;
  padding: 15px;
  background-color: #fff4e5;
  border-radius: 8px;
  border: 1px solid #ffe0b2;
}

.no-workspace-warning mat-icon {
  color: #f57c00;
  font-size: 24px;
}

.no-workspace-warning p {
  margin: 0;
  color: #e65100;
  font-weight: 500;
}

/* Classe pour le snackbar d'avertissement */
.warning-snackbar {
  background-color: #ff9800;
  color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}
