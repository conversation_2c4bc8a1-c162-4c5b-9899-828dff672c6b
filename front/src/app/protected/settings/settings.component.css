/* settings.component.css */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleDown {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.95);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(37, 99, 235, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(37, 99, 235, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(37, 99, 235, 0);
  }
}

/* Root Variables */
:root {
  --primary-color: #2563eb; /* Modern blue for primary elements */
  --primary-hover: #1d4ed8;
  --secondary-color: #f9fafb;
  --text-color: #040140;
  --text-muted: #6b7280;
  --sidebar-bg: #ffffff;
  --active-bg: #e6f0ff;
  --hover-bg: #f3f4f6;
  --danger-color: #dc2626;
  --card-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  --color-general: #2563eb;
  --color-privacy: #8b5cf6;
  --color-security: #d97706;
  --color-notifications: #ec4899;
  --color-ads: #f97316;
  --color-preferences: #10b981;
  --color-profile: #3b82f6;
  --color-invite: #14b8a6;
  --color-workspaces: #6366f1;
  --color-history: #6b7280;
  --color-subscriptions: #f59e0b;
  --color-support: #22c55e;
  --color-billing: #0ea5e9;
}


.container {
  display: flex;
  height: 100vh;
  color: var(--text-color);
}

.sidebar {
  width: 280px;
  padding: 20px;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.sidebar h2 {
  font-size: 22px;
  margin-bottom: 20px;
  font-weight: bold;
  color: var(--primary-color);
}

.sidebar ul {
  list-style: none;
  padding: 0;
}

.sidebar ul li {
  padding: 12px;
  cursor: pointer;
  font-size: 16px;
  border-radius: 8px;
  transition: background 0.3s;
  color: var(--text-color);
}

.sidebar ul li.active {
  background-color: var(--active-bg);
  font-weight: bold;
}

.content {
  flex-grow: 1;

  padding: 20px;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
}

.header {
  padding: 10px 0;
  border-bottom: 2px solid var(--primary-color);
}

.header h2 {
  font-size: 26px;
  font-weight: bold;
  color: var(--primary-color);
}

.settings-content {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 150px);
  overflow-y: auto;
  padding: 20px;
  border-radius: 8px;
  background: var(--secondary-color);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

mat-form-field {
  width: 100%;
  margin-bottom: 15px;
}

mat-checkbox,
mat-slide-toggle,
mat-radio-group {
  display: block;
  margin-bottom: 10px;
}

button {
  background-color: var(--primary-color);
  border: none;
  padding: 10px 15px;
  border-radius: 5px;
  cursor: pointer;
  transition: background 0.3s;
}

button:hover {
  background-color: var(--active-bg);
}

.cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-top: 16px;
}

mat-card {
  padding: 20px;
  margin-bottom: 24px;
}

.settings-card {
  padding: 16px;
  cursor: pointer;
  transition: transform 0.2s ease-in-out;
}

.settings-card:hover {
  transform: scale(1.05);
}

/* Container */
.settings-container {
  display: flex;
  min-height: 100vh;
  background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
  font-family: 'Inter', sans-serif;
  position: relative;
}

/* Sidebar Icons */
.sidebar-icons {
  width: 60px;
  background: var(--sidebar-bg);
  border-right: 1px solid #e5e7eb;
  padding-top: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.sidebar-icons ul {
  list-style: none;
  padding: 0;
  width: 100%;
}

.sidebar-icons li {
  padding: 12px;
  display: flex;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
}

.sidebar-icons li:hover,
.sidebar-icons li.active {
  background: var(--active-bg);
  border-radius: 8px;
}

.sidebar-icons li.danger {
  color: var(--danger-color);
}

.sidebar-icons li.danger:hover {
  background: #fee2e2;
}

.sidebar-icons mat-icon {
  font-size: 24px;
  transition: color 0.3s ease;
}

.sidebar-icons li:hover mat-icon,
.sidebar-icons li.active mat-icon {
  animation: pulse 1.5s infinite;
}

/* Icon Colors */
.icon-general mat-icon { color: var(--color-general); }
.icon-privacy mat-icon { color: var(--color-privacy); }
.icon-security mat-icon { color: var(--color-security); }
.icon-notifications mat-icon { color: var(--color-notifications); }
.icon-ads mat-icon { color: var(--color-ads); }
.icon-preferences mat-icon { color: var(--color-preferences); }
.icon-profile mat-icon { color: var(--color-profile); }
.icon-invite mat-icon { color: var(--color-invite); }
.icon-workspaces mat-icon { color: var(--color-workspaces); }
.icon-history mat-icon { color: var(--color-history); }
.icon-danger mat-icon { color: var(--danger-color); }
.icon-subscriptions mat-icon { color: var(--color-subscriptions); }
.icon-support mat-icon { color: var(--color-support); }
.icon-billing mat-icon { color: var(--color-billing); }

/* Sidebar */
.sidebar {
  width: 220px;
  background: var(--sidebar-bg);
  border-right: 1px solid #e5e7eb;
  padding: 0 16px 24px;
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease-in-out;
}

.sidebar-header {
  position: sticky;
  top: 0;
  background: var(--sidebar-bg);
  padding: 24px 0;
  z-index: 1;
}

.sidebar-header h2 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 24px;
  text-transform: uppercase;
  color: var(--text-color);
  letter-spacing: 0.5px;
}


.sidebar li {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 15px;
  font-weight: 500;
  color: var(--text-color);
  cursor: pointer;
  transition: all 0.3s ease-in-out;
}

.sidebar li:hover,
.sidebar li.active {
  background: var(--active-bg);
}

/* Apply Icon Colors to Sidebar Items */
.sidebar li.icon-general:hover,
.sidebar li.icon-general.active { color: var(--color-general); }
.sidebar li.icon-privacy:hover,
.sidebar li.icon-privacy.active { color: var(--color-privacy); }
.sidebar li.icon-security:hover,
.sidebar li.icon-security.active { color: var(--color-security); }
.sidebar li.icon-notifications:hover,
.sidebar li.icon-notifications.active { color: var(--color-notifications); }
.sidebar li.icon-ads:hover,
.sidebar li.icon-ads.active { color: var(--color-ads); }
.sidebar li.icon-preferences:hover,
.sidebar li.icon-preferences.active { color: var(--color-preferences); }

.sidebar li mat-icon {
  font-size: 20px;
  color: inherit;
}

/* Content */
.content {
  flex-grow: 1;
  padding: 32px;
  animation: fadeIn 0.5s ease-in-out;
}

.header {
  padding-bottom: 16px;
  border-bottom: 2px solid #e5e7eb;
  margin-bottom: 24px;
}

.header h2 {
  font-size: 28px;
  font-weight: 700;
  color: var(--text-color);
  margin: 0;
}

/* Settings Content */
.settings-content {
  background: #ffffff;
  border-radius: 12px;
  padding: 24px;
  box-shadow: var(--card-shadow);
  height: calc(100vh - 150px);
  overflow-y: auto;
  position: relative;
}

.settings-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(145deg, rgba(37, 99, 235, 0.03) 0%, rgba(255, 255, 255, 0) 100%);
  pointer-events: none;
}

.section-title {
  font-size: 22px;
  font-weight: 700;
  color: var(--text-color);
  margin-bottom: 8px;
}

.section-subtitle {
  font-size: 15px;
  font-weight: 400;
  color: var(--text-muted);
  margin-bottom: 24px;
}

/* Cards Container */
.cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-top: 16px;
}

.settings-card {
  padding: 16px;
  border-radius: 12px;
  background: #ffffff;
  box-shadow: var(--card-shadow);
  cursor: pointer;
  transition: transform 0.2s ease-in-out, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
}

.settings-card::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 4px;
  height: 100%;
  transition: width 0.3s ease;
}

.settings-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.settings-card:hover::before {
  width: 8px;
}

/* Card Gradients and Border Colors */
.card-profile { background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, #ffffff 100%); }
.card-profile::before { background: var(--color-profile); }
.card-invite { background: linear-gradient(135deg, rgba(20, 184, 166, 0.05) 0%, #ffffff 100%); }
.card-invite::before { background: var(--color-invite); }
.card-workspaces { background: linear-gradient(135deg, rgba(99, 102, 241, 0.05) 0%, #ffffff 100%); }
.card-workspaces::before { background: var(--color-workspaces); }
.card-history { background: linear-gradient(135deg, rgba(107, 114, 128, 0.05) 0%, #ffffff 100%); }
.card-history::before { background: var(--color-history); }
.card-preferences { background: linear-gradient(135deg, rgba(16, 185, 129, 0.05) 0%, #ffffff 100%); }
.card-preferences::before { background: var(--color-preferences); }
.card-danger { background: linear-gradient(135deg, rgba(220, 38, 38, 0.05) 0%, #ffffff 100%); }
.card-danger::before { background: var(--danger-color); }
.card-subscriptions { background: linear-gradient(135deg, rgba(245, 158, 11, 0.05) 0%, #ffffff 100%); }
.card-subscriptions::before { background: var(--color-subscriptions); }
.card-security { background: linear-gradient(135deg, rgba(217, 119, 6, 0.05) 0%, #ffffff 100%); }
.card-security::before { background: var(--color-security); }
.card-support { background: linear-gradient(135deg, rgba(34, 197, 94, 0.05) 0%, #ffffff 100%); }
.card-support::before { background: var(--color-support); }
.card-billing { background: linear-gradient(135deg, rgba(14, 165, 233, 0.05) 0%, #ffffff 100%); }
.card-billing::before { background: var(--color-billing); }

/* Card Icons */
mat-card-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

mat-card-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color);
}

mat-card-content {
  font-size: 14px;
  color: var(--text-muted);
}

mat-card-header mat-icon {
  transition: transform 0.3s ease;
}

.settings-card:hover mat-card-header mat-icon {
  transform: scale(1.1);
}

.card-profile mat-icon { color: var(--color-profile); }
.card-invite mat-icon { color: var(--color-invite); }
.card-workspaces mat-icon { color: var(--color-workspaces); }
.card-history mat-icon { color: var(--color-history); }
.card-preferences mat-icon { color: var(--color-preferences); }
.card-danger mat-icon { color: var(--danger-color); }
.card-subscriptions mat-icon { color: var(--color-subscriptions); }
.card-security mat-icon { color: var(--color-security); }
.card-support mat-icon { color: var(--color-support); }
.card-billing mat-icon { color: var(--color-billing); }

/* Form Elements */
mat-form-field {
  width: 100%;
  margin-bottom: 24px;
}

mat-form-field .mat-form-field-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-muted);
}

mat-form-field .mat-form-field-outline {
  transition: border-color 0.3s ease;
}

mat-form-field .mat-form-field-outline-thick {
  border-color: var(--primary-color);
}

input[matInput] {
  font-size: 15px;
  color: var(--text-color);
}

mat-checkbox,
mat-slide-toggle,
mat-radio-group,
mat-slider {
  display: block;
  margin-bottom: 20px;
  color: var(--text-color);
}

mat-radio-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-text {
  font-size: 14px;
  color: var(--text-muted);
  margin-top: 8px;
}

/* Buttons */
button[mat-raised-button] {
  font-size: 14px;
  font-weight: 600;
  padding: 8px 20px;
  border-radius: 8px;
  background: linear-gradient(90deg, var(--primary-color) 0%, #3b82f6 100%);
  color: white;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

button[mat-raised-button]:hover:not(:disabled) {
  background: linear-gradient(90deg, var(--primary-hover) 0%, var(--primary-color) 100%);
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
  transform: translateY(-1px);
}

button[mat-raised-button]:active:not(:disabled) {
  animation: scaleDown 0.3s ease;
}

button[mat-raised-button]:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

button[mat-button][color="warn"] {
  font-size: 14px;
  font-weight: 500;
  color: var(--danger-color);
  transition: background 0.3s ease;
}

button[mat-button][color="warn"]:hover:not(:disabled) {
  background: #fee2e2;
}

/* Spinner */
mat-spinner {
  margin-left: 8px;
}

/* Table Styling */
table {
  width: 100%;
  margin-top: 16px;
  border-collapse: collapse;
  background: #ffffff;
  box-shadow: var(--card-shadow);
  border-radius: 8px;
  overflow: hidden;
}

th, td {
  padding: 12px 16px;
  text-align: left;
  font-size: 14px;
  color: var(--text-color);
}

th {
  font-weight: 600;
  background: #f9fafb;
}

tr {
  border-bottom: 1px solid #e5e7eb;
}

tr:hover {
  background: #f3f4f6;
}

/* History List */
.history-list {
  list-style: none;
  padding: 0;
}

.history-list li {
  padding: 10px 0;
  font-size: 14px;
  color: var(--text-muted);
  border-bottom: 1px solid #e5e7eb;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .sidebar {
    width: 180px;
  }

  .sidebar-header h2 {
    font-size: 16px;
  }

  .sidebar li {
    font-size: 14px;
    padding: 10px 12px;
  }

  .content {
    padding: 24px;
  }

  .header h2 {
    font-size: 24px;
  }

  .section-title {
    font-size: 20px;
  }

  .section-subtitle {
    font-size: 14px;
  }

  .settings-card {
    padding: 12px;
  }

  mat-card-title {
    font-size: 15px;
  }

  mat-card-content {
    font-size: 13px;
  }
}

@media (max-width: 768px) {
  .sidebar {
    width: 160px;
  }

  .sidebar-header h2 {
    font-size: 14px;
  }

  .sidebar li {
    font-size: 13px;
    padding: 8px 10px;
  }

  .sidebar li mat-icon {
    font-size: 18px;
  }

  .content {
    padding: 16px;
  }

  .header h2 {
    font-size: 22px;
  }

  .settings-content {
    padding: 16px;
  }

  .cards-container {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 16px;
  }
}

@media (max-width: 480px) {
  .settings-container {
    flex-direction: column;
  }

  .sidebar-icons {
    width: 100%;
    height: 60px;
    border-right: none;
    border-bottom: 1px solid #e5e7eb;
    padding: 0;
  }

  .sidebar-icons ul {
    display: flex;
    justify-content: space-around;
  }

  .sidebar-icons li {
    padding: 16px 8px;
  }

  .sidebar {
    width: 100%;
    border-right: none;
    padding: 16px;
  }

  .sidebar-header {
    padding: 16px 0;
  }

  .sidebar ul {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }

  .sidebar li {
    flex: 1 1 calc(50% - 8px);
    font-size: 12px;
    padding: 8px;
  }

  .content {
    padding: 12px;
  }

  .header h2 {
    font-size: 20px;
  }

  .settings-content {
    padding: 12px;
  }

  .cards-container {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .settings-card {
    padding: 10px;
  }

  mat-card-title {
    font-size: 14px;
  }

  mat-card-content {
    font-size: 12px;
  }

  button[mat-raised-button] {
    font-size: 13px;
    padding: 6px 16px;
  }
}
