<div class="settings-container">
  <aside class="sidebar-icons">
    <ul class="sidebar-icons-list">
      <li [class.active]="selectedTab === 'general'" (click)="selectTab('general')" matTooltip="Général" matTooltipPosition="right" class="icon-general">
        <mat-icon>settings</mat-icon>
      </li>
      <li [class.active]="selectedTab === 'security'" (click)="selectTab('security')" matTooltip="Sécurité" matTooltipPosition="right" class="icon-security">
        <mat-icon>shield</mat-icon>
      </li>
      <li (click)="editProfile()" matTooltip="Modifier le Profil" matTooltipPosition="right" class="icon-profile">
        <mat-icon>person</mat-icon>
      </li>
      <li (click)="manageSubscriptions()" matTooltip="Gérer les Abonnements" matTooltipPosition="right" class="icon-subscriptions">
        <mat-icon>subscriptions</mat-icon>
      </li>
      <li (click)="securitySettings()" matTooltip="Paramètres de Sécurité" matTooltipPosition="right" class="icon-security">
        <mat-icon>lock</mat-icon>
      </li>
      <li (click)="billingInfo()" matTooltip="Informations de Facturation" matTooltipPosition="right" class="icon-billing">
        <mat-icon>credit_card</mat-icon>
      </li>
    </ul>
  </aside>
  <aside class="sidebar">
    <div class="sidebar-header">
      <h2>Paramètres</h2>
    </div>
    <ul class="sidebar-menu">
      <li [class.active]="selectedTab === 'general'" (click)="selectTab('general')" class="icon-general">
        <mat-icon>settings</mat-icon>
        <span>Général</span>
      </li>
      <li [class.active]="selectedTab === 'security'" (click)="selectTab('security')" class="icon-security">
        <mat-icon>shield</mat-icon>
        <span>Sécurité</span>
      </li>
    </ul>
  </aside>
  <main class="content">
    <header class="header">
      <h2>{{ getTitle() }}</h2>
    </header>
    <section class="settings-content">
      <div *ngIf="selectedTab === 'general'">
        <app-general-settings></app-general-settings>
      </div>
      <div *ngIf="selectedTab === 'security'" class="tab-content">
        <app-security-settings></app-security-settings>
      </div>
      <div *ngIf="selectedTab === 'edit-profile'" class="tab-content">
        <app-edit-profile></app-edit-profile>
      </div>
    </section>
  </main>
</div>
<div *ngIf="loading" class="loading-spinner">
  <mat-progress-spinner mode="indeterminate" diameter="40"></mat-progress-spinner>
</div>
