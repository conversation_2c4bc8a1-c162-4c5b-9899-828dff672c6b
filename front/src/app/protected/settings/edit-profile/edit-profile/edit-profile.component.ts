import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { MatSnackBar } from '@angular/material/snack-bar';
import { Router } from '@angular/router';
import { OidcSecurityService } from 'angular-auth-oidc-client';
import { switchMap } from 'rxjs/operators';
import { FormsModule } from '@angular/forms';
import {MatError, MatFormField, MatLabel} from '@angular/material/form-field';
import { MatInput } from '@angular/material/input';
import { MatButton } from '@angular/material/button';
import { NgIf } from '@angular/common';
import { MatProgressSpinner } from '@angular/material/progress-spinner';

@Component({
  selector: 'app-edit-profile',
  templateUrl: './edit-profile.component.html',
  standalone: true,
  imports: [
    FormsModule,
    MatFormField,
    MatInput,
    MatLabel,
    MatButton,
    NgIf,
    Mat<PERSON>rogressSpinner,
    MatError
  ],
  styleUrls: ['./edit-profile.component.css']
})
export class EditProfileComponent implements OnInit {
  user = {
    name: '',
    email: '',
    phoneNumber: '',
    jobTitle: '',
    location: '',
    bio: ''
  };
  loading = false;

  constructor(
    private http: HttpClient,
    private snackBar: MatSnackBar,
    private router: Router,
    private oidcSecurityService: OidcSecurityService
  ) {}

  ngOnInit(): void {
    this.loadUserProfile();
  }

  loadUserProfile(): void {
    this.loading = true;
    this.oidcSecurityService.getUserData().pipe(
      switchMap(userData => {
        if (!userData) {
          throw new Error('Aucune donnée utilisateur disponible.');
        }
        const email = userData.email || userData.preferred_username || userData.sub || '';
        this.user.email = email;
        this.user.name = userData.name || '';
        return this.http.get('/api/user/profile');
      })
    ).subscribe({
      next: (profile: any) => {
        this.user.phoneNumber = profile.phoneNumber || '';
        this.user.jobTitle = profile.jobTitle || '';
        this.user.location = profile.location || '';
        this.user.bio = profile.bio || '';
        this.loading = false;
      },
      error: (error) => {
        console.error('Erreur lors du chargement du profil:', error);
        this.snackBar.open('Erreur lors du chargement du profil', 'Fermer', { duration: 3000 });
        this.loading = false;
      }
    });
  }

  updateProfile(): void {
    if (!this.user.name || !this.user.phoneNumber) {
      this.snackBar.open('Le nom et le numéro de téléphone sont requis', 'Fermer', { duration: 3000 });
      return;
    }
    this.loading = true;
    this.http.patch('/api/user/profile', {
      name: this.user.name,
      phoneNumber: this.user.phoneNumber,
      jobTitle: this.user.jobTitle,
      location: this.user.location,
      bio: this.user.bio
    }).subscribe({
      next: () => {
        this.snackBar.open('Profil mis à jour avec succès', 'Fermer', { duration: 3000 });
        this.router.navigate(['/settings']);
        this.loading = false;
      },
      error: (error) => {
        console.error('Erreur lors de la mise à jour du profil:', error);
        this.snackBar.open('Erreur lors de la mise à jour du profil', 'Fermer', { duration: 3000 });
        this.loading = false;
      }
    });
  }

  goBack(): void {
    this.router.navigate(['/settings']);
  }
}
