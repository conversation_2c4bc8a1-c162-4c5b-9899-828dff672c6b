<div class="edit-profile-container" *ngIf="!loading">
  <div class="profile-card">
    <h2 class="profile-title">Modifier le Profil</h2>
    <form (ngSubmit)="updateProfile()" #profileForm="ngForm" class="profile-form">
      <mat-form-field appearance="fill" class="full-width">
        <mat-label>Nom</mat-label>
        <input matInput [(ngModel)]="user.name" name="name" required>
        <mat-error *ngIf="profileForm.controls['name']?.touched && profileForm.controls['name']?.invalid">
          Le nom est requis.
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="fill" class="full-width">
        <mat-label>Email</mat-label>
        <input matInput [(ngModel)]="user.email" name="email" readonly>
      </mat-form-field>

      <mat-form-field appearance="fill" class="full-width">
        <mat-label>Numéro de téléphone</mat-label>
        <input matInput [(ngModel)]="user.phoneNumber" name="phoneNumber" required pattern="^\+?[1-9]\d{1,14}$">
        <mat-error *ngIf="profileForm.controls['phoneNumber']?.touched && profileForm.controls['phoneNumber']?.invalid">
          Un numéro de téléphone valide est requis (ex: +1234567890).
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="fill" class="full-width">
        <mat-label>Titre professionnel</mat-label>
        <input matInput [(ngModel)]="user.jobTitle" name="jobTitle">
      </mat-form-field>

      <mat-form-field appearance="fill" class="full-width">
        <mat-label>Localisation</mat-label>
        <input matInput [(ngModel)]="user.location" name="location">
      </mat-form-field>

      <mat-form-field appearance="fill" class="full-width">
        <mat-label>Biographie</mat-label>
        <textarea matInput [(ngModel)]="user.bio" name="bio" rows="4"></textarea>
      </mat-form-field>

      <div class="button-group">
        <button mat-raised-button color="primary" type="submit" [disabled]="profileForm.invalid" class="submit-button">
          Mettre à jour
        </button>
        <button mat-raised-button color="accent" type="button" (click)="goBack()" class="back-button">
          Retour
        </button>
      </div>
    </form>
  </div>
</div>
<div *ngIf="loading" class="loading-spinner">
  <mat-progress-spinner mode="indeterminate" diameter="40"></mat-progress-spinner>
</div>
