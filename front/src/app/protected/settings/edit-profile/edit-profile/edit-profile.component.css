.edit-profile-container {
  padding: 30px;
  max-width: 600px;
  margin: 0 auto;
  background: #ffffff;
  min-height: calc(100vh - 60px);
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.profile-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  padding: 24px;
  animation: fadeIn 0.5s ease-in;
}

.profile-title {
  color: #1a73e8;
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 20px;
  text-align: center;
}

.profile-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.full-width {
  width: 100%;
}

mat-form-field {
  font-size: 16px;
}

mat-form-field .mat-form-field-wrapper {
  padding-bottom: 0.75em;
}

.button-group {
  display: flex;
  justify-content: space-between;
  margin-top: 24px;
}

.submit-button,
.back-button {
  min-width: 120px;
  border-radius: 8px;
  transition: transform 0.2s, box-shadow 0.2s;
}

.submit-button:hover,
.back-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: rgba(255, 255, 255, 0.8);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@media (max-width: 600px) {
  .edit-profile-container {
    padding: 15px;
  }

  .profile-card {
    padding: 16px;
  }

  .button-group {
    flex-direction: column;
    gap: 12px;
  }

  .submit-button,
  .back-button {
    width: 100%;
  }
}
