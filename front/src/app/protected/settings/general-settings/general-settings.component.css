/* General Settings Component CSS */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  --primary-color: #2563eb;
  --primary-hover: #1d4ed8;
  --secondary-color: #f9fafb;
  --text-color: #040140;
  --text-muted: #6b7280;
  --sidebar-bg: #ffffff;
  --active-bg: #e6f0ff;
  --hover-bg: #f3f4f6;
  --danger-color: #dc2626;
  --card-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  --color-general: #2563eb;
  --color-privacy: #8b5cf6;
  --color-security: #d97706;
  --color-notifications: #ec4899;
  --color-ads: #f97316;
  --color-preferences: #10b981;
  --color-profile: #3b82f6;
  --color-invite: #14b8a6;
  --color-workspaces: #6366f1;
  --color-history: #6b7280;
  --color-subscriptions: #f59e0b;
  --color-support: #22c55e;
  --color-billing: #0ea5e9;
}

.section-title {
  font-size: 22px;
  font-weight: 700;
  color: var(--text-color);
  margin-bottom: 8px;
}

.section-subtitle {
  font-size: 15px;
  font-weight: 400;
  color: var(--text-muted);
  margin-bottom: 24px;
}

/* Cards Container */
.cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-top: 16px;
}

.settings-card {
  padding: 16px;
  border-radius: 12px;
  background: #ffffff;
  box-shadow: var(--card-shadow);
  cursor: pointer;
  transition: transform 0.2s ease-in-out, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
}

.settings-card::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 4px;
  height: 100%;
  transition: width 0.3s ease;
}

.settings-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.settings-card:hover::before {
  width: 8px;
}

/* Card Gradients and Border Colors */
.card-profile { background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, #ffffff 100%); }
.card-profile::before { background: var(--color-profile); }
.card-invite { background: linear-gradient(135deg, rgba(20, 184, 166, 0.05) 0%, #ffffff 100%); }
.card-invite::before { background: var(--color-invite); }
.card-workspaces { background: linear-gradient(135deg, rgba(99, 102, 241, 0.05) 0%, #ffffff 100%); }
.card-workspaces::before { background: var(--color-workspaces); }
.card-history { background: linear-gradient(135deg, rgba(107, 114, 128, 0.05) 0%, #ffffff 100%); }
.card-history::before { background: var(--color-history); }
.card-preferences { background: linear-gradient(135deg, rgba(16, 185, 129, 0.05) 0%, #ffffff 100%); }
.card-preferences::before { background: var(--color-preferences); }
.card-danger { background: linear-gradient(135deg, rgba(220, 38, 38, 0.05) 0%, #ffffff 100%); }
.card-danger::before { background: var(--danger-color); }
.card-subscriptions { background: linear-gradient(135deg, rgba(245, 158, 11, 0.05) 0%, #ffffff 100%); }
.card-subscriptions::before { background: var(--color-subscriptions); }
.card-security { background: linear-gradient(135deg, rgba(217, 119, 6, 0.05) 0%, #ffffff 100%); }
.card-security::before { background: var(--color-security); }
.card-support { background: linear-gradient(135deg, rgba(34, 197, 94, 0.05) 0%, #ffffff 100%); }
.card-support::before { background: var(--color-support); }
.card-billing { background: linear-gradient(135deg, rgba(14, 165, 233, 0.05) 0%, #ffffff 100%); }
.card-billing::before { background: var(--color-billing); }

/* Card Icons */
mat-card-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

mat-card-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color);
}

mat-card-content {
  font-size: 14px;
  color: var(--text-muted);
}

mat-card-header mat-icon {
  transition: transform 0.3s ease;
}

.settings-card:hover mat-card-header mat-icon {
  transform: scale(1.1);
}

.card-profile mat-icon { color: var(--color-profile); }
.card-invite mat-icon { color: var(--color-invite); }
.card-workspaces mat-icon { color: var(--color-workspaces); }
.card-history mat-icon { color: var(--color-history); }
.card-preferences mat-icon { color: var(--color-preferences); }
.card-danger mat-icon { color: var(--danger-color); }
.card-subscriptions mat-icon { color: var(--color-subscriptions); }
.card-security mat-icon { color: var(--color-security); }
.card-support mat-icon { color: var(--color-support); }
.card-billing mat-icon { color: var(--color-billing); }

/* Responsive Design */
@media (max-width: 1024px) {
  .section-title {
    font-size: 20px;
  }

  .section-subtitle {
    font-size: 14px;
  }

  .settings-card {
    padding: 12px;
  }

  mat-card-title {
    font-size: 15px;
  }

  mat-card-content {
    font-size: 13px;
  }
}

@media (max-width: 768px) {
  .cards-container {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 16px;
  }
}

@media (max-width: 480px) {
  .cards-container {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .settings-card {
    padding: 10px;
  }

  mat-card-title {
    font-size: 14px;
  }

  mat-card-content {
    font-size: 12px;
  }
}
