<div>
  <p class="section-title">G<PERSON>rer les paramètres de votre compte</p>
  <div class="cards-container">
    <mat-card class="settings-card card-profile" (click)="editProfile()">
      <mat-card-header>
        <mat-icon class="icon-profile">person</mat-icon>
        <mat-card-title>Modifier le Profil</mat-card-title>
      </mat-card-header>
      <mat-card-content>Mettre à jour vos informations personnelles</mat-card-content>
    </mat-card>
    <mat-card class="settings-card card-invite" [routerLink]="['/settings/invite']">
      <mat-card-header>
        <mat-icon class="icon-invite">group_add</mat-icon>
        <mat-card-title>Inviter Quelqu'un</mat-card-title>
      </mat-card-header>
      <mat-card-content>Envoyer une invitation pour rejoindre</mat-card-content>
    </mat-card>
    <mat-card class="settings-card card-subscriptions" (click)="manageSubscriptions()">
      <mat-card-header>
        <mat-icon class="icon-subscriptions">subscriptions</mat-icon>
        <mat-card-title>G<PERSON>rer les Abonnements</mat-card-title>
      </mat-card-header>
      <mat-card-content>Voir et mettre à jour vos plans</mat-card-content>
    </mat-card>
    <mat-card class="settings-card card-security" (click)="securitySettings()">
      <mat-card-header>
        <mat-icon class="icon-security">lock</mat-icon>
        <mat-card-title>Paramètres de Sécurité</mat-card-title>
      </mat-card-header>
      <mat-card-content>Activer l'authentification à deux facteurs</mat-card-content>
    </mat-card>
    <mat-card class="settings-card card-billing" (click)="billingInfo()">
      <mat-card-header>
        <mat-icon class="icon-billing">credit_card</mat-icon>
        <mat-card-title>Informations de Facturation</mat-card-title>
      </mat-card-header>
      <mat-card-content>Gérer vos détails de paiement</mat-card-content>
    </mat-card>
  </div>
</div>
