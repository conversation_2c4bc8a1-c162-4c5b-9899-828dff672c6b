import { Component, OnInit } from '@angular/core';
import { MatSnackBar } from '@angular/material/snack-bar';
import { HttpClient } from '@angular/common/http';
import { Router, RouterLink } from '@angular/router';

import { NgForOf, NgIf } from '@angular/common';
import { MatCard, MatCardContent, MatCardHeader, MatCardTitle } from '@angular/material/card';
import { MatIcon } from '@angular/material/icon';

@Component({
  selector: 'app-general-settings',
  templateUrl: './general-settings.component.html',
  standalone: true,
  imports: [
    MatCard,
    MatCardTitle,
    MatCardContent,
    MatIcon,
    MatCardHeader,
    RouterLink,
  ],
  styleUrls: ['./general-settings.component.css']
})
export class GeneralSettingsComponent implements OnInit {
  loading: boolean = false;

  constructor(
    private http: HttpClient,
    private snackBar: MatSnackBar,
    private router: Router
  ) {}

  ngOnInit(): void {
  }

  editProfile(): void {
    this.loading = true;
    this.router.navigate(['/modification']).then(() => {
      this.snackBar.open('Redirection vers la modification du profil', 'Fermer', { duration: 3000 });
      this.loading = false;
    });
  }

  manageSubscriptions(): void {
    this.loading = true;
    this.router.navigate(['/subscriptions']).then(() => {
      this.snackBar.open('Redirection vers la gestion des abonnements', 'Fermer', { duration: 3000 });
      this.loading = false;
    });
  }

  billingInfo(): void {
    this.loading = true;
    this.router.navigate(['/billing']).then(() => {
      this.snackBar.open('Redirection vers les informations de facturation', 'Fermer', { duration: 3000 });
      this.loading = false;
    });
  }

  supportCenter(): void {
    window.open('https://support.example.com', '_blank');
    this.snackBar.open('Redirection vers le centre de support', 'Fermer', { duration: 3000 });
  }

  manageWorkspaces(): void {
    this.loading = true;
    this.router.navigate(['/workspaces']).then(() => {
      this.snackBar.open('Redirection vers la gestion des espaces de travail', 'Fermer', { duration: 3000 });
      this.loading = false;
    });
  }

  securitySettings(): void {
    this.router.navigate(['/settings'], { queryParams: { tab: 'security' } });
  }
}
