import { Component, OnInit } from '@angular/core';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { HttpClient } from '@angular/common/http';
import { Router, RouterLink } from '@angular/router';
import { OidcSecurityService } from 'angular-auth-oidc-client';
import { switchMap } from 'rxjs/operators';

import { DatePipe, NgForOf, NgIf } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatIcon } from '@angular/material/icon';
import { MatProgressSpinner } from '@angular/material/progress-spinner';
import { MatTooltip } from '@angular/material/tooltip';

// Import the components
import { GeneralSettingsComponent } from './general-settings/general-settings.component';
import { SecuritySettingsComponent } from './security-settings/security-settings.component';
import {EditProfileComponent} from '../edit-profile/edit-profile.component';

@Component({
  selector: 'app-settings',
  templateUrl: './settings.component.html',
  standalone: true,
  imports: [
    FormsModule,
    NgIf,
    MatIcon,
    MatSnackBarModule,
    MatProgressSpinner,
    MatTooltip,
    GeneralSettingsComponent,
    SecuritySettingsComponent,
    EditProfileComponent
  ],
  styleUrls: ['./settings.component.css']
})
export class SettingsComponent implements OnInit {
  selectedTab: string = 'general';
  user = {
    name: '',
    email: '',
    phoneNumber: '',
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
    twoFactorEnabled: false,
    twoFactorMethod: 'email'
  };
  activeSessions: any[] = [];
  loginHistory: any[] = [];
  users: any[] = [];
  selectedUserId: number | undefined;
  loading: boolean = false;
  twoFactorCode: string = '';
  showTwoFactorSetup: boolean = false;

  constructor(
    private http: HttpClient,
    private snackBar: MatSnackBar,
    private router: Router,
    private oidcSecurityService: OidcSecurityService
  ) {}

  ngOnInit(): void {
    this.loadUserData();
  }

  loadUserData(): void {
    this.loading = true;
    this.oidcSecurityService.getUserData().subscribe({
      next: (userData) => {
        if (userData) {
          this.user.email = userData.email || userData.preferred_username || userData.sub || '';
          this.user.name = userData.name || '';
          this.http.get('/api/user/profile').subscribe({
            next: (profile: any) => {
              this.user.phoneNumber = profile.phoneNumber || '';
              this.user.twoFactorEnabled = profile.twoFactorEnabled || false;
              this.user.twoFactorMethod = profile.twoFactorMethod || 'email';
              this.loading = false;
            },
            error: (error) => {
              console.error('Erreur lors du chargement du profil:', error);
              this.snackBar.open('Erreur lors du chargement des données utilisateur', 'Fermer', { duration: 3000 });
              this.loading = false;
            }
          });
        } else {
          this.snackBar.open('Aucune donnée utilisateur disponible', 'Fermer', { duration: 3000 });
          this.loading = false;
        }
      },
      error: (error) => {
        console.error('Erreur lors de la récupération des données utilisateur:', error);
        this.snackBar.open('Erreur lors de la récupération des données utilisateur', 'Fermer', { duration: 3000 });
        this.loading = false;
      }
    });
  }

  selectTab(tab: string): void {
    this.selectedTab = tab;
    if (tab === 'security') {
      this.getActiveSessions();
      this.getLoginHistory();
    }
  }

  getTitle(): string {
    switch (this.selectedTab) {
      case 'general': return 'Paramètres généraux';
      case 'security': return 'Sécurité';
      case 'edit-profile': return 'Modifier le Profil';
      default: return 'Paramètres';
    }
  }

  manageSubscriptions(): void {
    this.loading = true;
    this.router.navigate(['/subscriptions']).then(() => {
      this.snackBar.open('Redirection vers la gestion des abonnements', 'Fermer', { duration: 3000 });
      this.loading = false;
    });
  }

  billingInfo(): void {
    this.loading = true;
    this.router.navigate(['/billing']).then(() => {
      this.snackBar.open('Redirection vers les informations de facturation', 'Fermer', { duration: 3000 });
      this.loading = false;
    });
  }

  supportCenter(): void {
    window.open('https://support.example.com', '_blank');
    this.snackBar.open('Redirection vers le centre de support', 'Fermer', { duration: 3000 });
  }

  manageWorkspaces(): void {
    this.loading = true;
    this.router.navigate(['/workspaces']).then(() => {
      this.snackBar.open('Redirection vers la gestion des espaces de travail', 'Fermer', { duration: 3000 });
      this.loading = false;
    });
  }

  viewHistory(): void {
    this.selectTab('security');
    this.getLoginHistory();
  }

  securitySettings(): void {
    this.selectTab('security');
    this.getActiveSessions();
    this.getLoginHistory();
  }

  sendInvitation(): void {
    if (!this.selectedUserId) {
      this.snackBar.open('Veuillez sélectionner un utilisateur à inviter', 'Fermer', { duration: 3000 });
      return;
    }
    this.loading = true;
    const payload = { userId: this.selectedUserId };
    this.http.post('/api/workspaces/1/invite', payload).subscribe({
      next: () => {
        this.snackBar.open('Invitation envoyée avec succès', 'Fermer', { duration: 3000 });
        this.loading = false;
      },
      error: (error: any) => {
        console.error('Erreur lors de l\'envoi de l\'invitation:', error);
        this.snackBar.open('Erreur lors de l\'envoi de l\'invitation', 'Fermer', { duration: 3000 });
        this.loading = false;
      }
    });
  }

  updatePassword(): void {
    if (!this.user.currentPassword || !this.user.newPassword || !this.user.confirmPassword) {
      this.snackBar.open('Tous les champs de mot de passe sont requis', 'Fermer', { duration: 3000 });
      return;
    }
    if (this.user.newPassword !== this.user.confirmPassword) {
      this.snackBar.open('Le nouveau mot de passe et la confirmation ne correspondent pas', 'Fermer', { duration: 3000 });
      return;
    }
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
    if (!passwordRegex.test(this.user.newPassword)) {
      this.snackBar.open(
        'Le nouveau mot de passe doit contenir au moins 8 caractères, une majuscule, une minuscule, un chiffre et un caractère spécial',
        'Fermer',
        { duration: 5000 }
      );
      return;
    }
    if (this.user.currentPassword === this.user.newPassword) {
      this.snackBar.open('Le nouveau mot de passe doit être différent de l\'ancien', 'Fermer', { duration: 3000 });
      return;
    }
    this.loading = true;
    this.oidcSecurityService.getUserData().pipe(
      switchMap(userData => {
        if (!userData) {
          throw new Error('Aucune donnée utilisateur disponible. Veuillez vous reconnecter.');
        }
        const email = userData.email || userData.preferred_username || userData.sub;
        if (!email) {
          throw new Error('Email utilisateur introuvable dans le jeton.');
        }
        const payload = {
          oldPassword: this.user.currentPassword,
          newPassword: this.user.newPassword
        };
        return this.http.post('/api/user/change-password', payload);
      })
    ).subscribe({
      next: () => {
        this.snackBar.open('Mot de passe changé avec succès', 'Fermer', { duration: 3000 });
        this.user.currentPassword = '';
        this.user.newPassword = '';
        this.user.confirmPassword = '';
        this.loading = false;
      },
      error: (error) => {
        console.error('Erreur lors du changement de mot de passe:', error);
        const errorMessage = error.error?.error || 'Échec du changement de mot de passe. Vérifiez votre mot de passe actuel et réessayez.';
        this.snackBar.open(errorMessage, 'Fermer', { duration: 5000 });
        this.loading = false;
      }
    });
  }

  toggleTwoFactorAuth(): void {
    if (this.user.twoFactorEnabled) {
      this.loading = true;
      this.http.post('/api/user/disable-2fa', {}).subscribe({
        next: () => {
          this.user.twoFactorEnabled = false;
          this.showTwoFactorSetup = false;
          this.twoFactorCode = '';
          this.snackBar.open('Authentification à deux facteurs désactivée', 'Fermer', { duration: 3000 });
          this.loading = false;
        },
        error: (error) => {
          console.error('Erreur lors de la désactivation de la 2FA:', error);
          this.snackBar.open('Erreur lors de la désactivation de la 2FA', 'Fermer', { duration: 3000 });
          this.user.twoFactorEnabled = true;
          this.loading = false;
        }
      });
    } else {
      if (!this.user.email && this.user.twoFactorMethod === 'email') {
        this.snackBar.open('Aucun e-mail configuré pour recevoir le code de vérification', 'Fermer', { duration: 3000 });
        return;
      }
      if (this.user.twoFactorMethod === 'sms' && !this.validatePhoneNumber(this.user.phoneNumber)) {
        this.snackBar.open('Numéro de téléphone invalide. Veuillez entrer un numéro valide', 'Fermer', { duration: 3000 });
        return;
      }
      this.showTwoFactorSetup = true;
      this.sendTwoFactorCode();
    }
  }

  validatePhoneNumber(phoneNumber: string): boolean {
    const phoneRegex = /^\+\d{1,3}\d{6,14}$/;
    return phoneRegex.test(phoneNumber);
  }

  sendTwoFactorCode(): void {
    if (this.user.twoFactorMethod === 'email' && !this.user.email) {
      this.snackBar.open('Aucun e-mail configuré pour recevoir le code', 'Fermer', { duration: 3000 });
      return;
    }
    if (this.user.twoFactorMethod === 'sms' && !this.validatePhoneNumber(this.user.phoneNumber)) {
      this.snackBar.open('Numéro de téléphone invalide', 'Fermer', { duration: 3000 });
      return;
    }
    this.loading = true;
    this.oidcSecurityService.getUserData().pipe(
      switchMap(userData => {
        if (!userData) {
          throw new Error('Aucune donnée utilisateur disponible. Veuillez vous reconnecter.');
        }
        const email = userData.email || userData.preferred_username || userData.sub;
        if (!email) {
          throw new Error('Email utilisateur introuvable dans le jeton.');
        }
        const payload = {
          email,
          method: this.user.twoFactorMethod,
          phoneNumber: this.user.twoFactorMethod === 'sms' ? this.user.phoneNumber : undefined
        };
        return this.http.post('/api/user/send-2fa-code', payload);
      })
    ).subscribe({
      next: () => {
        this.snackBar.open(
          `Code de vérification envoyé par ${this.user.twoFactorMethod === 'email' ? 'e-mail' : 'SMS'}`,
          'Fermer',
          { duration: 3000 }
        );
        this.loading = false;
      },
      error: (error) => {
        console.error('Erreur lors de l\'envoi du code 2FA:', error);
        this.snackBar.open(
          error.error?.message || 'Erreur lors de l\'envoi du code de vérification',
          'Fermer',
          { duration: 3000 }
        );
        this.loading = false;
      }
    });
  }

  verifyTwoFactorCode(): void {
    if (!this.twoFactorCode || this.twoFactorCode.length < 6) {
      this.snackBar.open('Veuillez entrer un code de vérification valide (6 chiffres minimum)', 'Fermer', { duration: 3000 });
      return;
    }
    this.loading = true;
    this.oidcSecurityService.getUserData().pipe(
      switchMap(userData => {
        if (!userData) {
          throw new Error('Aucune donnée utilisateur disponible. Veuillez vous reconnecter.');
        }
        const email = userData.email || userData.preferred_username || userData.sub;
        if (!email) {
          throw new Error('Email utilisateur introuvable dans le jeton.');
        }
        const payload = {
          email,
          code: this.twoFactorCode,
          method: this.user.twoFactorMethod,
          phoneNumber: this.user.twoFactorMethod === 'sms' ? this.user.phoneNumber : undefined
        };
        return this.http.post('/api/user/verify-2fa', payload);
      })
    ).subscribe({
      next: () => {
        this.user.twoFactorEnabled = true;
        this.showTwoFactorSetup = false;
        this.twoFactorCode = '';
        this.snackBar.open('Authentification à deux facteurs activée avec succès', 'Fermer', { duration: 3000 });
        this.loading = false;
        this.http.patch('/api/user/profile', {
          twoFactorEnabled: true,
          twoFactorMethod: this.user.twoFactorMethod,
          phoneNumber: this.user.twoFactorMethod === 'sms' ? this.user.phoneNumber : undefined
        }).subscribe({
          error: (error) => {
            console.error('Erreur lors de la mise à jour du profil 2FA:', error);
          }
        });
      },
      error: (error) => {
        console.error('Erreur lors de la vérification du code 2FA:', error);
        this.snackBar.open(
          error.error?.message || 'Code de vérification invalide ou expiré',
          'Fermer',
          { duration: 3000 }
        );
        this.loading = false;
      }
    });
  }

  getActiveSessions(): void {
    this.loading = true;
    this.http.get('/api/user/active-sessions').subscribe({
      next: (sessions: any) => {
        this.activeSessions = sessions;
        this.loading = false;
      },
      error: (error) => {
        console.error('Erreur récupération des sessions actives:', error);
        this.snackBar.open('Erreur lors de la récupération des sessions actives', 'Fermer', { duration: 3000 });
        this.loading = false;
      }
    });
  }

  logoutSession(sessionId: number): void {
    this.loading = true;
    this.http.post(`/api/user/logout-session/${sessionId}`, {}).subscribe({
      next: () => {
        this.snackBar.open('Session déconnectée avec succès', 'Fermer', { duration: 3000 });
        this.getActiveSessions();
        this.loading = false;
      },
      error: (error) => {
        console.error('Erreur lors de la déconnexion de la session:', error);
        this.snackBar.open('Erreur lors de la déconnexion de la session', 'Fermer', { duration: 3000 });
        this.loading = false;
      }
    });
  }

  getLoginHistory(): void {
    this.loading = true;
    this.http.get('/api/user/login-history').subscribe({
      next: (history: any) => {
        this.loginHistory = history;
        this.loading = false;
      },
      error: (error) => {
        console.error('Erreur récupération de l’historique de connexion:', error);
        this.snackBar.open('Erreur lors de la récupération de l’historique de connexion', 'Fermer', { duration: 3000 });
        this.loading = false;
      }
    });
  }
  editProfile(): void {
    this.loading = true;
    this.router.navigate(['/modification']).then(() => {
      this.snackBar.open('Redirection vers la modification du profil', 'Fermer', { duration: 3000 });
      this.loading = false;
    });
  }

  deleteAccount(): void {
    if (confirm('Êtes-vous sûr de vouloir supprimer votre compte ? Cette action est irréversible.')) {
      this.loading = true;
      this.http.delete('/api/user/delete-account').subscribe({
        next: () => {
          this.snackBar.open('Compte supprimé avec succès', 'Fermer', { duration: 3000 });
          this.router.navigate(['/login']);
          this.loading = false;
        },
        error: (error) => {
          console.error('Erreur lors de la suppression du compte:', error);
          this.snackBar.open('Erreur lors de la suppression du compte', 'Fermer', { duration: 3000 });
          this.loading = false;
        }
      });
    }
  }
}
