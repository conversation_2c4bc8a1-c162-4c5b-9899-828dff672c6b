<div class="tab-content">
  <p class="section-title">🔒 Sécurité du compte</p>
  <p class="section-subtitle"><PERSON><PERSON><PERSON> la sécurité de votre compte, mettez à jour votre mot de passe, activez l'authentification à deux facteurs et consultez les sessions actives.</p>
  <mat-card class="settings-card card-security">
    <mat-card-header>
      <mat-card-title>Changer le mot de passe</mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <mat-form-field appearance="outline">
        <mat-label>Mot de passe actuel</mat-label>
        <input matInput type="password" [(ngModel)]="user.currentPassword" required>
      </mat-form-field>
      <mat-form-field appearance="outline">
        <mat-label>Nouveau mot de passe</mat-label>
        <input matInput type="password" [(ngModel)]="user.newPassword" required>
      </mat-form-field>
      <mat-form-field appearance="outline">
        <mat-label>Confirmer le nouveau mot de passe</mat-label>
        <input matInput type="password" [(ngModel)]="user.confirmPassword" required>
      </mat-form-field>
      <button mat-raised-button color="primary" (click)="updatePassword()" [disabled]="loading">
        {{ loading ? 'Modification...' : 'Modifier' }}
        <mat-spinner *ngIf="loading" diameter="20"></mat-spinner>
      </button>
    </mat-card-content>
  </mat-card>
  <mat-card class="settings-card card-security">
    <mat-card-header>
      <mat-card-title>Authentification à deux facteurs (2FA)</mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <mat-slide-toggle [(ngModel)]="user.twoFactorEnabled" (change)="toggleTwoFactorAuth()">
        Activer l'authentification à deux facteurs
      </mat-slide-toggle>
      <p *ngIf="user.twoFactorEnabled && !showTwoFactorSetup" class="info-text">L'authentification à deux facteurs est activée. Un code de vérification sera requis lors de la connexion.</p>
      <div *ngIf="showTwoFactorSetup" class="two-factor-setup">
        <p class="section-subtitle">Configurer l'authentification à deux facteurs</p>
        <mat-form-field appearance="outline">
          <mat-label>Méthode de réception du code</mat-label>
          <mat-select [(ngModel)]="user.twoFactorMethod" (selectionChange)="sendTwoFactorCode()">
            <mat-option value="email">E-mail ({{ user.email || 'Non défini' }})</mat-option>
            <mat-option value="sms" [disabled]="!user.phoneNumber">SMS ({{ user.phoneNumber || 'Non défini' }})</mat-option>
          </mat-select>
        </mat-form-field>
        <mat-form-field appearance="outline" *ngIf="user.twoFactorMethod === 'sms'">
          <mat-label>Numéro de téléphone</mat-label>
          <input matInput [(ngModel)]="user.phoneNumber" placeholder="+21612345678" required>
        </mat-form-field>
        <mat-form-field appearance="outline">
          <mat-label>Code de vérification</mat-label>
          <input matInput [(ngModel)]="twoFactorCode" placeholder="Entrez le code reçu" required>
        </mat-form-field>
        <button mat-raised-button color="primary" (click)="verifyTwoFactorCode()" [disabled]="loading">
          {{ loading ? 'Vérification...' : 'Vérifier le code' }}
          <mat-spinner *ngIf="loading" diameter="20"></mat-spinner>
        </button>
        <button mat-button color="accent" (click)="sendTwoFactorCode()" [disabled]="loading">
          Renvoyer le code
        </button>
      </div>
    </mat-card-content>
  </mat-card>
  <mat-card class="settings-card card-security">
    <mat-card-header>
      <mat-card-title>Sessions actives</mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <table mat-table [dataSource]="activeSessions">
        <ng-container matColumnDef="device">
          <th mat-header-cell *matHeaderCellDef>Appareil</th>
          <td mat-cell *matCellDef="let session">{{ session.device }}</td>
        </ng-container>
        <ng-container matColumnDef="ip">
          <th mat-header-cell *matHeaderCellDef>Adresse IP</th>
          <td mat-cell *matCellDef="let session">{{ session.ip }}</td>
        </ng-container>
        <ng-container matColumnDef="lastActive">
          <th mat-header-cell *matHeaderCellDef>Dernière activité</th>
          <td mat-cell *matCellDef="let session">{{ session.lastActive }}</td>
        </ng-container>
        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef>Actions</th>
          <td mat-cell *matCellDef="let session">
            <button mat-button color="warn" (click)="logoutSession(session.id)" [disabled]="loading">
              Déconnecter
            </button>
          </td>
        </ng-container>
        <tr mat-header-row *matHeaderRowDef="['device', 'ip', 'lastActive', 'actions']"></tr>
        <tr mat-row *matRowDef="let row; columns: ['device', 'ip', 'lastActive', 'actions'];"></tr>
      </table>
    </mat-card-content>
  </mat-card>
  <mat-card class="settings-card card-security">
    <mat-card-header>
      <mat-card-title>Historique des connexions</mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <ul class="history-list">
        <li *ngFor="let history of loginHistory">
          {{ history.date | date:'medium' }} - {{ history.ip }} - {{ history.device }}
        </li>
      </ul>
    </mat-card-content>
  </mat-card>
</div>
