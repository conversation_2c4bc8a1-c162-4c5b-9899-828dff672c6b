.subscriptions-container {
  padding: 30px;
  max-width: 800px;
  margin: 0 auto;
  background: #ffffff;
  min-height: calc(100vh - 60px);
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.subscriptions-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  padding: 24px;
  animation: fadeIn 0.5s ease-in;
}

mat-card-title {
  color: #1a73e8;
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 20px;
}

mat-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.subscription-item {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: transform 0.2s;
}

.subscription-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.subscription-details {
  flex-grow: 1;
}

.subscription-details h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: #333;
}

.subscription-details p {
  margin: 4px 0;
  color: #666;
}

.action-button {
  min-width: 100px;
  border-radius: 8px;
}

.no-subscriptions {
  text-align: center;
  color: #666;
  font-style: italic;
}

.button-group {
  margin-top: 24px;
  display: flex;
  justify-content: center;
}

.back-button {
  min-width: 120px;
  border-radius: 8px;
  transition: transform 0.2s, box-shadow 0.2s;
}

.back-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: rgba(255, 255, 255, 0.8);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@media (max-width: 600px) {
  .subscriptions-container {
    padding: 15px;
  }

  .subscriptions-card {
    padding: 16px;
  }

  .subscription-item {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .action-button {
    width: 100%;
  }
}
