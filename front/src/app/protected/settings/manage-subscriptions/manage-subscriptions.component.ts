import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { MatSnackBar } from '@angular/material/snack-bar';
import { Router } from '@angular/router';
import {<PERSON><PERSON><PERSON>cyPipe, DatePipe, NgFor, NgIf} from '@angular/common';
import { MatCard, MatCardContent, MatCardHeader, MatCardTitle } from '@angular/material/card';
import { MatButton } from '@angular/material/button';
import { MatProgressSpinner } from '@angular/material/progress-spinner';
import { MatList, MatListItem } from '@angular/material/list';

@Component({
  selector: 'app-manage-subscriptions',
  templateUrl: './manage-subscriptions.component.html',
  standalone: true,
  imports: [
    NgIf,
    NgFor,
    MatCard,
    MatCardHeader,
    MatCardTitle,
    MatCardContent,
    MatButton,
    MatProgressSpinner,
    MatList,
    MatListItem,
    CurrencyPipe,
    DatePipe
  ],
  styleUrls: ['./manage-subscriptions.component.css']
})
export class ManageSubscriptionsComponent implements OnInit {
  subscriptions: any[] = [];
  loading = false;

  constructor(
    private http: HttpClient,
    private snackBar: MatSnackBar,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadSubscriptions();
  }

  loadSubscriptions(): void {
    this.loading = true;
    this.http.get('/api/user/subscriptions').subscribe({
      next: (subscriptions: any) => {
        this.subscriptions = subscriptions;
        this.loading = false;
      },
      error: (error) => {
        console.error('Erreur lors du chargement des abonnements:', error);
        this.snackBar.open('Erreur lors du chargement des abonnements', 'Fermer', { duration: 3000 });
        this.loading = false;
      }
    });
  }

  cancelSubscription(subscriptionId: number): void {
    if (confirm('Êtes-vous sûr de vouloir annuler cet abonnement ? Cette action est irréversible.')) {
      this.loading = true;
      this.http.post(`/api/user/subscriptions/${subscriptionId}/cancel`, {}).subscribe({
        next: () => {
          this.snackBar.open('Abonnement annulé avec succès', 'Fermer', { duration: 3000 });
          this.loadSubscriptions(); // Refresh the list
        },
        error: (error) => {
          console.error('Erreur lors de l\'annulation de l\'abonnement:', error);
          this.snackBar.open('Erreur lors de l\'annulation de l\'abonnement', 'Fermer', { duration: 3000 });
          this.loading = false;
        }
      });
    }
  }

  goBack(): void {
    this.router.navigate(['/settings']);
  }
}
