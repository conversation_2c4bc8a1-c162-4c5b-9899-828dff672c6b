<div class="subscriptions-container" *ngIf="!loading">
  <mat-card class="subscriptions-card">
    <mat-card-header>
      <mat-card-title><PERSON><PERSON>rer les Abonnements</mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <mat-list *ngIf="subscriptions.length > 0; else noSubscriptions">
        <mat-list-item *ngFor="let sub of subscriptions" class="subscription-item">
          <div class="subscription-details">
            <h3>{{ sub.planName }} ({{ sub.price | currency:'EUR' }})</h3>
            <p>Statut: {{ sub.status }}</p>
            <p>Date de début: {{ sub.startDate | date:'mediumDate' }}</p>
            <p *ngIf="sub.endDate">Date de fin: {{ sub.endDate | date:'mediumDate' }}</p>
          </div>
          <button mat-raised-button color="warn" (click)="cancelSubscription(sub.id)" [disabled]="sub.status !== 'active'" class="action-button">
            Annuler
          </button>
        </mat-list-item>
      </mat-list>
      <ng-template #noSubscriptions>
        <p class="no-subscriptions">Aucun abonnement actif trouvé.</p>
      </ng-template>
      <div class="button-group">
        <button mat-raised-button color="accent" (click)="goBack()" class="back-button">
          Retour
        </button>
      </div>
    </mat-card-content>
  </mat-card>
</div>
<div *ngIf="loading" class="loading-spinner">
  <mat-progress-spinner mode="indeterminate" diameter="40"></mat-progress-spinner>
</div>
