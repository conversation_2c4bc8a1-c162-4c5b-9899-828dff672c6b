import {Compo<PERSON>, ElementRef, Inject, ViewChild} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialogContent, MatDialogRef, MatDialogTitle} from '@angular/material/dialog';
import {UserService} from '../../services/user/user.service';
import {<PERSON><PERSON><PERSON><PERSON>, MatIconButton} from '@angular/material/button';
import {MatIcon} from '@angular/material/icon';
import {FormsModule} from '@angular/forms';
import {NgClass, NgIf} from '@angular/common';
import {MatProgressSpinner} from '@angular/material/progress-spinner';
import {MatTooltip} from '@angular/material/tooltip';

@Component({
  selector: 'app-add-cv-modal',
  templateUrl: './add-cv-modal.component.html',
  styleUrls: ['./add-cv-modal.component.css'],
  standalone: true,
  imports: [
    MatDialogTitle,
    MatDialogContent,
    MatButton,
    MatI<PERSON>B<PERSON>on,
    <PERSON>I<PERSON>,
    FormsModule,
    NgI<PERSON>,
    Mat<PERSON>rogressSpin<PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON>ooltip
  ]
})
export class AddCvModalComponent {
  selectedFile: File | null = null;
  errorMessage: string | null = null;
  successMessage: string | null = null;
  isUploading: boolean = false;
  uploadSuccess: boolean = false;
  userEmail: string;
  uploadProgress: number = 0;

  @ViewChild('fileInput') fileInput!: ElementRef;

  constructor(
    public dialogRef: MatDialogRef<AddCvModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { email?: string } | null, // Allow data to be null
    private userService: UserService
  ) {
    // Safely access data.email with a fallback
    this.userEmail = data?.email ?? '<EMAIL>'; // Replace with actual auth logic if needed
  }

  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      const file = input.files[0];

      // Vérifier le type de fichier
      const fileType = file.type.toLowerCase();
      if (!fileType.includes('pdf') && !fileType.includes('word') && !fileType.includes('document')) {
        this.errorMessage = 'Format de fichier non pris en charge. Veuillez sélectionner un fichier PDF ou Word.';
        this.selectedFile = null;
        this.uploadSuccess = false;
        return;
      }

      // Vérifier la taille du fichier
      if (file.size > 5 * 1024 * 1024) {
        this.errorMessage = 'Le fichier est trop volumineux. La taille maximale est de 5 Mo.';
        this.selectedFile = null;
        this.uploadSuccess = false;
        return;
      }

      this.selectedFile = file;
      this.errorMessage = null;
      this.successMessage = null;
      this.uploadSuccess = false;

      // Ne pas télécharger automatiquement, laisser l'utilisateur cliquer sur le bouton
      // this.uploadCV();
    }
  }

  uploadCV(): void {
    if (!this.selectedFile) {
      this.errorMessage = 'Veuillez sélectionner un fichier.';
      return;
    }

    this.isUploading = true;
    this.errorMessage = null;
    this.uploadProgress = 0;

    const formData = new FormData();
    formData.append('file', this.selectedFile);
    formData.append('email', this.userEmail);

    this.userService.addCv(this.selectedFile, this.userEmail).subscribe({
      next: (response: any) => {
        if (response && response.progress) {
          // Mise à jour de la progression
          this.uploadProgress = response.progress;
        } else {
          // Téléchargement terminé
          this.isUploading = false;
          this.uploadSuccess = true;
          this.successMessage = 'CV ajouté avec succès !';
          console.log('CV ajouté:', response);
          // Ne pas fermer automatiquement le modal, laisser l'utilisateur cliquer sur Terminer
          // this.dialogRef.close({ success: true, cvData: response });
        }
      },
      error: (error: any) => {
        this.isUploading = false;
        this.uploadSuccess = false;
        this.errorMessage = error.error?.error || 'Une erreur est survenue lors de l\'ajout du CV.';
        console.error('Erreur lors de l\'upload du CV:', error);
      }
    });
  }

  removeFile(): void {
    this.selectedFile = null;
    this.uploadSuccess = false;
    this.successMessage = null;
    this.errorMessage = null;
    this.uploadProgress = 0;

    // Réinitialiser l'input file
    const input = document.getElementById('fileInput') as HTMLInputElement;
    if (input) input.value = ''; // Clear the file input
  }

  closeWithSuccess(): void {
    // Fermer le modal avec un indicateur de succès et les données du CV
    this.dialogRef.close({
      success: true,
      cvData: {
        name: this.selectedFile?.name,
        size: this.selectedFile?.size,
        type: this.selectedFile?.type,
        lastModified: new Date().toISOString()
      }
    });
  }

  triggerFileInput(): void {
    // Déclencher le clic sur l'input file caché
    document.getElementById('fileInput')?.click();
  }

  // Formater la taille du fichier en KB ou MB
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}
