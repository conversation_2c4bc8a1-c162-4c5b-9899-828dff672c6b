<div class="cv-modal-container">
  <div class="cv-modal-header">
    <div class="header-icon">
      <mat-icon>description</mat-icon>
    </div>
    <h1 mat-dialog-title>Ajouter votre CV</h1>
    <p class="header-subtitle">Téléchargez votre CV au format PDF pour compléter votre profil</p>
  </div>

  <div mat-dialog-content class="cv-modal-content">
    <div class="upload-area" [ngClass]="{'has-file': selectedFile, 'uploading': isUploading, 'success': uploadSuccess, 'error': errorMessage}">
      <div class="upload-placeholder" *ngIf="!selectedFile && !isUploading">
        <div class="upload-icon">
          <mat-icon>cloud_upload</mat-icon>
        </div>
        <div class="upload-text">
          <h3>Glissez votre CV ici</h3>
          <p>ou</p>
          <button mat-raised-button color="primary" class="browse-button" (click)="triggerFileInput()">
            <mat-icon>folder_open</mat-icon>
            Parcourir
          </button>
          <p class="file-hint">Format accepté: PDF (max 5 Mo)</p>
        </div>
      </div>

      <div class="file-preview" *ngIf="selectedFile && !isUploading && !uploadSuccess">
        <div class="file-icon">
          <mat-icon>picture_as_pdf</mat-icon>
        </div>
        <div class="file-details">
          <h3 class="file-name">{{ selectedFile.name }}</h3>
          <p class="file-size">{{ formatFileSize(selectedFile.size) }}</p>
        </div>
        <div class="file-actions">
          <button mat-icon-button color="warn" (click)="removeFile()" matTooltip="Supprimer">
            <mat-icon>delete</mat-icon>
          </button>
          <button mat-raised-button color="primary" (click)="uploadCV()" class="upload-button">
            <mat-icon>cloud_upload</mat-icon>
            Télécharger
          </button>
        </div>
      </div>

      <div class="upload-progress" *ngIf="isUploading">
        <mat-spinner diameter="50"></mat-spinner>
        <h3>Téléchargement en cours...</h3>
        <p>Veuillez patienter pendant que nous traitons votre fichier</p>
      </div>

      <div class="upload-result success" *ngIf="uploadSuccess && !isUploading">
        <div class="result-icon">
          <mat-icon>check_circle</mat-icon>
        </div>
        <h3>CV téléchargé avec succès !</h3>
        <p>Votre CV a été ajouté à votre profil</p>
        <button mat-raised-button color="primary" (click)="closeWithSuccess()" class="close-button">
          Terminer
        </button>
      </div>

      <div class="upload-result error" *ngIf="errorMessage && !isUploading">
        <div class="result-icon">
          <mat-icon>error</mat-icon>
        </div>
        <h3>Une erreur est survenue</h3>
        <p>{{ errorMessage }}</p>
        <button mat-raised-button color="warn" (click)="removeFile()" class="retry-button">
          Réessayer
        </button>
      </div>
    </div>
  </div>

  <input
    type="file"
    id="fileInput"
    class="file-input"
    (change)="onFileSelected($event)"
    accept=".pdf,.doc,.docx"
  />
</div>
