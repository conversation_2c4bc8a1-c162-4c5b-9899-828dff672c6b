/* Styles généraux du modal */
.cv-modal-container {
  font-family: 'Roboto', sans-serif;
  color: #333;
  max-width: 550px;
  margin: 0 auto;
}

/* En-tête du modal */
.cv-modal-header {
  text-align: center;
  padding: 20px 20px 10px;
  position: relative;
}

.header-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #0a66c2, #4a90e2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 15px;
  box-shadow: 0 4px 10px rgba(10, 102, 194, 0.2);
  position: relative;
}

.header-icon::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 12px;
  height: 12px;
  background-color: #ff9800; /* Petite touche d'orange */
  border-radius: 50%;
  border: 2px solid white;
}

.header-icon mat-icon {
  color: white;
  font-size: 30px;
  height: 30px;
  width: 30px;
}

.cv-modal-header h1 {
  font-size: 24px;
  font-weight: 500;
  margin: 0 0 8px;
  color: #0a66c2;
}

.header-subtitle {
  color: #666;
  font-size: 14px;
  margin: 0 0 15px;
}

/* Zone de contenu */
.cv-modal-content {
  padding: 0 20px 20px;
}

/* Zone de dépôt de fichier */
.upload-area {
  border: 2px dashed #ccc;
  border-radius: 12px;
  padding: 30px;
  text-align: center;
  background-color: #f9f9f9;
  transition: all 0.3s ease;
  min-height: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.upload-area.has-file {
  border-color: #0a66c2;
  background-color: #f0f7ff;
}

.upload-area.uploading {
  border-color: #0a66c2;
  background-color: #f0f7ff;
}

.upload-area.success {
  border-color: #4caf50;
  background-color: #f1f8e9;
}

.upload-area.error {
  border-color: #f44336;
  background-color: #fef6f6;
}

/* Placeholder pour le dépôt de fichier */
.upload-placeholder {
  width: 100%;
}

.upload-icon {
  margin-bottom: 15px;
}

.upload-icon mat-icon {
  font-size: 48px;
  height: 48px;
  width: 48px;
  color: #0a66c2;
}

.upload-text h3 {
  font-size: 18px;
  font-weight: 500;
  margin: 0 0 10px;
  color: #333;
}

.upload-text p {
  margin: 8px 0;
  color: #666;
}

.file-hint {
  font-size: 12px;
  color: #888;
  margin-top: 15px !important;
}

.browse-button {
  margin: 10px auto;
  background-color: #0a66c2;
  position: relative;
  overflow: hidden;
}

.browse-button::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 3px;
  height: 100%;
  background-color: #ff9800; /* Petite touche d'orange */
}

/* Aperçu du fichier sélectionné */
.file-preview {
  display: flex;
  align-items: center;
  width: 100%;
  background-color: white;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.file-icon {
  background-color: #e3f2fd;
  width: 50px;
  height: 50px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.file-icon mat-icon {
  color: #0a66c2;
  font-size: 30px;
}

.file-details {
  flex: 1;
  text-align: left;
}

.file-name {
  font-size: 16px;
  font-weight: 500;
  margin: 0 0 5px;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 250px;
}

.file-size {
  font-size: 12px;
  color: #666;
  margin: 0;
}

.file-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.upload-button {
  background-color: #0a66c2;
  position: relative;
  overflow: hidden;
}

.upload-button::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 3px;
  height: 100%;
  background-color: #ff9800; /* Petite touche d'orange */
}

/* Progression du téléchargement */
.upload-progress {
  width: 100%;
  text-align: center;
}

.upload-progress mat-spinner {
  margin: 0 auto 20px;
}

.upload-progress h3 {
  font-size: 18px;
  font-weight: 500;
  margin: 0 0 10px;
  color: #0a66c2;
}

.upload-progress p {
  color: #666;
  margin: 0;
}

/* Résultat du téléchargement */
.upload-result {
  width: 100%;
  text-align: center;
  padding: 20px;
  border-radius: 8px;
}

.result-icon {
  margin-bottom: 15px;
}

.result-icon mat-icon {
  font-size: 48px;
  height: 48px;
  width: 48px;
}

.upload-result.success .result-icon mat-icon {
  color: #4caf50;
}

.upload-result.error .result-icon mat-icon {
  color: #f44336;
}

.upload-result h3 {
  font-size: 18px;
  font-weight: 500;
  margin: 0 0 10px;
}

.upload-result.success h3 {
  color: #4caf50;
}

.upload-result.error h3 {
  color: #f44336;
}

.upload-result p {
  color: #666;
  margin: 0 0 20px;
}

.close-button, .retry-button {
  margin: 0 auto;
}

/* Input de fichier caché */
.file-input {
  display: none;
}

/* Animation pour le spinner */
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.spinner {
  animation: spin 1s linear infinite;
}
