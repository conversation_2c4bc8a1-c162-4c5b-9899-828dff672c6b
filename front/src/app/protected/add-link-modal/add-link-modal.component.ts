import { Component, Inject } from '@angular/core';
import {
  MatDialogRef,
  MAT_DIALOG_DATA,
  MatDialogActions,
  MatDialogTitle,
  MatDialogContent
} from '@angular/material/dialog';
import {<PERSON><PERSON><PERSON>r, MatFormField, <PERSON><PERSON>abe<PERSON>} from '@angular/material/form-field';
import { FormsModule } from '@angular/forms';
import { MatButton } from '@angular/material/button';
import { MatInput } from '@angular/material/input';
import { MatIcon } from '@angular/material/icon';
import { NgIf } from '@angular/common';

@Component({
  selector: 'app-add-link-modal',
  templateUrl: './add-link-modal.component.html',
  standalone: true,
  imports: [
    MatFormField,
    FormsModule,
    MatDialogActions,
    MatButton,
    MatInput,
    MatDialogTitle,
    MatDialogContent,
    MatLabel,
    MatIcon,
    NgIf,
    MatError
  ],
  styleUrls: ['./add-link-modal.component.scss']
})
export class AddLinkModalComponent {
  link: any = {
    name: '',
    url: ''
  };

  constructor(
    public dialogRef: MatDialogRef<AddLinkModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    // If data is provided (edit mode), pre-fill the form with existing values
    if (this.data) {
      this.link.name = this.data.name || '';
      this.link.url = this.data.url || '';
    }
  }

  onNoClick(): void {
    this.dialogRef.close();
  }

  addLink(): void {
    if (this.link.name && this.link.url) {
      this.dialogRef.close(this.link);
    } else {
      alert("Veuillez remplir tous les champs.");
    }
  }
}
