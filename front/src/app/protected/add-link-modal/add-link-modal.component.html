<h1 mat-dialog-title class="dialog-title">
  <mat-icon class="icon-title">link</mat-icon>
  {{ data ? 'Modifier un lien' : 'Ajouter un lien' }}
</h1>

<div mat-dialog-content class="dialog-content">
  <mat-form-field appearance="outline" class="full-width animate-field">
    <mat-label>Nom du lien</mat-label>
    <input matInput [(ngModel)]="link.name" required />
    <mat-error *ngIf="!link.name">Le nom du lien est requis</mat-error>
  </mat-form-field>

  <mat-form-field appearance="outline" class="full-width animate-field">
    <mat-label>URL</mat-label>
    <input matInput [(ngModel)]="link.url" required />
    <mat-error *ngIf="!link.url">L'URL est requise</mat-error>
  </mat-form-field>
</div>

<div mat-dialog-actions class="dialog-actions">
  <button mat-stroked-button color="warn" (click)="onNoClick()" class="btn-cancel">
    <mat-icon>cancel</mat-icon> Annuler
  </button>
  <button mat-flat-button color="primary" (click)="addLink()" [disabled]="!link.name || !link.url" class="btn-add">
    <mat-icon>{{ data ? 'edit' : 'add_circle' }}</mat-icon>
    {{ data ? 'Mettre à jour' : 'Ajouter' }}
  </button>
</div>
