<div class="main-container">
  <div class="header-container">
    <h2 class="posts-title">Posts Archivés</h2>
    <button mat-raised-button color="primary" [routerLink]="['/acceuilposts']" class="back-button">
      <mat-icon>arrow_back</mat-icon> Retour aux posts
    </button>
  </div>

  <div class="post-cards-container">
    <mat-card *ngFor="let post of archivedPosts" class="post-card">
      <mat-card-header class="post-card-header">
        <mat-icon mat-card-avatar class="avatar-icon">archive</mat-icon>
        <mat-card-title>{{ post.titre }}</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <p class="description">{{ post.description }}</p>
        <p class="info">Entreprise: {{ post.entreprise }}</p>
        <p class="info">Profil: {{ post.profileRequest }}</p>
        <p class="info">Type de contrat: {{ post.contractType }}</p>
        <p class="info">Publié le: {{ post.datePublication | date: 'short' }}</p>
      </mat-card-content>
      <mat-card-actions class="post-actions">
        <button mat-icon-button class="btn-details" (click)="viewPost(post)" aria-label="Voir le post" matTooltip="Voir">
          <mat-icon>visibility</mat-icon>
        </button>
        <button mat-icon-button class="btn-unarchive" (click)="unarchivePost(post)" aria-label="Désarchiver le post" matTooltip="Désarchiver">
          <mat-icon>unarchive</mat-icon>
        </button>
        <button mat-icon-button class="btn-delete" (click)="deletePost(post)" aria-label="Supprimer définitivement" matTooltip="Supprimer">
          <mat-icon>delete_forever</mat-icon>
        </button>
      </mat-card-actions>
    </mat-card>
  </div>

  <div class="empty-state" *ngIf="!archivedPosts.length">
    <p>Aucun post archivé trouvé.</p>
  </div>
</div>
