import { Component, OnInit } from '@angular/core';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router';
import { DatePipe } from '@angular/common';
import { PostsService } from '../../services/posts/posts.service';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { OidcSecurityService } from 'angular-auth-oidc-client'; // Ajout pour l'authentification
import { ConfirmationService } from '../../services/confirmation/confirmation.service';

type Post = {
  id: string;
  titre: string;
  description: string;
  entreprise: string;
  userId: string;
  profileRequest: string;
  contractType: string;
  datePublication: string;
  archived: boolean;
};

@Component({
  selector: 'app-archive',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule,
    RouterModule,
    DatePipe,
    MatSnackBarModule
  ],
  templateUrl: './archive.component.html',
  styleUrls: ['./archive.component.css']
})
export class ArchiveComponent implements OnInit {
  archivedPosts: Post[] = [];

  constructor(
    private postsService: PostsService,
    private router: Router,
    private snackBar: MatSnackBar,
    private oidcSecurityService: OidcSecurityService, // Ajout du service d'authentification
    private confirmationService: ConfirmationService
  ) {}

  ngOnInit(): void {
    this.loadArchivedPosts();
  }

  loadArchivedPosts(): void {
    this.postsService.getArchivedPosts().subscribe({
      next: (posts: Post[]) => {
        console.log('Posts archivés reçus:', posts);
        this.archivedPosts = posts;
        if (!posts.length) {
          this.snackBar.open('Aucun post archivé trouvé.', 'Fermer', {
            duration: 3000,
            panelClass: ['info-snackbar']
          });
        }
      },
      error: (error: any) => {
        console.error('Erreur lors du chargement des posts archivés:', error);
        this.snackBar.open('Erreur lors du chargement des posts archivés.', 'Fermer', {
          duration: 3000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  viewPost(post: Post): void {
    this.router.navigate(['/posts', post.id]);
  }

  deletePost(post: Post): void {
    this.confirmationService.confirmDeletion(`le post archivé "${post.titre}"`).subscribe(confirmed => {
      if (confirmed) {
        this.postsService.deletePost(post.id).subscribe({
          next: () => {
            this.archivedPosts = this.archivedPosts.filter(p => p.id !== post.id);
            this.snackBar.open('Post supprimé définitivement.', 'Fermer', {
              duration: 3000,
              panelClass: ['success-snackbar']
            });
          },
          error: (error: any) => {
            console.error('Erreur lors de la suppression du post:', error);
            this.snackBar.open('Erreur lors de la suppression du post.', 'Fermer', {
              duration: 3000,
              panelClass: ['error-snackbar']
            });
          }
        });
      }
    });
  }

  unarchivePost(post: Post): void {
    this.confirmationService.confirmAction('désarchiver', `le post "${post.titre}"`).subscribe(confirmed => {
      if (confirmed) {
        this.postsService.unarchivePost(post.id).subscribe({
          next: () => {
            this.archivedPosts = this.archivedPosts.filter(p => p.id !== post.id);
            this.snackBar.open('Post désarchivé avec succès.', 'Fermer', {
              duration: 3000,
              panelClass: ['success-snackbar']
            });
          },
          error: (error: any) => {
            console.error('Erreur lors du désarchivage du post:', error);
            this.snackBar.open('Erreur lors du désarchivage du post.', 'Fermer', {
              duration: 3000,
              panelClass: ['error-snackbar']
            });
          }
        });
      }
    });
  }
}
