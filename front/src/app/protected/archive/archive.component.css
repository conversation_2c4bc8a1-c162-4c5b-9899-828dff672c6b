.main-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.posts-title {
  font-size: 24px;
  font-weight: 500;
  color: #333;
}

.back-button {
  margin-left: 20px;
}

.post-cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.post-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.post-card:hover {
  transform: translateY(-4px);
}

.post-card-header {
  display: flex;
  align-items: center;
  padding: 16px;
}

.avatar-icon {
  font-size: 40px;
  color: #666;
}

mat-card-title {
  font-size: 18px;
  font-weight: 500;
}

.description {
  font-size: 14px;
  color: #555;
  margin-bottom: 12px;
}

.info {
  font-size: 13px;
  color: #777;
  margin: 4px 0;
}

.post-actions {
  display: flex;
  justify-content: flex-end;
  padding: 8px 16px;
}

.btn-details {
  color: #2196f3;
}

.btn-unarchive {
  color: #4caf50;
}

.btn-delete {
  color: #f44336;
}

.empty-state {
  text-align: center;
  padding: 40px;
  color: #666;
}

.success-snackbar {
  background-color: #4caf50;
  color: #ffffff;
  border-radius: 8px;
}

.error-snackbar {
  background-color: #f44336;
  color: #ffffff;
  border-radius: 8px;
}

.info-snackbar {
  background-color: #2196f3;
  color: #ffffff;
  border-radius: 8px;
}
