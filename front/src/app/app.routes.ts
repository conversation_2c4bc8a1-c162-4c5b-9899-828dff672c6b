import { Routes } from '@angular/router';
import { notAuthenticatedGuard } from './guards/not-authenticated.guard';
import { authenticatedGuard } from './guards/authenticated.guard';
import { RoleGuard } from './guards/role.guard';
import { WorkspaceComponent } from './protected/add-workspace/workspace.component';
import { LayoutComponent } from './protected/layout/layout.component';
import { SettingsComponent } from './protected/settings/settings.component';
import { InvitationComponent } from './protected/invitation/invitation.component';
import { HomeComponent } from './public/home/<USER>';
import { DashboardComponent } from './protected/dashboard/dashboard.component';
import { PaymentComponent } from './protected/payment/payment.component';
import { PlansComponent } from './protected/payment/plans/plans.component';
import { PaymentSuccessComponent } from './protected/payment/success/success.component';
import { PaymentCancelComponent } from './protected/payment/cancel/cancel.component';
import { PostsComponent } from './protected/posts/posts.component';
import { AcceuilPostsComponent } from './protected/acceuil-posts/acceuil-posts.component';
import { NotificationsComponent } from './protected/notifications/notifications.component';
import { ProfileWorkspaceComponent } from './protected/profile-workspace/profile-workspace.component';
import { AcceuilComponent } from './protected/acceuil/acceuil.component';
import { EditPostComponent } from './protected/edit-post/edit-post.component';
import { ArchiveComponent } from './protected/archive/archive.component';
import { ChangePasswordComponent } from './protected/change-password/change-password.component';
import { TestResultsComponent } from './protected/testResults/testResults.component';
import { AdminDashboardComponent } from './protected/admin-dashboard/admin-dashboard.component';
import { JobApplicationComponent } from './protected/job-application/job-application.component';
import { WorkspacesComponent } from './protected/workspaces/workspaces/workspaces.component';
import { SavedItemsComponent } from './protected/saved-items/saved-items.component';
import { RecruiterProfileComponent } from './protected/recruiter-profile/recruiter-profile.component';
import { RecruiterInvitationsComponent } from './protected/recruiter-invitations/recruiter-invitations.component';
import { ManageUsersComponent } from './protected/features/manage-users/manage-users.component';
import { ManagePaymentsComponent } from './protected/features/manage-payments/manage-payments.component';
import { ManageOffersComponent } from './protected/features/manage-offers/manage-offers.component';
import { ManageInvitationsComponent } from './protected/features/manage-invitations/manage-invitations.component';
import { PostDetailComponent } from './protected/post-detail/post-detail.component';
import { TestComponent } from './protected/test/test.component';
import { WelcomePageComponent } from './protected/welcome-page/welcome-page.component';
import { CandidatesListComponent } from './protected/candidates-list/candidates-list.component';
import { EditProfileComponent } from './protected/settings/edit-profile/edit-profile/edit-profile.component';
import {ManageSubscriptionsComponent} from './protected/settings/manage-subscriptions/manage-subscriptions.component';
import { TestSettingsComponent } from './test-settings/test-settings.component';
const afterLogin = localStorage.getItem('afterLogin');
const redirectTo = afterLogin ? '/'+JSON.parse(afterLogin).map((a: any) => a.path).join('/') : '/home';
localStorage.removeItem('afterLogin');

export const routes: Routes = [
  {
    path: 'auth',
    canActivate: [notAuthenticatedGuard],
    loadChildren: () => import('./public/auth/auth.module').then(m => m.AuthModule)
  },
  {
    path: 'profile',
    canActivate: [authenticatedGuard],
    loadChildren: () => import('./protected/profile/profile.module').then(m => m.ProfileModule)
  },
  {
    path: '',
    pathMatch: 'full',
    redirectTo: redirectTo
  },
  {
    path: 'workspace',
    component: LayoutComponent,
    children: [
      { path: '', pathMatch: 'full', component: WorkspaceComponent }
    ]
  },
  {
    path: 'settings',
    canActivate: [authenticatedGuard],
    component: LayoutComponent,
    children: [
      { path: '', pathMatch: 'full', component: SettingsComponent },
      { path: 'invite', component: InvitationComponent }
    ]
  },
  {
    path: 'home',
    canActivate: [notAuthenticatedGuard],
    component: HomeComponent
  },
  {
    path: 'protected/payment',
    canActivate: [authenticatedGuard],
    component: LayoutComponent,
    children: [
      { path: '', pathMatch: 'full', component: PaymentComponent },
      { path: 'plans', component: PlansComponent },
      { path: 'success', component: PaymentSuccessComponent },
      { path: 'cancel', component: PaymentCancelComponent }
    ]
  },
  {
    path: 'dashboard',
    canActivate: [authenticatedGuard, RoleGuard],
    component: DashboardComponent
  },
  {
    path: 'posts',
    component: LayoutComponent,
    canActivate: [authenticatedGuard, RoleGuard],
    children: [
      { path: '', pathMatch: 'full', component: PostsComponent }
    ]
  },
  {
    path: 'acceuilposts',
    component: LayoutComponent,
    canActivate: [authenticatedGuard, RoleGuard],
    children: [
      { path: '', pathMatch: 'full', component: AcceuilPostsComponent }
    ]
  },
  {
    path: 'acceuil',
    component: LayoutComponent,
    canActivate: [authenticatedGuard, RoleGuard],
    children: [
      { path: '', pathMatch: 'full', component: AcceuilComponent }
    ]
  },
  {
    path: 'notifications',
    component: LayoutComponent,
    canActivate: [authenticatedGuard, RoleGuard],
    children: [
      { path: '', pathMatch: 'full', component: NotificationsComponent }
    ]
  },
  {
    path: 'workspace-profile',
    component: LayoutComponent,
    canActivate: [authenticatedGuard, RoleGuard],
    children: [
      { path: '', pathMatch: 'full', component: ProfileWorkspaceComponent }
    ]
  },
  {
    path: 'results',
    component: TestResultsComponent
  },
  {
    path: 'admin/dashboard',
    canActivate: [authenticatedGuard],
    component: AdminDashboardComponent
  },
  {
    path: 'job-application/:postId/:postTitle',
    component: LayoutComponent,
    canActivate: [authenticatedGuard, RoleGuard],
    children: [
      { path: '', component: JobApplicationComponent }
    ]
  },
  {
    path: 'workspaces',
    component: LayoutComponent,
    children: [
      { path: '', pathMatch: 'full', component: WorkspacesComponent }
    ]
  },
  {
    path: 'saved-items',
    component: LayoutComponent,
    canActivate: [authenticatedGuard, RoleGuard],
    children: [
      { path: '', pathMatch: 'full', component: SavedItemsComponent }
    ]
  },
  {
    path: 'recruiter-profile',
    component: LayoutComponent,
    canActivate: [authenticatedGuard, RoleGuard],
    children: [
      { path: '', pathMatch: 'full', component: RecruiterProfileComponent }
    ]
  },
  {
    path: 'recruiter-invitations',
    component: LayoutComponent,
    canActivate: [authenticatedGuard, RoleGuard],
    children: [
      { path: '', pathMatch: 'full', component: RecruiterInvitationsComponent }
    ]
  },
  {
    path: 'edit-post/:id',
    component: LayoutComponent,
    canActivate: [authenticatedGuard, RoleGuard],
    children: [
      { path: '', component: EditPostComponent }
    ]
  },
  {
    path: 'archive',
    component: LayoutComponent,
    canActivate: [authenticatedGuard, RoleGuard],
    children: [
      { path: '', pathMatch: 'full', component: ArchiveComponent }
    ]
  },
  {
    path: 'change-password',
    component: LayoutComponent,
    children: [
      { path: '', pathMatch: 'full', component: ChangePasswordComponent }
    ]
  },
  {
    path: 'manage-users',
    canActivate: [authenticatedGuard],
    component: ManageUsersComponent
  },
  {
    path: 'manage-payments',
    canActivate: [authenticatedGuard],
    component: ManagePaymentsComponent
  },
  {
    path: 'manage-offers',
    canActivate: [authenticatedGuard],
    component: ManageOffersComponent
  },
  {
    path: 'manage-invitations',
    canActivate: [authenticatedGuard],
    component: ManageInvitationsComponent
  },
  {
    path: 'post-detail/:id/:title',
    component: LayoutComponent,
    children: [
      { path: '', component: PostDetailComponent }
    ]
  },
  {
    path: 'test/:id',
    canActivate: [authenticatedGuard],
    component: TestComponent
  },

  {
    path: 'welcome/:id',
    canActivate: [authenticatedGuard],
    component: WelcomePageComponent
  },
  {
    path: 'candidat/:postId',
    component: LayoutComponent,
    canActivate: [authenticatedGuard],
    children: [
      { path: '', component: CandidatesListComponent }
    ]
  },
  {
    path: 'modification',
    component: LayoutComponent,
    canActivate: [authenticatedGuard],
    children: [
      { path: '', component: EditProfileComponent }
    ]
  },
  {
    path: 'test-settings/:candidateId',
    component: LayoutComponent,
    canActivate: [authenticatedGuard],
    children: [
      { path: '', component: TestSettingsComponent }
    ]
  },

];

