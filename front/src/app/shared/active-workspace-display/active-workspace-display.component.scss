.workspace-display {
  display: flex;
  align-items: center;
  gap: 10px;
}

.workspace-logo {
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #001660;
  color: white;
}

.logo-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.logo-placeholder {
  color: white;
}

.workspace-name {
  font-weight: 500;
  color: #001660;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.loading-indicator {
  font-size: 14px;
  color: #666;
}

/* Tailles */
.small {
  .workspace-logo {
    width: 30px;
    height: 30px;
    
    .logo-placeholder {
      font-size: 18px;
      width: 18px;
      height: 18px;
    }
  }
  
  .workspace-name {
    font-size: 14px;
  }
}

.medium {
  .workspace-logo {
    width: 40px;
    height: 40px;
    
    .logo-placeholder {
      font-size: 24px;
      width: 24px;
      height: 24px;
    }
  }
  
  .workspace-name {
    font-size: 16px;
  }
}

.large {
  .workspace-logo {
    width: 60px;
    height: 60px;
    
    .logo-placeholder {
      font-size: 36px;
      width: 36px;
      height: 36px;
    }
  }
  
  .workspace-name {
    font-size: 18px;
    font-weight: 600;
  }
}
