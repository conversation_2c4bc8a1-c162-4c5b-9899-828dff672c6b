import { Component, OnInit, Input, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { WorkspaceService } from '../../services/workspace/workspace.service';
import { environment } from '../../../environments/environment';

@Component({
  selector: 'app-active-workspace-display',
  standalone: true,
  imports: [CommonModule, MatIconModule],
  templateUrl: './active-workspace-display.component.html',
  styleUrls: ['./active-workspace-display.component.scss']
})
export class ActiveWorkspaceDisplayComponent implements OnInit {
  @Input() showName: boolean = true;
  @Input() showLogo: boolean = true;
  @Input() size: 'small' | 'medium' | 'large' = 'medium';

  workspaceName: string = '';
  workspaceLogoUrl: string | null = null;
  workspaceId: string | null = null;
  assetsUrl = environment.assetsUrl;
  isLoading: boolean = true;

  constructor(
    private workspaceService: WorkspaceService,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.loadWorkspaceInfo();
  }

  private loadWorkspaceInfo(): void {
    this.isLoading = true;
    this.workspaceService.getActiveWorkspaceNameAndLogo().subscribe({
      next: (info: {name: string, logoUrl: string | null}) => {
        this.workspaceName = info.name;
        this.workspaceLogoUrl = info.logoUrl;
        this.isLoading = false;
        this.cdr.detectChanges();
      },
      error: (error) => {
        console.error('Erreur lors du chargement des informations du workspace:', error);
        this.workspaceName = 'Erreur';
        this.workspaceLogoUrl = null;
        this.isLoading = false;
        this.cdr.detectChanges();
      }
    });
  }

  getLogoUrl(logoPath: string | null): string {
    if (!logoPath) return 'assets/images/placeholder-logo.png';

    // Vérifier si le chemin est une URL complète
    if (logoPath.startsWith('http://') || logoPath.startsWith('https://')) {
      return logoPath;
    }

    // Construire l'URL avec le chemin du serveur
    return this.assetsUrl + (logoPath.startsWith('/') ? '' : '/') + logoPath;
  }
}
