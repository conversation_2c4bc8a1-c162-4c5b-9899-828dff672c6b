<div class="workspace-display" [ngClass]="size">
  <div *ngIf="isLoading" class="loading-indicator">
    <span>Chargement...</span>
  </div>
  
  <ng-container *ngIf="!isLoading">
    <div *ngIf="showLogo" class="workspace-logo">
      <img *ngIf="workspaceLogoUrl" [src]="getLogoUrl(workspaceLogoUrl)" [alt]="workspaceName + ' Logo'" class="logo-img">
      <mat-icon *ngIf="!workspaceLogoUrl" class="logo-placeholder">business</mat-icon>
    </div>
    
    <div *ngIf="showName" class="workspace-name">
      {{ workspaceName }}
    </div>
  </ng-container>
</div>
