import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { SignupStateService } from '../../services/signup-state.service';
import { trigger, state, style, transition, animate } from '@angular/animations';
import { OidcSecurityService } from 'angular-auth-oidc-client';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-profile-completion-message',
  standalone: true,
  imports: [CommonModule, MatIconModule, MatButtonModule],
  template: `
    <div
      *ngIf="showMessage"
      class="message-overlay"
      [@slideIn]="showMessage ? 'in' : 'out'"
    >
      <div class="message-container">
        <!-- Background Effects -->
        <div class="bg-effects">
          <div class="floating-orb orb-1"></div>
          <div class="floating-orb orb-2"></div>
          <div class="floating-orb orb-3"></div>
        </div>

        <!-- Main Content -->
        <div class="message-content">
          <!-- Icon Section -->
          <div class="icon-section">
            <div class="icon-wrapper">
              <mat-icon class="main-icon">verified_user</mat-icon>
              <div class="icon-glow"></div>
            </div>
          </div>

          <!-- Text Section -->
          <div class="text-section">
            <h2 class="welcome-title">Bienvenue sur Kairos IT !</h2>
            <div class="title-underline"></div>

            <div class="status-card">
              <div class="status-icon">
                <mat-icon>schedule</mat-icon>
              </div>
              <div class="status-content">
                <h3 class="status-title">Profil en cours de traitement</h3>
                <p class="status-description">
                  Votre profil sera rempli dans quelques minutes
                </p>
              </div>
            </div>

            <div class="info-section">
              <div class="info-item">
                <mat-icon class="info-icon">psychology</mat-icon>
                <span>Analyse intelligente de votre CV</span>
              </div>
              <div class="info-item">
                <mat-icon class="info-icon">auto_awesome</mat-icon>
                <span>Optimisation automatique du profil</span>
              </div>
              <div class="info-item">
                <mat-icon class="info-icon">notifications_active</mat-icon>
                <span>Notification dès que c'est prêt</span>
              </div>
            </div>
          </div>

          <!-- Progress Section -->
          <div class="progress-section">
            <div class="progress-bar">
              <div class="progress-fill"></div>
            </div>
            <p class="progress-text">Traitement en cours...</p>
          </div>

          <!-- Action Section -->
          <div class="action-section">
            <button
              class="primary-button"
              (click)="acknowledgeMessage()"
            >
              <span>Parfait, j'ai compris</span>
              <mat-icon>check</mat-icon>
            </button>

            <button
              class="secondary-button"
              (click)="exploreWhileWaiting()"
            >
              <mat-icon>explore</mat-icon>
              <span>Explorer en attendant</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  `,
  styleUrls: ['./profile-completion-message.component.scss'],
  animations: [
    trigger('slideIn', [
      state('in', style({ transform: 'translateY(0)', opacity: 1 })),
      state('out', style({ transform: 'translateY(-100%)', opacity: 0 })),
      transition('out => in', [
        animate('0.3s ease-out')
      ]),
      transition('in => out', [
        animate('0.3s ease-in')
      ])
    ])
  ]
})
export class ProfileCompletionMessageComponent implements OnInit, OnDestroy {
  showMessage = false;
  private authSubscription?: Subscription;

  constructor(
    private signupStateService: SignupStateService,
    private oidcSecurityService: OidcSecurityService
  ) {}

  ngOnInit(): void {
    console.log('🚀 ProfileCompletionMessage - ngOnInit called');

    // Écouter les changements d'état d'authentification pour affichage IMMÉDIAT
    this.authSubscription = this.oidcSecurityService.checkAuth().subscribe((loginResponse: any) => {
      console.log('🔐 Auth state changed:', loginResponse.isAuthenticated);
      if (loginResponse.isAuthenticated) {
        console.log('✅ User is authenticated - checking message conditions IMMEDIATELY');
        // Vérifier immédiatement après authentification
        this.checkAndShowMessageAfterLogin();
      }
    });

    // Vérification initiale au cas où l'utilisateur est déjà connecté
    setTimeout(() => {
      console.log('⏰ Initial check after component load...');
      this.checkAndShowMessageAfterLogin();
    }, 500);
  }

  private checkAndShowMessageAfterLogin(): void {
    console.log('🔍 Checking if should show profile message AFTER LOGIN...');

    // Vérifier les valeurs dans localStorage pour debug
    const signupCompleted = localStorage.getItem('signup_completed');
    console.log('📊 LocalStorage signup_completed:', signupCompleted);

    // Vérifier si l'utilisateur vient de s'inscrire
    const shouldShow = this.signupStateService.checkAndShowMessageAfterLogin();

    console.log('🎯 Should show message:', shouldShow);
    console.log('🎯 Current showMessage state:', this.showMessage);

    if (shouldShow) {
      console.log('✅ AFFICHAGE IMMÉDIAT DU MESSAGE: "Votre profil sera rempli dans quelques minutes"');
      this.showMessage = true;
      console.log('✅ showMessage set to:', this.showMessage);
    } else {
      console.log('❌ Conditions non remplies - message non affiché');
      console.log('❌ Raison: signup_completed =', signupCompleted);
    }
  }

  ngOnDestroy(): void {
    // Nettoyer l'abonnement aux événements d'authentification
    if (this.authSubscription) {
      this.authSubscription.unsubscribe();
    }
  }

  acknowledgeMessage(): void {
    console.log('👍 User acknowledged the message - closing message');
    // Fermer le message quand l'utilisateur clique sur "Parfait, j'ai compris"
    this.showMessage = false;
    
    // Nettoyer l'état d'inscription après fermeture
    this.signupStateService.markProfileMessageShown();
    
    console.log('✅ Message fermé et état nettoyé');
  }

  exploreWhileWaiting(): void {
    console.log('User wants to explore while waiting - message stays visible');
    // Le message reste affiché, l'utilisateur peut continuer à naviguer
  }
}
