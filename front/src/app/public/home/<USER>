// Variables
$primary-color: #0d1b2a;
$secondary-color: #011257;
$accent-color: #4db5ff;
$white: #ffffff;
$gray-light: #f0f4f8;
$gray-medium: #778ca3;
$shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
$transition: all 0.3s ease;
$button-primary: #ff5a5f; // From provided submit-btn
$button-hover: #ff767a; // From provided submit-btn hover

// Hero Section
.hero {
  position: relative;
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: $white;
  font-family: 'Poppins', sans-serif;
  overflow: hidden;

  &::before,
  &::after {
    content: '';
    position: absolute;
    border-radius: 50%;
    background: rgba(0, 123, 255, 0.2);
    animation: pulse 6s infinite ease-in-out;
    z-index: 1;
  }

  &::before {
    width: 200px;
    height: 200px;
    top: 10%;
    left: -5%;
    filter: blur(50px);
  }

  &::after {
    width: 300px;
    height: 300px;
    bottom: 15%;
    right: -10%;
    filter: blur(60px);
    animation-delay: 2s;
  }

  .carousel {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    overflow: hidden;
    z-index: 2;

    &-inner {
      display: flex;
      width: 100%;
      height: 100%;
      position: relative;
    }

    &-item {
      width: 100%;
      height: 100%;
      object-fit: cover;
      opacity: 0;
      position: absolute;
      transition: opacity 2s ease-in-out, transform 2s ease-in-out;
      filter: brightness(0.7);

      &.active {
        opacity: 1;
        transform: scale(1);
      }
    }

    &-btn {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      background: rgba($white, 0.8);
      border: none;
      color: #333;
      font-size: 2rem;
      cursor: pointer;
      padding: 15px;
      z-index: 3;
      border-radius: 50%;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);

      &:hover {
        background: #00b4d8;
        color: $white;
        transform: scale(1.2) translateY(-50%);
        box-shadow: 0 6px 20px rgba(0, 180, 216, 0.5);
      }

      &.prev {
        left: 20px;
      }

      &.next {
        right: 20px;
      }
    }

    &-indicators {
      position: absolute;
      bottom: 15px;
      left: 50%;
      transform: translateX(-50%);
      display: flex;
      gap: 12px;
      z-index: 3;

      span {
        width: 12px;
        height: 12px;
        background: rgba($white, 0.4);
        border-radius: 50%;
        cursor: pointer;
        transition: all 0.3s ease-in-out;

        &:hover {
          transform: scale(1.3);
          background: #00b4d8;
        }

        &.active {
          background: #00b4d8;
          transform: scale(1.5);
          box-shadow: 0 0 10px rgba(0, 180, 216, 0.6);
        }
      }
    }

    &-description {
      position: absolute;
      padding: 20px;
      text-align: center;
      font-size: 1.5rem;
      color: $white;
      border-radius: 12px;
      transition: opacity 0.3s ease-in-out;
      z-index: 2;

      h2 {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 15px;
      }

      p {
        font-size: 1.3rem;
        line-height: 1.8;
        margin-bottom: 10px;
      }
    }
  }

  .centered-description {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 45%;
    max-width: 600px;
    text-align: center;
    background: linear-gradient(135deg, rgba(20, 40, 80, 0.04), rgba(100, 150, 255, 0.08));
    backdrop-filter: blur(15px);
    border-radius: 25px;
    padding: 40px 50px;
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.25), inset 0 0 12px rgba($white, 0.08);
    border: 1px solid rgba(150, 200, 255, 0.4);
    transition: all 0.5s ease;
    animation: floatBounce 3s infinite ease-in-out, fadeInModern 1.5s ease-out forwards;

    h2 {
      font-size: 2.3rem;
      font-weight: 700;
      line-height: 1.2;
      color: $white;
      margin-bottom: 20px;
      letter-spacing: 1.5px;
      text-transform: uppercase;
      text-shadow: 0 3px 6px rgba(0, 0, 0, 0.5);
    }

    p {
      font-size: 1.15rem;
      line-height: 1.9;
      color: rgba(230, 240, 255, 0.92);
      font-weight: 300;
      opacity: 0.88;
    }
  }

  .cta-btn {
    background-color: #4CAF50;
    color: $white;
    padding: 10px 20px;
    font-size: 1.1rem;
    border: none;
    border-radius: 25px;
    margin-top: 20px;
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.3s ease;

    &:hover {
      background-color: #45a049;
      transform: scale(1.05);
    }

    &:active {
      background-color: #387a3b;
    }
  }

  .recruitment-description {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80%;
    max-width: 780px;
    text-align: center;
    border-radius: 30px;
    padding: 50px 60px;
    transition: all 0.5s ease;
    animation: lightFadeEntry 2s ease-out forwards;

    &::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: radial-gradient(circle, rgba(255, 250, 240, 0.2), transparent 70%);
      opacity: 0.3;
      animation: glowRotate 8s infinite linear;
      pointer-events: none;
    }

    h2 {
      font-size: 3rem;
      font-weight: 800;
      line-height: 1.1;
      background: linear-gradient(90deg, rgba(44, 39, 16, 0.75), rgba(1, 18, 87, 0.79), rgba(44, 39, 16, 0.75));
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
      margin-bottom: 25px;
      letter-spacing: 2px;
      text-transform: uppercase;
      transition: transform 0.4s ease;
    }

    &:hover h2 {
      transform: scale(1.03);
    }

    p {
      font-size: 1.3rem;
      line-height: 2;
      color: rgba(60, 60, 70, 0.9);
      font-weight: 300;
      opacity: 0.9;
      transition: opacity 0.4s ease, color 0.4s ease, transform 0.4s ease;
    }

    &:hover p {
      opacity: 1;
      color: rgba(50, 50, 60, 1);
      transform: translateY(-2px);
    }
  }

  .collaborative-description {
    position: absolute;
    top: 50%;
    left: 5%;
    transform: translateY(-50%);
    width: 38%;
    max-width: 480px;
    text-align: left;
    white-space: normal;
    background: linear-gradient(145deg, rgba(0, 180, 216, 0.15), rgba(10, 15, 35, 0.85));
    backdrop-filter: blur(15px);
    border-radius: 24px;
    padding: 35px 40px;
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.3), inset 0 0 12px rgba($white, 0.1);
    border: 2px solid rgba(0, 180, 216, 0.3);
    transition: transform 0.4s ease, box-shadow 0.4s ease, border-color 0.4s ease;
    animation: slideInFade 1.2s ease-out forwards, glowPulse 4s infinite ease-in-out;

    &:hover {
      transform: translateY(-48%) scale(1.04);
      box-shadow: 0 18px 45px rgba(0, 0, 0, 0.4), inset 0 0 15px rgba($white, 0.15);
      border-color: rgba(0, 180, 216, 0.6);
    }

    h2 {
      font-size: 2rem;
      font-weight: 600;
      line-height: 1.3;
      color: #e0fbfc;
      margin-bottom: 18px;
      letter-spacing: 1.2px;
      text-transform: uppercase;
      word-wrap: break-word;
      text-shadow: 0 2px 6px rgba(0, 180, 216, 0.5);
      transition: color 0.3s ease, text-shadow 0.3s ease;
    }

    &:hover h2 {
      color: $white;
      text-shadow: 0 2px 8px rgba(0, 180, 216, 0.7);
    }

    p {
      font-size: 1.1rem;
      line-height: 1.8;
      color: rgba($white, 0.92);
      font-weight: 400;
      opacity: 0.9;
      transition: opacity 0.3s ease, color 0.3s ease;
    }

    &:hover p {
      opacity: 1;
      color: #e0fbfc;
    }
  }
}

// Animations for Hero
@keyframes lightFadeEntry {
  0% { opacity: 0; transform: translate(-50%, -50%) scale(0.9); }
  50% { opacity: 0.6; transform: translate(-50%, -50%) scale(1.01); }
  100% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
}

@keyframes glowRotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes fadeSlideIn {
  0% { opacity: 0; transform: translateY(-60%); }
  100% { opacity: 1; transform: translateY(-50%); }
}

@keyframes slideInFade {
  0% { opacity: 0; transform: translateY(-70%); }
  100% { opacity: 1; transform: translateY(-50%); }
}

@keyframes fadeInModern {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes floatBounce {
  0% { transform: translate(-50%, -50%) translateY(0); }
  50% { transform: translate(-50%, -50%) translateY(-10px); }
  100% { transform: translate(-50%, -50%) translateY(0); }
}

@keyframes pulse {
  0% { transform: scale(1); opacity: 0.2; }
  50% { transform: scale(1.2); opacity: 0.4; }
  100% { transform: scale(1); opacity: 0.2; }
}

@keyframes glowPulse {
  0% { box-shadow: 0 12px 35px rgba(0, 0, 0, 0.3), inset 0 0 12px rgba($white, 0.1); }
  50% { box-shadow: 0 15px 40px rgba(0, 180, 216, 0.3), inset 0 0 15px rgba($white, 0.15); }
  100% { box-shadow: 0 12px 35px rgba(0, 0, 0, 0.3), inset 0 0 12px rgba($white, 0.1); }
}

// About Section
.about-section {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 80px 20px;
  background: linear-gradient(135deg, rgba($white, 0.8), $white);
  color: $white;
  text-align: left;
  position: relative;
  overflow: hidden;

  &::before {
    content: "";
    position: absolute;
    width: 200px;
    height: 200px;
    background: rgba($white, 0.2);
    border-radius: 50%;
    top: 10%;
    left: -50px;
    filter: blur(60px);
  }

  &::after {
    content: "";
    position: absolute;
    width: 300px;
    height: 300px;
    background: rgba($white, 0.1);
    border-radius: 50%;
    bottom: 10%;
    right: -80px;
    filter: blur(80px);
  }

  .about-content {
    max-width: 1100px;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 40px;
  }

  .about-description {
    width: 55%;
    animation: fadeInUp 1.5s ease-out, scaleUp 1s ease-in-out;

    h2 {
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 20px;
      color: rgba(1, 18, 87, 0.8);
    }

    p {
      font-size: 1.2rem;
      line-height: 1.8;
      margin-bottom: 20px;
      color: rgba(0, 0, 0, 0.9);
    }
  }

  .about-logo {
    width: 40%;
    display: flex;
    justify-content: center;
    align-items: center;

    img {
      max-width: 650px;
      height: auto;
      border-radius: 15px;
      transition: transform 0.4s ease-in-out, box-shadow 0.4s ease-in-out;
      margin-left: 200px;

      &:hover {
        transform: scale(1.08) rotate(5deg);
      }
    }
  }

  @media (max-width: 1024px) {
    .about-content {
      flex-direction: column;
      text-align: center;
      gap: 30px;
    }

    .about-description {
      width: 90%;
    }

    .about-logo {
      width: 100%;

      img {
        max-width: 280px;
      }
    }
  }
}

@keyframes slideInBlur {
  0% { opacity: 0; transform: translateX(-40px) scale(0.95); filter: blur(5px); }
  60% { opacity: 0.7; transform: translateX(10px) scale(1.02); filter: blur(0); }
  100% { opacity: 1; transform: translateX(0) scale(1); filter: blur(0); }
}

@keyframes bounceIn {
  0% { opacity: 0; transform: scale(0.7) translateY(30px); }
  50% { opacity: 0.8; transform: scale(1.05) translateY(-10px); }
  75% { transform: scale(0.98) translateY(5px); }
  100% { opacity: 1; transform: scale(1) translateY(0); }
}

// Workspaces Section
.workspaces-section {
  padding: 100px 20px;
  text-align: center;
  background: linear-gradient(135deg, rgba(6, 0, 25, 0.9), rgb(2, 35, 149));
  color: $white;
  position: relative;

  .section-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 40px;
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
    color: #f1f1f1;
  }

  .workspaces-container {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    max-width: 1400px;
    margin: auto;
  }

  .workspaces-cards {
    display: flex;
    gap: 20px;
    overflow-x: auto;
    scroll-snap-type: x mandatory;
    padding: 20px;
    scroll-behavior: smooth;
    scrollbar-width: none;
    -ms-overflow-style: none;
    white-space: nowrap;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  .workspace-card {
    background: rgba($white, 0.1);
    backdrop-filter: blur(12px);
    border-radius: 15px;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
    text-align: center;
    padding: 25px;
    min-width: 350px;
    flex-shrink: 0;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    color: $white;
    border: 2px solid rgba($white, 0.25);

    &:hover {
      transform: scale(1.05);
      box-shadow: 0 12px 30px rgba(0, 0, 0, 0.4);
    }

    .workspace-header {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 15px;
    }

    .workspace-avatar {
      font-size: 2.5rem;
      color: $white;
      border-radius: 50%;
      background-color: rgba($white, 0.15);
      width: 50px;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    img {
      width: 100%;
      height: 220px;
      object-fit: cover;
      border-radius: 12px;
      margin-bottom: 15px;
    }

    mat-card-title {
      font-size: 1.7rem;
      font-weight: bold;
      margin-bottom: 10px;
      color: $white;
    }

    mat-card-content p {
      font-size: 1.1rem;
      line-height: 1.6;
      color: rgba($white, 0.9);
    }
  }

  .carousel-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba($white, 0.15);
    color: $white;
    border: none;
    padding: 16px;
    cursor: pointer;
    font-size: 32px;
    border-radius: 50%;
    z-index: 10;
    transition: all 0.3s ease;

    &:hover {
      background: rgba($white, 0.3);
      transform: translateY(-50%) scale(1.1);
    }

    &.left-btn {
      left: 20px;
    }

    &.right-btn {
      right: 20px;
    }
  }

  @media (max-width: 1024px) {
    .workspace-card {
      width: 320px;
    }
  }

  @media (max-width: 768px) {
    .workspaces-cards {
      justify-content: flex-start;
      scroll-snap-type: x proximity;
    }

    .workspace-card {
      width: 100%;
    }
  }
}

// Why Join Us Section
.why-join-us {
  text-align: center;
  padding: 100px 20px;
  background: linear-gradient(135deg, $white, $white);
  color: rgba(0, 0, 0, 0.9);

  .section-title-wj {
    font-size: 36px;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 3px;
    margin-bottom: 60px;
    color: rgba(0, 0, 0, 0.9);
  }

  .benefits-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
    justify-content: center;
    align-items: stretch;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
  }

  .benefit-item {
    background: rgba(1, 18, 87, 0.89);
    backdrop-filter: blur(12px);
    padding: 50px;
    border-radius: 18px;
    text-align: center;
    transition: transform 0.4s ease, box-shadow 0.4s ease;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.4);
    border: 2px solid rgba($white, 0.3);

    &:hover {
      transform: translateY(-12px) scale(1.08);
      box-shadow: 0 15px 50px rgba(0, 0, 0, 0.6);
    }

    .benefit-icon {
      font-size: 65px;
      margin-bottom: 25px;
      color: #0096c7;
      transition: transform 0.3s ease, color 0.3s ease;
    }

    &:hover .benefit-icon {
      transform: rotate(12deg) scale(1.2);
      color: #66d9ff;
    }

    h3 {
      font-size: 24px;
      font-weight: bold;
      margin-bottom: 15px;
      color: $white;
      text-transform: uppercase;
      letter-spacing: 1px;
    }

    p {
      font-size: 18px;
      color: rgba($white, 0.85);
      line-height: 1.7;
      font-weight: 300;
    }
  }

  @media (max-width: 1024px) {
    .benefits-container {
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
  }
}

// Pricing Section
.pricing-section {
  background: linear-gradient(135deg, rgba(6, 0, 25, 0.9), rgb(2, 35, 149));
  padding: 80px 20px;
  font-family: 'Poppins', sans-serif;
  text-align: center;
  color: $white;

  .section-title {
    font-size: 36px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 2px;
    margin-bottom: 20px;
    color: #f8f9fa;
  }

  .typing-effect {
    font-size: 18px;
    font-weight: 500;
    opacity: 0.8;
    margin-bottom: 50px;
    color: #d1d9e6;
  }

  .pricing-container {
    display: flex;
    justify-content: center;
    gap: 30px;
    max-width: 1100px;
    margin: auto;
    flex-wrap: nowrap;
  }

  .pricing-card {
    background: rgba($white, 0.15);
    backdrop-filter: blur(12px);
    padding: 40px;
    width: 320px;
    border-radius: 16px;
    box-shadow: 0px 10px 25px rgba(0, 0, 0, 0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease, border 0.3s ease;
    text-align: center;
    border: 2px solid rgba($white, 0.3);
    color: $white;
    cursor: pointer;

    &:hover {
      transform: translateY(-8px);
      box-shadow: 0px 15px 35px rgba(0, 0, 0, 0.3);
      border: 2px solid #ffd500;
      box-shadow: 0 12px 30px rgba(0, 0, 0, 0.3);
    }

    &.selected {
      border: 3px solid #ffcc00;
      background: rgba(0, 150, 199, 0.15);
      box-shadow: 0 12px 35px rgba(0, 150, 199, 0.3);
    }

    &.featured {
      background: linear-gradient(135deg, #0096c7, #005f99);
      transform: scale(1.05);
      border: 2px solid #0096c7;
    }

    .pricing-header {
      h3 {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 10px;
      }

      .price {
        font-size: 32px;
        font-weight: bold;
        margin-bottom: 20px;
      }
    }

    .pricing-content {
      ul {
        list-style: none;
        padding: 0;
        margin-bottom: 30px;

        li {
          font-size: 18px;
          margin: 10px 0;
          display: flex;
          align-items: center;
          justify-content: center;

          i {
            color: #0096c7;
            margin-right: 10px;
            font-size: 20px;
          }
        }
      }
    }

    .cta-button {
      background: #0096c7;
      color: $white;
      border: none;
      padding: 14px 24px;
      font-size: 18px;
      cursor: pointer;
      border-radius: 8px;
      transition: background 0.3s ease, transform 0.2s ease;
      font-weight: bold;

      &:hover {
        background: #005f99;
        transform: translateY(-3px);
      }
    }
  }

  @media (max-width: 900px) {
    .pricing-container {
      flex-wrap: wrap;
      justify-content: center;
    }

    .pricing-card {
      width: 100%;
      max-width: 400px;
    }
  }

  .typing-effect-- {
    font-size: 2rem;
    font-weight: bold;
    color: $white;
    display: inline-block;
    white-space: nowrap;
    overflow: hidden;
    border-right: 4px solid $white;
    width: 0;
    animation: typing 3s steps(30) 1s forwards, blink 0.75s step-end infinite;
  }

  @keyframes typing {
    from { width: 0; }
    to { width: 100%; }
  }

  @keyframes blink {
    50% { border-color: transparent; }
  }
}
// Contact Section - Design professionnel et élégant
.contact-section {
  position: relative;
  padding: 100px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  color: #333;
  overflow: hidden;

  // Effet de fond subtil
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.03'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    opacity: 0.5;
    z-index: 0;
  }

  .contact-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 1;
  }

  // En-tête de la section
  .contact-header {
    text-align: center;
    margin-bottom: 60px;

    .section-title {
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 16px;
      color: #2c3e50;
      position: relative;
      display: inline-block;

      &::after {
        content: '';
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        width: 80px;
        height: 4px;
        background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
        border-radius: 2px;
      }
    }

    .section-subtitle {
      font-size: 1.25rem;
      color: #6c757d;
      max-width: 600px;
      margin: 0 auto;
    }
  }

  // Contenu principal
  .contact-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 40px;
  }

  // Réseaux sociaux
  .contact-info {
    width: 100%;
    max-width: 600px;
    margin-bottom: 20px;

    .social-links {
      display: flex;
      justify-content: center;
      gap: 20px;
      margin-top: 16px;

      .social-link {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: white;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #4facfe;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;

        &:hover {
          background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
          color: white;
          transform: translateY(-3px);
        }

        mat-icon {
          font-size: 24px;
          width: 24px;
          height: 24px;
        }
      }
    }
  }

  // Formulaire de contact
  .contact-form {
    width: 100%;
    max-width: 800px;
    border-radius: 24px;
    overflow: hidden;
    background: white;
    box-shadow: 0 15px 50px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    }

    .header {
      padding: 30px 30px 0;
      text-align: center;

      .title {
        font-size: 1.75rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 8px;
      }

      .subtitle {
        color: #6c757d;
        font-size: 1rem;
      }
    }

    .content {
      padding: 30px;
    }

    .form-grid {
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    .full-width {
      width: 100%;
    }

    .input-modern {
      .mat-form-field-wrapper {
        padding-bottom: 0;
      }

      .mat-form-field-flex {
        border-radius: 12px;
        padding: 0.75em 0.75em 0;
      }

      .mat-form-field-outline {
        color: rgba(0, 0, 0, 0.12);
      }

      &.mat-focused .mat-form-field-outline-thick {
        color: #4facfe;
      }

      .mat-form-field-label {
        margin-top: 0.25em;
      }

      &.mat-form-field-appearance-outline.mat-focused .mat-form-field-label {
        color: #4facfe;
      }

      .mat-input-element {
        color: #2c3e50;
      }

      .mat-form-field-suffix {
        color: #6c757d;
      }
    }

    .message-field {
      textarea {
        resize: none;
        min-height: 150px;
      }
    }

    .actions {
      padding: 0 30px 30px;
      display: flex;
      justify-content: center;

      .submit-btn {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
        border-radius: 50px;
        padding: 0 40px;
        height: 50px;
        font-size: 1.1rem;
        font-weight: 600;
        letter-spacing: 0.5px;
        box-shadow: 0 10px 20px rgba(79, 172, 254, 0.3);
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 10px;

        &:hover:not([disabled]) {
          box-shadow: 0 15px 25px rgba(79, 172, 254, 0.4);
          transform: translateY(-2px);
        }

        &:active:not([disabled]) {
          box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
          transform: translateY(0);
        }

        &[disabled] {
          background: #e9ecef;
          color: #adb5bd;
          box-shadow: none;
        }

        span {
          margin-right: 8px;
        }

        mat-icon {
          font-size: 20px;
          width: 20px;
          height: 20px;
        }
      }
    }
  }

  // Responsive design
  @media (max-width: 768px) {
    padding: 80px 0;

    .contact-header {
      margin-bottom: 40px;

      .section-title {
        font-size: 2rem;
      }

      .section-subtitle {
        font-size: 1.1rem;
      }
    }

    .contact-form {
      .header {
        padding: 24px 24px 0;

        .title {
          font-size: 1.5rem;
        }
      }

      .content {
        padding: 20px;
      }

      .actions {
        padding: 0 20px 20px;
      }
    }
  }

  @media (max-width: 576px) {
    .contact-header {
      .section-title {
        font-size: 1.75rem;
      }

      .section-subtitle {
        font-size: 1rem;
      }
    }

    .contact-form {
      .header {
        .title {
          font-size: 1.25rem;
        }

        .subtitle {
          font-size: 0.9rem;
        }
      }

      .actions {
        .submit-btn {
          width: 100%;
          justify-content: center;
        }
      }
    }
  }
}

// Animation pour le bouton d'envoi
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(79, 172, 254, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(79, 172, 254, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(79, 172, 254, 0);
  }
}

.contact-section .submit-btn:not([disabled]):hover {
  animation: pulse 1.5s infinite;
}
// Footer Section
.footer-mat {
  color: $white;
  padding: 60px 20px 30px;
  font-family: 'Poppins', sans-serif;
  background: linear-gradient(to bottom, $primary-color, $secondary-color);

  .footer-mat-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    max-width: 1200px;
    margin: 0 auto;
  }

  .footer-section {
    display: flex;
    flex-direction: column;
    gap: 12px;

    h3 {
      font-size: 18px;
      font-weight: 700;
      color: $white;
      margin-bottom: 8px;
      text-transform: uppercase;
      letter-spacing: 0.8px;
    }
  }

  .footer-logo {
    width: 250px;
    margin-bottom: 12px;
    transition: transform 0.3s ease;

    &:hover {
      transform: scale(1.05);
    }
  }

  .footer-description {
    font-size: 14px;
    color: rgba($white, 0.8);
    line-height: 1.6;
  }

  .footer-links {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;

    a {
      color: rgba($white, 0.9);
      font-size: 14px;
      text-decoration: none;
      transition: color 0.3s ease, transform 0.3s ease;

      &:hover {
        color: $accent-color;
        transform: translateX(3px);
      }
    }
  }

  .footer-social-icons {
    display: flex;
    flex-direction: column;
    gap: 15px;

    a {
      display: flex;
      align-items: center;
      color: $white;
      background: rgba($white, 0.1);
      border-radius: 40px;
      padding: 8px 12px;
      transition: background 0.3s ease, transform 0.3s ease;

      &:hover {
        background: $accent-color;
        color: $secondary-color;
        transform: scale(1.05);
      }

      mat-icon {
        font-size: 20px;
        margin-right: 10px;
      }

      span {
        font-size: 13px;
      }
    }
  }

  .newsletter-form {
    display: flex;
    flex-direction: column;
    gap: 10px;

    .newsletter-input {
      width: 100%;

      input {
        color: $white;
        font-weight: 400;
      }

      .mat-form-field-label {
        color: rgba($white, 0.7);
      }

      .mat-input-element {
        caret-color: $accent-color;
      }
    }

    button {
      align-self: flex-start;
      background-color: $secondary-color;
      color: $white;
      font-weight: 600;
      text-transform: uppercase;
      padding: 10px 20px;
      border-radius: 50px;
      transition: background 0.3s ease, transform 0.3s ease;

      &:hover {
        background-color: $accent-color;
        transform: scale(1.05);
      }
    }
  }

  .footer-bottom {
    margin-top: 30px;
    font-size: 13px;
    color: rgba($white, 0.8);
    text-align: center;
  }

  mat-divider {
    margin: 40px auto 20px;
    width: 80%;
    background: rgba($white, 0.3);
  }

  @media (max-width: 768px) {
    .footer-mat-container {
      grid-template-columns: 1fr;
      gap: 30px;
    }

    .footer-logo {
      width: 200px;
    }
  }
}

// Additional Animations for Contact Section
@keyframes slideInLeft {
  0% { opacity: 0; transform: translateX(-30px); }
  100% { opacity: 1; transform: translateX(0); }
}

@keyframes slideInRight {
  0% { opacity: 0; transform: translateX(30px); }
  100% { opacity: 1; transform: translateX(0); }
}

@keyframes slideInUp {
  0% { opacity: 0; transform: translateY(30px); }
  100% { opacity: 1; transform: translateY(0); }
}
