import {ChangeDetectorRef, Component, ElementRef, NgIterable, OnInit, ViewChild} from '@angular/core';
import { PostService } from '../../services/post/post.service';
import { NavBarComponent } from '../nav-bar/nav-bar.component';
import { MatCardModule } from '@angular/material/card';
import { CommonModule } from '@angular/common';
import {Router, RouterLink} from '@angular/router';
import {MatIcon, MatIconModule} from '@angular/material/icon';
import {FormsModule} from '@angular/forms';
import {MatFormField, MatFormFieldModule} from '@angular/material/form-field';
import {MatInput, MatInputModule} from '@angular/material/input';
import {MatButton, MatButtonModule} from '@angular/material/button';
import {MatList, MatListItem, MatListModule} from '@angular/material/list';
import {ContactService} from '../../services/contact/contact.service';
import {environment} from '../../../environments/environment';


class Workspace {
  id?: string;
  name?: string;
  description?: string;
  details?: string;
  location?: string;
  phone?: string;
  email?: string;
  image: any;
  avatar: any;
}

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss'],
  imports: [
    NavBarComponent,
    MatCardModule,
    CommonModule,
    MatIcon,
    FormsModule,
    MatFormField,
    MatInput,
    MatButton, // For ngModel or basic form binding
    MatCardModule, // For mat-card
    MatFormFieldModule, // For mat-form-field and mat-label
    MatInputModule, // For matInput directive
    MatButtonModule, // For mat-raised-button
    MatListModule, // For mat-list and mat-list-item
    MatIconModule,
    RouterLink,
    MatIconModule,
    MatButtonModule,
  ],
  standalone: true
})
export class HomeComponent implements OnInit {
  @ViewChild('scrollContainer', { static: false }) scrollContainer!: ElementRef;

  loginUrl = environment.authServerUrl
  interval: any;

  workspaces: Workspace[] = [];
  currentPage = 0;
  pageSize = 9; // 9 workspaces par page
  totalPages = 1;
  scrollAmount = 400; // Largeur du défilement (ajuste selon le design)
  images = [
    'assets/images/logg.jpg',
    'assets/images/img1.jpg',
    'assets/images/img2.jpg'
  ];
  currentSlide = 0;
  descriptions = [
    {
      "title": "Bienvenue chez Kairos IT – L'innovation au cœur de votre transformation digitale",
      "text1": "",
      "text2": "",
    },

    {
      "title": "Recrutement intelligent : Trouvez l’opportunité idéale ",
      "text1": "Accédez à un réseau d’entreprises innovantes et découvrez des offres adaptées à votre profil.",
      "text2": "Grâce à notre algorithme avancé, postulez en toute simplicité aux postes qui correspondent à vos compétences et ambitions.",
    },
    {
      "title": "Optimisez votre recrutement avec notre espace collaboratif",
      "text1": "Publiez vos offres, gérez vos recrutements et identifiez les talents clés pour votre entreprise.",
      "text2": "Notre plateforme vous offre des outils performants pour accélérer et affiner votre processus de sélection.",
    }
  ];
  contact = {
    name: '',
    email: '',
    phone: '',
    message: ''
  };


  get currentDescription() {
    return this.descriptions[this.currentSlide] || this.descriptions[0];
  }

  constructor(private postService: PostService, private router: Router , private cdRef: ChangeDetectorRef , private contactService: ContactService, ) {}

  ngOnInit(): void {
    this.startAutoSlide();
    this.loadWorkspaces();
  }

  loadWorkspaces(): void {
    this.postService.getAllWorkspaces(this.currentPage, this.pageSize).subscribe(
      (response) => {
        this.workspaces = response.content || [];
        this.totalPages = response.totalPages || 1;
      },
      (error) => {
        console.error('Erreur lors de la récupération des workspaces', error);
      }
    );
  }

  // Carrousel auto-slide
  startAutoSlide(): void {
    this.interval = setInterval(() => {
      this.nextSlide();
    }, 3000);
  }

  nextSlide(): void {
    this.currentSlide = (this.currentSlide + 1) % this.images.length;
    this.updateActiveSlide();
  }

  prevPage() {
    if (this.scrollContainer) {
      const container = this.scrollContainer.nativeElement;
      const newScrollLeft = container.scrollLeft - this.scrollAmount;

      // Assurer que le scroll ne dépasse pas la limite
      container.scrollTo({ left: Math.max(newScrollLeft, 0), behavior: 'smooth' });
    }
  }

  nextPage() {
    if (this.scrollContainer) {
      const container = this.scrollContainer.nativeElement;
      const maxScrollLeft = container.scrollWidth - container.clientWidth;
      const newScrollLeft = container.scrollLeft + this.scrollAmount;

      // Empêcher le scroll d'aller trop loin
      container.scrollTo({ left: Math.min(newScrollLeft, maxScrollLeft), behavior: 'smooth' });
    }
  }


  // Pagination
  get displayedWorkspaces() {
    const start = this.currentPage * this.pageSize;
    return this.workspaces.slice(start, start + this.pageSize);
  }

  goToPage(event: any) {
    this.currentPage = event.pageIndex;
    this.loadWorkspaces(); // Recharger les workspaces en fonction de la page
  }

  // Méthode pour rejoindre un workspace
  carouselImages: (NgIterable<unknown> & NgIterable<any>) | undefined | null;
  joinWorkspace(id?: string): void {
    console.log(`Joining workspace with ID: ${id}`);
  }

  applyForWorkspace(workspaceId: string): void {
    console.log(`Request to join workspace with ID: ${workspaceId}`);
  }

  selectPlan(plan: string) {
    console.log('Plan sélectionné :', plan);
    this.router.navigate(['/payment'], { queryParams: { plan: plan } });
  }

  scrollToNextSection() {
    const nextSection = document.getElementById('workspaces');
    if (nextSection) {
      nextSection.scrollIntoView({ behavior: 'smooth' });
    }
  }

  prevSlide(): void {
    this.currentSlide = (this.currentSlide - 1 + this.images.length) % this.images.length;
    this.updateActiveSlide();
  }

  updateActiveSlide(): void {
    const slides = document.querySelectorAll('.carousel-item');
    slides.forEach((slide, index) => {
      if (index === this.currentSlide) {
        slide.classList.add('active');
      } else {
        slide.classList.remove('active');
      }
    });

    // Force Angular à détecter le changement
    setTimeout(() => {}, 0);
  }


  goToSlide(index: number) {
    this.currentSlide = index;
    this.updateActiveSlide();
    this.cdRef.detectChanges(); // Force la mise à jour
  }


  sendMessage() {
    this.contactService.sendContactMessage(this.contact).subscribe(
      (response) => {
        console.log('Message envoyé avec succès !');
        alert('Votre message a été envoyé avec succès !');
        this.contact = { name: '', email: '', phone: '', message: '' }; // Réinitialiser le formulaire
      },
      (error) => {
        console.error('Erreur lors de l\'envoi du message', error);
        alert('Une erreur est survenue. Veuillez réessayer.');
      }
    );
  }
}

