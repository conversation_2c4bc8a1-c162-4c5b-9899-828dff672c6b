<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Kairos IT</title>
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700&display=swap" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
  <link rel="stylesheet" href="styles.scss">
</head>
<body>
<!-- Navigation -->
<app-nav-bar></app-nav-bar>

<!-- Hero Section -->
<section id="hero" class="hero">
  <div class="carousel">
    <div class="carousel-inner">
      <img *ngFor="let img of images; let i = index"
           [src]="img"
           [alt]="'Image ' + (i + 1)"
           class="carousel-item"
           [class.active]="i === currentSlide">
    </div>
    <div class="carousel-description"
         [ngClass]="{
             'custom-description': currentDescription.title === 'Rejoignez notre plateforme de recrutement',
             'centered-description': currentDescription.title === 'Bienvenue chez Kairos IT – L\'innovation au cœur de votre transformation digitale',
             'collaborative-description': currentDescription.title === 'Optimisez votre recrutement avec notre espace collaboratif',
             'growth-description': currentDescription.title === 'Évoluez avec nous vers un avenir meilleur',
             'recruitment-description': currentDescription.title === 'Recrutement intelligent : Trouvez l’opportunité idéale '
           }">
      <div class="description-content">
        <div class="text">
          <h2>{{ currentDescription.title }}</h2>
          <p>{{ currentDescription.text1 }}</p>
          <p *ngIf="currentDescription.text2">{{ currentDescription.text2 }}</p>
        </div>
      </div>
    </div>
    <button class="carousel-btn prev" (click)="prevSlide()">❮</button>
    <button class="carousel-btn next" (click)="nextSlide()">❯</button>
    <div class="carousel-indicators">
        <span *ngFor="let img of images; let i = index"
              [class.active]="i === currentSlide"
              (click)="goToSlide(i)"></span>
    </div>
  </div>
  <div class="overlay"></div>
  <div class="down-arrow-circle animate-bounce" (click)="scrollToNextSection()">
    <i class="fa fa-chevron-down"></i>
  </div>
</section>

<!-- About Section -->
<section class="about-section">
  <div class="about-content">
    <div class="about-description">
      <h2 class="section-title">À propos de Kairos IT</h2>
      <p>
        Chez Kairos IT, nous sommes passionnés par la création de solutions digitales innovantes. Nous croyons en l'importance d'une approche collaborative et nous mettons tout en œuvre pour fournir des services de haute qualité à nos clients.
      </p>
      <p>
        Notre équipe est composée de professionnels expérimentés qui travaillent ensemble pour résoudre des défis technologiques complexes et apporter une réelle valeur ajoutée.
      </p>
    </div>
    <div class="about-logo">
      <img src="assets/images/logo.png" alt="Kairos IT Logo">
    </div>
  </div>
</section>

<!-- Workspaces Section -->
<section id="workspaces" class="workspaces-section">
  <h2 class="section-title">Nos Espaces de Travail</h2>
  <div class="workspaces-container">
    <button class="carousel-btn left-btn" (click)="prevPage()">❮</button>
    <div class="workspaces-cards" #scrollContainer>
      <mat-card *ngFor="let workspace of workspaces" class="workspace-card">
        <mat-card-header>
          <div class="workspace-header">
            <mat-icon class="workspace-avatar">account_circle</mat-icon>
            <mat-card-title>{{ workspace.name }}</mat-card-title>
          </div>
        </mat-card-header>
        <mat-card-content>
          <p>{{ workspace.description }}</p>
        </mat-card-content>
      </mat-card>
    </div>
    <button class="carousel-btn right-btn" (click)="nextPage()">❯</button>
  </div>
</section>

<!-- Why Join Us Section -->
<section class="why-join-us">
  <h2 class="section-title-wj">Why Join Us Section</h2>
  <div class="benefits-container">
    <div class="benefit-item">
      <i class="fas fa-project-diagram benefit-icon" style="color: #ff6b6b;"></i>
      <h3>Exciting Projects</h3>
      <p>Work on cutting-edge technologies and impactful projects.</p>
    </div>
    <div class="benefit-item">
      <i class="fas fa-users benefit-icon" style="color: #1dd1a1;"></i>
      <h3>Collaborative Environment</h3>
      <p>Be part of a supportive and innovative team.</p>
    </div>
    <div class="benefit-item">
      <i class="fas fa-smile benefit-icon" style="color: #feca57;"></i>
      <h3>Friendly Culture</h3>
      <p>Enjoy a healthy work-life balance with an amazing team.</p>
    </div>
  </div>
</section>

<!-- Pricing Section -->
<section id="pricing" class="pricing-section">
  <h2 class="section-title">Pricing Plan</h2>
  <p class="typing-effect--">Choose the perfect plan for your needs</p>
  <div class="pricing-container">
    <div class="pricing-card">
      <div class="pricing-header">
        <h3>Standard</h3>
        <p class="price">$149/year</p>
      </div>
      <div class="pricing-content">
        <ul>
          <li><i class="fas fa-check"></i> Basic job listings</li>
          <li><i class="fas fa-check"></i> Standard support</li>
          <li><i class="fas fa-check"></i> Essential features</li>
        </ul>
      </div>
      <div class="pricing-footer" >
        <a [href]="loginUrl+'/login'" class="cta-button" >Choose Plan</a>
      </div>
    </div>
    <div class="pricing-card featured">
      <div class="pricing-header">
        <h3>Pro <span class="best-value">Best Value</span></h3>
        <p class="price">$249/year</p>
      </div>
      <div class="pricing-content">
        <ul>
          <li><i class="fas fa-check"></i> Unlimited job listings</li>
          <li><i class="fas fa-check"></i> Premium support</li>
          <li><i class="fas fa-check"></i> Advanced analytics</li>
        </ul>
      </div>
      <div class="pricing-footer">
        <button class="cta-button">Choose Plan</button>
      </div>
    </div>
    <div class="pricing-card">
      <div class="pricing-header">
        <h3>Enterprise</h3>
        <p class="price">$349/year</p>
      </div>
      <div class="pricing-content">
        <ul>
          <li><i class="fas fa-check"></i> Custom solutions</li>
          <li><i class="fas fa-check"></i> Dedicated support</li>
          <li><i class="fas fa-check"></i> API access & integrations</li>
        </ul>
      </div>
      <div class="pricing-footer">
        <button class="cta-button">Choose Plan</button>
      </div>
    </div>
  </div>
</section>
<section id="contact" class="contact-section">
  <div class="contact-container">
    <div class="contact-header">
      <h2 class="section-title">Contactez-nous</h2>
      <p class="section-subtitle">Nous sommes à votre écoute</p>
    </div>

    <div class="contact-content">

      <mat-card class="contact-form">
        <mat-card-header class="header">
          <mat-card-title class="title">Envoyez-nous un message</mat-card-title>
          <mat-card-subtitle class="subtitle">Nous vous répondrons dans les plus brefs délais</mat-card-subtitle>
        </mat-card-header>

        <mat-card-content class="content">
          <form class="form-grid" #contactForm="ngForm">
            <mat-form-field appearance="outline" class="full-width input-modern">
              <mat-label>Votre nom</mat-label>
              <input matInput [(ngModel)]="contact.name" name="name" placeholder="Votre nom" required>
              <mat-icon matSuffix>person</mat-icon>
              <mat-error *ngIf="contactForm.controls['name']?.errors?.['required']">
                Le nom est requis
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="full-width input-modern">
              <mat-label>Adresse email</mat-label>
              <input matInput [(ngModel)]="contact.email" name="email" placeholder="Votre email" type="email" required email>
              <mat-icon matSuffix>email</mat-icon>
              <mat-error *ngIf="contactForm.controls['email']?.errors?.['required']">
                L'email est requis
              </mat-error>
              <mat-error *ngIf="contactForm.controls['email']?.errors?.['email'] && !contactForm.controls['email']?.errors?.['required']">
                Veuillez entrer un email valide
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="full-width input-modern message-field">
              <mat-label>Votre message</mat-label>
              <textarea matInput [(ngModel)]="contact.message" name="message" placeholder="Votre message" rows="5" required></textarea>
              <mat-icon matSuffix>message</mat-icon>
              <mat-error *ngIf="contactForm.controls['message']?.errors?.['required']">
                Le message est requis
              </mat-error>
            </mat-form-field>
          </form>
        </mat-card-content>

        <mat-card-actions class="actions">
          <button mat-raised-button class="submit-btn" type="button" (click)="sendMessage()" [disabled]="contactForm.invalid">
            <span>Envoyer</span>
            <mat-icon>send</mat-icon>
          </button>
        </mat-card-actions>
      </mat-card>
    </div>
  </div>
</section>
<!-- Footer Section -->
<footer class="footer-mat">
  <div class="footer-mat-container">
    <!-- Branding -->
    <div class="footer-section">
      <img src="assets/images/logo.png" alt="Kairos IT" class="footer-logo" />
      <p class="footer-description">
        Kairos IT - Innover. Connecter. Réussir.
      </p>
    </div>
    <!-- Navigation -->
    <div class="footer-section">
      <h3>Navigation</h3>
      <nav class="footer-links">
        <a mat-button routerLink="/about">À propos</a>
        <a mat-button routerLink="/services">Nos Services</a>
        <a mat-button routerLink="/careers">Carrières</a>
        <a mat-button routerLink="/contact">Contact</a>
      </nav>
    </div>
    <!-- Suivez-nous -->
    <div class="footer-section">
      <h3>Suivez-nous</h3>
      <div class="footer-social-icons">
        <a mat-icon-button href="#">
          <mat-icon>facebook</mat-icon>
          <span>KairosIT</span>
        </a>
        <a mat-icon-button href="#">
          <mat-icon>linkedin</mat-icon>
          <span>KairosIT</span>
        </a>
        <a mat-icon-button href="#">
          <mat-icon>twitter</mat-icon>
          <span>KairosIT</span>
        </a>
        <a mat-icon-button href="#">
          <mat-icon>instagram</mat-icon>
          <span>KairosIT</span>
        </a>
      </div>
    </div>
    <!-- Newsletter -->
    <div class="footer-section">
      <h3>Newsletter</h3>
      <form class="newsletter-form">
        <mat-form-field appearance="fill" class="newsletter-input">
          <mat-label>Email</mat-label>
          <input matInput type="email" required />
        </mat-form-field>
        <button mat-flat-button color="primary" type="submit">S'abonner</button>
      </form>
    </div>
  </div>
  <mat-divider></mat-divider>
  <div class="footer-bottom">
    © 2025 Kairos IT. Tous droits réservés.
  </div>
</footer>
</body>
</html>
