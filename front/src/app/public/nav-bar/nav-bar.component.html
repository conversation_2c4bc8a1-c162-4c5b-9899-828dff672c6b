<mat-toolbar class="app-bar">
  <div class="left-section">
    <!-- Logo -->
    <a href="#hero">
      <img class="logo" src="assets/images/logo.png" alt="Logo" />
    </a>
    <!-- Langue Button -->
    <button mat-icon-button [matMenuTriggerFor]="langMenu" aria-label="Langue">
      <mat-icon>language</mat-icon>
    </button>
  </div>

  <span class="spacer"></span>

  <nav>

    <a mat-button (click)="scrollToWorkspaces()">
      <mat-icon>workspaces</mat-icon> {{ 'WORKSPACES' | translate }}
    </a>
    <a mat-button (click)="scrollToSection('pricing')">
      <mat-icon>attach_money</mat-icon> {{ 'PRICING' | translate }}
    </a>
    <a mat-button (click)="scrollToSection('contact')">
      <mat-icon>mail</mat-icon> {{ 'CONTACT' | translate }}
    </a>
  </nav>

  <span class="spacer"></span>

  <div class="auth-buttons">
    <button mat-button (click)="redirectToLogin()" class="login-btn">
      <mat-icon>login</mat-icon> {{ 'LOGIN' | translate }}
    </button>
    <button mat-button (click)="signup()" class="signup-btn">
      <mat-icon>person_add</mat-icon> {{ 'SIGN UP' | translate }}
    </button>
  </div>

  <mat-menu #langMenu="matMenu">
    <ng-container *ngFor="let lang of languages">
      <button mat-menu-item (click)="changeLanguage(lang.code)">
        <span>{{ lang.icon }}</span> {{ lang.name }}
      </button>
    </ng-container>
  </mat-menu>
</mat-toolbar>

<router-outlet></router-outlet>
