/* These tokens are generated using https://themes.angular-material.dev/ */
/* Preview: https://themes.angular-material.dev/?seed-error=%23ba1a1a&seed-neutral=%2376777b&seed-neutral-variant=%23747780&seed-primary=%230c3465&seed-secondary=%23535f74&seed-tertiary=%23784f84 */
/* Seed Colors: primary: #0c3465, secondary: #535f74, tertiary: #784f84, error: #ba1a1a, neutral: #76777b, neutral-variant: #747780 */

@use "@angular/material" as mat;

html {
  color-scheme: light dark;
  @include mat.theme((
    color: mat.$violet-palette,
    typography: Roboto,
    density: 0
  ));
}
/* Light Theme */
:root, :host {
  @include mat.theme-overrides((
    primary: #3e5f92,
    on-primary: #ffffff,
    primary-container: #d6e3ff,
    on-primary-container: #001b3d,
    inverse-primary: #a9c7ff,
    primary-fixed: #d6e3ff,
    primary-fixed-dim: #a9c7ff,
    on-primary-fixed: #001b3d,
    on-primary-fixed-variant: #254779,
    secondary: #535f74,
    on-secondary: #ffffff,
    secondary-container: #d7e3fc,
    on-secondary-container: #0f1c2e,
    secondary-fixed: #d7e3fc,
    secondary-fixed-dim: #bbc7df,
    on-secondary-fixed: #0f1c2e,
    on-secondary-fixed-variant: #3b475b,
    tertiary: #784f84,
    on-tertiary: #ffffff,
    tertiary-container: #fad7ff,
    on-tertiary-container: #2f093c,
    tertiary-fixed: #fad7ff,
    tertiary-fixed-dim: #e7b6f2,
    on-tertiary-fixed: #2f093c,
    on-tertiary-fixed-variant: #5e376b,
    background: #ffffff,
    on-background: #1c1b1c,
    surface: #ffffff,
    surface-dim: #ddd9d9,
    surface-bright: #ffffff,
    surface-container-lowest: #ffffff,
    surface-container-low: #f6f3f2,
    surface-container: #f1eded,
    surface-container-high: #ebe7e7,
    surface-container-highest: #e5e2e1,
    on-surface: #1c1b1c,
    shadow: #000000,
    scrim: #000000,
    surface-tint: #5d5e62,
    inverse-surface: #313030,
    inverse-on-surface: #f4f0f0,
    outline: #76777b,
    outline-variant: #c6c6cb,
    neutral: #797777,
    neutral10: #1c1b1c,
    error: #ba1a1a,
    error-container: #ffdad6,
    on-error: #ffffff,
    on-error-container: #410002,
    surface-variant: #e2e2e7,
    on-surface-variant: #45474b,
    neutral-variant: #76777c,
    neutral-variant20: #2f3035,
    inverse-secondary: #bbc7df,
    inverse-tertiary: #e7b6f2,
  ));
}

/* Dark Theme */
.dark {
  @include mat.theme-overrides((
    primary: #a9c7ff,
    on-primary: #053061,
    primary-container: #254779,
    on-primary-container: #d6e3ff,
    inverse-primary: #3e5f92,
    primary-fixed: #d6e3ff,
    primary-fixed-dim: #a9c7ff,
    on-primary-fixed: #001b3d,
    on-primary-fixed-variant: #254779,
    secondary: #bbc7df,
    on-secondary: #253144,
    secondary-container: #3b475b,
    on-secondary-container: #d7e3fc,
    secondary-fixed: #d7e3fc,
    secondary-fixed-dim: #bbc7df,
    on-secondary-fixed: #0f1c2e,
    on-secondary-fixed-variant: #3b475b,
    tertiary: #e7b6f2,
    on-tertiary: #462153,
    tertiary-container: #5e376b,
    on-tertiary-container: #fad7ff,
    tertiary-fixed: #fad7ff,
    tertiary-fixed-dim: #e7b6f2,
    on-tertiary-fixed: #2f093c,
    on-tertiary-fixed-variant: #5e376b,
    background: #141313,
    on-background: #e5e2e1,
    surface: #141313,
    surface-dim: #141313,
    surface-bright: #3a3939,
    surface-container-lowest: #0e0e0e,
    surface-container-low: #1c1b1c,
    surface-container: #201f20,
    surface-container-high: #2a2a2a,
    surface-container-highest: #353435,
    on-surface: #e5e2e1,
    shadow: #000000,
    scrim: #000000,
    surface-tint: #c6c6ca,
    inverse-surface: #e5e2e1,
    inverse-on-surface: #313030,
    outline: #909095,
    outline-variant: #45474b,
    neutral: #797777,
    neutral10: #1c1b1c,
    error: #ffb4ab,
    error-container: #93000a,
    on-error: #690005,
    on-error-container: #ffdad6,
    surface-variant: #45474b,
    on-surface-variant: #c6c6cb,
    neutral-variant: #76777c,
    neutral-variant20: #2f3035,
    inverse-secondary: #535f74,
    inverse-tertiary: #784f84,
  ));
}
