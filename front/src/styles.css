/* You can add global styles to this file, and also import other style files */
@import './styles/themes.scss';
@import './styles/material-theme.scss';
@import './styles/dark-theme-custom.scss';
@import './styles/material-dark-overrides.scss';
@import './styles/dark-theme-force.scss';
@import './styles/dark-theme-inline-styles.scss';
@import './styles/dark-theme-specific-fixes.scss';
@import './styles/dark-theme-pages.scss';
@import './styles/dark-theme-important.scss';
@import './styles/dark-theme-critical-fixes.scss';
@import './styles/dark-theme-profile.scss';
@import './styles/dark-theme-profile-inline.scss';
@import './styles/dark-theme-info-cards.scss';
@import './styles/dark-theme-add-buttons.scss';
@import './styles/dark-theme-card-names.scss';
@import './styles/dark-theme-mat-card-info-row.scss';

html, body {
  height: 100%;
  overflow-x: hidden; /* Éviter le défilement horizontal */
  scroll-behavior: smooth; /* Défilement fluide */
}

body {
  margin: 0;
  font-family: Roboto, "Helvetica Neue", sans-serif;
  background-color: var(--background-color);
  color: var(--text-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Styles globaux pour les éléments courants */
a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color 0.3s ease;
}

a:hover {
  color: var(--primary-light);
  text-decoration: underline;
}

button {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

input, select, textarea {
  background-color: var(--background-alt);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Styles pour les éléments HTML de base */
h1, h2, h3, h4, h5, h6 {
  color: var(--text-primary);
}

p, span, div {
  color: var(--text-secondary);
}

ul, ol {
  color: var(--text-secondary);
}

li {
  color: var(--text-secondary);
}

table {
  background-color: var(--background-card);
  color: var(--text-primary);
  border-color: var(--border-color);
}

th {
  background-color: var(--background-alt);
  color: var(--text-primary);
  border-color: var(--border-color);
}

td {
  color: var(--text-secondary);
  border-color: var(--border-color);
}

hr {
  border-color: var(--border-color);
}

code, pre {
  background-color: var(--background-alt);
  color: var(--text-primary);
  border-color: var(--border-color);
}

blockquote {
  background-color: var(--background-alt);
  color: var(--text-secondary);
  border-left-color: var(--primary-color);
}

img {
  max-width: 100%;
  height: auto;
}

/* Styles pour les scrollbars */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--background-alt);
}

::-webkit-scrollbar-thumb {
  background-color: var(--border-dark);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: var(--text-muted);
}

/* Masquer la barre de défilement pour tous les éléments */
::-webkit-scrollbar {
  width: 0; /* Pour Chrome, Safari, et les navigateurs basés sur WebKit */
  display: none; /* Masquer complètement la barre de défilement */
}

/* Pour Firefox */
* {
  scrollbar-width: none;
}

/* Pour IE et Edge */
* {
  -ms-overflow-style: none;
}

   .dialog-title {
     font-size: 22px;
     font-weight: 600;
     display: flex;
     align-items: center;
     gap: 10px;
     color: #2c3e50;
     animation: fadeSlide 0.4s ease-in-out;
   }

.icon-title {
  color: #1976d2;
  font-size: 28px;
}

.dialog-content {
  margin-top: 10px;
  padding-bottom: 16px;
  animation: fadeIn 0.6s ease-in-out;
}

.dialog-form {
  display: flex;
  flex-direction: column;
  gap: 18px;
}

.full-width {
  width: 100%;
}

.animate-field {
  animation: fieldFade 0.5s ease-in-out;
}

mat-form-field {
  background: #fdfdfd;
  border-radius: 10px;
  transition: all 0.3s ease-in-out;
}

mat-form-field:hover {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 20px;
  animation: fadeSlideUp 0.4s ease-in-out;
}

button[mat-button],
button[mat-flat-button],
button[mat-stroked-button] {
  border-radius: 8px;
  font-weight: 500;
  text-transform: none;
  transition: transform 0.2s ease;
}

button:hover {
  transform: scale(1.03);
}

.btn-add mat-icon,
.btn-cancel mat-icon {
  margin-right: 6px;
}

/* ✨ Animations */
@keyframes fadeSlide {
  0% {
    opacity: 0;
    transform: translateY(-10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeSlideUp {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes fieldFade {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

html, body { height: 100%; }
body { margin: 0; font-family: Roboto, "Helvetica Neue", sans-serif; }

/* Styles pour les snackbars */
.success-snackbar {
  background: linear-gradient(135deg, #4CAF50, #2E7D32) !important;
  color: white !important;
  font-weight: 500 !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 8px rgba(46, 125, 50, 0.3) !important;
}

.warning-snackbar {
  background: linear-gradient(135deg, #FF9800, #F57C00) !important;
  color: white !important;
  font-weight: 500 !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 8px rgba(245, 124, 0, 0.3) !important;
}

.error-snackbar {
  background: linear-gradient(135deg, #F44336, #C62828) !important;
  color: white !important;
  font-weight: 500 !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 8px rgba(198, 40, 40, 0.3) !important;
}

.mat-mdc-snack-bar-container .mdc-snackbar__surface {
  border-radius: 8px !important;
}

.mat-mdc-snack-bar-container .mat-mdc-button {
  color: white !important;
  font-weight: 500 !important;
}

/* Styles pour les boutons d'action des candidats */
.accept-button.mat-mdc-raised-button {
  background-color: #001660 !important;
  color: white !important;
}

.reject-button.mat-mdc-raised-button {
  background-color: #ff6600 !important;
  color: white !important;
}
