<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Message "Votre profil sera complété dans quelques minutes"</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #001040FF 0%, #ff6600 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
            overflow: hidden;
        }

        .header {
            background: #001040FF;
            color: white;
            padding: 40px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.8em;
            margin-bottom: 15px;
            font-weight: 700;
        }

        .header p {
            font-size: 1.3em;
            opacity: 0.9;
            line-height: 1.5;
        }

        .content {
            padding: 50px;
        }

        .instruction-section {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            border-left: 6px solid #001040FF;
        }

        .instruction-section h2 {
            color: #001040FF;
            margin-bottom: 20px;
            font-size: 1.8em;
            font-weight: 600;
        }

        .steps {
            list-style: none;
            counter-reset: step-counter;
        }

        .steps li {
            counter-increment: step-counter;
            margin-bottom: 15px;
            padding: 15px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: relative;
            padding-left: 60px;
        }

        .steps li::before {
            content: counter(step-counter);
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: #001040FF;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
        }

        .control-section {
            background: #fff;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            border: 2px solid #e9ecef;
        }

        .control-section h2 {
            color: #001040FF;
            margin-bottom: 25px;
            font-size: 1.8em;
        }

        .button-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .btn {
            padding: 15px 25px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #001040FF 0%, #000830 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #ff6600 0%, #ff8533 100%);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .status-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
        }

        .status-section h2 {
            color: #001040FF;
            margin-bottom: 25px;
            font-size: 1.8em;
        }

        .status-grid {
            display: grid;
            gap: 15px;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            background: white;
            border-radius: 10px;
            border: 1px solid #dee2e6;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }

        .status-label {
            font-weight: 600;
            color: #495057;
            font-size: 16px;
        }

        .status-value {
            font-family: 'Courier New', monospace;
            padding: 8px 12px;
            border-radius: 6px;
            font-weight: bold;
            font-size: 14px;
        }

        .status-true {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-false {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status-null {
            background: #e2e3e5;
            color: #383d41;
            border: 1px solid #d6d8db;
        }

        .message-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 16, 64, 0.95);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            backdrop-filter: blur(15px);
        }

        .message-container {
            background: white;
            border-radius: 25px;
            padding: 50px;
            max-width: 550px;
            width: 90%;
            text-align: center;
            box-shadow: 0 30px 80px rgba(0, 0, 0, 0.4);
            position: relative;
            overflow: hidden;
            animation: slideIn 0.5s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .message-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: linear-gradient(90deg, #001040FF, #ff6600);
        }

        .message-icon {
            font-size: 5em;
            margin-bottom: 25px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .message-title {
            color: #001040FF;
            font-size: 2.2em;
            margin-bottom: 20px;
            font-weight: 700;
        }

        .message-text {
            color: #666;
            font-size: 1.2em;
            margin-bottom: 35px;
            line-height: 1.7;
        }

        .message-button {
            background: linear-gradient(135deg, #001040FF 0%, #000830 100%);
            color: white;
            border: none;
            padding: 18px 35px;
            border-radius: 12px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .message-button:hover {
            background: linear-gradient(135deg, #000830 0%, #001040FF 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 16, 64, 0.3);
        }

        .hidden {
            display: none;
        }

        .logs-section {
            background: #1e1e1e;
            border-radius: 15px;
            padding: 25px;
            margin-top: 30px;
        }

        .logs-section h2 {
            color: #00ff00;
            margin-bottom: 20px;
            font-family: 'Courier New', monospace;
        }

        .logs {
            color: #00ff00;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
            line-height: 1.4;
        }

        .log-entry {
            margin-bottom: 8px;
            padding: 5px 0;
            border-bottom: 1px solid #333;
        }

        .log-entry:last-child {
            border-bottom: none;
        }

        @media (max-width: 768px) {
            .button-group {
                grid-template-columns: 1fr;
            }
            
            .status-item {
                flex-direction: column;
                gap: 10px;
                text-align: center;
            }
            
            .message-container {
                padding: 30px;
                margin: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Test Message de Profil</h1>
            <p>Simulation du flux : <strong>Inscription → Connexion → Message "Votre profil sera complété dans quelques minutes"</strong></p>
        </div>

        <div class="content">
            <div class="instruction-section">
                <h2>📋 Instructions de Test</h2>
                <ol class="steps">
                    <li><strong>Simuler l'inscription :</strong> Cliquez sur "📝 Simuler Inscription" pour marquer qu'un utilisateur vient de s'inscrire</li>
                    <li><strong>Simuler la connexion :</strong> Cliquez sur "🔑 Simuler Connexion" pour déclencher l'affichage du message</li>
                    <li><strong>Voir le message :</strong> Le message "Votre profil sera complété dans quelques minutes" doit s'afficher</li>
                    <li><strong>Fermer le message :</strong> Cliquez sur "Parfait, j'ai compris" pour fermer le message et nettoyer l'état</li>
                </ol>
            </div>

            <div class="control-section">
                <h2>🎮 Contrôles de Test</h2>
                <div class="button-group">
                    <button class="btn btn-primary" onclick="simulateSignup()">
                        📝 Simuler Inscription
                    </button>
                    <button class="btn btn-success" onclick="simulateLogin()">
                        🔑 Simuler Connexion
                    </button>
                    <button class="btn btn-warning" onclick="showMessage()">
                        💬 Forcer Affichage
                    </button>
                    <button class="btn btn-danger" onclick="resetState()">
                        🗑️ Reset État
                    </button>
                </div>
            </div>

            <div class="status-section">
                <h2>📊 État Actuel du Système</h2>
                <div class="status-grid">
                    <div class="status-item">
                        <span class="status-label">Inscription Complétée :</span>
                        <span class="status-value" id="signup-status">false</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Message Visible :</span>
                        <span class="status-value" id="message-status">false</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">LocalStorage (signup_completed) :</span>
                        <span class="status-value" id="localstorage-status">null</span>
                    </div>
                </div>
            </div>

            <div class="logs-section">
                <h2>📝 Logs du Système</h2>
                <div class="logs" id="logs"></div>
            </div>
        </div>
    </div>

    <!-- Message de profil -->
    <div class="message-overlay hidden" id="profileMessage">
        <div class="message-container">
            <div class="message-icon">✅</div>
            <h2 class="message-title">Bienvenue sur Kairos IT !</h2>
            <p class="message-text">
                <strong>Votre profil sera complété dans quelques minutes.</strong><br><br>
                Notre IA analyse votre CV pour optimiser votre profil automatiquement et vous proposer les meilleures opportunités.
            </p>
            <button class="message-button" onclick="acknowledgeMessage()">
                Parfait, j'ai compris
            </button>
        </div>
    </div>

    <script>
        let logs = [];

        function addLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            logs.unshift(`[${timestamp}] ${message}`);
            
            if (logs.length > 15) {
                logs = logs.slice(0, 15);
            }
            
            updateLogsDisplay();
        }

        function updateLogsDisplay() {
            const logsContainer = document.getElementById('logs');
            logsContainer.innerHTML = logs.map(log => 
                `<div class="log-entry">${log}</div>`
            ).join('');
        }

        function updateStatus() {
            const signupCompleted = localStorage.getItem('signup_completed') === 'true';
            const messageVisible = !document.getElementById('profileMessage').classList.contains('hidden');
            const localStorageValue = localStorage.getItem('signup_completed') || 'null';

            // Update signup status
            const signupEl = document.getElementById('signup-status');
            signupEl.textContent = signupCompleted;
            signupEl.className = `status-value ${signupCompleted ? 'status-true' : 'status-false'}`;

            // Update message status
            const messageEl = document.getElementById('message-status');
            messageEl.textContent = messageVisible;
            messageEl.className = `status-value ${messageVisible ? 'status-true' : 'status-false'}`;

            // Update localStorage status
            const localStorageEl = document.getElementById('localstorage-status');
            localStorageEl.textContent = localStorageValue;
            localStorageEl.className = `status-value ${localStorageValue === 'null' ? 'status-null' : (localStorageValue === 'true' ? 'status-true' : 'status-false')}`;
        }

        function simulateSignup() {
            addLog('🚀 ÉTAPE 1: Simulation de l\'inscription...');
            localStorage.setItem('signup_completed', 'true');
            addLog('✅ Inscription simulée - signup_completed: true');
            addLog('💡 Maintenant cliquez sur "Simuler Connexion" pour voir le message');
            updateStatus();
        }

        function simulateLogin() {
            addLog('🔑 ÉTAPE 2: Simulation de la connexion...');
            const signupCompleted = localStorage.getItem('signup_completed') === 'true';
            
            if (signupCompleted) {
                addLog('✅ Connexion réussie - utilisateur vient de s\'inscrire');
                addLog('🎯 Conditions remplies pour afficher le message !');
                addLog('🎉 AFFICHAGE: "Votre profil sera complété dans quelques minutes"');
                showMessage();
            } else {
                addLog('❌ Connexion réussie mais utilisateur n\'a pas fait d\'inscription récente');
                addLog('💡 Cliquez d\'abord sur "Simuler Inscription"');
            }
        }

        function showMessage() {
            addLog('💬 Affichage du message de profil...');
            document.getElementById('profileMessage').classList.remove('hidden');
            updateStatus();
        }

        function acknowledgeMessage() {
            addLog('👍 ÉTAPE 3: Utilisateur a cliqué sur "Parfait, j\'ai compris"');
            document.getElementById('profileMessage').classList.add('hidden');
            localStorage.removeItem('signup_completed');
            addLog('🧹 État nettoyé - signup_completed supprimé');
            addLog('✅ CYCLE TERMINÉ - Le message ne s\'affichera plus');
            updateStatus();
        }

        function resetState() {
            addLog('🗑️ Reset complet de l\'état...');
            localStorage.removeItem('signup_completed');
            document.getElementById('profileMessage').classList.add('hidden');
            addLog('✅ État réinitialisé - prêt pour un nouveau test');
            updateStatus();
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            addLog('🎯 Page de test chargée et prête');
            addLog('📖 Suivez les étapes : Inscription → Connexion → Message → Fermeture');
            updateStatus();
            
            // Vérifier si le message doit s'afficher au chargement
            const signupCompleted = localStorage.getItem('signup_completed') === 'true';
            if (signupCompleted) {
                addLog('🔍 signup_completed détecté au chargement');
                addLog('💡 Cliquez sur "Simuler Connexion" pour voir le message');
            }
        });
    </script>
</body>
</html>
