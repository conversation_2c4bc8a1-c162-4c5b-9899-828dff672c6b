/**
 * Script pour forcer l'application du mode sombre aux boutons et phrases d'ajout
 * Ce script est chargé après le chargement de la page et applique le mode sombre
 * aux boutons et phrases d'ajout dans les cartes
 */

(function() {
  // Vérifier si le thème sombre est actif
  function isDarkThemeActive() {
    return document.documentElement.classList.contains('dark-theme') || 
           document.body.classList.contains('dark-theme') ||
           localStorage.getItem('theme') === 'dark';
  }

  // Appliquer le mode sombre aux boutons et phrases d'ajout
  function applyDarkThemeToAddButtons() {
    if (!isDarkThemeActive()) {
      return;
    }

    console.log('Applying dark theme to add buttons and messages');

    // Sélecteurs pour les boutons d'ajout
    const addButtonSelectors = [
      '.add-btn',
      '.add-now-btn',
      '.add-cv-btn',
      '.add-item-btn',
      '.add-experience-btn',
      '.add-education-btn',
      '.add-certification-btn',
      '.add-skill-btn',
      '.add-language-btn',
      '.add-link-btn',
      '[class*="add-btn"]',
      '[class*="add-now-btn"]',
      '[class*="add-cv-btn"]',
      '[class*="add-item-btn"]',
      '[class*="add-experience-btn"]',
      '[class*="add-education-btn"]',
      '[class*="add-certification-btn"]',
      '[class*="add-skill-btn"]',
      '[class*="add-language-btn"]',
      '[class*="add-link-btn"]',
      'button[class*="add"]',
      'a[class*="add"]'
    ];

    // Appliquer le mode sombre à tous les boutons d'ajout
    addButtonSelectors.forEach(selector => {
      const addButtons = document.querySelectorAll(selector);
      
      addButtons.forEach(button => {
        // Appliquer les styles de base
        button.style.background = `linear-gradient(135deg, ${getComputedStyle(document.documentElement).getPropertyValue('--primary-color')}, ${getComputedStyle(document.documentElement).getPropertyValue('--primary-dark')})`;
        button.style.color = getComputedStyle(document.documentElement).getPropertyValue('--primary-contrast');
        button.style.boxShadow = getComputedStyle(document.documentElement).getPropertyValue('--shadow-sm');
        button.style.borderLeftColor = getComputedStyle(document.documentElement).getPropertyValue('--accent-color');
        
        // Appliquer les styles aux icônes
        const icons = button.querySelectorAll('mat-icon, .mat-icon, [class*="mat-icon"], i, .fa, .fas, .far, .fab, [class*="fa-"]');
        icons.forEach(icon => {
          icon.style.color = getComputedStyle(document.documentElement).getPropertyValue('--primary-contrast');
        });
      });
    });

    // Sélecteurs pour les phrases d'ajout
    const addTextSelectors = [
      '.add-text',
      '.add-message',
      '.add-prompt',
      '.add-label',
      '.add-description',
      '.empty-message',
      '.empty-text',
      '.empty-prompt',
      '.empty-label',
      '.empty-description',
      '[class*="add-text"]',
      '[class*="add-message"]',
      '[class*="add-prompt"]',
      '[class*="add-label"]',
      '[class*="add-description"]',
      '[class*="empty-message"]',
      '[class*="empty-text"]',
      '[class*="empty-prompt"]',
      '[class*="empty-label"]',
      '[class*="empty-description"]',
      'p[class*="add"]',
      'span[class*="add"]',
      'div[class*="add"]',
      'p[class*="empty"]',
      'span[class*="empty"]',
      'div[class*="empty"]'
    ];

    // Appliquer le mode sombre à toutes les phrases d'ajout
    addTextSelectors.forEach(selector => {
      const addTexts = document.querySelectorAll(selector);
      
      addTexts.forEach(text => {
        // Appliquer les styles de base
        text.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-secondary');
        
        // Appliquer les styles aux textes en gras
        const strongs = text.querySelectorAll('strong, b, em, i');
        strongs.forEach(strong => {
          strong.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-primary');
        });
        
        // Appliquer les styles aux icônes
        const icons = text.querySelectorAll('mat-icon, .mat-icon, [class*="mat-icon"], i, .fa, .fas, .far, .fab, [class*="fa-"]');
        icons.forEach(icon => {
          icon.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-muted');
        });
        
        // Appliquer les styles aux liens
        const links = text.querySelectorAll('a, .link, [class*="link"]');
        links.forEach(link => {
          link.style.color = getComputedStyle(document.documentElement).getPropertyValue('--primary-color');
        });
      });
    });

    // Sélecteurs pour les conteneurs d'ajout
    const addContainerSelectors = [
      '.add-container',
      '.add-section',
      '.add-wrapper',
      '.add-box',
      '.add-area',
      '.empty-container',
      '.empty-section',
      '.empty-wrapper',
      '.empty-box',
      '.empty-area',
      '[class*="add-container"]',
      '[class*="add-section"]',
      '[class*="add-wrapper"]',
      '[class*="add-box"]',
      '[class*="add-area"]',
      '[class*="empty-container"]',
      '[class*="empty-section"]',
      '[class*="empty-wrapper"]',
      '[class*="empty-box"]',
      '[class*="empty-area"]',
      'div[class*="add"]',
      'section[class*="add"]',
      'div[class*="empty"]',
      'section[class*="empty"]'
    ];

    // Appliquer le mode sombre à tous les conteneurs d'ajout
    addContainerSelectors.forEach(selector => {
      const addContainers = document.querySelectorAll(selector);
      
      addContainers.forEach(container => {
        // Appliquer les styles de base
        container.style.backgroundColor = getComputedStyle(document.documentElement).getPropertyValue('--background-alt');
        container.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-secondary');
        container.style.borderColor = getComputedStyle(document.documentElement).getPropertyValue('--border-color');
        container.style.boxShadow = getComputedStyle(document.documentElement).getPropertyValue('--shadow-sm');
      });
    });

    // Appliquer le mode sombre aux boutons d'ajout dans les cartes spécifiques
    const cardTypes = [
      'certification',
      'experience',
      'education',
      'skill',
      'language',
      'link'
    ];

    cardTypes.forEach(cardType => {
      // Sélecteurs pour les cartes spécifiques
      const cardSelectors = [
        `.${cardType}-card`,
        `[class*="${cardType}-card"]`,
        `[class*="${cardType}"]`
      ];

      // Appliquer le mode sombre aux boutons d'ajout dans les cartes spécifiques
      cardSelectors.forEach(cardSelector => {
        const cards = document.querySelectorAll(cardSelector);
        
        cards.forEach(card => {
          // Sélecteurs pour les boutons d'ajout dans les cartes spécifiques
          const buttonSelectors = [
            '.add-btn',
            '.add-now-btn',
            `.add-${cardType}-btn`,
            '[class*="add-btn"]',
            '[class*="add-now-btn"]',
            `[class*="add-${cardType}-btn"]`,
            'button[class*="add"]',
            'a[class*="add"]'
          ];

          // Appliquer le mode sombre aux boutons d'ajout dans les cartes spécifiques
          buttonSelectors.forEach(buttonSelector => {
            const buttons = card.querySelectorAll(buttonSelector);
            
            buttons.forEach(button => {
              // Appliquer les styles de base
              button.style.background = `linear-gradient(135deg, ${getComputedStyle(document.documentElement).getPropertyValue('--primary-color')}, ${getComputedStyle(document.documentElement).getPropertyValue('--primary-dark')})`;
              button.style.color = getComputedStyle(document.documentElement).getPropertyValue('--primary-contrast');
              button.style.boxShadow = getComputedStyle(document.documentElement).getPropertyValue('--shadow-sm');
              button.style.borderLeftColor = getComputedStyle(document.documentElement).getPropertyValue('--accent-color');
              
              // Appliquer les styles aux icônes
              const icons = button.querySelectorAll('mat-icon, .mat-icon, [class*="mat-icon"], i, .fa, .fas, .far, .fab, [class*="fa-"]');
              icons.forEach(icon => {
                icon.style.color = getComputedStyle(document.documentElement).getPropertyValue('--primary-contrast');
              });
            });
          });

          // Sélecteurs pour les phrases d'ajout dans les cartes spécifiques
          const textSelectors = [
            '.add-text',
            '.add-message',
            '.add-prompt',
            '.add-label',
            '.add-description',
            '.empty-message',
            '.empty-text',
            '.empty-prompt',
            '.empty-label',
            '.empty-description',
            '[class*="add-text"]',
            '[class*="add-message"]',
            '[class*="add-prompt"]',
            '[class*="add-label"]',
            '[class*="add-description"]',
            '[class*="empty-message"]',
            '[class*="empty-text"]',
            '[class*="empty-prompt"]',
            '[class*="empty-label"]',
            '[class*="empty-description"]',
            'p[class*="add"]',
            'span[class*="add"]',
            'div[class*="add"]',
            'p[class*="empty"]',
            'span[class*="empty"]',
            'div[class*="empty"]'
          ];

          // Appliquer le mode sombre aux phrases d'ajout dans les cartes spécifiques
          textSelectors.forEach(textSelector => {
            const texts = card.querySelectorAll(textSelector);
            
            texts.forEach(text => {
              // Appliquer les styles de base
              text.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-secondary');
              
              // Appliquer les styles aux textes en gras
              const strongs = text.querySelectorAll('strong, b, em, i');
              strongs.forEach(strong => {
                strong.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-primary');
              });
              
              // Appliquer les styles aux icônes
              const icons = text.querySelectorAll('mat-icon, .mat-icon, [class*="mat-icon"], i, .fa, .fas, .far, .fab, [class*="fa-"]');
              icons.forEach(icon => {
                icon.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-muted');
              });
              
              // Appliquer les styles aux liens
              const links = text.querySelectorAll('a, .link, [class*="link"]');
              links.forEach(link => {
                link.style.color = getComputedStyle(document.documentElement).getPropertyValue('--primary-color');
              });
            });
          });
        });
      });
    });
  }

  // Observer les changements dans le DOM
  function observeDOMChanges() {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        // Traiter les nouveaux nœuds ajoutés
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              // Vérifier si le nœud est un bouton d'ajout ou contient des boutons d'ajout
              if (node.classList && (
                  node.classList.contains('add-btn') ||
                  node.classList.contains('add-now-btn') ||
                  node.classList.contains('add-cv-btn') ||
                  node.classList.contains('add-item-btn') ||
                  node.classList.contains('add-experience-btn') ||
                  node.classList.contains('add-education-btn') ||
                  node.classList.contains('add-certification-btn') ||
                  node.classList.contains('add-skill-btn') ||
                  node.classList.contains('add-language-btn') ||
                  node.classList.contains('add-link-btn') ||
                  node.classList.contains('add-text') ||
                  node.classList.contains('add-message') ||
                  node.classList.contains('add-prompt') ||
                  node.classList.contains('add-label') ||
                  node.classList.contains('add-description') ||
                  node.classList.contains('empty-message') ||
                  node.classList.contains('empty-text') ||
                  node.classList.contains('empty-prompt') ||
                  node.classList.contains('empty-label') ||
                  node.classList.contains('empty-description') ||
                  node.classList.contains('add-container') ||
                  node.classList.contains('add-section') ||
                  node.classList.contains('add-wrapper') ||
                  node.classList.contains('add-box') ||
                  node.classList.contains('add-area') ||
                  node.classList.contains('empty-container') ||
                  node.classList.contains('empty-section') ||
                  node.classList.contains('empty-wrapper') ||
                  node.classList.contains('empty-box') ||
                  node.classList.contains('empty-area')
                )) {
                applyDarkThemeToAddButtons();
              } else {
                // Vérifier si le nœud contient des boutons d'ajout
                const addButtons = node.querySelectorAll('.add-btn, .add-now-btn, .add-cv-btn, .add-item-btn, .add-experience-btn, .add-education-btn, .add-certification-btn, .add-skill-btn, .add-language-btn, .add-link-btn, [class*="add-btn"], [class*="add-now-btn"], [class*="add-cv-btn"], [class*="add-item-btn"], [class*="add-experience-btn"], [class*="add-education-btn"], [class*="add-certification-btn"], [class*="add-skill-btn"], [class*="add-language-btn"], [class*="add-link-btn"], button[class*="add"], a[class*="add"]');
                if (addButtons.length > 0) {
                  applyDarkThemeToAddButtons();
                }
                
                // Vérifier si le nœud contient des phrases d'ajout
                const addTexts = node.querySelectorAll('.add-text, .add-message, .add-prompt, .add-label, .add-description, .empty-message, .empty-text, .empty-prompt, .empty-label, .empty-description, [class*="add-text"], [class*="add-message"], [class*="add-prompt"], [class*="add-label"], [class*="add-description"], [class*="empty-message"], [class*="empty-text"], [class*="empty-prompt"], [class*="empty-label"], [class*="empty-description"], p[class*="add"], span[class*="add"], div[class*="add"], p[class*="empty"], span[class*="empty"], div[class*="empty"]');
                if (addTexts.length > 0) {
                  applyDarkThemeToAddButtons();
                }
                
                // Vérifier si le nœud contient des conteneurs d'ajout
                const addContainers = node.querySelectorAll('.add-container, .add-section, .add-wrapper, .add-box, .add-area, .empty-container, .empty-section, .empty-wrapper, .empty-box, .empty-area, [class*="add-container"], [class*="add-section"], [class*="add-wrapper"], [class*="add-box"], [class*="add-area"], [class*="empty-container"], [class*="empty-section"], [class*="empty-wrapper"], [class*="empty-box"], [class*="empty-area"], div[class*="add"], section[class*="add"], div[class*="empty"], section[class*="empty"]');
                if (addContainers.length > 0) {
                  applyDarkThemeToAddButtons();
                }
              }
            }
          });
        }
        
        // Traiter les attributs modifiés
        if (mutation.type === 'attributes') {
          if (mutation.attributeName === 'style' || mutation.attributeName === 'class') {
            // Vérifier si le nœud est un bouton d'ajout ou contient des boutons d'ajout
            if (mutation.target.classList && (
                mutation.target.classList.contains('add-btn') ||
                mutation.target.classList.contains('add-now-btn') ||
                mutation.target.classList.contains('add-cv-btn') ||
                mutation.target.classList.contains('add-item-btn') ||
                mutation.target.classList.contains('add-experience-btn') ||
                mutation.target.classList.contains('add-education-btn') ||
                mutation.target.classList.contains('add-certification-btn') ||
                mutation.target.classList.contains('add-skill-btn') ||
                mutation.target.classList.contains('add-language-btn') ||
                mutation.target.classList.contains('add-link-btn') ||
                mutation.target.classList.contains('add-text') ||
                mutation.target.classList.contains('add-message') ||
                mutation.target.classList.contains('add-prompt') ||
                mutation.target.classList.contains('add-label') ||
                mutation.target.classList.contains('add-description') ||
                mutation.target.classList.contains('empty-message') ||
                mutation.target.classList.contains('empty-text') ||
                mutation.target.classList.contains('empty-prompt') ||
                mutation.target.classList.contains('empty-label') ||
                mutation.target.classList.contains('empty-description') ||
                mutation.target.classList.contains('add-container') ||
                mutation.target.classList.contains('add-section') ||
                mutation.target.classList.contains('add-wrapper') ||
                mutation.target.classList.contains('add-box') ||
                mutation.target.classList.contains('add-area') ||
                mutation.target.classList.contains('empty-container') ||
                mutation.target.classList.contains('empty-section') ||
                mutation.target.classList.contains('empty-wrapper') ||
                mutation.target.classList.contains('empty-box') ||
                mutation.target.classList.contains('empty-area')
              )) {
              applyDarkThemeToAddButtons();
            }
          }
        }
      });
    });

    // Observer les changements dans le document
    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['style', 'class']
    });
  }

  // Fonction principale
  function init() {
    // Appliquer le mode sombre aux boutons et phrases d'ajout
    applyDarkThemeToAddButtons();
    
    // Observer les changements dans le DOM
    observeDOMChanges();
    
    // Réappliquer le mode sombre toutes les 2 secondes pour les éléments qui pourraient être générés dynamiquement
    setInterval(() => {
      if (isDarkThemeActive()) {
        applyDarkThemeToAddButtons();
      }
    }, 2000);
  }

  // Attendre que le DOM soit chargé
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
  } else {
    init();
  }
})();
