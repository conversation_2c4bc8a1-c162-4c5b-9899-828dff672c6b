/**
 * Script pour forcer l'application du mode sombre aux éléments récalcitrants
 * Ce script est chargé après le chargement de la page et applique le mode sombre
 * aux éléments qui ne sont pas correctement stylisés par les CSS
 */

(function() {
  // Vérifier si le thème sombre est actif
  function isDarkThemeActive() {
    return document.documentElement.classList.contains('dark-theme') || 
           document.body.classList.contains('dark-theme') ||
           localStorage.getItem('theme') === 'dark';
  }

  // Appliquer le mode sombre aux éléments récalcitrants
  function applyDarkThemeToElements() {
    if (!isDarkThemeActive()) {
      return;
    }

    // Liste des sélecteurs d'éléments récalcitrants
    const selectors = [
      // Cartes d'information sur la page d'accueil
      '.info-card',
      '.post-card',
      '.company-card',
      '.workspace-card',
      '.pricing-card',
      '.profile-card',
      'mat-card',
      '.mat-mdc-card',
      
      // Éléments de la page de profil
      '.profile-header',
      '.profile-cover',
      '.profile-avatar-container',
      '.profile-avatar',
      '.profile-main-info',
      '.profile-body',
      
      // Éléments d'information
      '.info-item',
      '.info-text',
      
      // Éléments de la page d'accueil
      '.carousel-description h2',
      '.carousel-description p',
      '.section-title',
      '.about-description p',
      
      // Éléments de la page d'accueil des posts
      '.post-cards-container',
      '.post-card',
      '.post-card-header',
      '.avatar-icon',
      '.workspace-name',
      '.description',
      '.info',
      
      // Éléments de grille
      'mat-grid-list',
      'mat-grid-tile',
      
      // Éléments de fond
      'body',
      'html',
      '.app-container',
      '.main-container',
      '.content-container',
      '.page-container'
    ];

    // Appliquer le mode sombre à chaque élément
    selectors.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      
      elements.forEach(element => {
        // Appliquer les styles du mode sombre
        applyDarkThemeStyles(element);
      });
    });

    // Appliquer le mode sombre aux éléments avec des styles en ligne
    const allElements = document.querySelectorAll('*');
    
    allElements.forEach(element => {
      if (element.style && element.style.backgroundColor) {
        // Remplacer les couleurs de fond blanches
        if (isWhiteBackground(element.style.backgroundColor)) {
          element.style.backgroundColor = getComputedStyle(document.documentElement).getPropertyValue('--background-card');
        }
      }
      
      if (element.style && element.style.color) {
        // Remplacer les couleurs de texte noires
        if (isBlackText(element.style.color)) {
          element.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-primary');
        }
        
        // Remplacer les couleurs de texte grises
        if (isGrayText(element.style.color)) {
          element.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-secondary');
        }
      }
      
      if (element.style && element.style.borderColor) {
        // Remplacer les couleurs de bordure claires
        if (isLightBorder(element.style.borderColor)) {
          element.style.borderColor = getComputedStyle(document.documentElement).getPropertyValue('--border-color');
        }
      }
    });
  }

  // Appliquer les styles du mode sombre à un élément
  function applyDarkThemeStyles(element) {
    // Appliquer les styles de base du mode sombre
    element.classList.add('dark-theme-element');
    
    // Appliquer les styles spécifiques en fonction du type d'élément
    if (element.classList.contains('info-card') || 
        element.classList.contains('post-card') || 
        element.classList.contains('company-card') || 
        element.classList.contains('workspace-card') || 
        element.classList.contains('pricing-card') || 
        element.classList.contains('profile-card') || 
        element.tagName === 'MAT-CARD' || 
        element.classList.contains('mat-mdc-card')) {
      // Styles pour les cartes
      element.style.backgroundColor = getComputedStyle(document.documentElement).getPropertyValue('--background-card');
      element.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-primary');
      element.style.borderColor = getComputedStyle(document.documentElement).getPropertyValue('--border-color');
      element.style.boxShadow = getComputedStyle(document.documentElement).getPropertyValue('--shadow-sm');
    } else if (element.classList.contains('profile-header') || 
               element.classList.contains('profile-cover') || 
               element.classList.contains('profile-avatar-container') || 
               element.classList.contains('profile-avatar') || 
               element.classList.contains('profile-main-info') || 
               element.classList.contains('profile-body')) {
      // Styles pour les éléments de la page de profil
      element.style.backgroundColor = getComputedStyle(document.documentElement).getPropertyValue('--background-card');
      element.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-primary');
      element.style.borderColor = getComputedStyle(document.documentElement).getPropertyValue('--border-color');
    } else if (element.classList.contains('info-item') || 
               element.classList.contains('info-text')) {
      // Styles pour les éléments d'information
      element.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-secondary');
      element.style.borderColor = getComputedStyle(document.documentElement).getPropertyValue('--border-color');
    } else if (element.tagName === 'BODY' || 
               element.tagName === 'HTML' || 
               element.classList.contains('app-container') || 
               element.classList.contains('main-container') || 
               element.classList.contains('content-container') || 
               element.classList.contains('page-container')) {
      // Styles pour les éléments de fond
      element.style.backgroundColor = getComputedStyle(document.documentElement).getPropertyValue('--background-color');
      element.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-primary');
    }
  }

  // Vérifier si une couleur de fond est blanche ou très claire
  function isWhiteBackground(color) {
    return color === '#fff' || 
           color === '#ffffff' || 
           color === 'white' || 
           color === 'rgb(255, 255, 255)' || 
           color === 'rgba(255, 255, 255, 1)' ||
           color === '#f8f9fa' ||
           color === '#f5f5f5' ||
           color === '#f0f0f0' ||
           color === '#eeeeee' ||
           color === '#e9ecef';
  }

  // Vérifier si une couleur de texte est noire ou très foncée
  function isBlackText(color) {
    return color === '#000' || 
           color === '#000000' || 
           color === 'black' || 
           color === 'rgb(0, 0, 0)' || 
           color === 'rgba(0, 0, 0, 1)' ||
           color === '#111111' ||
           color === '#222222' ||
           color === '#333333' ||
           color === '#444444' ||
           color === '#1a2a44' ||
           color === '#001660';
  }

  // Vérifier si une couleur de texte est grise
  function isGrayText(color) {
    return color === '#555' || 
           color === '#555555' || 
           color === '#666' || 
           color === '#666666' || 
           color === '#777' || 
           color === '#777777' || 
           color === '#888' || 
           color === '#888888' || 
           color === '#999' || 
           color === '#999999' || 
           color === 'gray' || 
           color === 'grey' || 
           color === 'rgb(128, 128, 128)' || 
           color === 'rgba(128, 128, 128, 1)' ||
           color === '#4b5563';
  }

  // Vérifier si une couleur de bordure est claire
  function isLightBorder(color) {
    return color === '#ccc' || 
           color === '#cccccc' || 
           color === '#ddd' || 
           color === '#dddddd' || 
           color === '#eee' || 
           color === '#eeeeee' || 
           color === 'rgb(204, 204, 204)' || 
           color === 'rgba(204, 204, 204, 1)' ||
           color === 'rgb(221, 221, 221)' || 
           color === 'rgba(221, 221, 221, 1)' ||
           color === 'rgb(238, 238, 238)' || 
           color === 'rgba(238, 238, 238, 1)' ||
           color === '#e9ecef';
  }

  // Observer les changements dans le DOM
  function observeDOMChanges() {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        // Traiter les nouveaux nœuds ajoutés
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              // Appliquer le mode sombre aux nouveaux éléments
              applyDarkThemeStyles(node);
              
              // Appliquer le mode sombre aux enfants
              const children = node.querySelectorAll('*');
              children.forEach(child => {
                applyDarkThemeStyles(child);
              });
            }
          });
        }
        
        // Traiter les attributs modifiés
        if (mutation.type === 'attributes') {
          if (mutation.attributeName === 'style' || mutation.attributeName === 'class') {
            applyDarkThemeStyles(mutation.target);
          }
        }
      });
    });

    // Observer les changements dans le document
    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['style', 'class']
    });
  }

  // Fonction principale
  function init() {
    // Appliquer le mode sombre aux éléments existants
    applyDarkThemeToElements();
    
    // Observer les changements dans le DOM
    observeDOMChanges();
    
    // Réappliquer le mode sombre toutes les 2 secondes pour les éléments qui pourraient être générés dynamiquement
    setInterval(() => {
      if (isDarkThemeActive()) {
        applyDarkThemeToElements();
      }
    }, 2000);
  }

  // Attendre que le DOM soit chargé
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
  } else {
    init();
  }
})();
