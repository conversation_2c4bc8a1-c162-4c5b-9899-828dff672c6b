/**
 * Script pour forcer l'application du mode sombre aux mat-card et info-row
 * Ce script est chargé après le chargement de la page et applique le mode sombre
 * aux mat-card dans les cartes d'information du profil et aux div de classe info-row
 */

(function() {
  // Vérifier si le thème sombre est actif
  function isDarkThemeActive() {
    return document.documentElement.classList.contains('dark-theme') || 
           document.body.classList.contains('dark-theme') ||
           localStorage.getItem('theme') === 'dark';
  }

  // Appliquer le mode sombre aux mat-card et info-row
  function applyDarkThemeToMatCardInfoRow() {
    if (!isDarkThemeActive()) {
      return;
    }

    console.log('Applying dark theme to mat-card and info-row');

    // Sélecteurs pour les mat-card dans les cartes d'information
    const matCardSelectors = [
      '.info-card mat-card',
      '.info-card .mat-card',
      '.info-card .mat-mdc-card',
      '.info-card [class*="mat-card"]',
      '.info-card [class*="mat-mdc-card"]',
      'mat-card.info-card',
      '.mat-card.info-card',
      '.mat-mdc-card.info-card',
      '[class*="mat-card"].info-card',
      '[class*="mat-mdc-card"].info-card',
      'mat-card mat-card',
      '.mat-card .mat-card',
      '.mat-mdc-card .mat-mdc-card',
      '[class*="mat-card"] [class*="mat-card"]',
      '[class*="mat-mdc-card"] [class*="mat-mdc-card"]',
      '.item-block mat-card',
      '.item-block .mat-card',
      '.item-block .mat-mdc-card',
      '.item-block [class*="mat-card"]',
      '.item-block [class*="mat-mdc-card"]',
      '[class*="item-block"] mat-card',
      '[class*="item-block"] .mat-card',
      '[class*="item-block"] .mat-mdc-card',
      '[class*="item-block"] [class*="mat-card"]',
      '[class*="item-block"] [class*="mat-mdc-card"]'
    ];

    // Appliquer le mode sombre à tous les mat-card dans les cartes d'information
    matCardSelectors.forEach(selector => {
      const matCards = document.querySelectorAll(selector);
      
      matCards.forEach(card => {
        // Appliquer les styles de base
        card.style.backgroundColor = getComputedStyle(document.documentElement).getPropertyValue('--background-card');
        card.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-primary');
        card.style.borderColor = getComputedStyle(document.documentElement).getPropertyValue('--border-color');
        card.style.boxShadow = getComputedStyle(document.documentElement).getPropertyValue('--shadow-sm');
        
        // Appliquer les styles aux en-têtes de carte
        const cardHeaders = card.querySelectorAll('mat-card-header, .mat-card-header, .mat-mdc-card-header, [class*="mat-card-header"], [class*="mat-mdc-card-header"]');
        cardHeaders.forEach(header => {
          header.style.backgroundColor = getComputedStyle(document.documentElement).getPropertyValue('--background-alt');
          header.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-primary');
          header.style.borderColor = getComputedStyle(document.documentElement).getPropertyValue('--border-color');
          
          // Appliquer les styles aux titres de carte
          const cardTitles = header.querySelectorAll('mat-card-title, .mat-card-title, .mat-mdc-card-title, [class*="mat-card-title"], [class*="mat-mdc-card-title"]');
          cardTitles.forEach(title => {
            title.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-primary');
            
            // Appliquer les styles aux icônes
            const icons = title.querySelectorAll('mat-icon, .mat-icon, [class*="mat-icon"]');
            icons.forEach(icon => {
              icon.style.color = getComputedStyle(document.documentElement).getPropertyValue('--primary-color');
            });
          });
          
          // Appliquer les styles aux sous-titres de carte
          const cardSubtitles = header.querySelectorAll('mat-card-subtitle, .mat-card-subtitle, .mat-mdc-card-subtitle, [class*="mat-card-subtitle"], [class*="mat-mdc-card-subtitle"]');
          cardSubtitles.forEach(subtitle => {
            subtitle.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-secondary');
          });
        });
        
        // Appliquer les styles aux contenus de carte
        const cardContents = card.querySelectorAll('mat-card-content, .mat-card-content, .mat-mdc-card-content, [class*="mat-card-content"], [class*="mat-mdc-card-content"]');
        cardContents.forEach(content => {
          content.style.backgroundColor = getComputedStyle(document.documentElement).getPropertyValue('--background-card');
          content.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-secondary');
          
          // Appliquer les styles aux paragraphes
          const paragraphs = content.querySelectorAll('p');
          paragraphs.forEach(paragraph => {
            paragraph.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-secondary');
            
            // Appliquer les styles aux icônes
            const icons = paragraph.querySelectorAll('mat-icon, .mat-icon, [class*="mat-icon"]');
            icons.forEach(icon => {
              icon.style.color = getComputedStyle(document.documentElement).getPropertyValue('--primary-color');
            });
          });
        });
        
        // Appliquer les styles aux actions de carte
        const cardActions = card.querySelectorAll('mat-card-actions, .mat-card-actions, .mat-mdc-card-actions, [class*="mat-card-actions"], [class*="mat-mdc-card-actions"]');
        cardActions.forEach(actions => {
          actions.style.backgroundColor = getComputedStyle(document.documentElement).getPropertyValue('--background-card');
          actions.style.borderColor = getComputedStyle(document.documentElement).getPropertyValue('--border-color');
          
          // Appliquer les styles aux boutons
          const buttons = actions.querySelectorAll('button');
          buttons.forEach(button => {
            button.style.color = getComputedStyle(document.documentElement).getPropertyValue('--primary-color');
            
            // Appliquer les styles aux icônes
            const icons = button.querySelectorAll('mat-icon, .mat-icon, [class*="mat-icon"]');
            icons.forEach(icon => {
              icon.style.color = getComputedStyle(document.documentElement).getPropertyValue('--primary-color');
            });
          });
        });
        
        // Appliquer les styles aux pieds de carte
        const cardFooters = card.querySelectorAll('mat-card-footer, .mat-card-footer, .mat-mdc-card-footer, [class*="mat-card-footer"], [class*="mat-mdc-card-footer"]');
        cardFooters.forEach(footer => {
          footer.style.backgroundColor = getComputedStyle(document.documentElement).getPropertyValue('--background-card');
          footer.style.borderColor = getComputedStyle(document.documentElement).getPropertyValue('--border-color');
        });
      });
    });

    // Sélecteurs pour les info-row
    const infoRowSelectors = [
      '.info-row',
      '[class*="info-row"]'
    ];

    // Appliquer le mode sombre à tous les info-row
    infoRowSelectors.forEach(selector => {
      const infoRows = document.querySelectorAll(selector);
      
      infoRows.forEach(row => {
        // Appliquer les styles de base
        row.style.backgroundColor = getComputedStyle(document.documentElement).getPropertyValue('--background-alt');
        row.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-secondary');
        row.style.borderColor = getComputedStyle(document.documentElement).getPropertyValue('--border-color');
        
        // Appliquer les styles aux éléments de texte
        const textElements = row.querySelectorAll('span, p, div, label, h1, h2, h3, h4, h5, h6');
        textElements.forEach(element => {
          element.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-secondary');
        });
        
        // Appliquer les styles aux éléments forts
        const strongElements = row.querySelectorAll('strong, b, em, i');
        strongElements.forEach(element => {
          element.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-primary');
        });
        
        // Appliquer les styles aux liens
        const linkElements = row.querySelectorAll('a, .link, [class*="link"]');
        linkElements.forEach(element => {
          element.style.color = getComputedStyle(document.documentElement).getPropertyValue('--primary-color');
        });
        
        // Appliquer les styles aux icônes
        const iconElements = row.querySelectorAll('mat-icon, .mat-icon, [class*="mat-icon"], i, .fa, .fas, .far, .fab, [class*="fa-"]');
        iconElements.forEach(element => {
          element.style.color = getComputedStyle(document.documentElement).getPropertyValue('--primary-color');
        });
        
        // Appliquer les styles aux boutons
        const buttonElements = row.querySelectorAll('button');
        buttonElements.forEach(element => {
          element.style.color = getComputedStyle(document.documentElement).getPropertyValue('--primary-color');
          
          // Appliquer les styles aux icônes
          const icons = element.querySelectorAll('mat-icon, .mat-icon, [class*="mat-icon"], i, .fa, .fas, .far, .fab, [class*="fa-"]');
          icons.forEach(icon => {
            icon.style.color = getComputedStyle(document.documentElement).getPropertyValue('--primary-color');
          });
        });
      });
    });

    // Types de cartes spécifiques
    const cardTypes = [
      'certification',
      'experience',
      'education',
      'skill',
      'link',
      'language'
    ];

    // Appliquer le mode sombre aux mat-card dans les cartes spécifiques
    cardTypes.forEach(cardType => {
      // Sélecteurs pour les cartes spécifiques
      const cardSelectors = [
        `.${cardType}-card`,
        `[class*="${cardType}-card"]`,
        `[class*="${cardType}"]`
      ];

      // Appliquer le mode sombre aux mat-card dans les cartes spécifiques
      cardSelectors.forEach(cardSelector => {
        const cards = document.querySelectorAll(cardSelector);
        
        cards.forEach(card => {
          // Sélecteurs pour les mat-card dans les cartes spécifiques
          const matCardSelectors = [
            'mat-card',
            '.mat-card',
            '.mat-mdc-card',
            '[class*="mat-card"]',
            '[class*="mat-mdc-card"]'
          ];

          // Appliquer le mode sombre aux mat-card dans les cartes spécifiques
          matCardSelectors.forEach(matCardSelector => {
            const matCards = card.querySelectorAll(matCardSelector);
            
            matCards.forEach(matCard => {
              // Appliquer les styles de base
              matCard.style.backgroundColor = getComputedStyle(document.documentElement).getPropertyValue('--background-card');
              matCard.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-primary');
              matCard.style.borderColor = getComputedStyle(document.documentElement).getPropertyValue('--border-color');
              matCard.style.boxShadow = getComputedStyle(document.documentElement).getPropertyValue('--shadow-sm');
              
              // Appliquer les styles aux en-têtes de carte
              const cardHeaders = matCard.querySelectorAll('mat-card-header, .mat-card-header, .mat-mdc-card-header, [class*="mat-card-header"], [class*="mat-mdc-card-header"]');
              cardHeaders.forEach(header => {
                header.style.backgroundColor = getComputedStyle(document.documentElement).getPropertyValue('--background-alt');
                header.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-primary');
                header.style.borderColor = getComputedStyle(document.documentElement).getPropertyValue('--border-color');
                
                // Appliquer les styles aux titres de carte
                const cardTitles = header.querySelectorAll('mat-card-title, .mat-card-title, .mat-mdc-card-title, [class*="mat-card-title"], [class*="mat-mdc-card-title"]');
                cardTitles.forEach(title => {
                  title.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-primary');
                  
                  // Appliquer les styles aux icônes
                  const icons = title.querySelectorAll('mat-icon, .mat-icon, [class*="mat-icon"]');
                  icons.forEach(icon => {
                    icon.style.color = getComputedStyle(document.documentElement).getPropertyValue('--primary-color');
                  });
                });
                
                // Appliquer les styles aux sous-titres de carte
                const cardSubtitles = header.querySelectorAll('mat-card-subtitle, .mat-card-subtitle, .mat-mdc-card-subtitle, [class*="mat-card-subtitle"], [class*="mat-mdc-card-subtitle"]');
                cardSubtitles.forEach(subtitle => {
                  subtitle.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-secondary');
                });
              });
              
              // Appliquer les styles aux contenus de carte
              const cardContents = matCard.querySelectorAll('mat-card-content, .mat-card-content, .mat-mdc-card-content, [class*="mat-card-content"], [class*="mat-mdc-card-content"]');
              cardContents.forEach(content => {
                content.style.backgroundColor = getComputedStyle(document.documentElement).getPropertyValue('--background-card');
                content.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-secondary');
                
                // Appliquer les styles aux paragraphes
                const paragraphs = content.querySelectorAll('p');
                paragraphs.forEach(paragraph => {
                  paragraph.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-secondary');
                  
                  // Appliquer les styles aux icônes
                  const icons = paragraph.querySelectorAll('mat-icon, .mat-icon, [class*="mat-icon"]');
                  icons.forEach(icon => {
                    icon.style.color = getComputedStyle(document.documentElement).getPropertyValue('--primary-color');
                  });
                });
              });
              
              // Appliquer les styles aux actions de carte
              const cardActions = matCard.querySelectorAll('mat-card-actions, .mat-card-actions, .mat-mdc-card-actions, [class*="mat-card-actions"], [class*="mat-mdc-card-actions"]');
              cardActions.forEach(actions => {
                actions.style.backgroundColor = getComputedStyle(document.documentElement).getPropertyValue('--background-card');
                actions.style.borderColor = getComputedStyle(document.documentElement).getPropertyValue('--border-color');
                
                // Appliquer les styles aux boutons
                const buttons = actions.querySelectorAll('button');
                buttons.forEach(button => {
                  button.style.color = getComputedStyle(document.documentElement).getPropertyValue('--primary-color');
                  
                  // Appliquer les styles aux icônes
                  const icons = button.querySelectorAll('mat-icon, .mat-icon, [class*="mat-icon"]');
                  icons.forEach(icon => {
                    icon.style.color = getComputedStyle(document.documentElement).getPropertyValue('--primary-color');
                  });
                });
              });
              
              // Appliquer les styles aux pieds de carte
              const cardFooters = matCard.querySelectorAll('mat-card-footer, .mat-card-footer, .mat-mdc-card-footer, [class*="mat-card-footer"], [class*="mat-mdc-card-footer"]');
              cardFooters.forEach(footer => {
                footer.style.backgroundColor = getComputedStyle(document.documentElement).getPropertyValue('--background-card');
                footer.style.borderColor = getComputedStyle(document.documentElement).getPropertyValue('--border-color');
              });
            });
          });

          // Sélecteurs pour les info-row dans les cartes spécifiques
          const infoRowSelectors = [
            '.info-row',
            '[class*="info-row"]'
          ];

          // Appliquer le mode sombre aux info-row dans les cartes spécifiques
          infoRowSelectors.forEach(infoRowSelector => {
            const infoRows = card.querySelectorAll(infoRowSelector);
            
            infoRows.forEach(row => {
              // Appliquer les styles de base
              row.style.backgroundColor = getComputedStyle(document.documentElement).getPropertyValue('--background-alt');
              row.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-secondary');
              row.style.borderColor = getComputedStyle(document.documentElement).getPropertyValue('--border-color');
              
              // Appliquer les styles aux éléments de texte
              const textElements = row.querySelectorAll('span, p, div, label, h1, h2, h3, h4, h5, h6');
              textElements.forEach(element => {
                element.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-secondary');
              });
              
              // Appliquer les styles aux éléments forts
              const strongElements = row.querySelectorAll('strong, b, em, i');
              strongElements.forEach(element => {
                element.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-primary');
              });
              
              // Appliquer les styles aux liens
              const linkElements = row.querySelectorAll('a, .link, [class*="link"]');
              linkElements.forEach(element => {
                element.style.color = getComputedStyle(document.documentElement).getPropertyValue('--primary-color');
              });
              
              // Appliquer les styles aux icônes
              const iconElements = row.querySelectorAll('mat-icon, .mat-icon, [class*="mat-icon"], i, .fa, .fas, .far, .fab, [class*="fa-"]');
              iconElements.forEach(element => {
                element.style.color = getComputedStyle(document.documentElement).getPropertyValue('--primary-color');
              });
              
              // Appliquer les styles aux boutons
              const buttonElements = row.querySelectorAll('button');
              buttonElements.forEach(element => {
                element.style.color = getComputedStyle(document.documentElement).getPropertyValue('--primary-color');
                
                // Appliquer les styles aux icônes
                const icons = element.querySelectorAll('mat-icon, .mat-icon, [class*="mat-icon"], i, .fa, .fas, .far, .fab, [class*="fa-"]');
                icons.forEach(icon => {
                  icon.style.color = getComputedStyle(document.documentElement).getPropertyValue('--primary-color');
                });
              });
            });
          });
        });
      });
    });
  }

  // Observer les changements dans le DOM
  function observeDOMChanges() {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        // Traiter les nouveaux nœuds ajoutés
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              // Vérifier si le nœud est un mat-card ou un info-row ou contient des mat-card ou des info-row
              if (node.tagName === 'MAT-CARD' || 
                  node.classList && (
                    node.classList.contains('mat-card') ||
                    node.classList.contains('mat-mdc-card') ||
                    node.classList.contains('info-row')
                  )) {
                applyDarkThemeToMatCardInfoRow();
              } else {
                // Vérifier si le nœud contient des mat-card ou des info-row
                const matCards = node.querySelectorAll('mat-card, .mat-card, .mat-mdc-card, [class*="mat-card"], [class*="mat-mdc-card"]');
                const infoRows = node.querySelectorAll('.info-row, [class*="info-row"]');
                if (matCards.length > 0 || infoRows.length > 0) {
                  applyDarkThemeToMatCardInfoRow();
                }
              }
            }
          });
        }
        
        // Traiter les attributs modifiés
        if (mutation.type === 'attributes') {
          if (mutation.attributeName === 'style' || mutation.attributeName === 'class') {
            // Vérifier si le nœud est un mat-card ou un info-row ou contient des mat-card ou des info-row
            if (mutation.target.tagName === 'MAT-CARD' || 
                mutation.target.classList && (
                  mutation.target.classList.contains('mat-card') ||
                  mutation.target.classList.contains('mat-mdc-card') ||
                  mutation.target.classList.contains('info-row')
                )) {
              applyDarkThemeToMatCardInfoRow();
            }
          }
        }
      });
    });

    // Observer les changements dans le document
    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['style', 'class']
    });
  }

  // Fonction principale
  function init() {
    // Appliquer le mode sombre aux mat-card et info-row
    applyDarkThemeToMatCardInfoRow();
    
    // Observer les changements dans le DOM
    observeDOMChanges();
    
    // Réappliquer le mode sombre toutes les 2 secondes pour les éléments qui pourraient être générés dynamiquement
    setInterval(() => {
      if (isDarkThemeActive()) {
        applyDarkThemeToMatCardInfoRow();
      }
    }, 2000);
  }

  // Attendre que le DOM soit chargé
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
  } else {
    init();
  }
})();
