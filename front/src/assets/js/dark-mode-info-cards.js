/**
 * Script pour forcer l'application du mode sombre aux cartes d'information
 * Ce script est chargé après le chargement de la page et applique le mode sombre
 * aux cartes d'information et à leurs éléments imbriqués
 */

(function() {
  // Vérifier si le thème sombre est actif
  function isDarkThemeActive() {
    return document.documentElement.classList.contains('dark-theme') || 
           document.body.classList.contains('dark-theme') ||
           localStorage.getItem('theme') === 'dark';
  }

  // Appliquer le mode sombre aux cartes d'information
  function applyDarkThemeToInfoCards() {
    if (!isDarkThemeActive()) {
      return;
    }

    console.log('Applying dark theme to info cards');

    // Sélecteurs pour les cartes d'information
    const infoCardSelectors = [
      '.info-card',
      'mat-card.info-card',
      '.mat-card.info-card',
      '.mat-mdc-card.info-card',
      '[class*="info-card"]',
      'mat-card',
      '.mat-card',
      '.mat-mdc-card'
    ];

    // Appliquer le mode sombre à toutes les cartes d'information
    infoCardSelectors.forEach(selector => {
      const infoCards = document.querySelectorAll(selector);
      
      infoCards.forEach(card => {
        // Appliquer les styles de base
        card.style.backgroundColor = getComputedStyle(document.documentElement).getPropertyValue('--background-card');
        card.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-primary');
        card.style.borderColor = getComputedStyle(document.documentElement).getPropertyValue('--border-color');
        card.style.boxShadow = getComputedStyle(document.documentElement).getPropertyValue('--shadow-sm');
        
        // Appliquer les styles aux en-têtes de carte
        const cardHeaders = card.querySelectorAll('mat-card-header, .mat-card-header, .mat-mdc-card-header, [class*="mat-card-header"], [class*="mat-mdc-card-header"]');
        cardHeaders.forEach(header => {
          header.style.backgroundColor = getComputedStyle(document.documentElement).getPropertyValue('--background-alt');
          header.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-primary');
          header.style.borderColor = getComputedStyle(document.documentElement).getPropertyValue('--border-color');
        });
        
        // Appliquer les styles aux titres de carte
        const cardTitles = card.querySelectorAll('mat-card-title, .mat-card-title, .mat-mdc-card-title, [class*="mat-card-title"], [class*="mat-mdc-card-title"]');
        cardTitles.forEach(title => {
          title.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-primary');
          
          // Appliquer les styles aux icônes de titre
          const cardIcons = title.querySelectorAll('.card-icon, [class*="card-icon"], mat-icon, .mat-icon, [class*="mat-icon"]');
          cardIcons.forEach(icon => {
            icon.style.color = getComputedStyle(document.documentElement).getPropertyValue('--primary-color');
            icon.style.backgroundColor = `rgba(${getComputedStyle(document.documentElement).getPropertyValue('--primary-color-rgb')}, 0.1)`;
          });
        });
        
        // Appliquer les styles aux sous-titres de carte
        const cardSubtitles = card.querySelectorAll('mat-card-subtitle, .mat-card-subtitle, .mat-mdc-card-subtitle, [class*="mat-card-subtitle"], [class*="mat-mdc-card-subtitle"]');
        cardSubtitles.forEach(subtitle => {
          subtitle.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-secondary');
        });
        
        // Appliquer les styles aux contenus de carte
        const cardContents = card.querySelectorAll('mat-card-content, .mat-card-content, .mat-mdc-card-content, [class*="mat-card-content"], [class*="mat-mdc-card-content"]');
        cardContents.forEach(content => {
          content.style.backgroundColor = getComputedStyle(document.documentElement).getPropertyValue('--background-card');
          content.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-secondary');
          
          // Appliquer les styles aux paragraphes
          const paragraphs = content.querySelectorAll('p');
          paragraphs.forEach(paragraph => {
            paragraph.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-secondary');
            
            // Appliquer les styles aux icônes
            const icons = paragraph.querySelectorAll('mat-icon, .mat-icon, [class*="mat-icon"]');
            icons.forEach(icon => {
              icon.style.color = getComputedStyle(document.documentElement).getPropertyValue('--primary-color');
            });
            
            // Appliquer les styles aux textes en gras
            const strongs = paragraph.querySelectorAll('strong');
            strongs.forEach(strong => {
              strong.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-primary');
            });
          });
          
          // Appliquer les styles aux blocs d'éléments
          const itemBlocks = content.querySelectorAll('.item-block, [class*="item-block"]');
          itemBlocks.forEach(block => {
            block.style.backgroundColor = getComputedStyle(document.documentElement).getPropertyValue('--background-alt');
            block.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-primary');
            block.style.borderColor = getComputedStyle(document.documentElement).getPropertyValue('--border-color');
            block.style.boxShadow = getComputedStyle(document.documentElement).getPropertyValue('--shadow-sm');
            
            // Appliquer les styles aux en-têtes de bloc
            const itemHeaders = block.querySelectorAll('.item-header, [class*="item-header"]');
            itemHeaders.forEach(header => {
              header.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-primary');
              
              // Appliquer les styles aux titres de bloc
              const itemTitles = header.querySelectorAll('.item-title, [class*="item-title"]');
              itemTitles.forEach(title => {
                title.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-primary');
                
                // Appliquer les styles aux titres h4
                const h4s = title.querySelectorAll('h4');
                h4s.forEach(h4 => {
                  h4.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-primary');
                });
                
                // Appliquer les styles aux sous-titres
                const spans = title.querySelectorAll('span, .item-subtitle, [class*="item-subtitle"]');
                spans.forEach(span => {
                  span.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-secondary');
                });
                
                // Appliquer les styles aux liens
                const links = title.querySelectorAll('a, .link-title, [class*="link-title"]');
                links.forEach(link => {
                  link.style.color = getComputedStyle(document.documentElement).getPropertyValue('--primary-color');
                });
              });
              
              // Appliquer les styles aux actions de bloc
              const itemActions = header.querySelectorAll('.item-actions, [class*="item-actions"]');
              itemActions.forEach(actions => {
                actions.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-primary');
                
                // Appliquer les styles aux boutons
                const buttons = actions.querySelectorAll('button, .action-btn, [class*="action-btn"]');
                buttons.forEach(button => {
                  button.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-primary');
                  
                  // Appliquer les styles aux icônes
                  const icons = button.querySelectorAll('mat-icon, .mat-icon, [class*="mat-icon"]');
                  icons.forEach(icon => {
                    icon.style.color = getComputedStyle(document.documentElement).getPropertyValue('--primary-color');
                  });
                });
              });
            });
            
            // Appliquer les styles aux contenus de bloc
            const itemContents = block.querySelectorAll('.item-content, .item-details, [class*="item-content"], [class*="item-details"]');
            itemContents.forEach(content => {
              content.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-secondary');
              
              // Appliquer les styles aux paragraphes
              const paragraphs = content.querySelectorAll('p');
              paragraphs.forEach(paragraph => {
                paragraph.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-secondary');
              });
            });
            
            // Appliquer les styles aux pieds de bloc
            const itemFooters = block.querySelectorAll('.item-footer, [class*="item-footer"]');
            itemFooters.forEach(footer => {
              footer.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-secondary');
              footer.style.borderColor = getComputedStyle(document.documentElement).getPropertyValue('--border-color');
              
              // Appliquer les styles aux dates
              const itemDates = footer.querySelectorAll('.item-date, [class*="item-date"]');
              itemDates.forEach(date => {
                date.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-muted');
              });
            });
          });
          
          // Appliquer les styles aux boutons d'ajout
          const addButtons = content.querySelectorAll('.add-btn, [class*="add-btn"]');
          addButtons.forEach(button => {
            button.style.background = `linear-gradient(135deg, ${getComputedStyle(document.documentElement).getPropertyValue('--primary-color')}, ${getComputedStyle(document.documentElement).getPropertyValue('--primary-dark')})`;
            button.style.color = getComputedStyle(document.documentElement).getPropertyValue('--primary-contrast');
            button.style.boxShadow = getComputedStyle(document.documentElement).getPropertyValue('--shadow-sm');
            button.style.borderLeftColor = getComputedStyle(document.documentElement).getPropertyValue('--accent-color');
            
            // Appliquer les styles aux icônes
            const icons = button.querySelectorAll('mat-icon, .mat-icon, [class*="mat-icon"]');
            icons.forEach(icon => {
              icon.style.color = getComputedStyle(document.documentElement).getPropertyValue('--primary-contrast');
            });
          });
          
          // Appliquer les styles aux boutons d'ajout maintenant
          const addNowButtons = content.querySelectorAll('.add-now-btn, [class*="add-now-btn"]');
          addNowButtons.forEach(button => {
            button.style.backgroundColor = getComputedStyle(document.documentElement).getPropertyValue('--primary-color');
            button.style.color = getComputedStyle(document.documentElement).getPropertyValue('--primary-contrast');
          });
          
          // Appliquer les styles aux messages vides
          const emptyMessages = content.querySelectorAll('.empty-message, [class*="empty-message"]');
          emptyMessages.forEach(message => {
            message.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-muted');
            
            // Appliquer les styles aux icônes
            const icons = message.querySelectorAll('mat-icon, .mat-icon, [class*="mat-icon"]');
            icons.forEach(icon => {
              icon.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-muted');
            });
          });
          
          // Appliquer les styles aux boutons de voir plus
          const showMoreButtons = content.querySelectorAll('.show-more, [class*="show-more"]');
          showMoreButtons.forEach(showMore => {
            const buttons = showMore.querySelectorAll('button');
            buttons.forEach(button => {
              button.style.color = getComputedStyle(document.documentElement).getPropertyValue('--primary-color');
            });
          });
          
          // Appliquer les styles aux éléments de statistiques
          const statsGrids = content.querySelectorAll('.stats-grid, [class*="stats-grid"]');
          statsGrids.forEach(grid => {
            grid.style.backgroundColor = getComputedStyle(document.documentElement).getPropertyValue('--background-card');
            
            // Appliquer les styles aux éléments de statistiques
            const statItems = grid.querySelectorAll('.stat-item, [class*="stat-item"]');
            statItems.forEach(item => {
              item.style.backgroundColor = getComputedStyle(document.documentElement).getPropertyValue('--background-alt');
              item.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-primary');
              item.style.borderColor = getComputedStyle(document.documentElement).getPropertyValue('--border-color');
              item.style.boxShadow = getComputedStyle(document.documentElement).getPropertyValue('--shadow-sm');
              
              // Appliquer les styles aux titres
              const h4s = item.querySelectorAll('h4');
              h4s.forEach(h4 => {
                h4.style.color = getComputedStyle(document.documentElement).getPropertyValue('--primary-color');
              });
              
              // Appliquer les styles aux paragraphes
              const paragraphs = item.querySelectorAll('p');
              paragraphs.forEach(paragraph => {
                paragraph.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-secondary');
              });
              
              // Appliquer les styles aux icônes
              const statIcons = item.querySelectorAll('.stat-icon, [class*="stat-icon"]');
              statIcons.forEach(icon => {
                icon.style.backgroundColor = `rgba(${getComputedStyle(document.documentElement).getPropertyValue('--primary-color-rgb')}, 0.1)`;
                
                // Appliquer les styles aux icônes
                const icons = icon.querySelectorAll('mat-icon, .mat-icon, [class*="mat-icon"]');
                icons.forEach(icon => {
                  icon.style.color = getComputedStyle(document.documentElement).getPropertyValue('--primary-color');
                });
              });
            });
          });
        });
        
        // Appliquer les styles aux actions de carte
        const cardActions = card.querySelectorAll('mat-card-actions, .mat-card-actions, .mat-mdc-card-actions, [class*="mat-card-actions"], [class*="mat-mdc-card-actions"]');
        cardActions.forEach(actions => {
          actions.style.backgroundColor = getComputedStyle(document.documentElement).getPropertyValue('--background-card');
          actions.style.borderColor = getComputedStyle(document.documentElement).getPropertyValue('--border-color');
          
          // Appliquer les styles aux boutons
          const buttons = actions.querySelectorAll('button');
          buttons.forEach(button => {
            button.style.color = getComputedStyle(document.documentElement).getPropertyValue('--primary-color');
            
            // Appliquer les styles aux icônes
            const icons = button.querySelectorAll('mat-icon, .mat-icon, [class*="mat-icon"]');
            icons.forEach(icon => {
              icon.style.color = getComputedStyle(document.documentElement).getPropertyValue('--primary-color');
            });
          });
        });
        
        // Appliquer les styles aux pieds de carte
        const cardFooters = card.querySelectorAll('mat-card-footer, .mat-card-footer, .mat-mdc-card-footer, [class*="mat-card-footer"], [class*="mat-mdc-card-footer"]');
        cardFooters.forEach(footer => {
          footer.style.backgroundColor = getComputedStyle(document.documentElement).getPropertyValue('--background-card');
          footer.style.borderColor = getComputedStyle(document.documentElement).getPropertyValue('--border-color');
        });
      });
    });

    // Appliquer le mode sombre aux cartes bio
    const bioCards = document.querySelectorAll('.bio-card, [class*="bio-card"]');
    bioCards.forEach(card => {
      card.style.borderLeftColor = getComputedStyle(document.documentElement).getPropertyValue('--accent-color');
      
      // Appliquer les styles aux contenus bio
      const bioContents = card.querySelectorAll('.bio-content, [class*="bio-content"]');
      bioContents.forEach(content => {
        // Appliquer les styles aux paragraphes
        const paragraphs = content.querySelectorAll('p');
        paragraphs.forEach(paragraph => {
          paragraph.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-secondary');
        });
        
        // Appliquer les styles aux bios vides
        const emptyBios = content.querySelectorAll('.empty-bio, [class*="empty-bio"]');
        emptyBios.forEach(bio => {
          bio.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-muted');
        });
        
        // Appliquer les styles aux boutons d'édition de bio
        const editBioButtons = content.querySelectorAll('.edit-bio-btn, [class*="edit-bio-btn"]');
        editBioButtons.forEach(button => {
          button.style.color = getComputedStyle(document.documentElement).getPropertyValue('--accent-color');
          
          // Appliquer les styles aux icônes
          const icons = button.querySelectorAll('mat-icon, .mat-icon, [class*="mat-icon"]');
          icons.forEach(icon => {
            icon.style.color = getComputedStyle(document.documentElement).getPropertyValue('--accent-color');
          });
        });
      });
    });

    // Appliquer le mode sombre aux cartes CV
    const cvCards = document.querySelectorAll('.cv-card, [class*="cv-card"]');
    cvCards.forEach(card => {
      // Appliquer les styles aux cartes CV avec CV
      if (card.classList.contains('has-cv')) {
        card.style.borderLeftColor = getComputedStyle(document.documentElement).getPropertyValue('--primary-color');
      }
      
      // Appliquer les styles aux contenus CV
      const cvContents = card.querySelectorAll('.cv-content, [class*="cv-content"]');
      cvContents.forEach(content => {
        // Appliquer les styles aux aperçus CV
        const cvPreviews = content.querySelectorAll('.cv-preview, [class*="cv-preview"]');
        cvPreviews.forEach(preview => {
          // Appliquer les styles aux icônes CV
          const cvIcons = preview.querySelectorAll('.cv-icon, [class*="cv-icon"]');
          cvIcons.forEach(icon => {
            icon.style.backgroundColor = `rgba(${getComputedStyle(document.documentElement).getPropertyValue('--primary-color-rgb')}, 0.1)`;
            
            // Appliquer les styles aux icônes
            const icons = icon.querySelectorAll('mat-icon, .mat-icon, [class*="mat-icon"]');
            icons.forEach(icon => {
              icon.style.color = getComputedStyle(document.documentElement).getPropertyValue('--primary-color');
            });
          });
          
          // Appliquer les styles aux détails CV
          const cvDetails = preview.querySelectorAll('.cv-details, [class*="cv-details"]');
          cvDetails.forEach(details => {
            // Appliquer les styles aux noms CV
            const cvNames = details.querySelectorAll('.cv-name, [class*="cv-name"]');
            cvNames.forEach(name => {
              name.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-primary');
            });
            
            // Appliquer les styles aux dates CV
            const cvDates = details.querySelectorAll('.cv-date, [class*="cv-date"]');
            cvDates.forEach(date => {
              date.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-secondary');
            });
          });
        });
        
        // Appliquer les styles aux actions CV
        const cvActions = content.querySelectorAll('.cv-actions, [class*="cv-actions"]');
        cvActions.forEach(actions => {
          // Appliquer les styles aux boutons de visualisation CV
          const viewCvButtons = actions.querySelectorAll('.view-cv-btn, [class*="view-cv-btn"]');
          viewCvButtons.forEach(button => {
            button.style.color = getComputedStyle(document.documentElement).getPropertyValue('--primary-color');
            button.style.borderColor = getComputedStyle(document.documentElement).getPropertyValue('--primary-color');
          });
          
          // Appliquer les styles aux boutons de suppression CV
          const deleteCvButtons = actions.querySelectorAll('.delete-cv-btn, [class*="delete-cv-btn"]');
          deleteCvButtons.forEach(button => {
            button.style.color = getComputedStyle(document.documentElement).getPropertyValue('--error-color');
          });
        });
      });
      
      // Appliquer les styles aux contenus sans CV
      const noCvContents = card.querySelectorAll('.no-cv-content, [class*="no-cv-content"]');
      noCvContents.forEach(content => {
        // Appliquer les styles aux icônes sans CV
        const noCvIcons = content.querySelectorAll('.no-cv-icon, [class*="no-cv-icon"]');
        noCvIcons.forEach(icon => {
          icon.style.backgroundColor = getComputedStyle(document.documentElement).getPropertyValue('--background-alt');
          
          // Appliquer les styles aux icônes
          const icons = icon.querySelectorAll('mat-icon, .mat-icon, [class*="mat-icon"]');
          icons.forEach(icon => {
            icon.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-muted');
          });
        });
        
        // Appliquer les styles aux messages sans CV
        const noCvMessages = content.querySelectorAll('.no-cv-message, [class*="no-cv-message"]');
        noCvMessages.forEach(message => {
          // Appliquer les styles aux paragraphes
          const paragraphs = message.querySelectorAll('p');
          paragraphs.forEach(paragraph => {
            paragraph.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-secondary');
          });
          
          // Appliquer les styles aux boutons d'ajout CV
          const addCvButtons = message.querySelectorAll('.add-cv-btn, [class*="add-cv-btn"]');
          addCvButtons.forEach(button => {
            button.style.backgroundColor = getComputedStyle(document.documentElement).getPropertyValue('--primary-color');
            button.style.color = getComputedStyle(document.documentElement).getPropertyValue('--primary-contrast');
          });
        });
      });
    });

    // Appliquer le mode sombre aux cartes de statistiques
    const statsCards = document.querySelectorAll('.stats-card, [class*="stats-card"]');
    statsCards.forEach(card => {
      // Appliquer les styles aux grilles de statistiques
      const statsGrids = card.querySelectorAll('.stats-grid, [class*="stats-grid"]');
      statsGrids.forEach(grid => {
        // Appliquer les styles aux éléments de statistiques
        const statItems = grid.querySelectorAll('.stat-item, [class*="stat-item"]');
        statItems.forEach(item => {
          item.style.backgroundColor = getComputedStyle(document.documentElement).getPropertyValue('--background-alt');
          item.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-primary');
          item.style.borderColor = getComputedStyle(document.documentElement).getPropertyValue('--border-color');
          
          // Appliquer les styles aux titres
          const h4s = item.querySelectorAll('h4');
          h4s.forEach(h4 => {
            h4.style.color = getComputedStyle(document.documentElement).getPropertyValue('--primary-color');
          });
          
          // Appliquer les styles aux paragraphes
          const paragraphs = item.querySelectorAll('p');
          paragraphs.forEach(paragraph => {
            paragraph.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-secondary');
          });
          
          // Appliquer les styles aux icônes
          const statIcons = item.querySelectorAll('.stat-icon, [class*="stat-icon"]');
          statIcons.forEach(icon => {
            icon.style.backgroundColor = `rgba(${getComputedStyle(document.documentElement).getPropertyValue('--primary-color-rgb')}, 0.1)`;
            
            // Appliquer les styles aux icônes
            const icons = icon.querySelectorAll('mat-icon, .mat-icon, [class*="mat-icon"]');
            icons.forEach(icon => {
              icon.style.color = getComputedStyle(document.documentElement).getPropertyValue('--primary-color');
            });
          });
        });
      });
    });

    // Appliquer le mode sombre aux cartes mises en évidence
    const highlightCards = document.querySelectorAll('.highlight-card, [class*="highlight-card"]');
    highlightCards.forEach(card => {
      card.style.backgroundColor = `rgba(${getComputedStyle(document.documentElement).getPropertyValue('--primary-color-rgb')}, 0.05)`;
      card.style.borderColor = getComputedStyle(document.documentElement).getPropertyValue('--primary-color');
      
      // Appliquer les styles aux titres
      const cardTitles = card.querySelectorAll('mat-card-title, .mat-card-title, .mat-mdc-card-title, [class*="mat-card-title"], [class*="mat-mdc-card-title"]');
      cardTitles.forEach(title => {
        title.style.color = getComputedStyle(document.documentElement).getPropertyValue('--primary-color');
      });
      
      // Appliquer les styles aux contenus
      const cardContents = card.querySelectorAll('mat-card-content, .mat-card-content, .mat-mdc-card-content, [class*="mat-card-content"], [class*="mat-mdc-card-content"]');
      cardContents.forEach(content => {
        content.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-primary');
      });
    });
  }

  // Observer les changements dans le DOM
  function observeDOMChanges() {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        // Traiter les nouveaux nœuds ajoutés
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              // Vérifier si le nœud est une carte d'information ou contient des cartes d'information
              if (node.classList && (
                  node.classList.contains('info-card') ||
                  node.classList.contains('mat-card') ||
                  node.classList.contains('mat-mdc-card')
                )) {
                applyDarkThemeToInfoCards();
              } else {
                // Vérifier si le nœud contient des cartes d'information
                const infoCards = node.querySelectorAll('.info-card, mat-card, .mat-card, .mat-mdc-card');
                if (infoCards.length > 0) {
                  applyDarkThemeToInfoCards();
                }
              }
            }
          });
        }
        
        // Traiter les attributs modifiés
        if (mutation.type === 'attributes') {
          if (mutation.attributeName === 'style' || mutation.attributeName === 'class') {
            // Vérifier si le nœud est une carte d'information ou contient des cartes d'information
            if (mutation.target.classList && (
                mutation.target.classList.contains('info-card') ||
                mutation.target.classList.contains('mat-card') ||
                mutation.target.classList.contains('mat-mdc-card')
              )) {
              applyDarkThemeToInfoCards();
            }
          }
        }
      });
    });

    // Observer les changements dans le document
    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['style', 'class']
    });
  }

  // Fonction principale
  function init() {
    // Appliquer le mode sombre aux cartes d'information
    applyDarkThemeToInfoCards();
    
    // Observer les changements dans le DOM
    observeDOMChanges();
    
    // Réappliquer le mode sombre toutes les 2 secondes pour les éléments qui pourraient être générés dynamiquement
    setInterval(() => {
      if (isDarkThemeActive()) {
        applyDarkThemeToInfoCards();
      }
    }, 2000);
  }

  // Attendre que le DOM soit chargé
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
  } else {
    init();
  }
})();
