/**
 * Script pour forcer l'application du mode sombre à la page de profil
 * Ce script est chargé après le chargement de la page et applique le mode sombre
 * aux éléments spécifiques de la page de profil qui ne sont pas correctement stylisés par les CSS
 */

(function() {
  // Vérifier si le thème sombre est actif
  function isDarkThemeActive() {
    return document.documentElement.classList.contains('dark-theme') || 
           document.body.classList.contains('dark-theme') ||
           localStorage.getItem('theme') === 'dark';
  }

  // Vérifier si la page actuelle est la page de profil
  function isProfilePage() {
    return window.location.href.includes('/profile') || 
           window.location.href.includes('/recruiter-profile');
  }

  // Appliquer le mode sombre aux éléments spécifiques de la page de profil
  function applyDarkThemeToProfilePage() {
    if (!isDarkThemeActive() || !isProfilePage()) {
      return;
    }

    console.log('Applying dark theme to profile page');

    // Appliquer le mode sombre au conteneur principal
    const profileContainer = document.querySelector('.profile-container');
    if (profileContainer) {
      profileContainer.style.backgroundColor = getComputedStyle(document.documentElement).getPropertyValue('--background-color');
      profileContainer.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-primary');
    }

    // Appliquer le mode sombre au header
    const profileHeader = document.querySelector('.profile-header');
    if (profileHeader) {
      profileHeader.style.background = 'linear-gradient(135deg, #0a192f 0%, #112a5e 100%)';
      profileHeader.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-primary');
      profileHeader.style.borderColor = getComputedStyle(document.documentElement).getPropertyValue('--border-color');
    }

    // Appliquer le mode sombre à l'arrière-plan du header
    const headerBackground = document.querySelector('.header-background');
    if (headerBackground) {
      headerBackground.style.background = 'linear-gradient(135deg, #0a192f 0%, #112a5e 100%)';
    }

    // Appliquer le mode sombre aux onglets du profil
    const profileTabs = document.querySelector('.profile-tabs');
    if (profileTabs) {
      profileTabs.style.backgroundColor = getComputedStyle(document.documentElement).getPropertyValue('--background-card');
      profileTabs.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-primary');
      profileTabs.style.borderColor = getComputedStyle(document.documentElement).getPropertyValue('--border-color');
    }

    // Appliquer le mode sombre aux éléments de contact
    const contactItems = document.querySelectorAll('.contact-item');
    contactItems.forEach(item => {
      item.style.backgroundColor = getComputedStyle(document.documentElement).getPropertyValue('--background-alt');
      item.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-secondary');
      item.style.boxShadow = getComputedStyle(document.documentElement).getPropertyValue('--shadow-sm');
    });

    // Appliquer le mode sombre à la section de contenu
    const profileContent = document.querySelector('.profile-content');
    if (profileContent) {
      profileContent.style.backgroundColor = getComputedStyle(document.documentElement).getPropertyValue('--background-color');
      profileContent.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-primary');
    }

    // Appliquer le mode sombre aux colonnes
    const columns = document.querySelectorAll('.left-column, .right-column');
    columns.forEach(column => {
      column.style.backgroundColor = getComputedStyle(document.documentElement).getPropertyValue('--background-color');
      column.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-primary');
    });

    // Appliquer le mode sombre aux cartes d'information
    const infoCards = document.querySelectorAll('.info-card');
    infoCards.forEach(card => {
      card.style.backgroundColor = getComputedStyle(document.documentElement).getPropertyValue('--background-card');
      card.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-primary');
      card.style.borderColor = getComputedStyle(document.documentElement).getPropertyValue('--border-color');
      card.style.boxShadow = getComputedStyle(document.documentElement).getPropertyValue('--shadow-sm');
    });

    // Appliquer le mode sombre aux en-têtes de carte
    const cardHeaders = document.querySelectorAll('mat-card-header');
    cardHeaders.forEach(header => {
      header.style.backgroundColor = getComputedStyle(document.documentElement).getPropertyValue('--background-alt');
      header.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-primary');
      header.style.borderColor = getComputedStyle(document.documentElement).getPropertyValue('--border-color');
    });

    // Appliquer le mode sombre aux titres de carte
    const cardTitles = document.querySelectorAll('mat-card-title');
    cardTitles.forEach(title => {
      title.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-primary');
    });

    // Appliquer le mode sombre aux contenus de carte
    const cardContents = document.querySelectorAll('mat-card-content');
    cardContents.forEach(content => {
      content.style.backgroundColor = getComputedStyle(document.documentElement).getPropertyValue('--background-card');
      content.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-secondary');
    });

    // Appliquer le mode sombre aux blocs d'éléments
    const itemBlocks = document.querySelectorAll('.item-block');
    itemBlocks.forEach(block => {
      block.style.backgroundColor = getComputedStyle(document.documentElement).getPropertyValue('--background-alt');
      block.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-primary');
      block.style.borderColor = getComputedStyle(document.documentElement).getPropertyValue('--border-color');
    });

    // Appliquer le mode sombre aux titres des blocs d'éléments
    const itemTitles = document.querySelectorAll('.item-title h4');
    itemTitles.forEach(title => {
      title.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-primary');
    });

    // Appliquer le mode sombre aux sous-titres des blocs d'éléments
    const itemSubtitles = document.querySelectorAll('.item-title span, .item-subtitle');
    itemSubtitles.forEach(subtitle => {
      subtitle.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-secondary');
    });

    // Appliquer le mode sombre aux contenus des blocs d'éléments
    const itemContents = document.querySelectorAll('.item-content');
    itemContents.forEach(content => {
      content.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-secondary');
    });

    // Appliquer le mode sombre aux pieds des blocs d'éléments
    const itemFooters = document.querySelectorAll('.item-footer');
    itemFooters.forEach(footer => {
      footer.style.borderColor = getComputedStyle(document.documentElement).getPropertyValue('--border-color');
    });

    // Appliquer le mode sombre aux dates des blocs d'éléments
    const itemDates = document.querySelectorAll('.item-date');
    itemDates.forEach(date => {
      date.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-muted');
    });

    // Appliquer le mode sombre aux boutons d'ajout
    const addButtons = document.querySelectorAll('.add-btn');
    addButtons.forEach(button => {
      button.style.background = `linear-gradient(135deg, ${getComputedStyle(document.documentElement).getPropertyValue('--primary-color')}, ${getComputedStyle(document.documentElement).getPropertyValue('--primary-dark')})`;
      button.style.color = getComputedStyle(document.documentElement).getPropertyValue('--primary-contrast');
      button.style.borderLeftColor = getComputedStyle(document.documentElement).getPropertyValue('--accent-color');
    });

    // Appliquer le mode sombre aux boutons d'ajout maintenant
    const addNowButtons = document.querySelectorAll('.add-now-btn');
    addNowButtons.forEach(button => {
      button.style.backgroundColor = getComputedStyle(document.documentElement).getPropertyValue('--primary-color');
      button.style.color = getComputedStyle(document.documentElement).getPropertyValue('--primary-contrast');
    });

    // Appliquer le mode sombre aux messages vides
    const emptyMessages = document.querySelectorAll('.empty-message');
    emptyMessages.forEach(message => {
      message.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-muted');
    });

    // Appliquer le mode sombre aux cartes mises en évidence
    const highlightCards = document.querySelectorAll('.highlight-card');
    highlightCards.forEach(card => {
      card.style.backgroundColor = `rgba(${getComputedStyle(document.documentElement).getPropertyValue('--primary-color-rgb')}, 0.05)`;
      card.style.borderColor = getComputedStyle(document.documentElement).getPropertyValue('--primary-color');
    });

    // Appliquer le mode sombre aux cartes bio
    const bioCards = document.querySelectorAll('.bio-card');
    bioCards.forEach(card => {
      card.style.borderLeftColor = getComputedStyle(document.documentElement).getPropertyValue('--accent-color');
    });

    // Appliquer le mode sombre aux contenus bio
    const bioContents = document.querySelectorAll('.bio-content p');
    bioContents.forEach(content => {
      content.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-secondary');
    });

    // Appliquer le mode sombre aux bios vides
    const emptyBios = document.querySelectorAll('.empty-bio');
    emptyBios.forEach(bio => {
      bio.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-muted');
    });

    // Appliquer le mode sombre aux boutons d'édition de bio
    const editBioButtons = document.querySelectorAll('.edit-bio-btn');
    editBioButtons.forEach(button => {
      button.style.color = getComputedStyle(document.documentElement).getPropertyValue('--accent-color');
    });

    // Appliquer le mode sombre aux cartes CV
    const cvCards = document.querySelectorAll('.cv-card.has-cv');
    cvCards.forEach(card => {
      card.style.borderLeftColor = getComputedStyle(document.documentElement).getPropertyValue('--primary-color');
    });

    // Appliquer le mode sombre aux icônes CV
    const cvIcons = document.querySelectorAll('.cv-icon');
    cvIcons.forEach(icon => {
      icon.style.backgroundColor = `rgba(${getComputedStyle(document.documentElement).getPropertyValue('--primary-color-rgb')}, 0.1)`;
    });

    // Appliquer le mode sombre aux noms CV
    const cvNames = document.querySelectorAll('.cv-name');
    cvNames.forEach(name => {
      name.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-primary');
    });

    // Appliquer le mode sombre aux dates CV
    const cvDates = document.querySelectorAll('.cv-date');
    cvDates.forEach(date => {
      date.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-secondary');
    });

    // Appliquer le mode sombre aux boutons de visualisation CV
    const viewCvButtons = document.querySelectorAll('.view-cv-btn');
    viewCvButtons.forEach(button => {
      button.style.color = getComputedStyle(document.documentElement).getPropertyValue('--primary-color');
      button.style.borderColor = getComputedStyle(document.documentElement).getPropertyValue('--primary-color');
    });

    // Appliquer le mode sombre aux boutons de suppression CV
    const deleteCvButtons = document.querySelectorAll('.delete-cv-btn');
    deleteCvButtons.forEach(button => {
      button.style.color = getComputedStyle(document.documentElement).getPropertyValue('--error-color');
    });

    // Appliquer le mode sombre aux contenus sans CV
    const noCvContents = document.querySelectorAll('.no-cv-content');
    noCvContents.forEach(content => {
      const icon = content.querySelector('.no-cv-icon');
      if (icon) {
        icon.style.backgroundColor = getComputedStyle(document.documentElement).getPropertyValue('--background-alt');
      }

      const iconElement = content.querySelector('.no-cv-icon mat-icon');
      if (iconElement) {
        iconElement.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-muted');
      }

      const message = content.querySelector('.no-cv-message p');
      if (message) {
        message.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-secondary');
      }

      const addButton = content.querySelector('.add-cv-btn');
      if (addButton) {
        addButton.style.backgroundColor = getComputedStyle(document.documentElement).getPropertyValue('--primary-color');
        addButton.style.color = getComputedStyle(document.documentElement).getPropertyValue('--primary-contrast');
      }
    });

    // Appliquer le mode sombre aux grilles
    const gridLists = document.querySelectorAll('mat-grid-list');
    gridLists.forEach(grid => {
      grid.style.backgroundColor = getComputedStyle(document.documentElement).getPropertyValue('--background-color');
    });

    // Appliquer le mode sombre aux tuiles de grille
    const gridTiles = document.querySelectorAll('mat-grid-tile');
    gridTiles.forEach(tile => {
      tile.style.backgroundColor = getComputedStyle(document.documentElement).getPropertyValue('--background-color');
    });

    // Appliquer le mode sombre aux lignes d'information
    const infoRows = document.querySelectorAll('.info-row');
    infoRows.forEach(row => {
      const spans = row.querySelectorAll('span');
      spans.forEach(span => {
        span.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-secondary');
      });

      const buttons = row.querySelectorAll('button');
      buttons.forEach(button => {
        button.style.color = getComputedStyle(document.documentElement).getPropertyValue('--primary-color');
      });
    });

    // Appliquer le mode sombre aux éléments de formulaire
    const formFields = document.querySelectorAll('mat-form-field');
    formFields.forEach(field => {
      const label = field.querySelector('.mat-form-field-label');
      if (label) {
        label.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-secondary');
      }

      const underline = field.querySelector('.mat-form-field-underline');
      if (underline) {
        underline.style.backgroundColor = getComputedStyle(document.documentElement).getPropertyValue('--border-color');
      }

      const ripple = field.querySelector('.mat-form-field-ripple');
      if (ripple) {
        ripple.style.backgroundColor = getComputedStyle(document.documentElement).getPropertyValue('--primary-color');
      }

      const hint = field.querySelector('.mat-form-field-hint');
      if (hint) {
        hint.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-muted');
      }

      const error = field.querySelector('.mat-error');
      if (error) {
        error.style.color = getComputedStyle(document.documentElement).getPropertyValue('--error-color');
      }
    });

    // Appliquer le mode sombre aux champs de saisie
    const inputs = document.querySelectorAll('input, textarea, select');
    inputs.forEach(input => {
      input.style.backgroundColor = getComputedStyle(document.documentElement).getPropertyValue('--background-alt');
      input.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-primary');
      input.style.borderColor = getComputedStyle(document.documentElement).getPropertyValue('--border-color');
    });
  }

  // Observer les changements dans le DOM
  function observeDOMChanges() {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        // Traiter les nouveaux nœuds ajoutés
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              // Vérifier si le nœud est un élément de la page de profil
              if (isProfilePage()) {
                applyDarkThemeToProfilePage();
              }
            }
          });
        }
      });
    });

    // Observer les changements dans le document
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  }

  // Fonction principale
  function init() {
    // Appliquer le mode sombre à la page de profil
    applyDarkThemeToProfilePage();
    
    // Observer les changements dans le DOM
    observeDOMChanges();
    
    // Réappliquer le mode sombre toutes les 2 secondes pour les éléments qui pourraient être générés dynamiquement
    setInterval(() => {
      if (isDarkThemeActive() && isProfilePage()) {
        applyDarkThemeToProfilePage();
      }
    }, 2000);
  }

  // Attendre que le DOM soit chargé
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
  } else {
    init();
  }
})();
