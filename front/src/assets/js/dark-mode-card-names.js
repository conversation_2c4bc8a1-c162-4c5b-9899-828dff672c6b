/**
 * Script pour forcer l'application du mode sombre aux noms dans les cartes
 * Ce script est chargé après le chargement de la page et applique le mode sombre
 * aux noms dans les cartes de certification, expérience, éducation, compétence et lien
 */

(function() {
  // Vérifier si le thème sombre est actif
  function isDarkThemeActive() {
    return document.documentElement.classList.contains('dark-theme') || 
           document.body.classList.contains('dark-theme') ||
           localStorage.getItem('theme') === 'dark';
  }

  // Appliquer le mode sombre aux noms dans les cartes
  function applyDarkThemeToCardNames() {
    if (!isDarkThemeActive()) {
      return;
    }

    console.log('Applying dark theme to card names');

    // Sélecteurs pour les noms dans les cartes
    const nameSelectors = [
      '.item-title h4',
      '.item-title .title',
      '.item-title .name',
      '.item-title .heading',
      '.item-title .label',
      '.item-title .text',
      '.item-title span',
      '.item-title strong',
      '.item-title b',
      '.item-title em',
      '.item-title i',
      '.item-title a',
      '.item-title .link',
      '.item-name',
      '.item-title-text',
      '.item-heading',
      '.item-label',
      '.item-text',
      '.title-text',
      '.name-text',
      '.heading-text',
      '.label-text',
      '.text-primary',
      '.text-title',
      '.text-name',
      '.text-heading',
      '.text-label',
      '[class*="item-title"] h4',
      '[class*="item-title"] .title',
      '[class*="item-title"] .name',
      '[class*="item-title"] .heading',
      '[class*="item-title"] .label',
      '[class*="item-title"] .text',
      '[class*="item-title"] span',
      '[class*="item-title"] strong',
      '[class*="item-title"] b',
      '[class*="item-title"] em',
      '[class*="item-title"] i',
      '[class*="item-title"] a',
      '[class*="item-title"] .link',
      '[class*="item-name"]',
      '[class*="item-title-text"]',
      '[class*="item-heading"]',
      '[class*="item-label"]',
      '[class*="item-text"]',
      '[class*="title-text"]',
      '[class*="name-text"]',
      '[class*="heading-text"]',
      '[class*="label-text"]',
      '[class*="text-primary"]',
      '[class*="text-title"]',
      '[class*="text-name"]',
      '[class*="text-heading"]',
      '[class*="text-label"]'
    ];

    // Appliquer le mode sombre à tous les noms dans les cartes
    nameSelectors.forEach(selector => {
      const names = document.querySelectorAll(selector);
      
      names.forEach(name => {
        // Appliquer les styles de base
        name.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-primary');
        
        // Appliquer les styles aux liens
        const links = name.querySelectorAll('a, .link, [class*="link"]');
        links.forEach(link => {
          link.style.color = getComputedStyle(document.documentElement).getPropertyValue('--primary-color');
        });
      });
    });

    // Types de cartes spécifiques
    const cardTypes = [
      'certification',
      'experience',
      'education',
      'skill',
      'link'
    ];

    // Appliquer le mode sombre aux noms dans les cartes spécifiques
    cardTypes.forEach(cardType => {
      // Sélecteurs pour les cartes spécifiques
      const cardSelectors = [
        `.${cardType}-card`,
        `[class*="${cardType}-card"]`,
        `[class*="${cardType}"]`
      ];

      // Sélecteurs pour les noms spécifiques
      const specificNameSelectors = [
        `.${cardType}-name`,
        `.${cardType}-title`,
        `.${cardType}-heading`,
        `.${cardType}-label`,
        `.${cardType}-text`,
        `[class*="${cardType}-name"]`,
        `[class*="${cardType}-title"]`,
        `[class*="${cardType}-heading"]`,
        `[class*="${cardType}-label"]`,
        `[class*="${cardType}-text"]`
      ];

      // Appliquer le mode sombre aux noms spécifiques
      specificNameSelectors.forEach(selector => {
        const names = document.querySelectorAll(selector);
        
        names.forEach(name => {
          // Appliquer les styles de base
          name.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-primary');
          
          // Appliquer les styles aux liens
          const links = name.querySelectorAll('a, .link, [class*="link"]');
          links.forEach(link => {
            link.style.color = getComputedStyle(document.documentElement).getPropertyValue('--primary-color');
          });
        });
      });

      // Appliquer le mode sombre aux noms dans les cartes spécifiques
      cardSelectors.forEach(cardSelector => {
        const cards = document.querySelectorAll(cardSelector);
        
        cards.forEach(card => {
          // Appliquer le mode sombre aux noms dans les cartes spécifiques
          nameSelectors.forEach(nameSelector => {
            const names = card.querySelectorAll(nameSelector);
            
            names.forEach(name => {
              // Appliquer les styles de base
              name.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-primary');
              
              // Appliquer les styles aux liens
              const links = name.querySelectorAll('a, .link, [class*="link"]');
              links.forEach(link => {
                link.style.color = getComputedStyle(document.documentElement).getPropertyValue('--primary-color');
              });
            });
          });
        });
      });
    });

    // Cas spécifiques pour les cartes d'expérience
    const experienceSpecificSelectors = [
      '.company-name',
      '.company-title',
      '.company-heading',
      '.company-label',
      '.company-text',
      '.job-name',
      '.job-title',
      '.job-heading',
      '.job-label',
      '.job-text',
      '.position-name',
      '.position-title',
      '.position-heading',
      '.position-label',
      '.position-text',
      '[class*="company-name"]',
      '[class*="company-title"]',
      '[class*="company-heading"]',
      '[class*="company-label"]',
      '[class*="company-text"]',
      '[class*="job-name"]',
      '[class*="job-title"]',
      '[class*="job-heading"]',
      '[class*="job-label"]',
      '[class*="job-text"]',
      '[class*="position-name"]',
      '[class*="position-title"]',
      '[class*="position-heading"]',
      '[class*="position-label"]',
      '[class*="position-text"]'
    ];

    // Appliquer le mode sombre aux noms spécifiques pour les cartes d'expérience
    experienceSpecificSelectors.forEach(selector => {
      const names = document.querySelectorAll(selector);
      
      names.forEach(name => {
        // Appliquer les styles de base
        name.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-primary');
        
        // Appliquer les styles aux liens
        const links = name.querySelectorAll('a, .link, [class*="link"]');
        links.forEach(link => {
          link.style.color = getComputedStyle(document.documentElement).getPropertyValue('--primary-color');
        });
      });
    });

    // Cas spécifiques pour les cartes d'éducation
    const educationSpecificSelectors = [
      '.school-name',
      '.school-title',
      '.school-heading',
      '.school-label',
      '.school-text',
      '.university-name',
      '.university-title',
      '.university-heading',
      '.university-label',
      '.university-text',
      '.degree-name',
      '.degree-title',
      '.degree-heading',
      '.degree-label',
      '.degree-text',
      '[class*="school-name"]',
      '[class*="school-title"]',
      '[class*="school-heading"]',
      '[class*="school-label"]',
      '[class*="school-text"]',
      '[class*="university-name"]',
      '[class*="university-title"]',
      '[class*="university-heading"]',
      '[class*="university-label"]',
      '[class*="university-text"]',
      '[class*="degree-name"]',
      '[class*="degree-title"]',
      '[class*="degree-heading"]',
      '[class*="degree-label"]',
      '[class*="degree-text"]'
    ];

    // Appliquer le mode sombre aux noms spécifiques pour les cartes d'éducation
    educationSpecificSelectors.forEach(selector => {
      const names = document.querySelectorAll(selector);
      
      names.forEach(name => {
        // Appliquer les styles de base
        name.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-primary');
        
        // Appliquer les styles aux liens
        const links = name.querySelectorAll('a, .link, [class*="link"]');
        links.forEach(link => {
          link.style.color = getComputedStyle(document.documentElement).getPropertyValue('--primary-color');
        });
      });
    });

    // Cas spécifiques pour les cartes de lien
    const linkSpecificSelectors = [
      '.website-name',
      '.website-title',
      '.website-heading',
      '.website-label',
      '.website-text',
      '.url-name',
      '.url-title',
      '.url-heading',
      '.url-label',
      '.url-text',
      '[class*="website-name"]',
      '[class*="website-title"]',
      '[class*="website-heading"]',
      '[class*="website-label"]',
      '[class*="website-text"]',
      '[class*="url-name"]',
      '[class*="url-title"]',
      '[class*="url-heading"]',
      '[class*="url-label"]',
      '[class*="url-text"]'
    ];

    // Appliquer le mode sombre aux noms spécifiques pour les cartes de lien
    linkSpecificSelectors.forEach(selector => {
      const names = document.querySelectorAll(selector);
      
      names.forEach(name => {
        // Appliquer les styles de base
        name.style.color = getComputedStyle(document.documentElement).getPropertyValue('--text-primary');
        
        // Appliquer les styles aux liens
        const links = name.querySelectorAll('a, .link, [class*="link"]');
        links.forEach(link => {
          link.style.color = getComputedStyle(document.documentElement).getPropertyValue('--primary-color');
        });
      });
    });
  }

  // Observer les changements dans le DOM
  function observeDOMChanges() {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        // Traiter les nouveaux nœuds ajoutés
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              // Vérifier si le nœud est un nom de carte ou contient des noms de carte
              if (node.classList && (
                  node.classList.contains('item-title') ||
                  node.classList.contains('item-name') ||
                  node.classList.contains('item-title-text') ||
                  node.classList.contains('item-heading') ||
                  node.classList.contains('item-label') ||
                  node.classList.contains('item-text') ||
                  node.classList.contains('title-text') ||
                  node.classList.contains('name-text') ||
                  node.classList.contains('heading-text') ||
                  node.classList.contains('label-text') ||
                  node.classList.contains('text-primary') ||
                  node.classList.contains('text-title') ||
                  node.classList.contains('text-name') ||
                  node.classList.contains('text-heading') ||
                  node.classList.contains('text-label')
                )) {
                applyDarkThemeToCardNames();
              } else {
                // Vérifier si le nœud contient des noms de carte
                const names = node.querySelectorAll('.item-title, .item-name, .item-title-text, .item-heading, .item-label, .item-text, .title-text, .name-text, .heading-text, .label-text, .text-primary, .text-title, .text-name, .text-heading, .text-label');
                if (names.length > 0) {
                  applyDarkThemeToCardNames();
                }
              }
            }
          });
        }
        
        // Traiter les attributs modifiés
        if (mutation.type === 'attributes') {
          if (mutation.attributeName === 'style' || mutation.attributeName === 'class') {
            // Vérifier si le nœud est un nom de carte ou contient des noms de carte
            if (mutation.target.classList && (
                mutation.target.classList.contains('item-title') ||
                mutation.target.classList.contains('item-name') ||
                mutation.target.classList.contains('item-title-text') ||
                mutation.target.classList.contains('item-heading') ||
                mutation.target.classList.contains('item-label') ||
                mutation.target.classList.contains('item-text') ||
                mutation.target.classList.contains('title-text') ||
                mutation.target.classList.contains('name-text') ||
                mutation.target.classList.contains('heading-text') ||
                mutation.target.classList.contains('label-text') ||
                mutation.target.classList.contains('text-primary') ||
                mutation.target.classList.contains('text-title') ||
                mutation.target.classList.contains('text-name') ||
                mutation.target.classList.contains('text-heading') ||
                mutation.target.classList.contains('text-label')
              )) {
              applyDarkThemeToCardNames();
            }
          }
        }
      });
    });

    // Observer les changements dans le document
    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['style', 'class']
    });
  }

  // Fonction principale
  function init() {
    // Appliquer le mode sombre aux noms dans les cartes
    applyDarkThemeToCardNames();
    
    // Observer les changements dans le DOM
    observeDOMChanges();
    
    // Réappliquer le mode sombre toutes les 2 secondes pour les éléments qui pourraient être générés dynamiquement
    setInterval(() => {
      if (isDarkThemeActive()) {
        applyDarkThemeToCardNames();
      }
    }, 2000);
  }

  // Attendre que le DOM soit chargé
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
  } else {
    init();
  }
})();
