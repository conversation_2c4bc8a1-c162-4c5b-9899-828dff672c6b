/**
 * Script pour forcer l'application du mode sombre aux éléments générés par des bibliothèques tierces
 * Ce script est chargé après le chargement de la page et observe les changements dans le DOM
 * pour appliquer le thème sombre aux éléments générés dynamiquement
 */

(function() {
  // Vérifier si le thème sombre est actif
  function isDarkThemeActive() {
    return document.documentElement.classList.contains('dark-theme') || 
           document.body.classList.contains('dark-theme') ||
           localStorage.getItem('theme') === 'dark';
  }

  // Appliquer le thème sombre aux iframes
  function applyDarkThemeToIframes() {
    if (!isDarkThemeActive()) {
      return;
    }

    const iframes = document.querySelectorAll('iframe');
    
    iframes.forEach(iframe => {
      // Ne pas appliquer aux iframes de vidéos ou d'images
      if (iframe.src.includes('youtube.com') || 
          iframe.src.includes('vimeo.com') || 
          iframe.src.includes('dailymotion.com') ||
          iframe.src.includes('.mp4') ||
          iframe.src.includes('.webm') ||
          iframe.src.includes('.ogg') ||
          iframe.src.includes('.jpg') ||
          iframe.src.includes('.jpeg') ||
          iframe.src.includes('.png') ||
          iframe.src.includes('.gif') ||
          iframe.src.includes('.svg')) {
        return;
      }

      try {
        // Essayer d'accéder au contenu de l'iframe
        const iframeDocument = iframe.contentDocument || iframe.contentWindow.document;
        
        // Ajouter la classe dark-theme au document de l'iframe
        iframeDocument.documentElement.classList.add('dark-theme');
        iframeDocument.body.classList.add('dark-theme');
        
        // Ajouter un attribut data-theme pour les sélecteurs CSS
        iframeDocument.documentElement.setAttribute('data-theme', 'dark');
        
        // Injecter les styles du thème sombre dans l'iframe
        const style = iframeDocument.createElement('style');
        style.textContent = `
          body {
            background-color: #121212 !important;
            color: #e2e8f0 !important;
          }
          
          * {
            color-scheme: dark !important;
          }
          
          a {
            color: #4299e1 !important;
          }
          
          input, select, textarea {
            background-color: #1e1e1e !important;
            color: #e2e8f0 !important;
            border-color: #333333 !important;
          }
          
          button {
            background-color: #1e1e1e !important;
            color: #e2e8f0 !important;
            border-color: #333333 !important;
          }
        `;
        
        iframeDocument.head.appendChild(style);
      } catch (e) {
        // Ignorer les erreurs d'accès aux iframes cross-origin
        console.log('Cannot access iframe content due to same-origin policy');
      }
    });
  }

  // Appliquer le thème sombre aux éléments générés par des bibliothèques tierces
  function applyDarkThemeToThirdPartyElements() {
    if (!isDarkThemeActive()) {
      return;
    }

    // Sélecteurs pour les éléments générés par des bibliothèques tierces
    const selectors = [
      // Éléments générés par Angular Material
      '.mat-mdc-dialog-container',
      '.cdk-overlay-container',
      '.mat-mdc-menu-panel',
      '.mat-mdc-select-panel',
      '.mat-mdc-autocomplete-panel',
      '.mat-mdc-tooltip',
      '.mat-mdc-snack-bar-container',
      '.mat-mdc-bottom-sheet-container',
      '.mat-mdc-datepicker-content',
      
      // Éléments générés par d'autres bibliothèques
      '.modal',
      '.modal-dialog',
      '.modal-content',
      '.modal-header',
      '.modal-body',
      '.modal-footer',
      '.popover',
      '.tooltip',
      '.dropdown-menu',
      '.toast',
      '.alert',
      '.card',
      '.panel',
      '.collapse',
      '.accordion',
      '.tab-content',
      '.nav-tabs',
      '.navbar',
      '.sidebar',
      '.offcanvas',
      '.carousel',
      '.pagination',
      '.breadcrumb',
      '.badge',
      '.progress',
      '.spinner',
      '.toast',
      '.alert',
      '.jumbotron',
      '.media',
      '.list-group',
      '.input-group',
      '.form-control',
      '.btn',
      '.btn-group',
      '.dropdown',
      '.dropdown-toggle',
      '.dropdown-menu',
      '.dropdown-item',
      '.nav',
      '.nav-item',
      '.nav-link',
      '.navbar',
      '.navbar-brand',
      '.navbar-nav',
      '.navbar-toggler',
      '.navbar-collapse',
      '.navbar-text',
      '.navbar-dark',
      '.navbar-light',
      '.navbar-expand',
      '.navbar-expand-sm',
      '.navbar-expand-md',
      '.navbar-expand-lg',
      '.navbar-expand-xl',
      '.navbar-expand-xxl'
    ];

    // Appliquer le thème sombre à chaque élément
    selectors.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      
      elements.forEach(element => {
        // Ajouter la classe dark-theme
        element.classList.add('dark-theme');
        
        // Ajouter un attribut data-theme pour les sélecteurs CSS
        element.setAttribute('data-theme', 'dark');
      });
    });
  }

  // Observer les changements dans le DOM
  function observeDOMChanges() {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        // Traiter les nouveaux nœuds ajoutés
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              // Vérifier si le nœud est un iframe
              if (node.tagName === 'IFRAME') {
                applyDarkThemeToIframes();
              }
              
              // Vérifier si le nœud est un élément généré par une bibliothèque tierce
              applyDarkThemeToThirdPartyElements();
            }
          });
        }
      });
    });

    // Observer les changements dans le document
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  }

  // Fonction principale
  function init() {
    // Appliquer le thème sombre aux iframes existants
    applyDarkThemeToIframes();
    
    // Appliquer le thème sombre aux éléments générés par des bibliothèques tierces
    applyDarkThemeToThirdPartyElements();
    
    // Observer les changements dans le DOM
    observeDOMChanges();
    
    // Réappliquer le thème sombre toutes les 2 secondes pour les éléments qui pourraient être générés dynamiquement
    setInterval(() => {
      if (isDarkThemeActive()) {
        applyDarkThemeToIframes();
        applyDarkThemeToThirdPartyElements();
      }
    }, 2000);
  }

  // Attendre que le DOM soit chargé
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
  } else {
    init();
  }
})();
