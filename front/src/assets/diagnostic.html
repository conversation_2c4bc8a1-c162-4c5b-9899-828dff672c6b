<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Diagnostic - Message de Profil</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #001040FF;
            text-align: center;
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h2 {
            color: #001040FF;
            margin-top: 0;
        }
        .btn {
            background: #001040FF;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #000830;
        }
        .status {
            font-family: monospace;
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            margin: 10px 0;
        }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .logs {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Diagnostic - Message de Profil</h1>
        
        <div class="section">
            <h2>📊 État Actuel</h2>
            <div class="status" id="currentStatus">Chargement...</div>
            <button class="btn" onclick="checkStatus()">🔄 Actualiser</button>
        </div>

        <div class="section">
            <h2>🧪 Tests</h2>
            <button class="btn" onclick="testSignup()">1️⃣ Tester Inscription</button>
            <button class="btn" onclick="testLogin()">2️⃣ Tester Connexion</button>
            <button class="btn" onclick="testFullFlow()">🚀 Test Complet</button>
            <button class="btn" onclick="resetAll()">🗑️ Reset</button>
        </div>

        <div class="section">
            <h2>📝 Logs</h2>
            <div class="logs" id="logs"></div>
            <button class="btn" onclick="clearLogs()">🧹 Effacer Logs</button>
        </div>

        <div class="section">
            <h2>🔧 Actions Manuelles</h2>
            <button class="btn" onclick="openApp()">🌐 Ouvrir App</button>
            <button class="btn" onclick="openSignup()">📝 Ouvrir Inscription</button>
            <button class="btn" onclick="openLogin()">🔑 Ouvrir Connexion</button>
        </div>
    </div>

    <script>
        let logs = [];

        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            logs.unshift(logEntry);
            
            if (logs.length > 50) {
                logs = logs.slice(0, 50);
            }
            
            updateLogsDisplay();
            console.log(logEntry);
        }

        function updateLogsDisplay() {
            const logsContainer = document.getElementById('logs');
            logsContainer.innerHTML = logs.map(log => `<div>${log}</div>`).join('');
        }

        function checkStatus() {
            addLog('🔍 Vérification de l\'état...');
            
            const signupCompleted = localStorage.getItem('signup_completed');
            const currentUrl = window.location.href;
            const userAgent = navigator.userAgent;
            
            const status = `
                <strong>LocalStorage:</strong><br>
                • signup_completed: <span class="${signupCompleted === 'true' ? 'success' : 'error'}">${signupCompleted || 'null'}</span><br><br>
                
                <strong>Environnement:</strong><br>
                • URL actuelle: ${currentUrl}<br>
                • User Agent: ${userAgent.substring(0, 100)}...<br><br>
                
                <strong>Timestamp:</strong> ${new Date().toISOString()}
            `;
            
            document.getElementById('currentStatus').innerHTML = status;
            addLog(`État vérifié - signup_completed: ${signupCompleted}`);
        }

        function testSignup() {
            addLog('🚀 Test: Simulation d\'inscription...');
            
            // Simuler l'inscription
            localStorage.setItem('signup_completed', 'true');
            addLog('✅ signup_completed défini à true');
            
            checkStatus();
            addLog('💡 Maintenant testez la connexion ou ouvrez l\'application');
        }

        function testLogin() {
            addLog('🔑 Test: Simulation de connexion...');
            
            const signupCompleted = localStorage.getItem('signup_completed');
            
            if (signupCompleted === 'true') {
                addLog('✅ Conditions remplies pour afficher le message');
                addLog('🎯 Le message devrait s\'afficher dans l\'application');
                addLog('💡 Ouvrez l\'application pour vérifier');
            } else {
                addLog('❌ signup_completed n\'est pas défini');
                addLog('💡 Exécutez d\'abord le test d\'inscription');
            }
            
            checkStatus();
        }

        function testFullFlow() {
            addLog('🚀 Test complet: Inscription → Connexion...');
            
            // Étape 1: Inscription
            addLog('📝 Étape 1: Simulation inscription...');
            localStorage.setItem('signup_completed', 'true');
            addLog('✅ Inscription simulée');
            
            // Étape 2: Vérification
            setTimeout(() => {
                addLog('🔑 Étape 2: Vérification conditions...');
                const signupCompleted = localStorage.getItem('signup_completed');
                
                if (signupCompleted === 'true') {
                    addLog('✅ Toutes les conditions sont remplies');
                    addLog('🎉 Le message devrait s\'afficher dans l\'application');
                    addLog('🌐 Cliquez sur "Ouvrir App" pour vérifier');
                } else {
                    addLog('❌ Erreur: conditions non remplies');
                }
                
                checkStatus();
            }, 1000);
        }

        function resetAll() {
            addLog('🗑️ Reset: Nettoyage de tous les états...');
            
            localStorage.removeItem('signup_completed');
            localStorage.removeItem('profile_message_shown');
            
            addLog('✅ LocalStorage nettoyé');
            checkStatus();
        }

        function clearLogs() {
            logs = [];
            updateLogsDisplay();
            addLog('🧹 Logs effacés');
        }

        function openApp() {
            addLog('🌐 Ouverture de l\'application...');
            window.open('http://localhost:4200', '_blank');
        }

        function openSignup() {
            addLog('📝 Ouverture de la page d\'inscription...');
            window.open('http://localhost:4200/signup', '_blank');
        }

        function openLogin() {
            addLog('🔑 Ouverture de la page de connexion...');
            window.open('http://localhost:8081/oauth2/authorization/google', '_blank');
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            addLog('🎯 Page de diagnostic chargée');
            checkStatus();
            
            // Vérification automatique toutes les 5 secondes
            setInterval(() => {
                const signupCompleted = localStorage.getItem('signup_completed');
                if (signupCompleted === 'true') {
                    addLog('🔍 Auto-check: signup_completed détecté');
                }
            }, 5000);
        });

        // Instructions d'utilisation
        addLog('📋 Instructions:');
        addLog('1. Cliquez sur "Tester Inscription" pour simuler une inscription');
        addLog('2. Cliquez sur "Ouvrir App" pour vérifier si le message s\'affiche');
        addLog('3. Ou utilisez "Test Complet" pour tout faire automatiquement');
    </script>
</body>
</html>
