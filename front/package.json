{"name": "front", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@abacritt/angularx-social-login": "^2.3.0", "@angular/animations": "^19.1.0", "@angular/cdk": "^19.1.4", "@angular/common": "^19.1.0", "@angular/compiler": "^19.1.0", "@angular/core": "^19.1.6", "@angular/forms": "^19.1.0", "@angular/material": "^19.1.4", "@angular/platform-browser": "^19.1.0", "@angular/platform-browser-dynamic": "^19.1.0", "@angular/router": "^19.1.0", "@ngx-translate/core": "^16.0.4", "@ngx-translate/http-loader": "^16.0.1", "angular-auth-oidc-client": "^19.0.0", "angular-oauth2-oidc": "^19.0.0", "flag-icon-css": "^4.1.7", "google-libphonenumber": "^3.2.40", "i18next": "^24.2.2", "i18next-browser-languagedetector": "^8.0.4", "i18next-http-backend": "^3.0.2", "ngx-infinite-scroll": "^19.0.0", "ngx-intl-tel-input": "^16.0.0", "ngx-owl-carousel-o": "^19.0.0", "rxjs": "~7.8.0", "tslib": "^2.3.0", "uuid": "^11.1.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.1.6", "@angular/cli": "^19.1.6", "@angular/compiler-cli": "^19.1.0", "@types/google-libphonenumber": "^7.4.30", "@types/jasmine": "~5.1.0", "jasmine-core": "~5.5.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.7.2"}}