const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');

const app = express();
const PORT = 3000;

// Middleware
app.use(cors());
app.use(bodyParser.json());

// Simuler une base de données en mémoire
let tests = [];
let testIdCounter = 1;

// Endpoint pour générer un test avec l'IA
app.post('/api/ai/generate-test', async (req, res) => {
  try {
    console.log('🤖 Demande de génération de test reçue:', req.body);
    
    const { settings, candidateId, candidateProfile, jobRequirements } = req.body;
    
    // Simuler un délai de traitement de l'IA
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Générer un test basé sur les paramètres
    const generatedTest = generateMockTest(settings, candidateId, candidateProfile, jobRequirements);
    
    console.log('✅ Test généré:', generatedTest);
    res.json(generatedTest);
    
  } catch (error) {
    console.error('❌ Erreur lors de la génération:', error);
    res.status(500).json({ 
      error: 'Erreur lors de la génération du test',
      message: error.message 
    });
  }
});

// Endpoint pour sauvegarder un test
app.post('/api/tests', (req, res) => {
  try {
    const test = req.body;
    test.id = test.id || `test_${testIdCounter++}`;
    test.createdAt = new Date();
    
    tests.push(test);
    
    console.log('💾 Test sauvegardé:', test.id);
    res.json({ success: true, testId: test.id });
    
  } catch (error) {
    console.error('❌ Erreur lors de la sauvegarde:', error);
    res.status(500).json({ 
      error: 'Erreur lors de la sauvegarde',
      message: error.message 
    });
  }
});

// Endpoint pour récupérer un test
app.get('/api/tests/:testId', (req, res) => {
  try {
    const testId = req.params.testId;
    const test = tests.find(t => t.id === testId);
    
    if (!test) {
      return res.status(404).json({ error: 'Test non trouvé' });
    }
    
    res.json(test);
    
  } catch (error) {
    console.error('❌ Erreur lors de la récupération:', error);
    res.status(500).json({ 
      error: 'Erreur lors de la récupération',
      message: error.message 
    });
  }
});

// Fonction pour générer un test simulé
function generateMockTest(settings, candidateId, candidateProfile, jobRequirements) {
  const questions = generateQuestions(settings);
  
  return {
    questions: questions,
    metadata: {
      totalQuestions: questions.length,
      estimatedDuration: settings.duration,
      difficulty: settings.level,
      type: settings.testType,
      generatedAt: new Date().toISOString(),
      candidateId: candidateId
    }
  };
}

// Fonction pour générer des questions selon les paramètres
function generateQuestions(settings) {
  const questions = [];
  const { questionCount, testType, level } = settings;
  
  for (let i = 0; i < questionCount; i++) {
    let questionType;
    
    if (testType === 'qcm') {
      questionType = 'qcm';
    } else if (testType === 'code') {
      questionType = 'code';
    } else {
      // Pour mixte, alterner entre QCM et code
      questionType = i % 2 === 0 ? 'qcm' : 'code';
    }
    
    if (questionType === 'qcm') {
      questions.push(generateQCMQuestion(i + 1, level));
    } else {
      questions.push(generateCodeQuestion(i + 1, level));
    }
  }
  
  return questions;
}

// Générer une question QCM
function generateQCMQuestion(number, level) {
  const difficulties = {
    'debutant': {
      questions: [
        'Quelle est la syntaxe correcte pour déclarer une variable en JavaScript ?',
        'Que signifie HTML ?',
        'Quel est le sélecteur CSS pour une classe ?'
      ],
      options: [
        ['var myVar;', 'variable myVar;', 'declare myVar;', 'let myVar;'],
        ['Hyper Text Markup Language', 'High Tech Modern Language', 'Home Tool Markup Language', 'Hyperlink and Text Markup Language'],
        ['.className', '#className', 'className', '*className']
      ],
      answers: [0, 0, 0]
    },
    'intermediaire': {
      questions: [
        'Quelle est la différence entre "let" et "var" en JavaScript ?',
        'Qu\'est-ce que le Virtual DOM dans React ?',
        'Comment fonctionne le hoisting en JavaScript ?'
      ],
      options: [
        ['Aucune différence', 'let a une portée de bloc, var a une portée de fonction', 'var est plus récent', 'let est plus rapide'],
        ['Une copie du DOM réel', 'Une représentation virtuelle du DOM', 'Un DOM caché', 'Un DOM temporaire'],
        ['Les variables sont déplacées en haut', 'Les fonctions sont exécutées en premier', 'Le code est optimisé', 'Les erreurs sont corrigées']
      ],
      answers: [1, 1, 0]
    },
    'expert': {
      questions: [
        'Expliquez le concept de closure en JavaScript et donnez un exemple d\'utilisation',
        'Quelle est la complexité temporelle de l\'algorithme de tri rapide dans le pire cas ?',
        'Comment optimiser les performances d\'une application Angular ?'
      ],
      options: [
        ['Une fonction qui accède aux variables de son scope parent', 'Une fonction anonyme', 'Une fonction récursive', 'Une fonction asynchrone'],
        ['O(n)', 'O(n log n)', 'O(n²)', 'O(log n)'],
        ['OnPush strategy, lazy loading, tree shaking', 'Seulement lazy loading', 'Seulement OnPush', 'Aucune optimisation nécessaire']
      ],
      answers: [0, 2, 0]
    }
  };
  
  const levelData = difficulties[level] || difficulties['intermediaire'];
  const questionIndex = (number - 1) % levelData.questions.length;
  
  return {
    id: `qcm_${number}`,
    type: 'qcm',
    question: levelData.questions[questionIndex],
    options: levelData.options[questionIndex],
    correctAnswer: levelData.answers[questionIndex],
    difficulty: level,
    points: level === 'debutant' ? 5 : level === 'intermediaire' ? 10 : 15
  };
}

// Générer une question de code
function generateCodeQuestion(number, level) {
  const difficulties = {
    'debutant': {
      questions: [
        'Écrivez une fonction qui retourne la somme de deux nombres',
        'Créez une fonction qui vérifie si un nombre est pair',
        'Implémentez une fonction qui inverse une chaîne de caractères'
      ],
      templates: [
        'function sum(a, b) {\n  // Votre code ici\n  return ;\n}',
        'function isEven(number) {\n  // Votre code ici\n  return ;\n}',
        'function reverseString(str) {\n  // Votre code ici\n  return ;\n}'
      ],
      expectedOutputs: [
        'sum(2, 3) devrait retourner 5',
        'isEven(4) devrait retourner true, isEven(3) devrait retourner false',
        'reverseString("hello") devrait retourner "olleh"'
      ]
    },
    'intermediaire': {
      questions: [
        'Implémentez une fonction de recherche binaire',
        'Créez une fonction qui supprime les doublons d\'un tableau',
        'Écrivez une fonction qui calcule la factorielle d\'un nombre'
      ],
      templates: [
        'function binarySearch(arr, target) {\n  // Votre code ici\n  return ;\n}',
        'function removeDuplicates(arr) {\n  // Votre code ici\n  return ;\n}',
        'function factorial(n) {\n  // Votre code ici\n  return ;\n}'
      ],
      expectedOutputs: [
        'binarySearch([1,2,3,4,5], 3) devrait retourner 2',
        'removeDuplicates([1,2,2,3,3,4]) devrait retourner [1,2,3,4]',
        'factorial(5) devrait retourner 120'
      ]
    },
    'expert': {
      questions: [
        'Implémentez un algorithme de tri fusion (merge sort)',
        'Créez une fonction qui trouve le plus long sous-tableau avec une somme donnée',
        'Implémentez un système de cache LRU (Least Recently Used)'
      ],
      templates: [
        'function mergeSort(arr) {\n  // Votre code ici\n  return ;\n}',
        'function longestSubarrayWithSum(arr, targetSum) {\n  // Votre code ici\n  return ;\n}',
        'class LRUCache {\n  constructor(capacity) {\n    // Votre code ici\n  }\n  \n  get(key) {\n    // Votre code ici\n  }\n  \n  put(key, value) {\n    // Votre code ici\n  }\n}'
      ],
      expectedOutputs: [
        'mergeSort([3,1,4,1,5]) devrait retourner [1,1,3,4,5]',
        'longestSubarrayWithSum([1,2,3,4,5], 9) devrait retourner [2,3,4]',
        'Cache LRU fonctionnel avec capacité limitée'
      ]
    }
  };
  
  const levelData = difficulties[level] || difficulties['intermediaire'];
  const questionIndex = (number - 1) % levelData.questions.length;
  
  return {
    id: `code_${number}`,
    type: 'code',
    question: levelData.questions[questionIndex],
    codeTemplate: levelData.templates[questionIndex],
    expectedOutput: levelData.expectedOutputs[questionIndex],
    difficulty: level,
    points: level === 'debutant' ? 10 : level === 'intermediaire' ? 20 : 30
  };
}

// Démarrer le serveur
app.listen(PORT, () => {
  console.log(`🚀 Serveur de simulation IA démarré sur http://localhost:${PORT}`);
  console.log(`📋 Endpoints disponibles:`);
  console.log(`   POST /api/ai/generate-test - Générer un test avec l'IA`);
  console.log(`   POST /api/tests - Sauvegarder un test`);
  console.log(`   GET /api/tests/:testId - Récupérer un test`);
});
