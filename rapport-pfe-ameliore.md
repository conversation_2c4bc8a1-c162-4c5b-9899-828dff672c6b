# Présentation du projet

## Étude et critique de l'existant

Nous commencerons cette partie par l'analyse de l'existant, suivie d'une critique, et nous conclurons par l'élaboration de notre solution.

### Étude de l'existant

Le domaine du recrutement est très vaste et la technologie moderne a grandement facilité la mise en relation entre les entreprises et les différents candidats. Les plateformes de publication d'offres et de curricula vitae ne sont plus un sujet nouveau. En effet, plusieurs plateformes ont été mises en place. Concernant le recrutement, les plus connues restent LinkedIn (et la famille), Indeed, Glassdoor, Monster, et des plateformes locales comme Emploitic. Ces plateformes offrent entre autres les tâches suivantes :

- **Publication d'offres d'emploi** : les entreprises peuvent rédiger et publier des annonces détaillées.
- **Recherche d'emploi** : les candidats peuvent consulter les offres et postuler en ligne.
- **Gestion de la demande** : certaines plateformes offrent des tableaux de bord pour suivre les candidatures.
- **CVthèques** : les recruteurs peuvent accéder à des bases de données de profils.
- **Filtrage des recherches** : les critères de filtrage permettent de cibler les candidatures selon l'expérience, la localisation, le domaine, etc.

Certaines plateformes offrent également des services premium incluant :
- Des tests de compétences préétablis.
- Des outils de communication directe (messagerie intégrée, entretiens vidéo).
- Des analyses statistiques basiques sur les candidatures.

### Critique de l'existant

Malgré leur popularité et leur utilité, ces plateformes présentent plusieurs limitations majeures face aux exigences actuelles du recrutement moderne, notamment pour les postes techniques ou spécialisés :

#### Manque de personnalisation des évaluations
- Les tests proposés sont souvent génériques et ne tiennent pas compte des spécificités du poste.
- Les recruteurs doivent créer manuellement des questionnaires ou se contenter de grilles standardisées.

#### Absence de génération automatique de tests
- Aucune plateforme n'intègre un système d'intelligence artificielle capable de générer automatiquement des questions personnalisées à partir d'une offre d'emploi ou d'un document technique (ex : fichier PDF).
- Cela représente une perte de temps pour les recruteurs et limite l'efficacité du processus.

#### Évaluation peu objective
- L'analyse des CV reste manuelle, sujette aux biais humains.
- Aucune analyse approfondie n'est faite sur les compétences réelles des candidats, ce qui peut fausser les décisions de recrutement.

#### Expérience utilisateur limitée
- Les interfaces ne sont pas toujours ergonomiques ni optimisées pour les deux profils (recruteurs et candidats).
- L'interaction entre les deux parties reste souvent asynchrone et peu engageante.

#### Intégration technologique faible
- Peu de plateformes proposent une architecture modulaire ou des services intelligents intégrés comme le NLP (Natural Language Processing), le Machine Learning ou des tableaux de bord décisionnels.

## Objectifs

L'objectif principal de ce projet est de concevoir et développer une plateforme de recrutement en ligne intelligente qui facilite la mise en relation entre les entreprises et les candidats, tout en intégrant une intelligence artificielle capable de générer automatiquement des tests personnalisés à partir des offres d'emploi.

### Objectifs généraux
- Mettre en place une application web intuitive qui permet aux entreprises de publier des offres d'emploi et aux candidats de postuler facilement.
- Exploiter les technologies d'intelligence artificielle pour automatiser l'évaluation des candidats.
- Offrir un système de tests dynamiques et personnalisés, adaptés aux compétences requises pour chaque poste.
- Garantir une expérience utilisateur fluide et optimisée pour tous les types d'utilisateurs (entreprises et candidats).

### Objectifs spécifiques

#### Pour les recruteurs
- Créer et gérer des offres d'emploi avec des descriptions détaillées.
- Gérer les candidatures reçues via un espace personnel.
- Générer automatiquement des tests techniques ou QCM à partir d'une offre ou d'un fichier PDF (job description).
- Visualiser les résultats et performances des candidats à travers des rapports intelligents.
- Gérer les comptes et les droits d'accès de leur équipe RH.

#### Pour les candidats
- Créer un profil personnel complet avec CV, expériences, compétences, etc.
- Rechercher et postuler facilement aux offres d'emploi publiées.
- Répondre à des tests générés automatiquement liés à une offre spécifique.
- Suivre l'évolution de leurs candidatures et évaluations.

#### Du point de vue technique
- Développer une architecture logicielle modulaire, scalable et sécurisée.
- Intégrer des modèles d'intelligence artificielle et de NLP pour extraire les informations clés et générer des questions pertinentes.
- Garantir une sécurité des données des utilisateurs (RGPD, confidentialité).
- Permettre une administration facile du système par des utilisateurs superadministrateurs.

## Solution proposée

Pour répondre aux limites des systèmes traditionnels de recrutement et améliorer l'efficacité des processus de sélection, nous proposons de concevoir et développer une plateforme de recrutement en ligne intelligente, combinant des fonctionnalités classiques de gestion des candidatures avec des technologies d'intelligence artificielle (IA) et de traitement automatique du langage naturel (NLP).

### Fonctionnalités clés de la solution

#### Gestion des utilisateurs
- Deux types de comptes principaux : Recruteur et Candidat.
- Possibilité pour un utilisateur de basculer entre les deux rôles selon ses besoins.
- Tableau de bord personnalisé pour chaque type d'utilisateur.
- Authentification sécurisée et gestion des accès basée sur les rôles.

#### Publication et gestion des offres d'emploi
- Interface intuitive pour créer, modifier ou supprimer une offre.
- Ajout de fichiers PDF ou descriptions détaillées pour générer les tests automatiquement.
- Classification des offres par catégories, compétences, niveaux d'expérience, etc.
- Système de gestion des offres archivées et actives.

#### Exploration et postulation pour les candidats
- Moteur de recherche intelligent pour filtrer les offres par mot-clé, lieu ou type de contrat.
- Espace personnel pour gérer le profil, le CV et les candidatures.
- Suivi de l'état de chaque candidature en temps réel.
- Système de sauvegarde des offres intéressantes.

#### Génération automatique de tests par IA
- Analyse des descriptions de poste via NLP pour détecter les compétences clés.
- Création dynamique de questions QCM, exercices techniques, ou tests de codage.
- Adaptation du niveau de difficulté selon le profil recherché.

#### Évaluation et résultats
- Interface de passage de test intégrée pour les candidats.
- Correction automatique et génération de rapports détaillés pour les recruteurs.
- Système de notation basé sur la pertinence des réponses et la rapidité d'exécution.

#### Espace entreprise (Workspace)
- Création et gestion de workspaces pour les entreprises.
- Visualisation des statistiques de chaque offre (nombre de vues, candidatures, taux de réussite).
- Accès à un répertoire de candidats avec possibilité de contact direct.
- Possibilité de créer des équipes RH avec gestion des rôles et permissions.

#### Système d'invitation
- Possibilité pour les recruteurs d'inviter des candidats à rejoindre leur workspace.
- Gestion des invitations envoyées et reçues.
- Suivi des statuts des invitations (en attente, acceptées, refusées).

#### Back-office et administration
- Interface d'administration pour gérer les utilisateurs, les contenus, les offres, et les signalements.
- Suivi de l'activité sur la plateforme (logs, statistiques, performances IA).

### Avantages de la solution
- Gain de temps pour les recruteurs grâce à l'automatisation.
- Évaluation objective et personnalisée des compétences.
- Expérience utilisateur fluide et professionnelle.
- Accès centralisé à l'information pour tous les utilisateurs.
- Adaptabilité à différents secteurs d'activité.

Cette solution vise à moderniser le recrutement, le rendre plus intelligent, rapide et équitable, tout en garantissant une valeur ajoutée pour les entreprises comme pour les chercheurs d'emploi.

## Identification des acteurs

L'identification des acteurs est une étape essentielle dans l'analyse des besoins d'un système, car elle permet de déterminer les différents types d'utilisateurs qui interagiront avec la plateforme, leurs rôles, ainsi que leurs attentes spécifiques. Pour notre plateforme de recrutement en ligne, les principaux acteurs identifiés sont :

### Le Candidat

Le candidat est un utilisateur qui cherche un emploi et utilise la plateforme pour trouver des opportunités professionnelles correspondant à ses compétences et aspirations.

#### Rôles et actions possibles
- Créer et gérer un compte personnel.
- Compléter et mettre à jour son profil (informations personnelles, expériences, compétences, CV, bio).
- Rechercher des offres d'emploi via un moteur de recherche intelligent.
- Postuler aux offres qui l'intéressent.
- Passer les tests générés automatiquement par la plateforme.
- Suivre l'état de ses candidatures.
- Sauvegarder des offres pour consultation ultérieure.
- Recevoir et gérer les invitations des recruteurs.

### Le Recruteur

Le recruteur représente une entreprise qui utilise la plateforme pour publier des offres d'emploi et évaluer les candidatures reçues. Il bénéficie d'une interface dédiée lui permettant de gérer les différentes étapes du recrutement.

#### Rôles et actions possibles
- Créer et gérer un compte entreprise (workspace).
- Ajouter et modifier les informations sur l'entreprise.
- Publier, modifier ou archiver des offres d'emploi.
- Examiner, accepter ou refuser les candidatures reçues.
- Visualiser et analyser les candidatures reçues.
- Suivre les performances des candidats aux tests.
- Organiser des entretiens à partir des résultats.
- Gérer une équipe RH interne avec différents niveaux d'accès.
- Envoyer des invitations aux candidats potentiels.
- Basculer entre le mode recruteur et le mode candidat.

### L'Administrateur

L'administrateur est responsable de la gestion globale de la plateforme, assurant son bon fonctionnement et la qualité des services offerts.

#### Rôles et actions possibles
- Gérer les utilisateurs (création, modification, suppression).
- Superviser les workspaces et les offres d'emploi.
- Modérer le contenu publié sur la plateforme.
- Gérer les paiements et les abonnements.
- Accéder aux statistiques et rapports d'utilisation.
- Résoudre les problèmes techniques et les litiges.

## Architecture technique

Notre plateforme de recrutement est construite sur une architecture moderne et modulaire, permettant une grande flexibilité et une évolutivité optimale.

### Architecture frontend

Le frontend de l'application est développé avec Angular, un framework moderne et robuste pour la création d'applications web dynamiques. Cette partie de l'application est responsable de l'interface utilisateur et de l'expérience utilisateur.

#### Composants principaux
- **Module d'authentification** : Gestion de l'inscription, connexion et récupération de mot de passe.
- **Module de profil utilisateur** : Gestion des informations personnelles, CV, compétences, etc.
- **Module de gestion des offres** : Publication, recherche et candidature aux offres d'emploi.
- **Module de tests** : Interface pour passer et évaluer les tests.
- **Module d'administration** : Gestion des utilisateurs, workspaces et paramètres.

#### Gestion des rôles
Un système de garde de routes (Guards) est implémenté pour contrôler l'accès aux différentes parties de l'application en fonction du rôle de l'utilisateur (candidat ou recruteur). Ce système permet de :
- Rediriger les utilisateurs non authentifiés vers la page de connexion.
- Limiter l'accès aux pages réservées aux recruteurs pour les candidats.
- Limiter l'accès aux pages réservées aux candidats pour les recruteurs.
- Permettre aux utilisateurs de basculer entre les rôles de candidat et recruteur.

### Architecture backend

Le backend de l'application est développé avec Spring Boot, un framework Java qui facilite la création d'applications robustes et sécurisées. Cette partie de l'application est responsable de la logique métier, de la gestion des données et de la sécurité.

#### Composants principaux
- **API RESTful** : Endpoints pour toutes les fonctionnalités de l'application.
- **Service d'authentification** : Gestion des utilisateurs et des sessions avec OAuth2.
- **Service de gestion des offres** : CRUD pour les offres d'emploi.
- **Service de gestion des candidatures** : Traitement des candidatures et des tests.
- **Service d'IA** : Génération automatique de tests basés sur les descriptions de poste.

#### Base de données
MongoDB est utilisé comme système de gestion de base de données, offrant une grande flexibilité pour stocker des données structurées et non structurées.

#### Sécurité
La sécurité est assurée par Spring Security avec JWT (JSON Web Tokens) pour l'authentification et l'autorisation. Les rôles et permissions sont gérés via un système d'autorités.

### Intégration et déploiement

L'application est conçue pour être facilement déployable dans différents environnements, du développement à la production.

#### CI/CD
Un pipeline d'intégration et de déploiement continus est mis en place pour automatiser les tests et le déploiement de l'application.

#### Conteneurisation
Docker est utilisé pour conteneuriser l'application, facilitant son déploiement et sa scalabilité.

## Fonctionnalités implémentées

### Gestion des utilisateurs et authentification
- Inscription et connexion des utilisateurs
- Authentification OAuth2 avec support pour les fournisseurs externes (Google, GitHub)
- Gestion des profils utilisateurs avec informations personnelles et professionnelles
- Système de rôles et permissions

### Gestion des workspaces (entreprises)
- Création et configuration de workspaces pour les recruteurs
- Gestion des membres et de leurs permissions
- Personnalisation du profil de l'entreprise (logo, description, etc.)
- Système de paiement pour les fonctionnalités premium

### Gestion des offres d'emploi
- Publication d'offres avec descriptions détaillées
- Recherche et filtrage des offres
- Candidature aux offres
- Suivi des candidatures

### Système d'invitation
- Envoi d'invitations aux candidats potentiels
- Gestion des invitations reçues et envoyées
- Suivi des statuts des invitations

### Génération de tests automatisés
- Analyse des descriptions de poste pour identifier les compétences requises
- Génération de questions adaptées au profil recherché
- Évaluation automatique des réponses
- Rapports détaillés sur les performances des candidats

### Interface utilisateur adaptative
- Interface différente selon le rôle de l'utilisateur (candidat ou recruteur)
- Possibilité de basculer entre les rôles
- Design responsive pour une utilisation sur différents appareils

## Perspectives d'évolution

Notre plateforme de recrutement a été conçue avec une vision d'évolution continue. Voici les principales perspectives d'amélioration et d'extension pour les versions futures :

### Amélioration de l'IA pour la génération de tests
- Intégration de modèles d'IA plus avancés pour une meilleure compréhension des compétences requises
- Personnalisation plus fine des tests en fonction du niveau d'expérience et du secteur d'activité
- Ajout de nouveaux formats de tests (simulations, études de cas, etc.)

### Fonctionnalités de communication avancées
- Système de messagerie instantanée entre recruteurs et candidats
- Planification et conduite d'entretiens vidéo directement sur la plateforme
- Feedback automatisé pour les candidats non retenus

### Analyse prédictive et recommandations
- Système de recommandation d'offres pour les candidats basé sur leur profil et leurs préférences
- Prédiction de l'adéquation entre un candidat et un poste
- Analyse des tendances du marché de l'emploi

### Intégration avec d'autres plateformes
- Connexion avec les principaux réseaux sociaux professionnels
- Import/export de données vers d'autres systèmes de gestion RH
- API publique pour permettre des intégrations tierces

### Fonctionnalités collaboratives
- Évaluation collaborative des candidats par plusieurs recruteurs
- Partage de notes et commentaires sur les candidatures
- Workflows personnalisables pour le processus de recrutement

### Internationalisation et localisation
- Support de multiples langues pour l'interface utilisateur
- Adaptation aux spécificités régionales du recrutement
- Conformité avec les réglementations locales en matière de protection des données

## Conclusion

Notre plateforme de recrutement intelligente représente une avancée significative dans le domaine des solutions RH, en combinant les fonctionnalités traditionnelles de gestion des candidatures avec des technologies d'intelligence artificielle avancées.

En automatisant la génération et l'évaluation des tests, notre solution permet aux recruteurs de gagner un temps précieux tout en améliorant la qualité et l'objectivité de leurs décisions. Pour les candidats, la plateforme offre une expérience utilisateur fluide et personnalisée, facilitant leur recherche d'emploi et leur permettant de mettre en valeur leurs compétences de manière plus pertinente.

L'architecture modulaire et évolutive de notre application garantit sa pérennité et sa capacité à s'adapter aux besoins changeants du marché du recrutement. Les perspectives d'évolution identifiées ouvrent la voie à des améliorations continues qui renforceront encore la valeur ajoutée de notre solution.

En définitive, notre plateforme ne se contente pas de digitaliser le processus de recrutement, elle le transforme en profondeur pour le rendre plus efficace, plus équitable et plus adapté aux défis du marché du travail moderne.
