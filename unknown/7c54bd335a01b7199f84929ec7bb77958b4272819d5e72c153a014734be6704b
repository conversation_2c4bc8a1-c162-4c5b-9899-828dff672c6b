package org.example.controller.advice;

import jakarta.servlet.http.HttpServletRequest;
import org.example.exceptions.login.*;
import org.example.exceptions.user.UserNotFoundException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.AuthenticationException;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

import java.util.Map;
import java.util.stream.Collectors;

@ControllerAdvice
public class UserAuthControllerAdvice {

    public record UserValidationError(boolean success, Map<String, String> errors) {}

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<UserValidationError> handleMethodArgumentNotValidException(
            final MethodArgumentNotValidException ex, final HttpServletRequest request) {

        Map<String, String> errors = ex.getBindingResult()
                .getAllErrors()
                .stream()
                .filter(objectError -> objectError.getDefaultMessage() != null)
                .filter(objectError -> objectError instanceof FieldError)
                .collect(Collectors.toMap(
                        objectError -> ((FieldError) objectError).getField(),
                        ObjectError::getDefaultMessage));

        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(new UserValidationError(false, errors));
    }

    @ExceptionHandler(LoginException.class)
    public ResponseEntity<UserValidationError> handleLoginException(
            final LoginException ex, final HttpServletRequest request) {

        return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                .body(new UserValidationError(false, Map.of("message", "Invalid credentials")));
    }

    @ExceptionHandler(LoginFailedException.class)
    public ResponseEntity<UserValidationError> handleLoginFailedException(
            final LoginFailedException ex, final HttpServletRequest request) {

        return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                .body(new UserValidationError(false, Map.of("message", "Login attempt failed. Please check your credentials.")));
    }

    @ExceptionHandler(AuthenticationException.class)
    public ResponseEntity<UserValidationError> handleAuthenticationException(
            final AuthenticationException ex, final HttpServletRequest request) {

        return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                .body(new UserValidationError(false, Map.of("message", "Authentication failed. Please try again.")));
    }

    @ExceptionHandler(SignUpExistingEmailException.class)
    public ResponseEntity<UserValidationError> handleSignUpExistingEmailException(
            final SignUpExistingEmailException ex, final HttpServletRequest request) {

        return ResponseEntity.status(HttpStatus.CONFLICT)
                .body(new UserValidationError(false, Map.of("message", "Email already exists")));
    }

    @ExceptionHandler(UserNotFoundException.class)
    public ResponseEntity<UserValidationError> handleUserNotFoundException(
            final UserNotFoundException ex, final HttpServletRequest request) {

        return ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(new UserValidationError(false, Map.of("message", "User not found")));
    }

    @ExceptionHandler(InvalidTokenException.class)
    public ResponseEntity<UserValidationError> handleInvalidTokenException(
            final InvalidTokenException ex, final HttpServletRequest request) {

        return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                .body(new UserValidationError(false, Map.of("message", "Invalid or expired token")));
    }

    @ExceptionHandler(WeakPasswordException.class)
    public ResponseEntity<UserValidationError> handleWeakPasswordException(
            final WeakPasswordException ex, final HttpServletRequest request) {

        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(new UserValidationError(false, Map.of("message", "Password is too weak")));
    }

    @ExceptionHandler(AccountLockedException.class)
    public ResponseEntity<UserValidationError> handleAccountLockedException(
            final AccountLockedException ex, final HttpServletRequest request) {

        return ResponseEntity.status(HttpStatus.FORBIDDEN)
                .body(new UserValidationError(false, Map.of("message", "Account is locked. Try again later.")));
    }
}
