<div class="manage-invitations-container">
  <!-- Sidebar -->
  <app-sidebar></app-sidebar>

  <!-- Main Content -->
  <div class="content-container">
    <!-- Header -->
    <header class="header-section">
      <h1>G<PERSON>rer les Invitations</h1>
      <div class="header-actions">
        <button mat-raised-button class="back-button" (click)="goBack()">
          <mat-icon>arrow_back</mat-icon> Retour
        </button>
      </div>
    </header>



    <!-- Quick Stats -->
    <section class="quick-stats-container">
      <div class="quick-stats-content">
        <div class="stats-item">
          <mat-icon>notifications</mat-icon>
          <div>
            <h3>Total des Invitations</h3>
            <p>{{ totalInvitations }}</p>
          </div>
        </div>
        <div class="stats-item">
          <mat-icon>send</mat-icon>
          <div>
            <h3>Invitations Envoyées</h3>
            <p></p>
          </div>
        </div>
        <div class="stats-item">
          <mat-icon>hourglass_empty</mat-icon>
          <div>
            <h3>Invitations en Attente</h3>
            <p></p>
          </div>
        </div>
        <div class="stats-item">
          <mat-icon>delete</mat-icon>
          <div>
            <h3>Invitations Supprimées</h3>
            <p></p>
          </div>
        </div>
        <div class="stats-item">
          <mat-icon>cancel</mat-icon>
          <div>
            <h3>Invitations Rejetées</h3>
            <p></p>
          </div>
        </div>
      </div>
    </section>

    <!-- Table -->
    <div class="table-container">
      <mat-spinner *ngIf="isLoading" class="spinner"></mat-spinner>
      <table mat-table [dataSource]="invitations" *ngIf="!isLoading && !errorMessage">
        <!-- ID Column -->
        <ng-container matColumnDef="id">
          <th mat-header-cell *matHeaderCellDef> ID </th>
          <td mat-cell *matCellDef="let invitation"> {{ invitation.id }} </td>
        </ng-container>

        <!-- Sender Column -->
        <ng-container matColumnDef="sender">
          <th mat-header-cell *matHeaderCellDef> Expéditeur </th>
          <td mat-cell *matCellDef="let invitation"> {{ invitation.sender }} </td>
        </ng-container>

        <!-- Receiver Column -->
        <ng-container matColumnDef="receiver">
          <th mat-header-cell *matHeaderCellDef> Destinataire </th>
          <td mat-cell *matCellDef="let invitation"> {{ invitation.receiver }} </td>
        </ng-container>

        <!-- Workspace Column -->
        <ng-container matColumnDef="workspace">
          <th mat-header-cell *matHeaderCellDef> Espace de Travail </th>
          <td mat-cell *matCellDef="let invitation"> {{ invitation.workspace }} </td>
        </ng-container>

        <!-- Message Column -->
        <ng-container matColumnDef="message">
          <th mat-header-cell *matHeaderCellDef> Message </th>
          <td mat-cell *matCellDef="let invitation"> {{ invitation.message }} </td>
        </ng-container>

        <!-- Created At Column -->
        <ng-container matColumnDef="createdAt">
          <th mat-header-cell *matHeaderCellDef> Date de Création </th>
          <td mat-cell *matCellDef="let invitation"> {{ invitation.createdAt | date:'dd/MM/yyyy HH:mm' }} </td>
        </ng-container>

        <!-- Status Column -->
        <ng-container matColumnDef="status">
          <th mat-header-cell *matHeaderCellDef> Statut </th>
          <td mat-cell *matCellDef="let invitation">
            <span [ngClass]="{
              'status-pending': invitation.status === 'PENDING',
              'status-accepted': invitation.status === 'ACCEPTED',
              'status-rejected': invitation.status === 'REJECTED',
              'status-deleted': invitation.status === 'DELETED'
            }">
              {{ invitation.status | titlecase }}
            </span>
          </td>
        </ng-container>

        <!-- Actions Column -->
        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef> Actions </th>
          <td mat-cell *matCellDef="let invitation">
            <button mat-icon-button color="warn" matTooltip="Marquer comme supprimé" (click)="markAsDeleted(invitation)" [disabled]="invitation.status === 'DELETED'">
              <mat-icon>delete_forever</mat-icon>
            </button>
            <button mat-icon-button color="warn" matTooltip="Supprimer définitivement" (click)="deleteInvitation(invitation)">
              <mat-icon>delete</mat-icon>
            </button>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
      </table>

      <!-- Pagination Controls -->
      <div class="pagination" *ngIf="!isLoading && !errorMessage">
        <button mat-button [disabled]="page === 0" (click)="previousPage()">
          <mat-icon>chevron_left</mat-icon> Précédent
        </button>
        <span>Page {{ page + 1 }}</span>
        <button mat-button [disabled]="invitations.length < size" (click)="nextPage()">
          Suivant <mat-icon>chevron_right</mat-icon>
        </button>
      </div>
    </div>

    <!-- Floating Action Button -->
    <button mat-fab class="fab" color="primary" matTooltip="Rafraîchir" (click)="refresh()">
      <mat-icon>refresh</mat-icon>
    </button>
  </div>
</div>
