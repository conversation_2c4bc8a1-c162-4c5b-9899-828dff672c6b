  /* 🌟 General Style - Elegant, Professional */
.quiz-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #1A2A44, #2A9D8F);
  background-size: 200% 200%;
  animation: gradientFlow 12s ease infinite;
  padding: 16px;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
  font-family: 'Montserrat', sans-serif;
}

@keyframes gradientFlow {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Subtle Geometric Overlay */
.quiz-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    45deg,
    rgba(233, 196, 106, 0.05) 25%,
    transparent 25%,
    transparent 50%,
    rgba(233, 196, 106, 0.05) 50%,
    rgba(233, 196, 106, 0.05) 75%,
    transparent 75%
  )
  0 0 / 40px 40px;
  opacity: 0.3;
  animation: patternShift 30s linear infinite;
}

@keyframes patternShift {
  0% { transform: translate(0, 0); }
  100% { transform: translate(-40px, -40px); }
}

/* Quiz Body Card */
.quiz-body-card {
  padding: 18px 24px;
  border-radius: 14px;
  background: #F8F9FA;
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.1);
  margin: 20px auto;
  max-width: 740px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
  border: 1px solid transparent;
  border-image: linear-gradient(45deg, #E9C46A, #2A9D8F) 1;
  will-change: transform, box-shadow;
  animation: fadeInUp 0.5s ease-out;
}

.quiz-body-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 32px rgba(0, 0, 0, 0.15);
}

/* 🕒 Top Bar - Sleek and Professional */
.quiz-top-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 8px;
  background: #FFFFFF;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border-bottom: 1px solid #E9C46A;
}

/* Date with Elegant Typography */
.quiz-top-bar .date {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1A2A44;
  letter-spacing: 1px;
  text-transform: uppercase;
  font-family: 'Montserrat', sans-serif;
  text-shadow: 0 1px 2px rgba(233, 196, 106, 0.2);
}

/* Timer - Refined and Vibrant */
.timer-panel {
  background: linear-gradient(135deg, #1A2A44, #2A9D8F);
  color: #F8F9FA;
  padding: 8px 16px;
  border-radius: 6px;
  text-align: center;
  min-width: 130px;
  box-shadow: 0 4px 16px rgba(42, 157, 143, 0.25);
  font-weight: 600;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  will-change: transform, box-shadow;
}

.timer-panel:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 20px rgba(42, 157, 143, 0.35);
}

.timer-title {
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.8px;
  margin-bottom: 2px;
  color: #F8F9FA;
  font-family: 'Montserrat', sans-serif;
}

.timer {
  font-size: 1.5rem;
  font-weight: 700;
  font-family: 'Montserrat', sans-serif;
}

/* 🎯 Quiz Title - Bold and Luxurious */
.quiz-title {
  font-size: 1.8rem;
  font-weight: 700;
  color: #1A2A44;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  letter-spacing: -0.2px;
  font-family: 'Montserrat', sans-serif;
  position: relative;
  animation: fadeInUp 0.5s ease-out;
}

.quiz-title::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 2px;
  background: linear-gradient(90deg, #E9C46A, #2A9D8F);
  border-radius: 1px;
  transition: width 0.3s ease;
}

.quiz-title:hover::after {
  width: 60px;
}

.centered-title {
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  width: 100%;
}

.flag-icon {
  font-size: 1.8rem;
  color: #E9C46A;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  transition: transform 0.3s ease;
}

.flag-icon:hover {
  transform: scale(1.1);
}

/* 💬 Question - Elegant and Polished */
.question-title {
  font-size: 2.2rem;
  font-weight: 800;
  color: #1A2A44;
  margin-bottom: 16px;
  letter-spacing: -0.4px;
  line-height: 1.3;
  text-align: center;
  max-width: 95%;
  margin-left: auto;
  margin-right: auto;
  font-family: 'Montserrat', sans-serif;
  position: relative;
  animation: fadeInUp 0.6s ease-out;
}

.question-title::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 2px;
  background: linear-gradient(90deg, #E9C46A, #2A9D8F);
  border-radius: 1px;
  transition: width 0.3s ease;
}

.question-title:hover::after {
  width: 70px;
}

.question-text {
  font-size: 1rem;
  color: #264653;
  margin-bottom: 20px;
  line-height: 1.7;
  font-weight: 400;
  text-align: justify;
  max-width: 100%;
  font-family: 'Lora', serif;
  animation: fadeInUp 0.7s ease-out;
}

/* ✅ Options - Sophisticated and Vibrant */
.options {
  display: flex;
  flex-direction: column;
  gap: 16px;
  background: rgba(248, 249, 250, 0.5);
  padding: 16px;
  border-radius: 12px;
  border: 1px solid rgba(233, 196, 106, 0.2);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  will-change: transform, box-shadow;
}

.options:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
}

.options mat-radio-group {
  display: flex;
  flex-direction: column;
  gap: inherit;
}

/* Option - Minimal and Interactive */
.option {
  background: #FFFFFF;
  border-radius: 10px;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  border: 1px solid rgba(233, 196, 106, 0.3);
  cursor: pointer;
  gap: 12px;
  transition: all 0.3s ease;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.06);
  margin: 0;
  animation: fadeInUp 0.4s ease-out;
  will-change: transform, box-shadow, border;
}

.option:hover {
  border: 1px solid #E9C46A;
  box-shadow: 0 0 15px rgba(233, 196, 106, 0.3);
  transform: translateY(-2px);
}

.option:active {
  transform: translateY(0);
  box-shadow: 0 3px 8px rgba(233, 196, 106, 0.2);
}

.option:focus-within {
  outline: 2px solid #2A9D8F;
  outline-offset: 2px;
}

.option.mat-card {
  padding: 0;
  margin: 0;
  box-shadow: none;
}

.option mat-checkbox,
.option mat-radio-button {
  margin: 0;
  flex-shrink: 0;
  align-self: flex-start;
}

.option mat-radio-button ::ng-deep .mat-radio-outer-circle {
  border-color: #1A2A44;
  transition: border-color 0.3s ease;
}

.option mat-radio-button ::ng-deep .mat-radio-inner-circle {
  background-color: #2A9D8F;
}

.option mat-radio-button ::ng-deep .mat-radio-checked .mat-radio-outer-circle {
  border-color: #2A9D8F;
}

.option mat-checkbox ::ng-deep .mat-checkbox-frame {
  border-color: #1A2A44;
}

.option mat-checkbox ::ng-deep .mat-checkbox-checked .mat-checkbox-background {
  background-color: #2A9D8F;
}

/* Option Text - Elegant and Precise */
.option-text {
  font-weight: 500;
  color: #264653;
  text-align: justify;
  flex: 1;
  font-size: 0.95rem;
  line-height: 1.6;
  font-family: 'Lora', serif;
  transition: color 0.3s ease;
}

.option:hover .option-text {
  color: #1A2A44;
}

/* ⬅️➡️ Navigation - Polished and Dynamic */
.quiz-footer {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
  flex-wrap: wrap;
  gap: 8px;
  padding: 0;
}

.quiz-footer button {
  padding: 10px 16px;
  font-size: 0.95rem;
  border-radius: 8px;
  background: #2A9D8F;
  color: #F8F9FA;
  font-weight: 600;
  flex: 1;
  min-width: 100px;
  box-shadow: 0 4px 12px rgba(42, 157, 143, 0.2);
  transition: all 0.3s ease;
  font-family: 'Montserrat', sans-serif;
  border: 1px solid #E9C46A;
  position: relative;
  overflow: hidden;
  will-change: transform, box-shadow, background;
}

.quiz-footer button::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(233, 196, 106, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.4s ease, height 0.4s ease;
  pointer-events: none;
}

.quiz-footer button:hover:not(:disabled)::after {
  width: 200px;
  height: 200px;
}

.quiz-footer button:hover:not(:disabled) {
  background: #1A2A44;
  box-shadow: 0 0 15px rgba(233, 196, 106, 0.4);
  transform: translateY(-2px);
}

.quiz-footer button:disabled {
  background: #DDE1E6;
  color: #9CA3AF;
  cursor: not-allowed;
  box-shadow: none;
  border: 1px solid #DDE1E6;
}

.quiz-footer button.mat-primary {
  background: #2A9D8F;
  box-shadow: 0 4px 14px rgba(42, 157, 143, 0.3);
}

.quiz-footer button.mat-primary:hover:not(:disabled) {
  background: #1A2A44;
  box-shadow: 0 0 18px rgba(233, 196, 106, 0.4);
}

/* 🌟 Progress Indicator - Sleek and Vibrant */
.progress-section {
  margin-bottom: 14px;
  text-align: center;
}

.progress-text {
  font-size: 0.9rem;
  font-weight: 600;
  color: #1A2A44;
  margin-bottom: 6px;
  font-family: 'Montserrat', sans-serif;
  letter-spacing: 0.4px;
}

.progress-bar {
  background: #DDE1E6;
  height: 6px;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  background: linear-gradient(135deg, #E9C46A, #2A9D8F);
  height: 100%;
  transition: width 0.4s ease;
  box-shadow: 0 0 8px rgba(233, 196, 106, 0.3);
}

/* 💡 Hint Section - Subtle and Elegant */
.hint-section {
  margin-bottom: 14px;
  text-align: center;
}

.hint-button {
  padding: 6px 12px;
  font-size: 0.9rem;
  font-weight: 600;
  color: #1A2A44;
  background: #FFFFFF;
  border: 1px solid #E9C46A;
  border-radius: 6px;
  transition: all 0.3s ease;
  font-family: 'Montserrat', sans-serif;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.06);
  position: relative;
  overflow: hidden;
  will-change: transform, box-shadow, background;
}

.hint-button::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(233, 196, 106, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.4s ease, height 0.4s ease;
  pointer-events: none;
}

.hint-button:hover::after {
  width: 150px;
  height: 150px;
}

.hint-button:hover {
  background: #E9C46A;
  color: #1A2A44;
  box-shadow: 0 0 12px rgba(233, 196, 106, 0.3);
  transform: translateY(-1px);
}

.hint-text {
  font-size: 0.9rem;
  color: #264653;
  margin-top: 6px;
  line-height: 1.6;
  font-weight: 400;
  font-family: 'Lora', serif;
  background: #FFFFFF;
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid rgba(233, 196, 106, 0.3);
  max-width: 90%;
  margin-left: auto;
  margin-right: auto;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.06);
  animation: fadeInUp 0.5s ease-out;
}

/* 🌟 Bookmark Section - Clean and Professional */
.bookmark-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 14px;
}

.bookmark-button {
  color: #1A2A44;
  transition: color 0.3s ease, transform 0.3s ease;
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
}

.bookmark-button.bookmarked {
  color: #E9C46A;
}

.bookmark-button:hover {
  transform: scale(1.05);
  color: #2A9D8F;
}

.view-bookmarks-button {
  padding: 6px 12px;
  font-size: 0.9rem;
  font-weight: 600;
  color: #1A2A44;
  background: #FFFFFF;
  border: 1px solid #E9C46A;
  border-radius: 6px;
  transition: all 0.3s ease;
  font-family: 'Montserrat', sans-serif;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.06);
  position: relative;
  overflow: hidden;
  will-change: transform, box-shadow, background;
}

.view-bookmarks-button::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(233, 196, 106, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.4s ease, height 0.4s ease;
  pointer-events: none;
}

.view-bookmarks-button:hover::after {
  width: 150px;
  height: 150px;
}

.view-bookmarks-button:hover {
  background: #E9C46A;
  color: #1A2A44;
  box-shadow: 0 0 12px rgba(233, 196, 106, 0.3);
  transform: translateY(-1px);
}

/* 🌟 Answer Feedback - Vibrant and Clear */
.option.correct {
  border: 1px solid #2A9D8F;
  background: rgba(42, 157, 143, 0.1);
  box-shadow: 0 0 12px rgba(42, 157, 143, 0.3);
}

.option.incorrect {
  border: 1px solid #E76F51;
  background: rgba(231, 111, 81, 0.1);
  box-shadow: 0 0 12px rgba(231, 111, 81, 0.3);
}

/* 📱 Responsive Design - Fluid and Professional */
@media (max-width: 768px) {
  .quiz-container {
    padding: 12px;
  }

  .quiz-body-card {
    padding: 14px 18px;
    margin: 16px 10px;
    max-width: 100%;
  }

  .quiz-title {
    font-size: 1.5rem;
  }

  .flag-icon {
    font-size: 1.5rem;
  }

  .question-title {
    font-size: 1.8rem;
  }

  .question-text {
    font-size: 0.95rem;
    margin-bottom: 16px;
  }

  .options {
    padding: 14px;
    gap: 14px;
  }

  .option {
    padding: 10px 14px;
    gap: 10px;
  }

  .option-text {
    font-size: 0.9rem;
  }

  .quiz-footer button {
    font-size: 0.9rem;
    padding: 8px 12px;
    min-width: 90px;
  }

  .quiz-top-bar {
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
    padding: 6px 12px;
  }

  .quiz-top-bar .date {
    font-size: 0.95rem;
  }

  .timer-panel {
    min-width: auto;
    width: 100%;
    padding: 6px 12px;
  }

  .timer-title {
    font-size: 0.8rem;
  }

  .timer {
    font-size: 1.3rem;
  }

  .progress-section {
    margin-bottom: 12px;
  }

  .progress-text {
    font-size: 0.85rem;
  }

  .progress-bar {
    height: 5px;
  }

  .hint-section {
    margin-bottom: 12px;
  }

  .hint-button {
    font-size: 0.85rem;
    padding: 5px 10px;
  }

  .hint-text {
    font-size: 0.85rem;
    max-width: 95%;
  }

  .bookmark-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
    margin-bottom: 12px;
  }

  .view-bookmarks-button {
    font-size: 0.85rem;
    padding: 5px 10px;
  }
}

@media (max-width: 480px) {
  .quiz-container {
    padding: 8px;
  }

  .quiz-body-card {
    padding: 12px 14px;
  }

  .quiz-title {
    font-size: 1.3rem;
  }

  .flag-icon {
    font-size: 1.3rem;
  }

  .question-title {
    font-size: 1.6rem;
  }

  .question-text {
    font-size: 0.85rem;
  }

  .options {
    padding: 12px;
    gap: 12px;
  }

  .option {
    padding: 8px 12px;
    gap: 8px;
  }

  .option-text {
    font-size: 0.9rem;
  }

  .quiz-footer {
    flex-direction: column;
  }

  .quiz-footer button {
    width: 100%;
    padding: 8px 0;
  }

  .quiz-top-bar .date {
    font-size: 0.85rem;
  }

  .timer-panel {
    padding: 5px 10px;
  }

  .timer-title {
    font-size: 0.75rem;
  }

  .timer {
    font-size: 1.15rem;
  }

  .progress-text {
    font-size: 0.8rem;
  }

  .hint-button {
    font-size: 0.8rem;
    padding: 4px 8px;
  }

  .hint-text {
    font-size: 0.8rem;
    max-width: 100%;
    padding: 6px 10px;
  }

  .view-bookmarks-button {
    font-size: 0.8rem;
    padding: 4px 8px;
    width: 100%;
  }
}

/* 🌟 Dark Mode - Luxurious and Professional */
@media (prefers-color-scheme: dark) {
  .quiz-container {
    background: linear-gradient(135deg, #264653, #2A9D8F);
  }

  .quiz-body-card {
    background: #2A2F3B;
    border-image: linear-gradient(45deg, #E9C46A, #2A9D8F) 1;
    box-shadow: 0 6px 24px rgba(0, 0, 0, 0.2);
  }

  .quiz-top-bar {
    background: #2A2F3B;
    border-bottom: 1px solid #E9C46A;
  }

  .quiz-top-bar .date {
    color: #E9C46A;
    text-shadow: 0 1px 2px rgba(233, 196, 106, 0.3);
  }

  .timer-panel {
    background: linear-gradient(135deg, #2A9D8F, #1A2A44);
    color: #F8F9FA;
  }

  .timer-title {
    color: #F8F9FA;
  }

  .quiz-title {
    color: #F8F9FA;
  }

  .question-title {
    color: #F8F9FA;
  }

  .question-text {
    color: #DDE1E6;
  }

  .options {
    background: rgba(42, 47, 59, 0.5);
    border-color: rgba(233, 196, 106, 0.3);
  }

  .option {
    background: #2A2F3B;
    border: 1px solid rgba(233, 196, 106, 0.3);
  }

  .option:hover {
    border: 1px solid #E9C46A;
    background: rgba(233, 196, 106, 0.1);
  }

  .option-text {
    color: #DDE1E6;
  }

  .option:hover .option-text {
    color: #F8F9FA;
  }

  .quiz-footer button {
    background: #1A2A44;
    border: 1px solid #E9C46A;
    color: #F8F9FA;
  }

  .quiz-footer button:hover:not(:disabled) {
    background: #2A9D8F;
    box-shadow: 0 0 15px rgba(42, 157, 143, 0.4);
  }

  .quiz-footer button:disabled {
    background: #4A5263;
    color: #9CA3AF;
    border: 1px solid #4A5263;
  }

  .quiz-footer button.mat-primary {
    background: #2A9D8F;
  }

  .quiz-footer button.mat-primary:hover:not(:disabled) {
    background: #1A2A44;
  }

  .progress-text {
    color: #E9C46A;
  }

  .progress-bar {
    background: #4A5263;
  }

  .progress-fill {
    background: linear-gradient(135deg, #E9C46A, #2A9D8F);
  }

  .hint-button {
    color: #F8F9FA;
    background: #2A2F3B;
    border: 1px solid #E9C46A;
  }

  .hint-button:hover {
    background: #E9C46A;
    color: #1A2A44;
  }

  .hint-text {
    color: #DDE1E6;
    background: #2A2F3B;
    border-color: rgba(233, 196, 106, 0.3);
  }

  .bookmark-button {
    color: #E9C46A;
  }

  .bookmark-button.bookmarked {
    color: #2A9D8F;
  }

  .view-bookmarks-button {
    color: #F8F9FA;
    background: #2A2F3B;
    border: 1px solid #E9C46A;
  }

  .view-bookmarks-button:hover {
    background: #E9C46A;
    color: #1A2A44;
  }

  .option.correct {
    border: 1px solid #2A9D8F;
    background: rgba(42, 157, 143, 0.2);
  }

  .option.incorrect {
    border: 1px solid #E76F51;
    background: rgba(231, 111, 81, 0.2);
  }
}

/* Animations */
@keyframes fadeInUp {
  from { opacity: 0; transform: translateY(15px); }
  to { opacity: 1; transform: translateY(0); }
}

