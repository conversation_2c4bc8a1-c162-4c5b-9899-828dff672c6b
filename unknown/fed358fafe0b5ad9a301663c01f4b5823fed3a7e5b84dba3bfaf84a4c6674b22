import { Injectable } from '@angular/core';
import i18next from 'i18next';
import Backend from 'i18next-http-backend';
import LanguageDetector from 'i18next-browser-languagedetector';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})

export class I18nService {
  private currentLanguage = new BehaviorSubject<string>('en');

  constructor() {
    i18next
      .use(Backend)
      .use(LanguageDetector)
      .init({
        fallbackLng: 'en',
        debug: true,
        backend: {
          loadPath: './assets/i18n/{{lng}}.json'
        }
      }).then(() => {
      this.currentLanguage.next(i18next.language);
    });
  }

  changeLanguage(language: string): Observable<any> {
    return new Observable(observer => {
      i18next.changeLanguage(language, (err, t) => {
        if (err) {
          observer.error(err);
        } else {
          this.currentLanguage.next(language);
          observer.next(t);
          observer.complete();
        }
      });
    });
  }

  getCurrentLanguage(): Observable<string> {
    return this.currentLanguage.asObservable();
  }

  translate(key: string, options?: any): string {
    const result = i18next.t(key, options);
    return typeof result === 'string' ? result : '';
  }

}
