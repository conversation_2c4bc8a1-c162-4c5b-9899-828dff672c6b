import { Component, EventEmitter, Output } from '@angular/core';
import {<PERSON><PERSON><PERSON>ogA<PERSON>, MatDialogContent, MatDialogRef, MatDialogTitle} from '@angular/material/dialog';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { CommonModule } from '@angular/common';
import { PostsService } from '../../services/posts/posts.service';

@Component({
  selector: 'app-add-post',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatCardModule,
    MatIconModule,
    MatD<PERSON>ogActions,
    MatD<PERSON>ogContent,
    MatDialogTitle,
  ],
  templateUrl: './add-post.component.html',
  styleUrls: ['./add-post.component.css'],
})
export class AddPostComponent {
  postForm: FormGroup;
  @Output() postAdded = new EventEmitter<void>();

  constructor(
    public dialogRef: MatDialogRef<AddPostComponent>,
    private fb: FormBuilder,
    private postsService: PostsService
  ) {
    this.postForm = this.fb.group({
      jobTitle: ['', Validators.required],
      profileRequest: ['', Validators.required],
      contractType: ['', Validators.required],
      location: ['', Validators.required],
      description: ['', Validators.required],
    });
  }

  closeDialog(): void {
    this.dialogRef.close();
  }

  addPost() {
    if (this.postForm.valid) {
      console.log('Données envoyées :', this.postForm.value);

      this.postsService.createPost(this.postForm.value).subscribe({
        next: (response) => {
          console.log('Post ajouté avec succès !', response);
          alert('Post ajouté avec succès !');
          this.dialogRef.close(true); // Fermer et signaler un succès
        },
        error: (error) => {
          console.error("Erreur lors de l'ajout du post", error);
          alert("Erreur lors de l'ajout du post");
        },
      });
    } else {
      alert('Veuillez remplir tous les champs');
    }
  }
}
