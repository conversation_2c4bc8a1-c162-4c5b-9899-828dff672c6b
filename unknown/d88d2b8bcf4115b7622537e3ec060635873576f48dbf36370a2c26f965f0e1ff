
.bio-container {
  width: 100%;
  margin: 30px 0;
  position: relative;
}

.bio-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 12px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;

  &:hover {
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, #0a66c2, #4a90e2);
  }
}

.bio-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);

  .bio-title {
    display: flex;
    align-items: center;
    gap: 12px;

    mat-icon {
      color: #0a66c2;
      font-size: 28px;
      height: 28px;
      width: 28px;
    }

    h2 {
      margin: 0;
      font-size: 20px;
      font-weight: 600;
      color: #333;
    }
  }

  .bio-actions {
    display: flex;
    gap: 8px;

    button {
      transition: all 0.3s ease;

      &:hover {
        background-color: rgba(0, 0, 0, 0.05);
      }

      mat-icon {
        color: #666;
      }
    }

    .edit-button:hover mat-icon {
      color: #0a66c2;
    }
  }
}

.bio-content {
  padding: 24px;
  position: relative;

  p {
    margin: 0;
    font-size: 16px;
    line-height: 1.6;
    color: #444;
    white-space: pre-line;
  }

  .empty-bio {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    padding: 20px;
    text-align: center;
    color: #888;

    mat-icon {
      font-size: 36px;
      height: 36px;
      width: 36px;
      color: #ccc;
    }

    span {
      font-size: 16px;
      font-style: italic;
    }

    button {
      margin-top: 10px;
    }
  }
}

.bio-edit {
  padding: 24px;

  .full-width {
    width: 100%;
  }

  .error-message {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 16px 0;
    padding: 12px;
    border-radius: 4px;
    background-color: #ffebee;
    color: #d32f2f;

    mat-icon {
      color: #d32f2f;
    }
  }

  .bio-edit-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 16px;
  }
}

// Animation pour l'icône spinner
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.spinner {
  animation: spin 1s linear infinite;
}

// Media queries pour la responsivité
@media (max-width: 768px) {
  .bio-header {
    padding: 16px;

    .bio-title {
      h2 {
        font-size: 18px;
      }

      mat-icon {
        font-size: 24px;
        height: 24px;
        width: 24px;
      }
    }
  }

  .bio-content, .bio-edit {
    padding: 16px;
  }
}
