<div class="edit-post-container">
  <h2>Modifier le Post</h2>
  <form [formGroup]="postForm" (ngSubmit)="savePost()">
    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Titre</mat-label>
      <input matInput formControlName="jobTitle" required>
      <mat-error *ngIf="postForm.get('jobTitle')?.hasError('required')">
        Le titre est requis
      </mat-error>
    </mat-form-field>

    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Description</mat-label>
      <textarea matInput formControlName="description" rows="4" required></textarea>
      <mat-error *ngIf="postForm.get('description')?.hasError('required')">
        La description est requise
      </mat-error>
    </mat-form-field>

    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Entreprise</mat-label>
      <input matInput formControlName="location" required>
      <mat-error *ngIf="postForm.get('location')?.hasError('required')">
        L’entreprise est requise
      </mat-error>
    </mat-form-field>

    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Profil requis</mat-label>
      <input matInput formControlName="profileRequest" required>
      <mat-error *ngIf="postForm.get('profileRequest')?.hasError('required')">
        Le profil requis est requis
      </mat-error>
    </mat-form-field>

    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Type de contrat</mat-label>
      <input matInput formControlName="contractType" required>
      <mat-error *ngIf="postForm.get('contractType')?.hasError('required')">
        Le type de contrat est requis
      </mat-error>
    </mat-form-field>

    <div class="form-actions">
      <button mat-raised-button color="primary" type="submit" [disabled]="isLoading">
        {{ isLoading ? 'Enregistrement...' : 'Enregistrer' }}
      </button>
      <button mat-button color="warn" type="button" (click)="router.navigate(['/acceuilposts'])" [disabled]="isLoading">
        Annuler
      </button>
    </div>

    <div *ngIf="error" class="error-message">
      {{ error }}
    </div>
  </form>
</div>
