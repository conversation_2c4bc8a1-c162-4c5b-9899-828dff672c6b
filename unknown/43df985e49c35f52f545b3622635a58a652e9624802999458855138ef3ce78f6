import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import {SignUpComponent} from './sign-up/sign-up.component';
import {AuthCallbackComponent} from './auth-callback/auth-callback.component';
const routes: Routes = [

  {
    path: "sign-up",
    component: SignUpComponent
  },
  { path: 'callback', component: AuthCallbackComponent },


];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class AuthRoutingModule { }
