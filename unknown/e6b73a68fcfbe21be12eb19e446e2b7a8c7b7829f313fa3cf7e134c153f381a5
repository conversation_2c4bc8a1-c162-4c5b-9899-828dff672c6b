import { <PERSON>mpo<PERSON>, OnIni<PERSON>, Inject, ChangeDetector<PERSON>ef } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { CommonModule } from '@angular/common';
import {MatProgressSpinner} from '@angular/material/progress-spinner';
import {MatIcon} from '@angular/material/icon';
import {<PERSON><PERSON><PERSON><PERSON>ield, MatError, MatLabel} from '@angular/material/form-field';
import {MatButton, MatIconButton} from '@angular/material/button';
import {MatInput} from '@angular/material/input';
import {MatOption, MatSelect} from '@angular/material/select';
import {UserService} from '../../../services/user/user.service';

@Component({
  selector: 'app-complete-profile-modal',
  templateUrl: './complete-profile-modal.component.html',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>I<PERSON>,
    MatFormField,
    MatError,
    MatLabel,
    MatButton,
    MatInput,
    MatIconButton,
    MatSelect,
    MatOption
  ],
  styleUrls: ['./complete-profile-modal.component.scss']
})
export class CompleteProfileModalComponent implements OnInit {
  profileForm: FormGroup;
  isLoading = false;
  isSaving = false;
  errorMessage: string | null = null;
  successMessage: string | null = null;

  currentStep = 0;
  totalSteps = 0;
  missingItems: string[] = [];
  completedItems: string[] = [];

  showPersonalInfo = false;
  showSkills = false;
  showEducation = false;
  showExperience = false;
  showCertifications = false;
  showLinks = false;
  showCV = false;
  private userId: string | undefined;

  constructor(
    private fb: FormBuilder,
    private userService: UserService,
    public dialogRef: MatDialogRef<CompleteProfileModalComponent, boolean>,
    @Inject(MAT_DIALOG_DATA) public data: { user: any, userProfile: any, missingItems: string[] },
    private cdr: ChangeDetectorRef
  ) {
    this.profileForm = this.fb.group({
      firstName: ['', Validators.required],
      lastName: ['', Validators.required],
      email: ['', [Validators.required, Validators.email]],
      phoneNumber: [''],
      address: [''],
      dateOfBirth: [''],

      newSkill: this.fb.group({
        name: [''],
        level: ['']
      }),

      newEducation: this.fb.group({
        school: [''],
        degree: [''],
        fieldOfStudy: [''],
        startDate: [''],
        endDate: [''],
        description: ['']
      }),

      newExperience: this.fb.group({
        title: [''],
        company: [''],
        location: [''],
        startDate: [''],
        endDate: [''],
        description: ['']
      }),

      newCertification: this.fb.group({
        name: [''],
        issuingOrganization: [''],
        issueDate: [''],
        expirationDate: [''],
        credentialID: ['']
      }),

      newLink: this.fb.group({
        url: ['', Validators.pattern('https?://.+')]
      })
    });
  }

  ngOnInit(): void {
    this.initializeForm();
    this.setupMissingItems();
    this.userId = this.data?.user?.id;  // Assigner l'ID de l'utilisateur à partir des données reçues

  }

  initializeForm(): void {
    if (this.data && this.data.user) {
      this.profileForm.patchValue({

        firstName: this.data.user.firstName || '',
        lastName: this.data.user.lastName || '',
        email: this.data.user.email || '',
        phoneNumber: this.data.user.phoneNumber || '',
        address: this.data.user.address || '',
        dateOfBirth: this.data.user.dateOfBirth || ''
      });
    }
  }

  setupMissingItems(): void {
    if (this.data && this.data.missingItems) {
      this.missingItems = [...this.data.missingItems];
      this.totalSteps = this.missingItems.length;

      this.showPersonalInfo = this.missingItems.some(item =>
        ['Numéro de téléphone', 'Adresse', 'Date de naissance'].includes(item));
      this.showSkills = this.missingItems.includes('Compétences');
      this.showEducation = this.missingItems.includes('Formations');
      this.showExperience = this.missingItems.includes('Expériences');
      this.showCertifications = this.missingItems.includes('Certifications');
      this.showLinks = this.missingItems.includes('Liens');
      this.showCV = this.missingItems.includes('CV');

      this.goToFirstMissingItem();
    }
  }

  goToFirstMissingItem(): void {
    if (this.showPersonalInfo) this.currentStep = 0;
    else if (this.showSkills) this.currentStep = 1;
    else if (this.showEducation) this.currentStep = 2;
    else if (this.showExperience) this.currentStep = 3;
    else if (this.showCertifications) this.currentStep = 4;
    else if (this.showLinks) this.currentStep = 5;
    else if (this.showCV) this.currentStep = 6;
  }

  nextStep(): void {
    if (this.currentStep < 6) {
      this.currentStep++;
    } else {
      this.dialogRef.close(true); // Fermeture après la dernière étape
    }
  }

  prevStep(): void {
    if (this.currentStep > 0) this.currentStep--;
  }

  getProgressPercentage(): number {
    return Math.round((this.completedItems.length / this.totalSteps) * 100);
  }

  isSectionComplete(section: string): boolean {
    return this.completedItems.includes(section);
  }

  markSectionComplete(section: string): void {
    if (!this.completedItems.includes(section)) {
      this.completedItems.push(section);
    }
  }

  addSkill(): void {
    const skill = this.profileForm.get('newSkill')?.value;
    if (skill?.name) {
      this.isSaving = true;
      this.userService.addSkill(skill).subscribe({
        next: () => {
          this.isSaving = false;
          this.successMessage = 'Compétence ajoutée avec succès';
          this.markSectionComplete('Compétences');
          this.profileForm.get('newSkill')?.reset();
          this.cdr.detectChanges();
          this.autoNextStep();
        },
        error: err => {
          this.isSaving = false;
          this.errorMessage = 'Erreur lors de l\'ajout de la compétence: ' + (err.message || 'Veuillez réessayer');
          this.cdr.detectChanges();
        }
      });
    }
  }

  addEducation(): void {
    const education = this.profileForm.get('newEducation')?.value;
    if (education?.school && education?.degree) {
      this.isSaving = true;
      this.userService.addEducation(education).subscribe({
        next: () => {
          this.isSaving = false;
          this.successMessage = 'Formation ajoutée avec succès';
          this.markSectionComplete('Formations');
          this.profileForm.get('newEducation')?.reset();
          this.cdr.detectChanges();
          this.autoNextStep();
        },
        error: err => {
          this.isSaving = false;
          this.errorMessage = 'Erreur lors de l\'ajout de la formation: ' + (err.message || 'Veuillez réessayer');
          this.cdr.detectChanges();
        }
      });
    }
  }

  addExperience(): void {
    const exp = this.profileForm.get('newExperience')?.value;
    if (exp?.title && exp?.company) {
      this.isSaving = true;
      this.userService.addExperience(exp).subscribe({
        next: () => {
          this.isSaving = false;
          this.successMessage = 'Expérience ajoutée avec succès';
          this.markSectionComplete('Expériences');
          this.profileForm.get('newExperience')?.reset();
          this.cdr.detectChanges();
          this.autoNextStep();
        },
        error: err => {
          this.isSaving = false;
          this.errorMessage = 'Erreur lors de l\'ajout de l\'expérience: ' + (err.message || 'Veuillez réessayer');
          this.cdr.detectChanges();
        }
      });
    }
  }

  addCertification(): void {
    const cert = this.profileForm.get('newCertification')?.value;
    if (cert?.name && cert?.issuingOrganization) {
      this.isSaving = true;
      this.userService.addCertification(cert).subscribe({
        next: () => {
          this.isSaving = false;
          this.successMessage = 'Certification ajoutée avec succès';
          this.markSectionComplete('Certifications');
          this.profileForm.get('newCertification')?.reset();
          this.cdr.detectChanges();
          this.autoNextStep();
        },
        error: err => {
          this.isSaving = false;
          this.errorMessage = 'Erreur lors de l\'ajout de la certification: ' + (err.message || 'Veuillez réessayer');
          this.cdr.detectChanges();
        }
      });
    }
  }

  addLink(): void {
    const link = this.profileForm.get('newLink')?.value;
    if (link?.url) {
      this.isSaving = true;
      this.userService.addLink(link).subscribe({
        next: () => {
          this.isSaving = false;
          this.successMessage = 'Lien ajouté avec succès';
          this.markSectionComplete('Liens');
          this.profileForm.get('newLink')?.reset();
          this.cdr.detectChanges();
          this.autoNextStep();
        },
        error: err => {
          this.isSaving = false;
          this.errorMessage = 'Erreur lors de l\'ajout du lien: ' + (err.message || 'Veuillez réessayer');
          this.cdr.detectChanges();
        }
      });
    }
  }

  uploadCV(event: any): void {
    const file = event.target.files[0];
    if (file) {
      // Vérifier le type de fichier
      if (file.type !== 'application/pdf' && !file.type.includes('word')) {
        this.errorMessage = 'Format de fichier non supporté. Veuillez télécharger un fichier PDF ou Word.';
        this.cdr.detectChanges();
        return;
      }

      // Vérifier la taille du fichier (max 5MB)
      const maxSize = 5 * 1024 * 1024; // 5MB
      if (file.size > maxSize) {
        this.errorMessage = 'Le fichier est trop volumineux. La taille maximale est de 5MB.';
        this.cdr.detectChanges();
        return;
      }

      this.isSaving = true;
      this.errorMessage = null;

      this.userService.uploadCV(file).subscribe({
        next: (response) => {
          // Gérer la progression ou la réponse finale
          if (response && response.progress) {
            // Mise à jour de la progression si nécessaire
            console.log(`Upload progress: ${response.progress}%`);
          } else {
            // Téléchargement terminé
            this.isSaving = false;
            this.successMessage = 'CV téléchargé avec succès';
            this.markSectionComplete('CV');
            this.cdr.detectChanges();
            this.autoNextStep();
          }
        },
        error: err => {
          this.isSaving = false;
          this.errorMessage = 'Erreur lors du téléchargement du CV: ' + (err.message || 'Veuillez réessayer');
          this.cdr.detectChanges();
        }
      });
    }
  }

  updatePersonalInfo(): void {
    const info = {
      firstName: this.profileForm.get('firstName')?.value,
      lastName: this.profileForm.get('lastName')?.value,
      email: this.profileForm.get('email')?.value,
      phoneNumber: this.profileForm.get('phoneNumber')?.value,
      address: this.profileForm.get('address')?.value,
      dateOfBirth: this.profileForm.get('dateOfBirth')?.value
    };

    if (info.firstName && info.lastName && info.email) {
      this.isSaving = true;
      this.userService.updateFullUserProfile(info).subscribe({
        next: () => {
          this.isSaving = false;
          this.successMessage = 'Informations personnelles mises à jour';
          this.markSectionComplete('Informations personnelles');
          this.cdr.detectChanges();
          this.autoNextStep();
        },
        error: err => {
          this.isSaving = false;
          this.errorMessage = 'Erreur lors de la mise à jour: ' + (err.message || 'Veuillez réessayer');
          this.cdr.detectChanges();
        }
      });
    }
  }

  private autoNextStep(): void {
    if (this.currentStep < 6) {
      setTimeout(() => {
        this.successMessage = null;
        this.nextStep();
      }, 1000);
    } else {
      setTimeout(() => {
        this.successMessage = null;
        this.dialogRef.close(true);
      }, 1000);
    }
  }

  // Méthode pour fermer le modal
  cancel(): void {
    this.dialogRef.close(false);
  }

  // Méthode pour terminer le processus de complétion du profil
  completeProfile(): void {
    this.dialogRef.close(true);
  }
}
