import { TestBed } from '@angular/core/testing';
import { CanActivateFn } from '@angular/router';
import { notAuthenticatedGuard } from './not-authenticated.guard';


describe('notAuthenticatedGuard', () => {
  const executeGuard: CanActivateFn = (...guardParameters) =>
      TestBed.runInInjectionContext(() => notAuthenticatedGuard(...guardParameters));

  beforeEach(() => {
    TestBed.configureTestingModule({});
  });

  it('should be created', () => {
    expect(executeGuard).toBeTruthy();
  });
});
