import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { FormsModule } from '@angular/forms';
import { MatLineModule } from '@angular/material/core'; // Correction : MatLine -> MatLineModule
import { WorkspaceService } from '../../../services/workspace/workspace.service';

@Component({
  selector: 'app-workspaces',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatPaginatorModule,
    MatInputModule,
    MatFormFieldModule,
    FormsModule,
    MatLineModule, // Utilisé correctement ici
  ],
  templateUrl: './workspaces.component.html',
  styleUrls: ['./workspaces.component.css'],
})
export class WorkspacesComponent implements OnInit {
  workspaces: any[] = [];
  filteredWorkspaces: any[] = [];
  paginatedWorkspaces: any[] = [];
  loading = false;
  pageSize = 4;
  pageIndex = 0;
  searchTerm = '';

  constructor(private workspaceService: WorkspaceService) {}

  ngOnInit(): void {
    this.loadWorkspaces();
  }

  private loadWorkspaces(): void {
    this.loading = true;
    this.workspaceService.getWorkspacesForUser().subscribe({
      next: (data: any[]) => {
        this.workspaces = data ?? []; // Sécurité si data est null/undefined
        this.filteredWorkspaces = [...this.workspaces];
        this.loading = false;
        this.updatePaginatedWorkspaces();
        console.log('Workspaces loaded:', this.workspaces);
      },
      error: (error: any) => {
        console.error('Failed to load workspaces:', error);
        this.workspaces = [];
        this.filteredWorkspaces = [];
        this.loading = false;
        this.updatePaginatedWorkspaces();
      },
    });
  }

  filterWorkspaces(): void {
    this.pageIndex = 0;
    const searchLower = this.searchTerm.toLowerCase().trim();
    this.filteredWorkspaces = this.workspaces.filter((workspace) =>
      [
        workspace.name,
        workspace.description,
        workspace.location,
        workspace.phoneNumber,
        workspace.email,
      ].some((field) => field?.toLowerCase().includes(searchLower))
    );
    this.updatePaginatedWorkspaces();
  }

  private updatePaginatedWorkspaces(): void {
    const startIndex = this.pageIndex * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    this.paginatedWorkspaces = this.filteredWorkspaces.slice(startIndex, endIndex);
  }

  onPageChange(event: PageEvent): void {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.updatePaginatedWorkspaces();
  }
}
