<h2 mat-dialog-title>Modifier l'Utilisateur</h2>
<mat-dialog-content>
  <form [formGroup]="userForm" class="edit-user-form">
    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Prénom</mat-label>
      <input matInput formControlName="firstName" required>
      <mat-error *ngIf="userForm.get('firstName')?.hasError('required')">
        Le prénom est requis
      </mat-error>
    </mat-form-field>

    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Nom</mat-label>
      <input matInput formControlName="lastName" required>
      <mat-error *ngIf="userForm.get('lastName')?.hasError('required')">
        Le nom est requis
      </mat-error>
    </mat-form-field>

    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Email</mat-label>
      <input matInput formControlName="email" type="email" required>
      <mat-error *ngIf="userForm.get('email')?.hasError('required')">
        L'email est requis
      </mat-error>
      <mat-error *ngIf="userForm.get('email')?.hasError('email')">
        Veuillez entrer un email valide
      </mat-error>
    </mat-form-field>

    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Rôle</mat-label>
      <mat-select formControlName="role" required>
        <mat-option *ngFor="let role of roles" [value]="role">{{ role }}</mat-option>
      </mat-select>
      <mat-error *ngIf="userForm.get('role')?.hasError('required')">
        Le rôle est requis
      </mat-error>
    </mat-form-field>
  </form>
</mat-dialog-content>
<mat-dialog-actions align="end">
  <button mat-button (click)="onCancel()" [disabled]="isSaving">Annuler</button>
  <button mat-raised-button color="primary" (click)="onSave()" [disabled]="userForm.invalid || isSaving">
    <mat-spinner *ngIf="isSaving" diameter="20"></mat-spinner>
    <span *ngIf="!isSaving">Enregistrer</span>
  </button>
</mat-dialog-actions>
