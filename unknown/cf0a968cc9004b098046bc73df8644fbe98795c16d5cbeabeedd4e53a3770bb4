/* General Styling */
.app-bar {
  padding: 10px 20px;
  position: sticky;
  top: 0;
  z-index: 1000;
  width: 100%;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  background: linear-gradient(135deg, rgb(255, 255, 255), rgba(255, 255, 255, 0.51)); /* Gradient background */
}

/* Left Section: Logo and Language button */
.left-section {
  display: flex;
  align-items: center;
  gap: 20px; /* Space between logo and language button */
}

.logo {
  height: 60px; /* Adjust logo size */
  transition: transform 0.3s ease-in-out;
}

.logo:hover {
  transform: scale(1.1); /* Slight zoom effect on logo hover */
}

nav {
  display: flex;
  gap: 30px;
  align-items: center;
}

nav a {
  font-size: 16px;
  text-transform: uppercase;
  font-weight: bold;
  color: rgba(0, 0, 0, 0.44);
  padding: 8px 12px;
  border-radius: 5px;
  transition: background 0.3s, color 0.3s;
}

nav a:hover {
  background-color: rgba(0, 0, 0, 0.15); /* Hover effect */
  color: #011257; /* Change text color on hover */
}

/* Auth Buttons */
.auth-buttons button {
  padding: 8px 18px;
  font-size: 14px;
  margin-left: 15px;
  font-weight: bold;
  background-color: rgba(0, 0, 0, 0.2);
  color: #000000;
  transition: background 0.3s, transform 0.3s;
}

.auth-buttons button:hover {
  background-color: #011257;
  color: beige;
  transition: background 0.3s, transform 0.3s;
}

.auth-buttons .signup-btn {
  background-color: rgba(0, 0, 0, 0.2);
}

.auth-buttons .signup-btn:hover {
  background-color: #011257;
  color: beige;
}

/* Menu (Langue) */
mat-menu {
  background-color: #000000;
}

mat-menu-item {
  font-weight: bold;
}

/* Spacer */
.spacer {
  flex: 1 1 auto;
}

/* Responsiveness */
@media (max-width: 1024px) {
  nav {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }

  .auth-buttons {
    margin-top: 10px;
  }

  .left-section {
    gap: 10px;
  }

  .logo {
    height: 30px; /* Resize logo for smaller screens */
  }
}
