<div class="bio-container" [@fadeInOut]>
  <div class="bio-card">
    <div class="bio-header">
      <div class="bio-title">
        <mat-icon>person_outline</mat-icon>
        <h2>À propos de moi</h2>
      </div>
      <div class="bio-actions">
        <button mat-icon-button *ngIf="!isEditing && bio" (click)="toggleExpand()" class="expand-button">
          <mat-icon [@rotateIcon]="isExpanded ? 'expanded' : 'collapsed'">expand_more</mat-icon>
        </button>
        <button mat-icon-button *ngIf="!isEditing" (click)="toggleEdit()" class="edit-button">
          <mat-icon>edit</mat-icon>
        </button>
      </div>
    </div>

    <div class="bio-content" *ngIf="!isEditing" [@expandCollapse]="isExpanded ? 'expanded' : 'collapsed'">
      <p *ngIf="bio">{{ bio }}</p>
      <p *ngIf="!bio" class="empty-bio">
        <mat-icon>info</mat-icon>
        <span>Ajoutez une bio pour vous présenter aux autres utilisateurs.</span>
        <button mat-stroked-button color="primary" (click)="toggleEdit()">
          <mat-icon>add</mat-icon>
          Ajouter ma bio
        </button>
      </p>
    </div>

    <div class="bio-edit" *ngIf="isEditing" [@fadeInOut]>
      <mat-form-field class="full-width">
        <mat-label>Votre bio</mat-label>
        <textarea
          matInput
          [(ngModel)]="tempBio"
          rows="5"
          placeholder="Parlez de vous, de votre parcours, de vos compétences..."
        ></textarea>
        <mat-hint>Décrivez-vous en quelques phrases pour que les autres utilisateurs puissent mieux vous connaître.</mat-hint>
      </mat-form-field>

      <div class="error-message" *ngIf="errorMessage">
        <mat-icon>error</mat-icon>
        <span>{{ errorMessage }}</span>
      </div>

      <div class="bio-edit-actions">
        <button mat-stroked-button (click)="toggleEdit()" [disabled]="isSaving">
          Annuler
        </button>
        <button mat-raised-button color="primary" (click)="saveBio()" [disabled]="isSaving">
          <mat-icon *ngIf="isSaving" class="spinner">autorenew</mat-icon>
          <span *ngIf="!isSaving">Enregistrer</span>
        </button>
      </div>
    </div>
  </div>
</div>
