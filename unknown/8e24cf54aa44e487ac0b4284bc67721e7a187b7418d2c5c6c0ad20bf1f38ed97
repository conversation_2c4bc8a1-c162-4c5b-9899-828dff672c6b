import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { environment } from '../../../environments/environment';

interface Candidate {
  name: string;
  userId: string;
  id: string;
  email: string;
  // autres propriétés pertinentes
}

@Injectable({
  providedIn: 'root'
})
export class JobApplicationService {
  private apiUrl = `${environment.apiUrl}/job-applications`;

  constructor(private http: HttpClient) {}

  getCandidates(postId: string): Observable<any[]> {
    return this.http.get<any[]>(`${this.apiUrl}/post/${postId}/candidates`);

  }

  hasApplied(postId: string, userId: string): Observable<boolean> {
    return this.http.get<boolean>(`${this.apiUrl}/check/${postId}/${userId}`).pipe(
      catchError(error => {
        console.error('Error checking application status:', error);
        return throwError(() => error);
      })
    );
  }

  saveApplication(formData: FormData): Observable<any> {
    return this.http.post(this.apiUrl, formData).pipe(
      catchError(error => {
        console.error('Error saving application:', error);
        return throwError(() => error);
      })
    );
  }

  acceptApplication(applicationId: string): Observable<any> {
    return this.http.post<any>(`${this.apiUrl}/${applicationId}/accept`, {}).pipe(
      catchError(error => {
        console.error('Error accepting application:', error);
        return throwError(() => error);
      })
    );
  }

  rejectApplication(applicationId: string): Observable<any> {
    return this.http.post<any>(`${this.apiUrl}/${applicationId}/reject`, {}).pipe(
      catchError(error => {
        console.error('Error rejecting application:', error);
        return throwError(() => error);
      })
    );
  }
}
