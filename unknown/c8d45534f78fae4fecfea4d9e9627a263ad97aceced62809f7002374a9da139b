import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, throwError, of, BehaviorSubject } from 'rxjs';
import { catchError, map, switchMap, tap } from 'rxjs/operators';
import { OidcSecurityService } from 'angular-auth-oidc-client';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class SavedItemsService {
  private apiUrl = `${environment.apiUrl}/saved-items`;

  // BehaviorSubject pour suivre les publications sauvegardées
  private savedItemsSubject = new BehaviorSubject<string[]>([]);
  savedItems$ = this.savedItemsSubject.asObservable();

  // Stockage local des IDs des publications sauvegardées
  private savedItemsIds: string[] = [];

  constructor(
    private http: HttpClient,
    private oidcSecurityService: OidcSecurityService
  ) {
    // Charger les éléments sauvegardés au démarrage
    this.loadSavedItems();
  }

  // Obtenir les en-têtes d'authentification
  private getAuthHeaders(): Observable<HttpHeaders> {
    return this.oidcSecurityService.getAccessToken().pipe(
      map(token => {
        return new HttpHeaders().set('Authorization', `Bearer ${token}`);
      })
    );
  }

  // Charger les éléments sauvegardés depuis le localStorage ou le serveur
  private loadSavedItems(): void {
    // Pour l'instant, nous utilisons le localStorage pour stocker les IDs
    const savedItems = localStorage.getItem('savedItems');
    if (savedItems) {
      this.savedItemsIds = JSON.parse(savedItems);
      this.savedItemsSubject.next(this.savedItemsIds);
    }

    // Dans une implémentation future, vous pourriez charger depuis le serveur
    // this.getSavedItems().subscribe();
  }

  // Vérifier si un élément est sauvegardé
  isItemSaved(postId: string): boolean {
    // S'assurer que savedItemsIds est à jour avec le localStorage
    const savedItems = localStorage.getItem('savedItems');
    if (savedItems) {
      this.savedItemsIds = JSON.parse(savedItems);
    }
    return this.savedItemsIds.includes(postId);
  }

  // Sauvegarder un élément
  saveItem(post: any): Observable<boolean> {
    console.log('Saving item:', post);

    // Vérifier si l'objet post est valide
    if (!post || !post.id) {
      console.error('Invalid post object:', post);
      return throwError(() => new Error('Invalid post object'));
    }

    // Si l'élément est déjà sauvegardé, ne rien faire
    if (this.isItemSaved(post.id)) {
      console.log('Item already saved:', post.id);
      return of(true);
    }

    // Ajouter l'ID à la liste locale
    this.savedItemsIds.push(post.id);
    localStorage.setItem('savedItems', JSON.stringify(this.savedItemsIds));

    // Sauvegarder l'objet post complet dans le localStorage
    let savedPosts = JSON.parse(localStorage.getItem('savedPosts') || '[]');
    savedPosts.push(post);
    localStorage.setItem('savedPosts', JSON.stringify(savedPosts));

    this.savedItemsSubject.next(this.savedItemsIds);

    console.log('Item saved successfully:', post.id);
    console.log('Current saved items:', this.savedItemsIds);

    // Dans une implémentation future, vous pourriez sauvegarder sur le serveur
    // return this.getAuthHeaders().pipe(
    //   switchMap(headers =>
    //     this.http.post<any>(`${this.apiUrl}`, { post }, { headers }).pipe(
    //       catchError(error => {
    //         console.error('Error saving item:', error);
    //         return throwError(() => error);
    //       })
    //     )
    //   )
    // );

    return of(true);
  }

  // Supprimer un élément sauvegardé
  removeItem(postId: string): Observable<boolean> {
    console.log('Removing item:', postId);

    // Vérifier si l'ID est valide
    if (!postId) {
      console.error('Invalid post ID:', postId);
      return throwError(() => new Error('Invalid post ID'));
    }

    // Si l'élément n'est pas sauvegardé, ne rien faire
    if (!this.isItemSaved(postId)) {
      console.log('Item not saved, nothing to remove:', postId);
      return of(false);
    }

    // Supprimer l'ID de la liste locale
    this.savedItemsIds = this.savedItemsIds.filter(id => id !== postId);
    localStorage.setItem('savedItems', JSON.stringify(this.savedItemsIds));

    // Supprimer l'objet post du localStorage
    let savedPosts = JSON.parse(localStorage.getItem('savedPosts') || '[]');
    savedPosts = savedPosts.filter((post: any) => post.id !== postId);
    localStorage.setItem('savedPosts', JSON.stringify(savedPosts));

    this.savedItemsSubject.next(this.savedItemsIds);

    console.log('Item removed successfully:', postId);
    console.log('Current saved items:', this.savedItemsIds);

    // Dans une implémentation future, vous pourriez supprimer du serveur
    // return this.getAuthHeaders().pipe(
    //   switchMap(headers =>
    //     this.http.delete<any>(`${this.apiUrl}/${postId}`, { headers }).pipe(
    //       catchError(error => {
    //         console.error('Error removing saved item:', error);
    //         return throwError(() => error);
    //       })
    //     )
    //   )
    // );

    return of(true);
  }

  // Obtenir tous les IDs des éléments sauvegardés
  getSavedItems(): Observable<string[]> {
    // Pour l'instant, nous retournons simplement la liste locale
    return of(this.savedItemsIds);

    // Dans une implémentation future, vous pourriez charger depuis le serveur
    // return this.getAuthHeaders().pipe(
    //   switchMap(headers =>
    //     this.http.get<any[]>(`${this.apiUrl}`, { headers }).pipe(
    //       map(items => items.map(item => item.postId)),
    //       tap(ids => {
    //         this.savedItemsIds = ids;
    //         this.savedItemsSubject.next(this.savedItemsIds);
    //       }),
    //       catchError(error => {
    //         console.error('Error getting saved items:', error);
    //         return throwError(() => error);
    //       })
    //     )
    //   )
    // );
  }

  // Obtenir tous les posts sauvegardés
  getSavedPosts(): any[] {
    return JSON.parse(localStorage.getItem('savedPosts') || '[]');
  }

  // Basculer l'état sauvegardé d'un élément (ajouter ou supprimer)
  toggleSavedItem(post: any): Observable<boolean> {
    console.log('Toggling saved item:', post);
    if (!post || !post.id) {
      console.error('Invalid post object:', post);
      return throwError(() => new Error('Invalid post object'));
    }

    if (this.isItemSaved(post.id)) {
      console.log('Removing saved item:', post.id);
      return this.removeItem(post.id);
    } else {
      console.log('Saving item:', post.id);
      return this.saveItem(post);
    }
  }
}
