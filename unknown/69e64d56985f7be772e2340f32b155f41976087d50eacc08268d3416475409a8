import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import {UserService} from '../../../services/user/user.service';
@Component({
  selector: 'app-auth-callback',
  standalone: true,
  template: `<p>Authentification en cours...</p>`
})
export class AuthCallbackComponent implements OnInit {

  constructor(
    private route: ActivatedRoute,
    private userService: UserService,
    private router: Router
  ) {}
  ngOnInit() {
    this.route.queryParams.subscribe(params => {
      const code = params['code'];
      if (code) {
        this.userService.loginWithGitHub(code).subscribe((response: any) => {
          this.router.navigate(['/dashboard']).then();
        });
      }
    });
  }
}
