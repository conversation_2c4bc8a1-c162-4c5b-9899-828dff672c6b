/* src/app/protected/manage-invitations/manage-invitations.component.css */
:root {
  --primary-color: #6366f1;
  --accent-color: #f472b6;
  --background-color: #f8fafc;
  --card-background: #ffffff;
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --border-color: #e5e7eb;
  --shadow: 0 6px 20px rgba(0, 0, 0, 0.06);
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --gradient-primary: linear-gradient(135deg, #6366f1, #a5b4fc);
  --gradient-accent: linear-gradient(135deg, #f472b6, #fda4af);
  --stats-color-1: linear-gradient(135deg, #c7d2fe, #a5b4fc);
  --stats-color-2: linear-gradient(135deg, #fef9c3, #fef08a);
  --stats-color-3: linear-gradient(135deg, #d1fae5, #a7f3d0);
  --stats-color-4: linear-gradient(135deg, #f5f5f5, #e5e7eb);
  --stats-color-5: linear-gradient(135deg, #fee2e2, #fecaca);
  --stats-icon-color-1: #4f46e5;
  --stats-icon-color-2: #f59e0b;
  --stats-icon-color-3: #10b981;
  --stats-icon-color-4: #6b7280;
  --stats-icon-color-5: #ef4444;
}

/* Dark mode */
body.dark-mode {
  --primary-color: #a5b4fc;
  --accent-color: #f9a8d4;
  --background-color: #111827;
  --card-background: #1f2937;
  --text-primary: #f9fafb;
  --text-secondary: #9ca3af;
  --border-color: #374151;
  --shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
  --success-color: #34d399;
  --warning-color: #fbbf24;
  --error-color: #f87171;
  --gradient-primary: linear-gradient(135deg, #a5b4fc, #6366f1);
  --gradient-accent: linear-gradient(135deg, #f9a8d4, #f472b6);
  --stats-color-1: linear-gradient(135deg, #4b5563, #6b7280);
  --stats-color-2: linear-gradient(135deg, #92400e, #6b7280);
  --stats-color-3: linear-gradient(135deg, #2d4e3b, #6b7280);
  --stats-color-4: linear-gradient(135deg, #374151, #4b5563);
  --stats-color-5: linear-gradient(135deg, #7f1d1d, #991b1b);
}

.manage-invitations-container {
  display: flex;
  min-height: 100vh;
  background: var(--background-color);
  font-family: 'Inter', sans-serif;
  transition: background 0.3s ease;
}

body.dark-mode .manage-invitations-container {
  background: var(--background-color);
}

app-sidebar {
  width: 260px;
  background: var(--card-background);
  border-right: 1px solid var(--border-color);
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  color: var(--text-primary);
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.05);
  transition: width 0.3s ease, transform 0.3s ease;
  z-index: 1000;
}

body.dark-mode app-sidebar {
  background: var(--card-background);
  border-right: 1px solid var(--border-color);
}

.content-container {
  margin-left: 260px;
  flex: 1;
  padding: 40px;
  max-width: 1600px;
  transition: margin-left 0.3s ease;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40px;
  background: var(--card-background);
  padding: 24px 32px;
  border-radius: 16px;
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow);
  animation: slideIn 0.5s ease-in-out;
}

h1 {
  font-size: 32px;
  font-weight: 800;
  color: var(--text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 10px;
  letter-spacing: -0.5px;
}

h1::before {
  content: '📋';
  font-size: 28px;
  animation: bounce 0.5s ease-in-out;
}

.header-actions {
  display: flex;
  gap: 16px;
  align-items: center;
}

.back-button {
  padding: 12px 24px;
  border-radius: 10px;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background: var(--gradient-accent);
  color: #ffffff;
}

.back-button:hover {
  background: linear-gradient(135deg, #ec4899, #f43f5e);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(244, 63, 94, 0.3);
}

.back-button mat-icon {
  margin-right: 8px;
}

/* Error Message */
.error-message {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--error-color);
  background: #fef2f2;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: var(--shadow);
}

body.dark-mode .error-message {
  background: #991b1b;
  color: #ffffff;
}

/* Quick Stats Panel */
.quick-stats-container {
  margin-bottom: 40px;
  background: var(--card-background);
  border-radius: 16px;
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow);
  overflow: hidden;
  animation: slideIn 0.5s ease-in-out;
}

.quick-stats-content {
  padding: 24px 32px;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 20px;
  background: var(--card-background);
  animation: fadeIn 0.3s ease-in-out;
}

.stats-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  border-radius: 12px;
  transition: all 0.3s ease;
  flex: 1;
  min-width: 220px;
  border: 1px solid var(--border-color);
}

.stats-item:nth-child(1) {
  background: var(--stats-color-1);
}

.stats-item:nth-child(2) {
  background: var(--stats-color-2);
}

.stats-item:nth-child(3) {
  background: var(--stats-color-3);
}

.stats-item:nth-child(4) {
  background: var(--stats-color-4);
}

.stats-item:nth-child(5) {
  background: var(--stats-color-5);
}

.stats-item:hover {
  background: linear-gradient(135deg, #93c5fd, #60a5fa);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

body.dark-mode .stats-item:hover {
  background: linear-gradient(135deg, #6b7280, #9ca3af);
}

.stats-item:nth-child(1) mat-icon {
  color: var(--stats-icon-color-1);
}

.stats-item:nth-child(2) mat-icon {
  color: var(--stats-icon-color-2);
}

.stats-item:nth-child(3) mat-icon {
  color: var(--stats-icon-color-3);
}

.stats-item:nth-child(4) mat-icon {
  color: var(--stats-icon-color-4);
}

.stats-item:nth-child(5) mat-icon {
  color: var(--stats-icon-color-5);
}

.stats-item mat-icon {
  font-size: 28px;
  width: 28px;
  height: 28px;
  line-height: 28px;
  transition: transform 0.3s ease;
}

.stats-item:hover mat-icon {
  transform: scale(1.1);
}

.stats-item h3 {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-secondary);
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.8px;
}

.stats-item p {
  font-size: 20px;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
}

/* Table Section */
.table-container {
  width: 100%;
  overflow-x: auto;
  background: var(--card-background);
  border-radius: 16px;
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow);
  padding: 24px 32px;
  animation: fadeIn 0.5s ease-in-out;
}

table {
  width: 100%;
  border-collapse: collapse;
}

th, td {
  padding: 16px 20px;
  text-align: left;
  transition: background-color 0.3s ease;
}

th {
  background: #f1f5f9;
  font-weight: 700;
  color: var(--text-secondary);
  border-bottom: 2px solid var(--border-color);
  text-transform: uppercase;
  font-size: 12px;
  letter-spacing: 1px;
}

body.dark-mode th {
  background: #2d3748;
}

td {
  border-bottom: 1px solid var(--border-color);
  color: var(--text-primary);
  font-size: 14px;
  font-weight: 500;
}

tr {
  transition: all 0.3s ease;
}

tr:hover {
  background: #e0e7ff;
  transform: scale(1.005);
}

body.dark-mode tr:hover {
  background: #4b5563;
}

button[mat-icon-button] {
  margin: 0 6px;
  transition: all 0.3s ease;
}

button[mat-icon-button]:hover {
  transform: scale(1.15);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

button[color="primary"] {
  color: var(--primary-color);
}

button[color="warn"] {
  color: var(--error-color);
}

.spinner {
  margin: 40px auto;
  color: var(--primary-color);
}

body.dark-mode .spinner {
  color: var(--accent-color);
}

/* Status styling */
.status-pending {
  color: var(--warning-color);
  font-weight: 600;
  background: #fefce8;
  padding: 6px 12px;
  border-radius: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.status-accepted {
  color: var(--success-color);
  font-weight: 600;
  background: #ecfdf5;
  padding: 6px 12px;
  border-radius: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.status-rejected {
  color: var(--error-color);
  font-weight: 600;
  background: #fef2f2;
  padding: 6px 12px;
  border-radius: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.status-deleted {
  color: #6b7280;
  font-weight: 600;
  background: #f3f4f6;
  padding: 6px 12px;
  border-radius: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

body.dark-mode .status-pending {
  background: #713f12;
  color: #ffffff;
}

body.dark-mode .status-accepted {
  background: #064e3b;
  color: #ffffff;
}

body.dark-mode .status-rejected {
  background: #991b1b;
  color: #ffffff;
}

body.dark-mode .status-deleted {
  background: #4b5563;
  color: #ffffff;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  padding: 16px 0;
}

.pagination button {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.pagination button:hover {
  background: #e0e7ff;
  transform: translateY(-2px);
}

body.dark-mode .pagination button:hover {
  background: #4b5563;
}

.pagination button[disabled] {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination span {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
}

/* Floating Action Button */
.fab {
  position: fixed;
  bottom: 30px;
  right: 30px;
  background: var(--gradient-primary);
  color: #ffffff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  animation: bounceIn 0.5s ease-in-out;
}

.fab:hover {
  background: linear-gradient(135deg, #4f46e5, #818cf8);
  transform: rotate(90deg);
  box-shadow: 0 6px 16px rgba(79, 70, 229, 0.3);
}

/* Animations */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounceIn {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  60% {
    transform: scale(1.2);
    opacity: 1;
  }
  100% {
    transform: scale(1);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .content-container {
    padding: 32px;
  }

  app-sidebar {
    width: 220px;
  }

  .content-container {
    margin-left: 220px;
  }

  h1 {
    font-size: 28px;
  }

  .quick-stats-content {
    flex-direction: column;
    gap: 16px;
  }

  .stats-item {
    justify-content: flex-start;
    text-align: left;
  }
}

@media (max-width: 768px) {
  .manage-invitations-container {
    flex-direction: column;
  }

  app-sidebar {
    width: 100%;
    height: auto;
    position: relative;
    transform: translateY(0);
  }

  app-sidebar[style*="display: none"] {
    transform: translateY(-100%);
  }

  .content-container {
    margin-left: 0;
    padding: 20px;
  }

  h1 {
    font-size: 24px;
  }

  .header-section {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .header-actions {
    width: 100%;
    justify-content: space-between;
  }
}

@media (max-width: 480px) {
  .header-actions {
    flex-direction: column;
    gap: 12px;
  }

  .back-button {
    width: 100%;
    text-align: center;
  }

  .fab {
    bottom: 20px;
    right: 20px;
  }
}
