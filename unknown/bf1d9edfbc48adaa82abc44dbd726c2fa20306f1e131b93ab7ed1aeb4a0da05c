import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ReactionService {
  private apiUrl = 'http://localhost:8081/api/reactions';

  constructor(private http: HttpClient) {}

  addReaction(postId: string, userId: number, reactionType: string): Observable<any> {
    const payload = { postId, userId, reactionType };
    return this.http.post(this.apiUrl, payload); // Line 15
  }


  getReactionCounts(postId: string): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/counts/${postId}`);
  }

  getUserReaction(postId: string, userId: number): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/${postId}/${userId}`);
  }
}
