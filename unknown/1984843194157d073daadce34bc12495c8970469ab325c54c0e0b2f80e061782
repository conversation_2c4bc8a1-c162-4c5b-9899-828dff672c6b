import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { MatTableModule } from '@angular/material/table';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTooltipModule } from '@angular/material/tooltip';
import { InvitationService } from '../../../services/invitation/invitation.service';
import { SidebarComponent } from '../../sidebar/sidebar.component';

@Component({
  selector: 'app-manage-invitations',
  standalone: true,
  imports: [
    CommonModule,
    MatTableModule,
    MatIconModule,
    MatButtonModule,
    MatProgressSpinnerModule,
    MatTooltipModule,
    SidebarComponent
  ],
  templateUrl: './manage-invitations.component.html',
  styleUrls: ['./manage-invitations.component.css']
})
export class ManageInvitationsComponent implements OnInit {
  invitations: any[] = [];
  displayedColumns: string[] = ['id', 'sender', 'receiver', 'workspace', 'message', 'createdAt', 'status', 'actions'];
  isLoading = true;
  page = 0;
  size = 10;
  totalInvitations = 0;
  errorMessage: string | null = null;

  constructor(
    private invitationsService: InvitationService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.fetchInvitations();
  }

  fetchInvitations(): void {
    this.isLoading = true;
    this.errorMessage = null;
    this.invitationsService.getInvitations(this.page, this.size).subscribe({
      next: (response) => {
        this.invitations = response.content.map((invitation: any) => ({
          id: invitation.id,
          sender: invitation.sender?.username || 'N/A', // Assuming User has a username field
          receiver: invitation.receiver?.username || 'N/A',
          workspace: invitation.workspace?.name || 'N/A', // Assuming Workspace has a name field
          message: invitation.message || 'N/A',
          createdAt: invitation.createdAt || 'N/A',
          status: invitation.status || 'PENDING'
        }));
        this.totalInvitations = response.totalElements || 0; // Use totalElements from response
        this.isLoading = false;
      },
      error: (error) => {
        this.errorMessage = error.message || 'Failed to load invitations. Please try again.';
        this.isLoading = false;
      }
    });
  }

  markAsDeleted(invitation: any): void {
    if (invitation.status === 'DELETED') {
      return; // Already deleted, no action needed
    }
    this.invitationsService.updateStatus(invitation.id, 'DELETED').subscribe({
      next: () => {
        invitation.status = 'DELETED'; // Update the UI
      },
      error: (error) => {
        this.errorMessage = error.message || 'Failed to mark invitation as deleted. Please try again.';
      }
    });
  }

  deleteInvitation(invitation: any): void {
    this.invitationsService.deleteInvitation(invitation.id).subscribe({
      next: () => {
        // Remove the deleted invitation from the list
        this.invitations = this.invitations.filter(i => i.id !== invitation.id);
        // Fetch the updated total from the backend
        this.invitationsService.getInvitations(this.page, this.size).subscribe({
          next: (response) => {
            this.totalInvitations = response.totalElements || 0;
          },
          error: (error) => {
            this.errorMessage = error.message || 'Failed to refresh total invitations. Please try again.';
          }
        });
      },
      error: (error) => {
        this.errorMessage = error.message || 'Failed to delete invitation. Please try again.';
      }
    });
  }

  goBack(): void {
    this.router.navigate(['/admin/dashboard']);
  }

  refresh(): void {
    this.page = 0;
    this.fetchInvitations();
  }

  previousPage(): void {
    if (this.page > 0) {
      this.page--;
      this.fetchInvitations();
    }
  }

  nextPage(): void {
    if (this.invitations.length === this.size) {
      this.page++;
      this.fetchInvitations();
    }
  }
}
