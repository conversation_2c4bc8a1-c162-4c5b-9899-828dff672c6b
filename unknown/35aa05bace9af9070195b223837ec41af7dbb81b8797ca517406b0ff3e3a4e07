<div class="dashboard">
  <app-sidebar-recruiter></app-sidebar-recruiter>

  <main class="dashboard-main">
    <header class="dashboard-header">
      <h1 class="dashboard-title">Tableau de Bord</h1>
      <div class="search-container">
        <input class="search-bar" type="text" placeholder="Rechercher..." (input)="filterDashboard($event)">
        <mat-icon class="search-icon">search</mat-icon>
      </div>
      <button mat-button color="accent" class="profile-button">
        <mat-icon>account_circle</mat-icon> Mon Profil
      </button>
    </header>

    <div class="card-container">
      <mat-card class="dashboard-card" (click)="goToWorkspace()">
        <mat-card-header>
          <mat-icon class="card-icon">work</mat-icon>
          <mat-card-title>Voir votre workspace</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <p>Accédez à votre espace de travail</p>
          <button mat-button color="primary" class="card-button">Accéder</button>
        </mat-card-content>
      </mat-card>

      <mat-card class="dashboard-card" (click)="goToPosts()">
        <mat-card-header>
          <mat-icon class="card-icon">post_add</mat-icon>
          <mat-card-title>Voir vos posts</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <p>Découvrez vos offres d’emploi publiées et leur statut.</p>
          <button mat-button color="primary" class="card-button">Voir plus</button>
        </mat-card-content>
      </mat-card>

      <mat-card class="dashboard-card" (click)="goToSettings()">
        <mat-card-header>
          <mat-icon class="card-icon">settings</mat-icon>
          <mat-card-title>Configurer votre compte</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <p>Modifiez vos paramètres et préférences.</p>
          <button mat-button color="primary" class="card-button">Configurer</button>
        </mat-card-content>
      </mat-card>

      <mat-card class="dashboard-card" (click)="prepareTest()">
        <mat-card-header>
          <mat-icon class="card-icon">assignment</mat-icon>
          <mat-card-title>Préparer un test</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <p>Créez un nouveau test technique personnalisé à envoyer aux candidats.</p>
          <button mat-button color="primary" class="card-button">Créer un test</button>
        </mat-card-content>
      </mat-card>

      <mat-card class="dashboard-card" (click)="goToTestResults()">
        <mat-card-header>
          <mat-icon class="card-icon">assignment_turned_in</mat-icon>
          <mat-card-title>Voir les résultats des tests</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <p>Consultez les résultats des tests des candidats.</p>
          <button mat-button color="primary" class="card-button">Voir plus</button>
        </mat-card-content>
      </mat-card>
    </div>
  </main>
</div>

<router-outlet></router-outlet>
