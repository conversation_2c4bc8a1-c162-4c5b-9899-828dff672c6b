import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, catchError, switchMap, throwError } from 'rxjs';
import { OidcSecurityService } from 'angular-auth-oidc-client';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class FileService {
  private apiUrl = `${environment.apiUrl}/files`;

  constructor(
    private http: HttpClient,
    private oidcSecurityService: OidcSecurityService
  ) { }

  /**
   * Vérifie si le dossier d'upload existe et le crée si nécessaire
   */
  ensureUploadDirectoryExists(): Observable<any> {
    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap(token => {
        if (!token) {
          throw new Error('No access token available');
        }
        
        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
        return this.http.post(`${this.apiUrl}/ensure-upload-directory`, {}, { headers });
      }),
      catchError(error => {
        console.error('Error ensuring upload directory exists:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Télécharge un fichier sur le serveur
   */
  uploadFile(file: File, path: string): Observable<any> {
    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap(token => {
        if (!token) {
          throw new Error('No access token available');
        }
        
        const formData = new FormData();
        formData.append('file', file);
        formData.append('path', path);
        
        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
        return this.http.post(`${this.apiUrl}/upload`, formData, { headers });
      }),
      catchError(error => {
        console.error('Error uploading file:', error);
        return throwError(() => error);
      })
    );
  }
}
