import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Observable } from 'rxjs';
import { switchMap } from 'rxjs/operators';
import { CommonModule } from '@angular/common';
import { MatTableModule } from '@angular/material/table';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTooltipModule } from '@angular/material/tooltip';
import {SidebarComponent} from '../../sidebar/sidebar.component';
import {PostsService} from '../../../services/posts/posts.service';

@Component({
  selector: 'app-manage-offers',
  standalone: true,
  imports: [
    CommonModule,
    MatTableModule,
    MatIconModule,
    MatButtonModule,
    MatProgressSpinnerModule,
    MatTooltipModule,
    SidebarComponent
  ],
  templateUrl: './manage-offers.component.html',
  styleUrls: ['./manage-offers.component.css']
})
export class ManageOffersComponent implements OnInit {
  posts: any[] = [];
  displayedColumns: string[] = ['id', 'title', 'company', 'location', 'createdAt', 'status', 'actions'];
  isLoading = true;
  page = 0;
  size = 10;
  totalPosts = 0;
  errorMessage: string | null = null;

  constructor(
    private postsService: PostsService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.fetchPosts();
  }

  fetchPosts(): void {
    this.isLoading = true;
    this.errorMessage = null;
    this.postsService.getPosts(this.page, this.size).subscribe({
      next: (response) => {
        // Map the API response fields to match the HTML expectations
        this.posts = response.content.map((post: any) => ({
          id: post.id, // Map _id to id
          title: post.jobTitle || post.titre || 'N/A', // Map jobTitle or titre to title
          company: post.entreprise || 'N/A', // Map entreprise to company
          location: post.location || 'N/A', // Map location to location
          createdAt: post.datePublication || 'N/A', // Map datePublication to createdAt
          status: post.status || 'pending' // Default to 'pending' since status is missing
        }));
        this.totalPosts = response.totalElements || this.posts.length;
        this.isLoading = false;
      },
      error: (error) => {
        this.errorMessage = error.message || 'Failed to load posts. Please try again.';
        this.isLoading = false;
      }
    });
  }

  goBack(): void {
    this.router.navigate(['/admin/dashboard']);
  }

  refresh(): void {
    this.page = 0;
    this.fetchPosts();
  }

  previousPage(): void {
    if (this.page > 0) {
      this.page--;
      this.fetchPosts();
    }
  }

  nextPage(): void {
    if (this.posts.length === this.size) {
      this.page++;
      this.fetchPosts();
    }
  }
}
