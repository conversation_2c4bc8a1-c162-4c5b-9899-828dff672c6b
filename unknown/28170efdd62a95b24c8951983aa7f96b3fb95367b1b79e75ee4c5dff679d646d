import {Component} from '@angular/core';
import {MatMenu, MatMenuItem, MatMenuTrigger} from '@angular/material/menu';
import {MatIcon} from '@angular/material/icon';
import {MatToolbar} from '@angular/material/toolbar';
import {MatAnchor, MatButton, MatIconButton} from '@angular/material/button';
import {TranslatePipe} from '@ngx-translate/core';
import {changeLanguage} from 'i18next';
import {Router, RouterOutlet} from '@angular/router';
import {NgForOf} from '@angular/common';
import {MatCardModule} from '@angular/material/card';

@Component({
  selector: 'app-nav-bar',
  imports: [
    MatMenuTrigger,
    MatIcon,
    MatToolbar,
    MatIconButton,
    TranslatePipe,
    MatMenu,
    RouterOutlet,
    NgForOf,
    MatAnchor,
    MatButton,
    MatMenuItem,
    MatCardModule
  ],
  templateUrl: './nav-bar.component.html',
  standalone: true,
  styleUrl: './nav-bar.component.scss'
})
export class NavBarComponent {
  menuClicked: any;

  toggleSearchBar() {

  }

  protected readonly changeLanguage = changeLanguage;
  languages: any;

  isSettingsPage: any;
  showLogin: boolean = false;

  constructor(private router: Router) {
  }

  login() {
    this.showLogin = true;
  }

  redirectToLogin() {
    window.location.href = 'http://localhost:8080/login';
  }

  signup() {
    window.location.href = "http://localhost:4200/auth/sign-up";
  }

  scrollToWorkspaces() {
    const element = document.getElementById('workspaces');
    if (element) {
      element.scrollIntoView({behavior: 'smooth'});
    }
  }

  scrollToSection(sectionId: string) {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({behavior: 'smooth'});
    }
  }
}
