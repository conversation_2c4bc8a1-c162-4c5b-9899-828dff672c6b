# Created by https://www.gitignore.io/api/eclipse

### Eclipse ###
build
.gradle
.idea
.metadata
bin/
tmp/
*.tmp
*.bak
*.swp
*~.nib
local.properties
.settings/
.loadpath
.recommenders

# External tool builders
.externalToolBuilders/

# Locally stored "Eclipse launch configurations"
*.launch

# PyDev specific (Python IDE for Eclipse)
*.pydevproject

# CDT-specific (C/C++ Development Tooling)
.cproject

# Java annotation processor (APT)
.factorypath

# PDT-specific (PHP Development Tools)
.buildpath

# sbteclipse plugin
.target

# Tern plugin
.tern-project

# TeXlipse plugin
.texlipse

# STS (Spring Tool Suite)
.springBeans

# Code Recommenders
.recommenders/

# Scala IDE specific (Scala & Java development for Eclipse)
.cache-main
.scala_dependencies
.worksheet

### Eclipse Patch ###
# Eclipse Core
.project

# JDT-specific (Eclipse Java Development Tools)
.classpath

# End of https://www.gitignore.io/api/eclipse


# See https://docs.github.com/get-started/getting-started-with-git/ignoring-files for more about ignoring files.

# Compiled output
front/dist
front/tmp
front/out-tsc
front/bazel-out

# Node
front/node_modules
frontnpm-debug.log
frontyarn-error.log


# Miscellaneous
front/.angular/cache
front/.sass-cache/
front/connect.lock
front/coverage
front/libpeerconnection.log
front/testem.log
front/typings

# System files
front/.DS_Store
front/Thumbs.db
