package org.example.model;

import java.util.Date;

public class Certificate {
    private String title;
    private String issuingOrganization;
    private Date issueDate;
    private Date expirationDate;
    private String description;

    public Certificate() {}

    public Certificate(String title, String issuingOrganization, Date issueDate, Date expirationDate, String description) {
        this.title = title;
        this.issuingOrganization = issuingOrganization;
        this.issueDate = issueDate;
        this.expirationDate = expirationDate;
        this.description = description;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getIssuingOrganization() {
        return issuingOrganization;
    }

    public void setIssuingOrganization(String issuingOrganization) {
        this.issuingOrganization = issuingOrganization;
    }

    public Date getIssueDate() {
        return issueDate;
    }

    public void setIssueDate(Date issueDate) {
        this.issueDate = issueDate;
    }

    public Date getExpirationDate() {
        return expirationDate;
    }

    public void setExpirationDate(Date expirationDate) {
        this.expirationDate = expirationDate;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public String toString() {
        return "Certificate{" +
                "title='" + title + '\'' +
                ", issuingOrganization='" + issuingOrganization + '\'' +
                ", issueDate=" + issueDate +
                ", expirationDate=" + expirationDate +
                ", description='" + description + '\'' +
                '}';
    }
}
