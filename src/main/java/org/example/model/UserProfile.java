package org.example.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.springframework.data.annotation.Id;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class UserProfile {

    @Id
    private String id;
    private String userId;
    private String photoUrl;
    private String bio;
    private String resumeUrl;
    private Date dateOfBirth;
    private String phoneNumber;
    private String address;
    private Cv cv;
    private String firstName;
    private String lastName;
    private String email;



    private List<Certificate> certifications = new ArrayList<>();
    private List<Education> educations = new ArrayList<>();
    private List<Experience> experiences = new ArrayList<>();
    private List<Skill> skills = new ArrayList<>();
    private List<Link> links = new ArrayList<>();  // Liste de Link, pas de String

    // ===== Constructors =====
    public UserProfile() {}


    public UserProfile(String userId) {
        this.userId = userId;
    }

    // ===== Getters and Setters =====
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }
    public String getFirstName() {
        return firstName;
    }
    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }
    public void setLastName(String lastName) {
        this.lastName = lastName;
    }
    public Cv getCv() {
        return cv;
    }

    public void setCv(Cv cv) {
        this.cv = cv;
    }


    // Optionnel : méthode utilitaire
    public boolean hasCv() {
        return this.cv != null;
    }


    public String getPhotoUrl() {
        return photoUrl;
    }

    public void setPhotoUrl(String photoUrl) {
        this.photoUrl = photoUrl;
    }

    public String getBio() {
        return bio;
    }

    public void setBio(String bio) {
        this.bio = bio;
    }

    public String getResumeUrl() {
        return resumeUrl;
    }

    public void setResumeUrl(String resumeUrl) {
        this.resumeUrl = resumeUrl;
    }
    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Date getDateOfBirth() {
        return dateOfBirth;
    }

    public void setDateOfBirth(Date dateOfBirth) {
        this.dateOfBirth = dateOfBirth;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public List<Certificate> getCertifications() {
        if (certifications == null) {
            certifications = new ArrayList<>();
        }
        return certifications;
    }

    public void setCertifications(List<Certificate> certifications) {
        this.certifications = certifications;
    }

    public void addCertification(Certificate certification) {
        if (this.certifications == null) {
            this.certifications = new ArrayList<>();
        }
        this.certifications.add(certification);
    }

    public List<Education> getEducations() {
        if (educations == null) {
            educations = new ArrayList<>();
        }
        return educations;
    }

    public void setEducations(List<Education> educations) {
        this.educations = educations;
    }

    public void addEducation(Education education) {
        if (this.educations == null) {
            this.educations = new ArrayList<>();
        }
        this.educations.add(education);
    }

    public List<Experience> getExperiences() {
        if (experiences == null) {
            experiences = new ArrayList<>();
        }
        return experiences;
    }

    public void setExperiences(List<Experience> experiences) {
        this.experiences = experiences;
    }

    public void addExperience(Experience experience) {
        if (this.experiences == null) {
            this.experiences = new ArrayList<>();
        }
        this.experiences.add(experience);
    }

    public List<Skill> getSkills() {
        return skills;
    }

    public void setSkills(List<Skill> skills) {
        this.skills = skills;
    }

    public List<Link> getLinks() {  // Utiliser Link au lieu de String
        if (links == null) {
            links = new ArrayList<>();
        }
        return links;
    }

    public void setLinks(List<Link> links) {
        this.links = links;
    }

    // Méthode pour ajouter un lien
    public void addLink(Link link) {
        if (this.links == null) {
            this.links = new ArrayList<>();
        }
        this.links.add(link);
    }

}
