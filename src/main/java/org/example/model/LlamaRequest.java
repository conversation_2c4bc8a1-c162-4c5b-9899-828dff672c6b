package org.example.model;

public class LlamaRequest {
    private String prompt;
    private String model;

    public LlamaRequest(String prompt) {
        this.prompt = prompt;
        this.model = "deepseek-coder-v2"; // Default model
    }

    public String getPrompt() {
        return prompt;
    }

    public void setPrompt(String prompt) {
        this.prompt = prompt;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }
}