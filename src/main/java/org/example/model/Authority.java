package org.example.model;

public enum Authority {
    CREATE_POST(true),
    SEE_DRAFTS(true),
    DELETE_POST(false),
    EDIT_POST(false),
    MANAGE_USERS(false),
    MANAGE_AUTHORITIES(false),
    ACCEPT_CANDIDATE(true),
    REJECT_CANDIDATE(true),
    GENERATE_TESTS(true),
    SEE_TESTS(true),
    ADD_RECRUITER(true),
    ;

    private final boolean defaultAuthority;

    Authority(boolean defaultAuthority) {
        this.defaultAuthority = defaultAuthority;
    }

    public boolean isDefaultAuthority() {
        return defaultAuthority;
    }
}
