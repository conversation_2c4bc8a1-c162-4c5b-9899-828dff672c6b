package org.example.model;

import jakarta.persistence.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.DBRef;

import java.util.Set;

@Document(collection = "workspaces")
public class Workspace {

    @Id
    private String id;
    private String name;
    private String description;
    private String location;
    private String phoneNumber;
    private String email;
    private String logoUrl;
    private boolean finalized = false;
    private boolean paymentDone = false;


    // Getter / Setter
    public boolean isPaymentDone()            { return paymentDone; }
    public void    setPaymentDone(boolean b)  { this.paymentDone = b; }

    public boolean isFinalized() { return finalized; }
    public void setFinalized(boolean finalized) { this.finalized = finalized; }

    @DBRef
    private Set<Membership> memberships;

    public Workspace() {}

    public Workspace(String id, String name, String description, Set<Membership> memberships) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.memberships = memberships;
    }

    // Getters et Setters

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getLogoUrl() { return logoUrl; }
    public void setLogoUrl(String logoUrl) { this.logoUrl = logoUrl; }

    public Set<Membership> getMemberships() {
        return memberships;
    }

    public void setMemberships(Set<Membership> memberships) {
        this.memberships = memberships;
    }
}
