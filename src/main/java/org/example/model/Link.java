package org.example.model;
public class Link {
    private String name;  // Nom du lien (par exemple : "LinkedIn", "Portfolio")
    private String url;   // URL du lien (par exemple : "https://www.linkedin.com/in/username")

    // Constructeur par défaut
    public Link() {}

    // Constructeur avec paramètres
    public Link(String name, String url) {
        this.name = name;
        this.url = url;
    }

    // Getters et Setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }
}
