package org.example.model;

import java.util.List;

public class Cv {
    private String fileName;
    private String originalName;
    private String uploadDate;
    private String fileUrl;

    // Données extraites du CV
    private List<String> skills;
    private List<Experience> experiences;
    private List<Education> educationList;

    public Cv() {
    }

    public Cv(String fileName, String originalName, String uploadDate, String fileUrl) {
        this.fileName = fileName;
        this.originalName = originalName;
        this.uploadDate = uploadDate;
        this.fileUrl = fileUrl;
    }

    // Getters et Setters

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getOriginalName() {
        return originalName;
    }

    public void setOriginalName(String originalName) {
        this.originalName = originalName;
    }

    public String getUploadDate() {
        return uploadDate;
    }

    public void setUploadDate(String uploadDate) {
        this.uploadDate = uploadDate;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public List<String> getSkills() {
        return skills;
    }

    public void setSkills(List<String> skills) {
        this.skills = skills;
    }

    public List<Experience> getExperiences() {
        return experiences;
    }

    public void setExperiences(List<Experience> experiences) {
        this.experiences = experiences;
    }

    public List<Education> getEducationList() {
        return educationList;
    }

    public void setEducationList(List<Education> educationList) {
        this.educationList = educationList;
    }
}
