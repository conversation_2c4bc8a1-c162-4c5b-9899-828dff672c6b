package org.example.model;

import jakarta.persistence.Id;

import java.util.List;

public class Test {

    @Id
    private String id;

    private String name;

    private List<Question> questions;

    // Get<PERSON> and Setters

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setTitle(String title) {
        this.name = title;
    }

    public List<Question> getQuestions() {
        return questions;
    }

    public void setQuestions(List<Question> questions) {
        this.questions = questions;
    }
}