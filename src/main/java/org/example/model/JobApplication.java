package org.example.model;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import java.time.LocalDateTime;

@Document(collection = "job_applications")
public class JobApplication {
    @Id
    private String id; // MongoDB _id
    private String name;
    private String email;
    private String phone;
    private String linkedin;
    private String introduction;
    private String postId;      // ID du post
    private String userId;      // ID de l'utilisateur qui postule
    private String postTitle;
    private String cvFileName;
    private LocalDateTime applicationDate;
    private ApplicationStatus status = ApplicationStatus.PENDING;

    public enum ApplicationStatus {
        PENDING, ACCEPTED, REJECTED
    }

    public JobApplication() {
        this.applicationDate = LocalDateTime.now();
    }

    public JobApplication(String name, String email, String phone, String linkedin,
                         String introduction, String postId, String userId,
                         String postTitle, String cvFileName) {
        this();
        this.name = name;
        this.email = email;
        this.phone = phone;
        this.linkedin = linkedin;
        this.introduction = introduction;
        this.postId = postId;
        this.postTitle = postTitle;
        this.cvFileName = cvFileName;
    }

    // Getters and Setters
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }
    public String getPhone() { return phone; }
    public void setPhone(String phone) { this.phone = phone; }
    public String getLinkedin() { return linkedin; }
    public void setLinkedin(String linkedin) { this.linkedin = linkedin; }
    public String getIntroduction() { return introduction; }
    public void setIntroduction(String introduction) { this.introduction = introduction; }
    public String getPostId() { return postId; }
    public void setPostId(String postId) { this.postId = postId; }
    public String getPostTitle() { return postTitle; }
    public void setPostTitle(String postTitle) { this.postTitle = postTitle; }
    public String getCvFileName() { return cvFileName; }
    public void setCvFileName(String cvFileName) { this.cvFileName = cvFileName; }
    public LocalDateTime getApplicationDate() { return applicationDate; }
    public void setApplicationDate(LocalDateTime applicationDate) { this.applicationDate = applicationDate; }

    @Override
    public String toString() {
        return "JobApplication{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", email='" + email + '\'' +
                ", phone='" + phone + '\'' +
                ", linkedin='" + linkedin + '\'' +
                ", introduction='" + introduction + '\'' +
                ", postId='" + postId + '\'' +
                ", postTitle='" + postTitle + '\'' +
                ", cvFileName='" + cvFileName + '\'' +
                ", applicationDate=" + applicationDate +
                '}';
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public ApplicationStatus getStatus() {
        return status;
    }

    public void setStatus(ApplicationStatus status) {
        this.status = status;
    }
}