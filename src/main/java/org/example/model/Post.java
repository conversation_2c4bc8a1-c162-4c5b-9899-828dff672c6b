package org.example.model;

import jakarta.persistence.PrePersist;
import org.example.dto.UserDto;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.DBRef;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

@Document(collection = "posts")
public class Post {

    @Id
    private String id;
    private String titre;
    private String description;
    private String entreprise;
    private String userId;
    private String userFullName;  // Pour stocker le nom complet de l'utilisateur
    private String profileRequest;
    private String contractType;
    private LocalDateTime datePublication;
    private String jobTitle;
    private String location;
    private boolean archived;
    private UserDto user; // Optionnel : si tu veux envoyer un objet complet vers le frontend

    // Informations du workspace associé au post
    private WorkspaceInfo workspace;

    @PrePersist
    protected void onCreate() {
        this.datePublication = LocalDateTime.now().truncatedTo(ChronoUnit.SECONDS);
    }

    // Getters et Setters

    public String getId() { return id; }
    public void setId(String id) { this.id = id; }

    public String getTitre() { return titre; }
    public void setTitre(String titre) { this.titre = titre; }

    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }

    public String getEntreprise() { return entreprise; }
    public void setEntreprise(String entreprise) { this.entreprise = entreprise; }

    public String getUserId() { return userId; }
    public void setUserId(String userId) { this.userId = userId; }

    public String getUserFullName() {
        return userFullName;
    }

    public void setUserFullName(String userFullName) {
        this.userFullName = userFullName;
    }

    public String getProfileRequest() { return profileRequest; }
    public void setProfileRequest(String profileRequest) { this.profileRequest = profileRequest; }

    public String getContractType() { return contractType; }
    public void setContractType(String contractType) { this.contractType = contractType; }

    public LocalDateTime getDatePublication() { return datePublication; }
    public void setDatePublication(LocalDateTime datePublication) { this.datePublication = datePublication; }

    public String getJobTitle() { return jobTitle; }
    public void setJobTitle(String jobTitle) { this.jobTitle = jobTitle; }

    public String getLocation() { return location; }
    public void setLocation(String location) { this.location = location; }

    public boolean isArchived() { return archived; }
    public void setArchived(boolean archived) { this.archived = archived; }

    public UserDto getUser() { return user; }
    public void setUser(UserDto user) { this.user = user; }

    public WorkspaceInfo getWorkspace() { return workspace; }
    public void setWorkspace(WorkspaceInfo workspace) { this.workspace = workspace; }

    // Classe interne pour stocker les informations du workspace
    public static class WorkspaceInfo {
        private String id;
        private String name;
        private String logoUrl;

        public WorkspaceInfo() {}

        public WorkspaceInfo(String id, String name, String logoUrl) {
            this.id = id;
            this.name = name;
            this.logoUrl = logoUrl;
        }

        public String getId() { return id; }
        public void setId(String id) { this.id = id; }

        public String getName() { return name; }
        public void setName(String name) { this.name = name; }

        public String getLogoUrl() { return logoUrl; }
        public void setLogoUrl(String logoUrl) { this.logoUrl = logoUrl; }
    }
}
