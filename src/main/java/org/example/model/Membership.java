package org.example.model;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Set;

@Document(collection = "memberships")
public class Membership {
    @Id
    private String id;
    private String userId;
    private String workspaceId;
    private Set<Authority> authorities = Set.of();
    private boolean owner = false;
    private boolean anonymous = false;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getWorkspaceId() {
        return workspaceId;
    }

    public void setWorkspaceId(String workspaceId) {
        this.workspaceId = workspaceId;
    }

    public Set<Authority> getAuthorities() {
        return authorities;
    }

    public void setAuthorities(Set<Authority> authorities) {
        this.authorities = authorities;
    }

    public void setOwner(boolean owner) {
        this.owner = owner;
    }

    public boolean isOwner() {
        return owner;
    }

    public boolean isAnonymous() {
        return anonymous;
    }

    public void setAnonymous(boolean anonymous) {
        this.anonymous = anonymous;
    }

    public boolean hasAuthority(String authority) {
        if (authority == null) {
            return false;
        }
        if (owner) {
            return true;
        }
        Authority auth = Authority.valueOf(authority);
        if (auth.isDefaultAuthority() && authorities.isEmpty()) {
            return true;
        }
        return authorities.contains(Authority.valueOf(authority));
    }
}
