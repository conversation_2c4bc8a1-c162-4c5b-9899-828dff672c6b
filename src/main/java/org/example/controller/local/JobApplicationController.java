package org.example.controller.local;

import org.example.model.JobApplication;
import org.example.model.User;
import org.example.service.JobApplication.local.DefaultJobApplicationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@RestController
@RequestMapping("/api/job-applications")
public class JobApplicationController {
    @Autowired
    private DefaultJobApplicationService defaultJobApplicationService;

    @PostMapping
    public ResponseEntity<?> saveApplication(
            @RequestParam("name") String name,
            @RequestParam("email") String email,
            @RequestParam("phone") String phone,
            @RequestParam(value = "linkedin", required = false) String linkedin,
            @RequestParam(value = "introduction", required = false) String introduction,
            @RequestParam("postId") String postId,
            @RequestParam("userId") String userId,
            @RequestParam("postTitle") String postTitle,
            @RequestParam(value = "cv", required = false) MultipartFile cv
    ) {
        try {
            if (name == null || name.isEmpty()) throw new IllegalArgumentException("Name is required");
            if (email == null || email.isEmpty()) throw new IllegalArgumentException("Email is required");
            if (phone == null || phone.isEmpty()) throw new IllegalArgumentException("Phone is required");
            if (postId == null || postId.isEmpty()) throw new IllegalArgumentException("Post ID is required");
            if (userId == null || userId.isEmpty()) throw new IllegalArgumentException("User ID is required");

            JobApplication application = new JobApplication(
                name, email, phone, linkedin, introduction,
                postId, userId, postTitle,
                cv != null ? cv.getOriginalFilename() : null
            );

            JobApplication savedApplication = defaultJobApplicationService.saveApplication(application);
            return ResponseEntity.ok(savedApplication);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body("Validation error: " + e.getMessage());
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("Error saving application: " + e.getMessage());
        }
    }


    @GetMapping("/post/{postId}/candidates")
    public ResponseEntity<List<JobApplication>> getCandidates(@PathVariable String postId) {
        List<JobApplication> applications = defaultJobApplicationService.getApplicationsByPostId(postId);
        return ResponseEntity.ok(applications);
    }


    @GetMapping("/check/{postId}/{userId}")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<Boolean> hasApplied(
            @PathVariable String postId,
            @PathVariable String userId) {
        boolean hasApplied = defaultJobApplicationService.hasUserApplied(postId, userId);
        return ResponseEntity.ok(hasApplied);
    }

    @PostMapping("/{applicationId}/accept")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<JobApplication> acceptApplication(@PathVariable String applicationId) {
        try {
            JobApplication updatedApplication = defaultJobApplicationService.acceptApplication(applicationId);
            return ResponseEntity.ok(updatedApplication);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    @PostMapping("/{applicationId}/reject")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<JobApplication> rejectApplication(@PathVariable String applicationId) {
        try {
            JobApplication updatedApplication = defaultJobApplicationService.rejectApplication(applicationId);
            return ResponseEntity.ok(updatedApplication);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }
}