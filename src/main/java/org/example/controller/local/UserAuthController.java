package org.example.controller.local;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.example.model.Cv;
import org.example.model.User;
import org.example.model.UserProfile;
import org.example.service.modelIA.LlamaService;
import org.example.service.pdf.PdfSummaryService;
import org.example.service.user.UserService;
import org.example.service.userProfile.UserProfileService;
import org.example.service.userProfile.local.DefaultUserProfileService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import reactor.core.publisher.Mono;

import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@RestController
@CrossOrigin
@RequestMapping("api/user")
public class UserAuthController {

    private final UserService userService;
    private final PdfSummaryService pdfSummaryService;
    private final BCryptPasswordEncoder passwordEncoder;
    private final Logger logger = LoggerFactory.getLogger(UserAuthController.class);
    private final LlamaService llamaService;
    private final UserProfileService userProfileService;

    @Autowired
    public UserAuthController(UserService userService,
                              PdfSummaryService pdfSummaryService,
                              BCryptPasswordEncoder passwordEncoder,
                              LlamaService llamaService,
                              UserProfileService userProfileService) {
        this.userService = userService;
        this.pdfSummaryService = pdfSummaryService;
        this.passwordEncoder = passwordEncoder;
        this.llamaService = llamaService;
        this.userProfileService = userProfileService;
    }

    @PostMapping("check-email")
    public boolean checkEmail(@RequestBody String email) {
        logger.debug("Checking email existence for: {}", email);
        return userService.checkEmail(email);
    }

    @PostMapping("signup")
    public ResponseEntity<?> signup(
            @RequestParam("firstName") String firstName,
            @RequestParam("lastName") String lastName,
            @RequestParam("email") String email,
            @RequestParam("password") String password,
            @RequestParam("phoneNumber") String phoneNumber,
            @RequestParam("birthday") String birthday,
            @RequestParam("cv") MultipartFile cv,
            @RequestParam(value = "address", defaultValue = "") String address,
            @RequestParam(value = "agreeToTerms", defaultValue = "false") boolean agreeToTerms) {
        logger.debug("Signup request received for email: {}", email);

        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.isAuthenticated()
                && !"anonymousUser".equals(authentication.getPrincipal())) {
            return ResponseEntity.status(HttpStatus.FORBIDDEN).body(Map.of("message", "You are already logged in."));
        }

        if (userService.checkEmail(email)) {
            return ResponseEntity.badRequest().body(Map.of("message", "Email already in use."));
        }

        if (cv == null || cv.isEmpty()) {
            return ResponseEntity.badRequest().body(Map.of("message", "CV is required."));
        }

        if (!cv.getOriginalFilename().endsWith(".pdf") &&
                !cv.getOriginalFilename().endsWith(".doc") &&
                !cv.getOriginalFilename().endsWith(".docx")) {
            return ResponseEntity.badRequest().body(Map.of("message", "Only PDF, DOC, or DOCX files are supported."));
        }

        try {
            Map<String, Object> cvData = pdfSummaryService.getPdfSummary(cv);
            if (cvData == null || cvData.isEmpty()) {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body(Map.of("message", "Failed to extract data from CV."));
            }

            Cv cvModel = new Cv();
            cvModel.setFileName((String) cvData.get("originalName"));
            cvModel.setOriginalName(cv.getOriginalFilename());
            cvModel.setUploadDate((String) cvData.get("uploadDate"));
            cvModel.setFileUrl((String) cvData.get("cvPath"));

            User user = new User();
            user.setFirstName(firstName);
            user.setLastName(lastName);
            user.setEmail(email);
            user.setPassword(passwordEncoder.encode(password));
            user.setPhoneNumber(phoneNumber);
            user.setDateOfBirth(new SimpleDateFormat("yyyy-MM-dd").parse(birthday));
            user.setAddress(address);
            user.setAgreeToTerms(agreeToTerms);
            user.setCv(cvModel);
            user.setSkills((List<String>) cvData.get("skills"));
            user.setExperience((List<String>) cvData.get("experience"));
            user.setEducation((String) cvData.get("education"));

            userService.saveUser(user);

            new Thread(() -> {
                UserProfile profile = prepareProfile((String) cvData.get("text")).block();
                if (profile != null) {
                    profile.setEmail(user.getEmail());
                    profile.setUserId(user.getId());
                    userProfileService.save(profile);
                }
            }).start();

            return ResponseEntity.ok(Map.of("message", "Signup successful!"));

        } catch (Exception e) {
            logger.error("Error during signup", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("message", "Error during signup: " + e.getMessage()));
        }
    }


    private Mono<UserProfile> prepareProfile(String text) {

        try {
            String prompt = """
                Résume le contenu suivant extrait d’un CV dans ce format JSON :
                {
                  "bio": "...",
                  "certifications": [
                    {
                      "title": "Titre",
                      "issuingOrganization": "Entreprise",
                      "issueDate": "yyyy-MM-dd",
                      "expirationDate": "yyyy-MM-dd" | null,
                      "description": "Description",
                    }
                  ],
                  "skills": [
                    {
                      "nom": "Nom",
                      "niveau": "Niveau"
                     }
                  ],
              
                  "experiences": [
                    {
                      "title": "Titre",
                      "company": "Entreprise",
                      "location": "localisation",
                      "startDate": "yyyy-MM-dd",
                      "endDate": "yyyy-MM-dd" | null,
                      "description": "Description",
               
                    }
                  ],
                  "educations": [
                    {
                      "degree": "Diplôme",
                      "school": "Établissement",
                      "startDate": "yyyy-MM-dd",
                      "endDate": "yyyy-MM-dd" | null,
                      "description": "Description"
                    }
                  ],
                   "links": [
                    {
                      "name": "nom",
                      "url": "url"
                    }
                  ]
                }

                Texte :
                %s
               """.formatted(text);

            return llamaService.askQuestion(prompt)
                    .map(json -> {
                        try {
                            // Nettoyage du bloc de code JSON
                            Pattern jsonPattern = Pattern.compile("```json\\s*(.*?)```", Pattern.DOTALL);
                            Matcher matcher = jsonPattern.matcher(json);
                            String cleanedJson = matcher.find() ? matcher.group(1).trim() : json;

                            ObjectMapper mapper = new ObjectMapper();
                            return mapper.readValue(cleanedJson, UserProfile.class);
                        } catch (Exception e) {
                            e.printStackTrace();
                            return new UserProfile();
                        }
                    })
                    .onErrorResume(e -> {
                        return Mono.just(new UserProfile());
                    });

        } catch (Exception e) {
            return Mono.just(new UserProfile());
        }
    }
}