package org.example.controller.local;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.example.service.modelIA.LlamaService;
import org.example.service.pdf.PdfSummaryService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@RestController
@RequestMapping("/pdf")
public class PdfController {

    private final PdfSummaryService pdfSummaryService;
    private final LlamaService llamaService;

    public PdfController(PdfSummaryService pdfSummaryService, LlamaService llamaService) {
        this.pdfSummaryService = pdfSummaryService;
        this.llamaService = llamaService;
    }

}