package org.example.controller.local;

import org.example.model.WorkspaceProfile;
import org.example.service.WorkspaceProfile.WorkspaceProfileService;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/workspace-profiles")
public class WorkspaceProfileController {

    private final WorkspaceProfileService service;

    public WorkspaceProfileController(WorkspaceProfileService service) {
        this.service = service;
    }

    @GetMapping("/{workspaceId}")
    public ResponseEntity<WorkspaceProfile> getProfile(@PathVariable String workspaceId) {
        try {
            WorkspaceProfile profile = service.getProfileByWorkspaceId(workspaceId);
            return new ResponseEntity<>(profile, HttpStatus.OK);
        } catch (RuntimeException e) {
            return new ResponseEntity<>(null, HttpStatus.NOT_FOUND);
        }
    }

    @PostMapping
    public ResponseEntity<WorkspaceProfile> saveProfile(@RequestBody WorkspaceProfile profile) {
        try {
            WorkspaceProfile savedProfile = service.createOrUpdateProfile(profile);
            return new ResponseEntity<>(savedProfile, HttpStatus.CREATED);
        } catch (IllegalArgumentException e) {
            return new ResponseEntity<>(null, HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @DeleteMapping("/{workspaceId}")
    public ResponseEntity<Void> deleteProfile(@PathVariable String workspaceId) {
        try {
            service.deleteProfileByWorkspaceId(workspaceId);
            return new ResponseEntity<>(HttpStatus.NO_CONTENT);
        } catch (RuntimeException e) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
    }
}
