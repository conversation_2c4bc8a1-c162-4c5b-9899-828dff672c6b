package org.example.controller.local;

import jakarta.validation.Valid;
import org.example.model.Invitation;
import org.example.model.Membership;
import org.example.model.User;
import org.example.service.invitation.InvitationService;
import org.example.service.user.UserService;
import org.example.service.user.local.UserRepository;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.server.ResponseStatusException;
import static org.springframework.http.HttpStatus.NOT_FOUND;

@RestController
@RequestMapping("/api/invitations")
public class InvitationController {

    private final InvitationService invitationService;
    private final UserService userService;
    private final UserRepository userRepository;

    public InvitationController(InvitationService invitationService, UserService userService, UserRepository userRepository) {
        this.invitationService = invitationService;
        this.userService = userService;
        this.userRepository = userRepository;
    }


    @GetMapping("/user/{userId}")
    public ResponseEntity<Page<Invitation>> getUserInvitations(
            @PathVariable String userId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        Page<Invitation> invitations = invitationService.getUserInvitations(userId, page, size);
        return ResponseEntity.ok(invitations);
    }

    @GetMapping("/me")
    public ResponseEntity<Page<Invitation>> getUserInvitations(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        Page<Invitation> invitations = invitationService.getUserInvitations(userService.me().getId(), page, size);
        return ResponseEntity.ok(invitations);
    }

    @GetMapping("/sent")
    public ResponseEntity<Page<Invitation>> getSentInvitations(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        Page<Invitation> invitations = invitationService.getSentInvitations(userService.me().getId(), page, size);
        return ResponseEntity.ok(invitations);
    }

    public record SendInvitationRequest(
            @jakarta.validation.constraints.Email String email,
            String message
    ) {
    }

    @PostMapping("/send")
    public ResponseEntity<?> sendInvitation(
            @Valid @RequestBody SendInvitationRequest request,
            Membership membership) {
        User receiver = userService.findByEmail(request.email());
        User sender = userService.me();
        if (receiver == null) {
            throw new ResponseStatusException(NOT_FOUND, "Utilisateur non trouvé avec cet email.");
        }

        Invitation invitation = invitationService.sendInvitation(
                sender.getId(),
                receiver.getId(),
                membership.getWorkspaceId(),
                request.message()
        );

        return ResponseEntity.ok(invitation);
    }



    @PostMapping("/{invitationId}/accept")
    public ResponseEntity<?> acceptInvitation(@PathVariable String invitationId, Authentication authentication) {
        String userEmail = authentication.getName();
        User user = userRepository.findByEmail(userEmail).orElseThrow();
        Invitation updatedInvitation = invitationService.acceptInvitation(invitationId, user.getId());
        return ResponseEntity.ok(updatedInvitation);
    }


    @PostMapping("/{id}/reject")
    public ResponseEntity<Void> rejectInvitation(@PathVariable String id) {
        invitationService.rejectInvitation(id);
        return ResponseEntity.noContent().build();
    }

    @PostMapping("/{id}/resend")
    public ResponseEntity<Invitation> resendInvitation(@PathVariable String id) {
        Invitation invitation = invitationService.resendInvitation(id);
        return ResponseEntity.ok(invitation);
    }

    @PostMapping("/{id}/cancel")
    public ResponseEntity<Void> cancelInvitation(@PathVariable String id) {
        invitationService.cancelInvitation(id);
        return ResponseEntity.noContent().build();
    }
    @PutMapping("/{id}/status")
    public ResponseEntity<Invitation> updateStatus(
            @PathVariable String id,
            @RequestParam String status) {
        Invitation.InvitationStatus invitationStatus = Invitation.InvitationStatus.valueOf(status.toUpperCase());
        Invitation updatedInvitation = invitationService.updateStatus(id, invitationStatus);
        return ResponseEntity.ok(updatedInvitation);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteInvitation(@PathVariable String id) {
        invitationService.deleteInvitation(id);
        return ResponseEntity.noContent().build();
    }

}
