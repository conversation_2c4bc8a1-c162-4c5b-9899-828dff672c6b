package org.example.controller.local;

import com.stripe.Stripe;
import com.stripe.exception.StripeException;
import com.stripe.model.checkout.Session;
import com.stripe.param.checkout.SessionCreateParams;
import org.example.model.Workspace;
import org.example.service.workspace.WorkspaceService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
// import org.springframework.beans.factory.annotation.Autowired; // Supprimé car nous utilisons l'injection par constructeur
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/payment")
@CrossOrigin(origins = "http://localhost:4200") // autorise les appels depuis ton frontend Angular
public class PaymentController {

    private final Logger logger = LoggerFactory.getLogger(PaymentController.class);
    private final String STRIPE_API_KEY = "sk_test_51RGqvWQ990p20cocOrR0SsQTXkLfEIRUHYJWeNy7SvuJ9Gnh20iym9wkSNoYYrLosyAlhBHMEO6bcjqjnCDnIGjI00BYgPy1Dd"; // Clé secrète Stripe
    private final String SUCCESS_URL = "http://localhost:4200/protected/payment/success";
    private final String CANCEL_URL = "http://localhost:4200/protected/payment/cancel";
    private final WorkspaceService workspaceService;

    public PaymentController(WorkspaceService workspaceService) {
        this.workspaceService = workspaceService;
        Stripe.apiKey = STRIPE_API_KEY;
    }

    @PostMapping("/create-checkout-session")
    public ResponseEntity<Map<String, String>> createCheckoutSession(@RequestBody Map<String, String> request) {
        try {
            String plan = request.get("plan");
            String workspaceId = request.get("workspaceId");

            logger.info("Creating checkout session for plan: {} and workspaceId: {}", plan, workspaceId);

            if (plan == null || workspaceId == null) {
                logger.error("Missing required parameters: plan or workspaceId");
                throw new IllegalArgumentException("Plan and workspaceId are required");
            }

            long amount;
            String planName;

            switch (plan) {
                case "standard":
                    planName = "Standard";
                    amount = 14900; // 149€ en centimes
                    break;
                case "pro":
                    planName = "Pro";
                    amount = 24900; // 249€ en centimes
                    break;
                case "enterprise":
                    planName = "Enterprise";
                    amount = 34900; // 349€ en centimes
                    break;
                default:
                    logger.error("Invalid plan: {}", plan);
                    throw new IllegalArgumentException("Invalid plan: " + plan);
            }

            // Pour les tests, nous utilisons le mode PAYMENT au lieu de SUBSCRIPTION
            SessionCreateParams params = SessionCreateParams.builder()
                    .setMode(SessionCreateParams.Mode.PAYMENT)
                    .setSuccessUrl(SUCCESS_URL + "?session_id={CHECKOUT_SESSION_ID}&workspace_id=" + workspaceId)
                    .setCancelUrl(CANCEL_URL + "?workspace_id=" + workspaceId)
                    .addLineItem(
                            SessionCreateParams.LineItem.builder()
                                    .setQuantity(1L)
                                    .setPriceData(
                                            SessionCreateParams.LineItem.PriceData.builder()
                                                    .setCurrency("eur")
                                                    .setUnitAmount(amount)
                                                    .setProductData(
                                                            SessionCreateParams.LineItem.PriceData.ProductData.builder()
                                                                    .setName("Plan " + planName)
                                                                    .setDescription("Abonnement au plan " + planName)
                                                                    .build()
                                                    )
                                                    .build()
                                    )
                                    .build())
                    .setPaymentIntentData(
                            SessionCreateParams.PaymentIntentData.builder()
                                    .putMetadata("workspaceId", workspaceId)
                                    .putMetadata("plan", plan)
                                    .build()
                    )
                    .build();

            Session session = Session.create(params);
            logger.info("Checkout session created with id: {}", session.getId());

            Map<String, String> responseData = new HashMap<>();
            responseData.put("id", session.getId());
            responseData.put("url", session.getUrl());

            return ResponseEntity.ok(responseData);
        } catch (StripeException e) {
            logger.error("Stripe error creating checkout session: {}", e.getMessage(), e);
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.status(500).body(errorResponse);
        } catch (Exception e) {
            logger.error("Error creating checkout session: {}", e.getMessage(), e);
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    // Webhook supprimé car non nécessaire pour le scénario actuel

    @GetMapping("/verify-payment/{sessionId}")
    public ResponseEntity<Map<String, Object>> verifyPayment(@PathVariable String sessionId) {
        Map<String, Object> response = new HashMap<>();

        try {
            logger.info("Verifying payment for session: {}", sessionId);
            Session session = Session.retrieve(sessionId);

            boolean isPaid = "paid".equals(session.getPaymentStatus());
            String workspaceId = null;

            // Récupérer l'ID du workspace depuis les métadonnées de la session
            try {
                // Obtenir l'ID du PaymentIntent
                String paymentIntentId = session.getPaymentIntent();
                if (paymentIntentId != null && !paymentIntentId.isEmpty()) {
                    // Récupérer le PaymentIntent
                    com.stripe.model.PaymentIntent paymentIntent = com.stripe.model.PaymentIntent.retrieve(paymentIntentId);
                    // Récupérer les métadonnées
                    workspaceId = paymentIntent.getMetadata().get("workspaceId");
                    logger.info("Retrieved workspaceId from payment intent metadata: {}", workspaceId);
                }
            } catch (Exception e) {
                logger.error("Error retrieving payment intent metadata: {}", e.getMessage(), e);
            }

            response.put("paid", isPaid);
            response.put("workspaceId", workspaceId);

            // Si le paiement est confirmé, mettre à jour le statut du workspace
            if (isPaid && workspaceId != null) {
                try {
                    logger.info("Payment confirmed for workspace: {}. Updating payment status.", workspaceId);

                    // Mettre à jour le statut de paiement du workspace
                    Workspace updatedWorkspace = workspaceService.updateWorkspacePaymentStatus(workspaceId, true);

                    // Vérifier que les changements ont bien été enregistrés
                    // Utiliser les getters pour accéder aux propriétés
                    boolean paymentDone = updatedWorkspace.isPaymentDone();
                    boolean finalized = updatedWorkspace.isFinalized();
                    logger.info("Workspace updated with paymentDone={} and finalized={}",
                               paymentDone,
                               finalized);

                    response.put("workspaceUpdated", true);

                    // Créer une Map avec les propriétés du workspace au lieu d'ajouter l'objet directement
                    Map<String, Object> workspaceMap = new HashMap<>();
                    workspaceMap.put("id", updatedWorkspace.getId());
                    workspaceMap.put("name", updatedWorkspace.getName());
                    workspaceMap.put("description", updatedWorkspace.getDescription());
                    workspaceMap.put("location", updatedWorkspace.getLocation());
                    workspaceMap.put("phoneNumber", updatedWorkspace.getPhoneNumber());
                    workspaceMap.put("email", updatedWorkspace.getEmail());
                    workspaceMap.put("logoUrl", updatedWorkspace.getLogoUrl());
                    workspaceMap.put("paymentDone", paymentDone);
                    workspaceMap.put("finalized", finalized);

                    response.put("workspace", workspaceMap);
                } catch (Exception e) {
                    logger.error("Error updating workspace payment status: {}", e.getMessage(), e);
                    response.put("workspaceUpdated", false);
                    response.put("updateError", e.getMessage());
                }
            }

            return ResponseEntity.ok(response);
        } catch (StripeException e) {
            logger.error("Stripe error verifying payment: {}", e.getMessage(), e);
            response.put("paid", false);
            response.put("error", e.getMessage());
            return ResponseEntity.status(500).body(response);
        } catch (Exception e) {
            logger.error("Error verifying payment: {}", e.getMessage(), e);
            response.put("paid", false);
            response.put("error", e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }
}
