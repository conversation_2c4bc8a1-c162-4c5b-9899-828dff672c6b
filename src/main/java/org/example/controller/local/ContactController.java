package org.example.controller.local;

import jakarta.validation.Valid;
import org.example.dto.ContactFormDTO;
import org.example.service.cantact.ContactService;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;


@RestController
@RequestMapping("/api/contact")
public class ContactController {

    private final ContactService contactService;

    public ContactController(ContactService contactService) {
        this.contactService = contactService;
    }

    @PostMapping
    public ResponseEntity<String> submitContactForm(@Valid @RequestBody ContactFormDTO contactForm, BindingResult result) {
        if (result.hasErrors()) {
            return ResponseEntity.badRequest().body("Validation failed: " + result.getAllErrors());
        }
        try {
            contactService.sendContactMessage(contactForm);
            return ResponseEntity.ok("Message envoyé avec succès !");
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Erreur lors de l'envoi du message: " + e.getMessage());
        }
    }

}
