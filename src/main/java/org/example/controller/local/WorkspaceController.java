package org.example.controller.local;
import org.example.exceptions.workspace.WorkspaceNotFoundException;
import org.example.model.Membership;
import org.example.model.User;
import org.example.model.Workspace;
import org.example.service.user.local.UserRepository;
import org.example.service.workspace.WorkspaceService;
import org.example.service.workspace.local.MembershipRepository;
import org.example.service.workspace.local.WorkspaceRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/workspaces")
@CrossOrigin(origins = "*")
public class WorkspaceController {

    private final WorkspaceService workspaceService;
    private final Logger logger = LoggerFactory.getLogger(WorkspaceController.class);
    private final MembershipRepository membershipRepository;
    private final WorkspaceRepository workspaceRepository;
    private final UserRepository userRepository;

    public WorkspaceController(WorkspaceService workspaceService,
                               MembershipRepository membershipRepository,
                               WorkspaceRepository workspaceRepository,
                               UserRepository userRepository ) {
        this.workspaceService = workspaceService;
        this.membershipRepository = membershipRepository;
        this.workspaceRepository = workspaceRepository;
        this.userRepository = userRepository;

    }

    /**@GetMapping("/current")
    public ResponseEntity<?> getCurrentWorkspace(HttpServletRequest request) {
    logger.info("Fetching current workspace...");

    // Récupérer l'email ou l'identifiant de l'utilisateur à partir de la session
    String userEmail = (String) request.getSession().getAttribute("userEmail");

    if (userEmail == null) {
    logger.warn("Utilisateur non authentifié.");
    return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(Map.of("message", "Utilisateur non authentifié."));
    }

    logger.info("Utilisateur authentifié: {}", userEmail);

    // Récupérer le workspace pour cet utilisateur
    Optional<Workspace> workspace = workspaceService.getWorkspaceForUser(userEmail);
    if (workspace == null) {
    logger.warn("Aucun workspace trouvé pour l'utilisateur {}", userEmail);
    return ResponseEntity.status(HttpStatus.NOT_FOUND).body(Map.of("message", "Aucun workspace trouvé pour l'utilisateur."));
    }

    // Retourner le workspace trouvé
    return ResponseEntity.ok(workspace);
    }**/
    @PostMapping("/create")
    public ResponseEntity<Workspace> createWorkspace(@RequestBody Workspace workspace) {

        logger.debug("Creating draft workspace: {}", workspace.getName());

        // Sécurité : le workspace n’est pas encore payé
        workspace.setPaymentDone(false);

        Workspace saved = workspaceService.createWorkspace(workspace);

        logger.info("Draft workspace {} created with id={}", saved.getName(), saved.getId());
        return ResponseEntity.ok(saved);
    }
    @PostMapping("/search")
    @PreAuthorize("hasMembershipAuthority('VIEW_WORKSPACE')")
    public Page<Workspace> searchWorkspaces(@RequestBody Workspace example, Pageable pageable) {
        logger.debug("Searching for workspaces...");
        logger.trace("Search criteria: {}", example);
        return workspaceService.search(example, pageable);
    }

    @GetMapping("/{id}")
    @PreAuthorize("hasMembershipAuthority('GET_WORKSPACE')")
    public Workspace getWorkspaceById(@PathVariable String id) {
        logger.debug("Fetching workspace with ID: {}", id);
        return workspaceService.getWorkspaceById(id)
                .orElseThrow(() -> {
                    logger.warn("Workspace with ID {} not found", id);
                    return new WorkspaceNotFoundException("Workspace avec ID " + id + " non trouvé.");
                });
    }

    /**
     * Récupère les informations de base d'un workspace sans nécessiter d'autorisation spécifique
     * Cet endpoint est utilisé pour afficher les informations publiques d'un workspace
     */
    @GetMapping("/{id}/public")
    public ResponseEntity<?> getPublicWorkspaceInfo(@PathVariable String id) {
        logger.debug("Fetching public workspace info with ID: {}", id);
        try {
            return workspaceService.getWorkspaceById(id)
                    .map(workspace -> {
                        // Créer un objet avec seulement les informations publiques
                        Map<String, Object> publicInfo = new HashMap<>();
                        publicInfo.put("id", workspace.getId());
                        publicInfo.put("name", workspace.getName());
                        publicInfo.put("description", workspace.getDescription());
                        publicInfo.put("logoUrl", workspace.getLogoUrl());

                        return ResponseEntity.ok(publicInfo);
                    })
                    .orElse(ResponseEntity.status(HttpStatus.NOT_FOUND)
                            .body(Map.of("message", "Workspace avec ID " + id + " non trouvé.")));
        } catch (Exception e) {
            logger.error("Error fetching public workspace info: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("message", "Erreur lors de la récupération des informations du workspace."));
        }
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasMembershipAuthority('EDIT_WORKSPACE')")
    public Workspace updateWorkspace(@PathVariable String id, @RequestBody Workspace workspaceDetails) {
        logger.debug("Updating workspace with ID: {}", id);
        logger.trace("Updated details: {}", workspaceDetails);
        return workspaceService.updateWorkspace(id, workspaceDetails);
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasMembershipAuthority('DELETE_WORKSPACE')")
    public Map<String, Boolean> deleteWorkspace(@PathVariable String id) {
        logger.debug("Deleting workspace with ID: {}", id);
        workspaceService.deleteWorkspace(id);
        return Map.of("deleted", true);
    }

    @GetMapping
// @PreAuthorize("hasMembershipAuthority('VIEW_WORKSPACE')")
    public ResponseEntity<Page<Workspace>> getAllWorkspaces(Pageable pageable) {
        logger.debug("Fetching all workspaces...");
        Page<Workspace> workspaces = workspaceService.getAllWorkspaces(pageable);
        if (workspaces.isEmpty()) {
            logger.warn("No workspaces found");
            return ResponseEntity.status(HttpStatus.NO_CONTENT).body(workspaces);
        }
        return ResponseEntity.ok(workspaces);
    }
    @GetMapping("/me")
    public ResponseEntity<?> getWorkspacesForCurrentUser(Authentication authentication) {
        logger.info("Fetching workspaces for current user...");

        if (authentication == null || authentication.getName() == null) {
            logger.warn("Utilisateur non authentifié.");
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(Map.of("message", "Utilisateur non authentifié."));
        }

        String userEmail = authentication.getName();  // par exemple "<EMAIL>"
        logger.info("Utilisateur authentifié : {}", userEmail);

        try {
            // Étape 1 : récupérer l'utilisateur via son email
            Optional<User> userOptional = userRepository.findByEmail(userEmail); // ✅ CORRECTION ICI
            if (userOptional.isEmpty()) {
                logger.warn("Aucun utilisateur trouvé avec l'email : {}", userEmail);
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(Map.of("message", "Utilisateur introuvable."));
            }

            User user = userOptional.get();
            String userId = user.getId();

            // Étape 2 : rechercher les memberships avec le bon userId
            List<Membership> memberships = membershipRepository.findByUserId(userId);
            List<String> workspaceIds = memberships.stream()
                    .map(Membership::getWorkspaceId)
                    .toList();

            List<Workspace> workspaces = workspaceRepository.findAllById(workspaceIds);
            return ResponseEntity.ok(workspaces);

        } catch (Exception e) {
            logger.error("Erreur lors de la récupération des workspaces : {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("message", "Erreur lors du chargement des workspaces."));
        }
    }
    @GetMapping("/{id}/profile")
    @PreAuthorize("hasMembershipAuthority('GET_WORKSPACE')")
    public ResponseEntity<?> getWorkspaceProfile(@PathVariable String id) {
        logger.debug("Fetching workspace profile with ID: {}", id);
        try {
            Workspace workspace = workspaceService.getWorkspaceById(id)
                    .orElseThrow(() -> {
                        logger.warn("Workspace with ID {} not found", id);
                        return new WorkspaceNotFoundException("Workspace avec ID " + id + " non trouvé.");
                    });
            return ResponseEntity.ok(workspace);
        } catch (WorkspaceNotFoundException e) {
            logger.warn("Workspace not found: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(Map.of("message", e.getMessage()));
        } catch (Exception e) {
            logger.error("Error fetching workspace profile: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("message", "Erreur lors de la récupération du profil du workspace."));
        }
    }
    @PostMapping("/{id}/finalize")
    // Suppression temporaire de la sécurité pour permettre l'upload du logo
    // @PreAuthorize("hasMembershipAuthority('EDIT_WORKSPACE')")
    public ResponseEntity<Workspace> finalizeWorkspace(@PathVariable String id, @RequestParam("logo") MultipartFile logo) {
        logger.debug("Finalizing workspace with ID: {}", id);

        Optional<Workspace> workspaceOptional = workspaceRepository.findById(id);
        if (workspaceOptional.isEmpty()) {
            logger.warn("Workspace with ID {} not found", id);
            // Retourner un ResponseEntity avec le message d'erreur, mais de type Workspace, pour être compatible
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null); // Pas besoin de Map ici
        }

        Workspace workspace = workspaceOptional.get();

        // Traitement du logo
        if (logo != null && !logo.isEmpty()) {
            try {
                // Créer un chemin de stockage absolu pour éviter les problèmes de chemin relatif
                String uploadDir = System.getProperty("user.dir") + "/uploads/workspaces/";
                String fileName = UUID.randomUUID() + "_" + logo.getOriginalFilename();
                Path uploadPath = Paths.get(uploadDir);

                logger.info("Creating upload directory at: {}", uploadDir);

                // Créer le répertoire s'il n'existe pas
                try {
                    if (!Files.exists(uploadPath)) {
                        Files.createDirectories(uploadPath);
                        logger.info("Upload directory created successfully");
                    }
                } catch (Exception e) {
                    logger.error("Error creating upload directory: {}", e.getMessage(), e);
                    // Essayer un autre emplacement si le premier échoue
                    uploadDir = System.getProperty("java.io.tmpdir") + "/uploads/workspaces/";
                    uploadPath = Paths.get(uploadDir);
                    logger.info("Trying alternative upload directory: {}", uploadDir);
                    Files.createDirectories(uploadPath);
                }

                // Sauvegarde physique du fichier
                Path filePath = uploadPath.resolve(fileName);
                Files.copy(logo.getInputStream(), filePath, StandardCopyOption.REPLACE_EXISTING);

                // Enregistre l'URL (ou chemin) dans l'entité
                workspace.setLogoUrl("/" + uploadDir + fileName); // ou seulement le chemin relatif
            } catch (IOException e) {
                e.printStackTrace();
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null); // Retourner null en cas d'erreur
            }
        }

        // Marquer le workspace comme payé et finalisé
        workspace.setPaymentDone(true);
        workspace.setFinalized(true); // Ajouter cette ligne pour marquer le workspace comme finalisé

        logger.info("Setting workspace {} as paid and finalized", workspace.getId());

        // Sauvegarder le workspace après ajout du logo et mise à jour du paiement
        Workspace finalizedWorkspace = workspaceRepository.save(workspace);

        // Vérifier que les changements ont bien été enregistrés
        logger.info("Workspace saved with paymentDone={} and finalized={}",
                finalizedWorkspace.isPaymentDone(),
                finalizedWorkspace.isFinalized());

        logger.info("Workspace {} finalized with logo and payment done.", finalizedWorkspace.getId());
        return ResponseEntity.ok(finalizedWorkspace); // Renvoyer le workspace finalisé
    }

}