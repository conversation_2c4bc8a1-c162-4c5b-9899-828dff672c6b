package org.example.controller.local;

import org.example.model.Reaction;
import org.example.model.User;
import org.example.service.reaction.local.DefaultReactionService;
import org.example.service.user.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/api/reactions")
@CrossOrigin(origins = "http://localhost:4200")
public class ReactionController {
    @Autowired
    private DefaultReactionService reactionService;
    @Autowired
    private UserService userService;

    @PostMapping
    public ResponseEntity<?> addReaction(@RequestBody ReactionRequest request, Authentication authentication) {
        try {
            if (request.getPostId() == null || request.getPostId().isEmpty()) {
                throw new IllegalArgumentException("postId is required");
            }
            if (request.getReactionType() == null || request.getReactionType().isEmpty()) {
                throw new IllegalArgumentException("reactionType is required");
            }
            User user = userService.getUserByEmail(authentication.getName()).orElseThrow();

            Reaction savedReaction = reactionService.saveReaction(
                    request.getPostId(),
                    user.getId(),
                    request.getReactionType()
            );
            return ResponseEntity.ok(savedReaction);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body("Validation error: " + e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.badRequest().body("Error: " + e.getMessage());
        }
    }

    @GetMapping("/counts/{postId}")
    public ResponseEntity<?> getReactionCounts(@PathVariable String postId, Authentication authentication) { // Updated endpoint
        try {
            if (postId == null || postId.isEmpty()) {
                throw new IllegalArgumentException("postId is required");
            }
            String email = authentication.getName();
            User user = userService.getUserByEmail(email).orElseThrow();
            Map<String, Object> counts = reactionService.getReactionCounts(postId, user.getId());
            return ResponseEntity.ok(counts);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body("Validation error: " + e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.badRequest().body("Error: " + e.getMessage());
        }
    }
}

class ReactionRequest {
    private String postId;
    private String reactionType;

    public String getPostId() { return postId; }
    public void setPostId(String postId) { this.postId = postId; }
    public String getReactionType() { return reactionType; }
    public void setReactionType(String reactionType) { this.reactionType = reactionType; }

    @Override
    public String toString() {
        return "ReactionRequest{postId='" + postId + "', reactionType='" + reactionType + "'}";
    }
}