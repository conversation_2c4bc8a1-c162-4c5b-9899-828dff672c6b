package org.example.controller.local;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.example.dto.TestDTO;
import org.example.model.Test;
import org.example.model.TestResult;
import org.example.service.modelIA.LlamaService;
import org.example.service.test.TestService;
import org.example.service.testresult.TestResultRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@RestController
@RequestMapping("/api/tests")
public class TestController {

    private final Pattern CLEANING_PATTERN = Pattern.compile("```json([?:.|\\n\\s\\S]*)```");

    private final TestService testService;
    private final LlamaService llamaService;
    private final ObjectMapper objectMapper;
    @Autowired
    private TestResultRepository testResultRepository;

    public TestController(TestService testService, LlamaService llamaService, ObjectMapper objectMapper) {
        this.testService = testService;
        this.llamaService = llamaService;
        this.objectMapper = objectMapper;
    }

    @PostMapping
    public Mono<Test> createTest(@RequestBody TestDTO testDTO) {
        return Mono.just(testService.createTest(testDTO));
    }

    @GetMapping
    public Mono<List<Test>> getAllTests() {
        return Mono.just(testService.getAllTests());
    }

    @GetMapping("/{id}")
    public Mono<Test> getTestById(@PathVariable String id) {
        return testService.getTestById(id)
                .map(Mono::just)
                .orElseGet(() -> Mono.error(new RuntimeException("Test not found with id: " + id)));
    }

    @PutMapping("/{id}")
    public Mono<Test> updateTest(@PathVariable String id, @RequestBody TestDTO testDTO) {
        return Mono.just(testService.updateTest(id, testDTO));
    }

    @DeleteMapping("/{id}")
    public Mono<Void> deleteTest(@PathVariable String id) {
        testService.deleteTest(id);
        return Mono.empty();
    }

    @PostMapping("/generate")
    public Mono<Test> generateTest(@RequestBody Map<String, Object> testParams) {
        // Extraction des paramètres
        String subject = (String) testParams.getOrDefault("subject", "Développement");
        Integer count = (Integer) testParams.getOrDefault("count", 10);
        String level = (String) testParams.getOrDefault("level", "intermediaire");
        String testType = (String) testParams.getOrDefault("testType", "mixte");
        Integer duration = (Integer) testParams.getOrDefault("duration", 60);

        // Validation des paramètres
        if (subject == null || subject.trim().isEmpty()) {
            return Mono.error(new IllegalArgumentException("Subject cannot be empty"));
        }

        // Construction du prompt personnalisé
        String levelDescription = getLevelDescription(level);
        String typeDescription = getTypeDescription(testType);
        String difficultyInstructions = getDifficultyInstructions(level);

        String prompt = """
                Génère un test technique professionnel sur le thème: %s

                PARAMÈTRES DU TEST:
                - Nombre de questions: %d
                - Niveau de difficulté: %s (%s)
                - Type de test: %s
                - Durée estimée: %d minutes

                INSTRUCTIONS SPÉCIFIQUES:
                %s

                %s

                CONTRAINTES TECHNIQUES:
                - Chaque question doit être claire et précise
                - Les options doivent être plausibles et bien formulées
                - Minimum 4 options par question
                - Les questions doivent couvrir différents aspects du sujet
                - Respecter le niveau de difficulté demandé

                FORMAT DE RÉPONSE OBLIGATOIRE (JSON strict):
                {
                  "title": "Test %s - Niveau %s",
                  "description": "Test de %d questions sur %s (durée: %d min)",
                  "level": "%s",
                  "duration": %d,
                  "questions": [
                    {
                      "content": "Votre question ici",
                      "type": "RADIO",
                      "options": ["Option 1", "Option 2", "Option 3", "Option 4"],
                      "correctOptions": [0],
                      "explanation": "Explication de la réponse correcte"
                    }
                  ]
                }

                TYPES DE QUESTIONS:
                - "RADIO" pour sélection simple (une seule bonne réponse)
                - "SELECT" pour sélection multiple (plusieurs bonnes réponses possibles)

                Génère exactement %d questions variées et équilibrées.
                """.formatted(
                    subject, count, level, levelDescription, typeDescription, duration,
                    difficultyInstructions, getContentInstructions(testType),
                    subject, level, count, subject, duration, level, duration, count
                );

        return llamaService.askQuestion(prompt)
                .map(json -> {
                    try {
                        // Log raw JSON for debugging
                        System.out.println("Raw JSON from LlamaService: " + json);
                        Matcher matcher = CLEANING_PATTERN.matcher(json);
                        if (matcher.find()) {
                            String cleanedJson = matcher.group(1).trim();
                            return objectMapper.readValue(cleanedJson, TestDTO.class);
                        }
                        throw new JsonParseException(json);
                    } catch (JsonParseException e) {
                        throw new RuntimeException("Invalid JSON format received from LlamaService: " + e.getMessage(), e);
                    } catch (Exception e) {
                        throw new RuntimeException("Error during JSON conversion to TestDTO: " + e.getMessage(), e);
                    }
                })
                .map(testService::createTest)
                .onErrorResume(e -> {
                    if (e.getCause() instanceof org.apache.catalina.connector.ClientAbortException) {
                        System.out.println("Client disconnected: " + e.getMessage());
                        return Mono.empty();
                    }
                    return Mono.error(e);
                });
    }

    @PostMapping("/{id}/submit")
    public Mono<Map<String, String>> submitTest(
            @PathVariable String id,
            @RequestBody TestResult testResult) {
        testResult.setTestId(id);
        testResult.setSubmittedAt(new Date());
        return Mono.fromCallable(() -> testResultRepository.save(testResult))
                .thenReturn(Map.of("message", "Réponses enregistrées pour le test " + id));
    }


}