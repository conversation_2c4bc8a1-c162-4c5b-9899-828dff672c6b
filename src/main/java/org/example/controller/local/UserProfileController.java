package org.example.controller.local;

import org.example.model.*;
import org.example.service.user.UserService;
import org.example.service.userProfile.UserProfileService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Map;

@RestController
@RequestMapping("/api/user-profiles")
public class UserProfileController {
    private final Logger logger = LoggerFactory.getLogger(UserProfileController.class);

    private final UserProfileService service;
    private final UserService userService;

    public UserProfileController(UserProfileService service, UserService userService) {
        this.service = service;
        this.userService = userService;
    }

    @GetMapping("/{userId}")
    public UserProfile getProfile(@PathVariable String userId) {
        return service.getByUserId(userId);
    }

    @GetMapping("/current")
    public ResponseEntity<?> getCurrentUserProfile(Authentication authentication) {
        if (authentication == null || !authentication.isAuthenticated()) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("User not authenticated");
        }
        String email = authentication.getName();
        User user = userService.getUserByEmail(email).orElseThrow();
        try {
            UserProfile userProfile = service.getByUserId(user.getId());
            return ResponseEntity.ok(userProfile);
        } catch (Exception e) {
            logger.error("Error fetching profile for user: {}", user.getId(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Error fetching profile: " + e.getMessage());
        }
    }

    @PostMapping
    public UserProfile saveProfile(@RequestBody UserProfile profile) {
        return service.save(profile);
    }

    @DeleteMapping("/{id}")
    public void deleteProfile(@PathVariable String id) {
        service.delete(id);
    }

    @PostMapping({"/uploadCV", "/upload-cv", "/add-cv"})
    public ResponseEntity<UserProfile> uploadCV(@RequestParam("file") MultipartFile file, Authentication authentication) {
        if (authentication == null || !authentication.isAuthenticated()) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(null);
        }
        String userId = authentication.getName();
        logger.debug("Uploading CV for user: {}", userId);

        if (file.isEmpty()) {
            logger.warn("No file uploaded for user: {}", userId);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(null);
        }

        String contentType = file.getContentType();
        if (contentType == null || !(contentType.equals("application/pdf") ||
                                    contentType.equals("application/msword") ||
                                    contentType.equals("application/vnd.openxmlformats-officedocument.wordprocessingml.document"))) {
            logger.warn("Invalid file type for CV upload: {}. Expected PDF or Word document.", contentType);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(null);
        }

        long maxSize = 5 * 1024 * 1024;
        if (file.getSize() > maxSize) {
            logger.warn("File size exceeds limit for user: {}. Size: {}, Max: {}", userId, file.getSize(), maxSize);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(null);
        }

        try {
            UserProfile userProfile = service.uploadCV(userId, file);
            logger.info("CV uploaded successfully for user: {}", userId);
            return ResponseEntity.ok(userProfile);
        } catch (IOException e) {
            logger.error("Error uploading CV for user: {}", userId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    @PostMapping("/upload-photo")
    public ResponseEntity<?> uploadProfilePhoto(@RequestParam("file") MultipartFile file, Authentication authentication) {
        try {
            logger.info("Début de la requête d'upload de photo");

            // Vérifier l'authentification
            if (authentication == null || !authentication.isAuthenticated()) {
                logger.warn("Tentative d'upload sans authentification");
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(Map.of("error", "User not authenticated"));
            }

            logger.info("Utilisateur authentifié: {}", authentication.getName());

            // Vérifier si le fichier est vide
            if (file == null) {
                logger.warn("Fichier null pour l'utilisateur: {}", authentication.getName());
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(Map.of("error", "File is null"));
            }

            if (file.isEmpty()) {
                logger.warn("Fichier vide uploadé pour l'utilisateur: {}", authentication.getName());
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(Map.of("error", "File is empty"));
            }

            // Vérifier le type de fichier
            String contentType = file.getContentType();
            logger.info("Type de contenu du fichier: {}", contentType);

            if (contentType == null || !contentType.startsWith("image/")) {
                logger.warn("Type de fichier invalide pour l'upload de photo: {}. Image attendue.", contentType);
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(Map.of("error", "Invalid file type. Only images are allowed."));
            }

            // Informations sur le fichier
            logger.info("Upload de photo de profil pour l'utilisateur: {}, taille du fichier: {}, type de contenu: {}, nom original: {}",
                    authentication.getName(), file.getSize(), file.getContentType(), file.getOriginalFilename());

            // Appel au service
            logger.info("Appel au service uploadProfilePhoto");
            UserProfile profile = service.uploadProfilePhoto(authentication.getName(), file);

            // Vérification du résultat
            if (profile == null) {
                logger.warn("Profil utilisateur non trouvé pour: {}", authentication.getName());
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(Map.of("error", "User profile not found"));
            }

            logger.info("Photo de profil uploadée avec succès pour l'utilisateur: {}", authentication.getName());
            return ResponseEntity.ok(profile);
        } catch (IOException e) {
            logger.error("Erreur IO lors de l'upload de la photo de profil pour l'utilisateur: {}, message: {}",
                    authentication != null ? authentication.getName() : "unknown", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to upload photo: " + e.getMessage()));
        } catch (Exception e) {
            logger.error("Erreur inattendue lors de l'upload de la photo de profil: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Unexpected error: " + e.getMessage()));
        }
    }

    @PutMapping("/edit")
    public ResponseEntity<?> updateProfile(@RequestBody UserProfile updatedProfile, Authentication authentication) {
        if (authentication == null || !authentication.isAuthenticated()) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("User not authenticated");
        }
        String email = authentication.getName();
        User user = userService.getUserByEmail(email).orElseThrow();
        String userId = user.getId();
        logger.debug("Received updatedProfile for user {}: {}", userId, updatedProfile);

        UserProfile existingProfile = service.getByUserId(userId);
        if (existingProfile == null) {
            logger.info("No profile found for user {}, creating a new one", userId);
            existingProfile = new UserProfile();
            existingProfile.setUserId(userId);
            existingProfile.setEmail(updatedProfile.getEmail());
        } else {
            logger.info("Found existing profile for user {} with ID: {}", userId, existingProfile.getId());
            if (updatedProfile.getId() != null) {
                existingProfile.setId(updatedProfile.getId());
            }
        }

        existingProfile.setFirstName(updatedProfile.getFirstName());
        existingProfile.setLastName(updatedProfile.getLastName());
        existingProfile.setEmail(updatedProfile.getEmail());
        existingProfile.setPhoneNumber(updatedProfile.getPhoneNumber());
        existingProfile.setAddress(updatedProfile.getAddress());

        user.setFirstName(updatedProfile.getFirstName());
        user.setLastName(updatedProfile.getLastName());
        user.setEmail(updatedProfile.getEmail());
        user.setPhoneNumber(updatedProfile.getPhoneNumber());
        user.setAddress(updatedProfile.getAddress());

        userService.saveUser(user);

        // Gérer le champ bio s'il est présent dans la requête
        if (updatedProfile.getBio() != null) {
            existingProfile.setBio(updatedProfile.getBio());
            logger.debug("Updated bio for user {}: {}", userId, updatedProfile.getBio());
        }

        existingProfile.setUserId(userId);

        try {
            UserProfile savedProfile = service.save(existingProfile);
            logger.info("Profile saved to database for user {}: {}", userId, savedProfile);
            // Verify the saved profile matches the input
            UserProfile verifiedProfile = service.getByUserId(userId);
            if (verifiedProfile != null && !verifiedProfile.equals(savedProfile)) {
                logger.warn("Saved profile does not match verified profile for user {}: Saved={}, Verified={}", userId, savedProfile, verifiedProfile);
            }
            return ResponseEntity.ok(savedProfile);
        } catch (Exception e) {
            logger.error("Error saving profile for user {}: {}", userId, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Error saving profile: " + e.getMessage());
        }
    }

    @PostMapping("/add-education")
    public ResponseEntity<UserProfile> addEducation(@RequestBody Education education, Authentication authentication) {
        if (authentication == null || !authentication.isAuthenticated()) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(null);
        }
        try {
            UserProfile profile = service.addEducation(authentication.getName(), education);
            return ResponseEntity.ok(profile);
        } catch (Exception e) {
            logger.error("Error adding education for user: {}", authentication.getName(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(null);
        }
    }

    @PutMapping("/edit-education/{index}")
    public ResponseEntity<UserProfile> editEducation(@PathVariable int index, @RequestBody Education education, Authentication authentication) {
        if (authentication == null || !authentication.isAuthenticated()) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(null);
        }
        try {
            UserProfile profile = service.editEducation(authentication.getName(), education, index);
            return ResponseEntity.ok(profile);
        } catch (Exception e) {
            logger.error("Error editing education for user: {}", authentication.getName(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(null);
        }
    }

    @DeleteMapping("/delete-education/{index}")
    public ResponseEntity<Void> deleteEducation(@PathVariable int index, Authentication authentication) {
        if (authentication == null || !authentication.isAuthenticated()) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
        }
        try {
            service.deleteEducation(authentication.getName(), index);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            logger.error("Error deleting education for user: {}", authentication.getName(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
        }
    }

    @PostMapping("/add-certification")
    public ResponseEntity<UserProfile> addCertification(@RequestBody Certificate certification, Authentication authentication) {
        if (authentication == null || !authentication.isAuthenticated()) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(null);
        }
        try {
            UserProfile profile = service.addCertification(authentication.getName(), certification);
            return ResponseEntity.ok(profile);
        } catch (Exception e) {
            logger.error("Error adding certification for user: {}", authentication.getName(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(null);
        }
    }

    @PutMapping("/edit-certification/{index}")
    public ResponseEntity<UserProfile> editCertification(@PathVariable int index, @RequestBody Certificate certification, Authentication authentication) {
        if (authentication == null || !authentication.isAuthenticated()) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(null);
        }
        try {
            UserProfile profile = service.editCertification(authentication.getName(), certification, index);
            return ResponseEntity.ok(profile);
        } catch (Exception e) {
            logger.error("Error editing certification for user: {}", authentication.getName(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(null);
        }
    }

    @DeleteMapping("/delete-certification/{index}")
    public ResponseEntity<Void> deleteCertification(@PathVariable int index, Authentication authentication) {
        if (authentication == null || !authentication.isAuthenticated()) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
        }
        try {
            service.deleteCertification(authentication.getName(), index);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            logger.error("Error deleting certification for user: {}", authentication.getName(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
        }
    }

    @PostMapping("/add-experience")
    public ResponseEntity<UserProfile> addExperience(@RequestBody Experience experience, Authentication authentication) {
        if (authentication == null || !authentication.isAuthenticated()) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(null);
        }
        try {
            UserProfile profile = service.addExperience(authentication.getName(), experience);
            return ResponseEntity.ok(profile);
        } catch (Exception e) {
            logger.error("Error adding experience for user: {}", authentication.getName(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(null);
        }
    }

    @PutMapping("/edit-experience/{index}")
    public ResponseEntity<UserProfile> editExperience(@PathVariable int index, @RequestBody Experience experience, Authentication authentication) {
        if (authentication == null || !authentication.isAuthenticated()) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(null);
        }
        try {
            UserProfile profile = service.editExperience(authentication.getName(), experience, index);
            return ResponseEntity.ok(profile);
        } catch (Exception e) {
            logger.error("Error editing experience for user: {}", authentication.getName(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(null);
        }
    }

    @DeleteMapping("/delete-experience/{index}")
    public ResponseEntity<Void> deleteExperience(@PathVariable int index, Authentication authentication) {
        if (authentication == null || !authentication.isAuthenticated()) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
        }
        try {
            service.deleteExperience(authentication.getName(), index);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            logger.error("Error deleting experience for user: {}", authentication.getName(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
        }
    }

    @PostMapping("/add-skill")
    public ResponseEntity<UserProfile> addSkill(@RequestBody Skill skill, Authentication authentication) {
        if (authentication == null || !authentication.isAuthenticated()) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(null);
        }
        try {
            UserProfile profile = service.addSkill(authentication.getName(), skill);
            return ResponseEntity.ok(profile);
        } catch (Exception e) {
            logger.error("Error adding skill for user: {}", authentication.getName(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(null);
        }
    }

    @PutMapping("/edit-skill/{index}")
    public ResponseEntity<UserProfile> editSkill(@PathVariable int index, @RequestBody Skill skill, Authentication authentication) {
        if (authentication == null || !authentication.isAuthenticated()) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(null);
        }
        try {
            UserProfile profile = service.editSkill(authentication.getName(), skill, index);
            return ResponseEntity.ok(profile);
        } catch (Exception e) {
            logger.error("Error editing skill for user: {}", authentication.getName(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(null);
        }
    }

    @DeleteMapping("/delete-skill/{index}")
    public ResponseEntity<Void> deleteSkill(@PathVariable int index, Authentication authentication) {
        if (authentication == null || !authentication.isAuthenticated()) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
        }
        try {
            service.deleteSkill(authentication.getName(), index);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            logger.error("Error deleting skill for user: {}", authentication.getName(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
        }
    }

    @PostMapping("/add-link")
    public ResponseEntity<UserProfile> addLink(@RequestBody Link link, Authentication authentication) {
        if (authentication == null || !authentication.isAuthenticated()) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(null);
        }
        try {
            UserProfile profile = service.addLink(authentication.getName(), link);
            return ResponseEntity.ok(profile);
        } catch (Exception e) {
            logger.error("Error adding link for user: {}", authentication.getName(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(null);
        }
    }

    @PutMapping("/edit-link/{index}")
    public ResponseEntity<UserProfile> editLink(@PathVariable int index, @RequestBody Link link, Authentication authentication) {
        if (authentication == null || !authentication.isAuthenticated()) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(null);
        }
        try {
            UserProfile profile = service.editLink(authentication.getName(), link, index);
            return ResponseEntity.ok(profile);
        } catch (Exception e) {
            logger.error("Error editing link for user: {}", authentication.getName(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(null);
        }
    }

    @DeleteMapping("/delete-link/{index}")
    public ResponseEntity<Void> deleteLink(@PathVariable int index, Authentication authentication) {
        if (authentication == null || !authentication.isAuthenticated()) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
        }
        try {
            service.deleteLink(authentication.getName(), index);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            logger.error("Error deleting link for user: {}", authentication.getName(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
        }
    }

    /**
     * Endpoint spécifique pour mettre à jour uniquement la bio de l'utilisateur
     */
    @PatchMapping("/bio")
    public ResponseEntity<?> updateBio(@RequestBody BioUpdateRequest bioRequest, Authentication authentication) {
        if (authentication == null || !authentication.isAuthenticated()) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("User not authenticated");
        }

        String userId = authentication.getName();
        logger.info("Updating bio for user {}", userId);

        try {
            UserProfile existingProfile = service.getByUserId(userId);
            if (existingProfile == null) {
                logger.warn("No profile found for user {} when updating bio", userId);
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body("User profile not found");
            }

            // Mettre à jour uniquement la bio
            existingProfile.setBio(bioRequest.getBio());
            logger.debug("New bio for user {}: {}", userId, bioRequest.getBio());

            UserProfile savedProfile = service.save(existingProfile);
            logger.info("Bio updated successfully for user {}", userId);

            return ResponseEntity.ok(savedProfile);
        } catch (Exception e) {
            logger.error("Error updating bio for user {}: {}", userId, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error updating bio: " + e.getMessage());
        }
    }

    /**
     * Classe pour la requête de mise à jour de la bio
     */
    public static class BioUpdateRequest {
        private String bio;

        public String getBio() {
            return bio;
        }

        public void setBio(String bio) {
            this.bio = bio;
        }
    }

    private String saveFile(MultipartFile file) {
        try {
            String uploadDir = "path/to/save/directory";
            String fileName = file.getOriginalFilename();
            String filePath = uploadDir + "/" + fileName;

            file.transferTo(new java.io.File(filePath));

            return filePath;
        } catch (Exception e) {
            throw new RuntimeException("Error saving file: " + e.getMessage());
        }
    }
}