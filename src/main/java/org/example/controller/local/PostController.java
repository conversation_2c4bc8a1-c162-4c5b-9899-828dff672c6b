package org.example.controller.local;

import org.example.dto.UserDto;
import org.example.exceptions.post.PostNotFoundException;
import org.example.model.Post;
import org.example.service.post.PostService;
import org.example.service.post.local.PostRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.springframework.security.core.Authentication;

@RestController
@RequestMapping("/api/posts")
@CrossOrigin(origins = "http://localhost:4200", allowedHeaders = {"Authorization", "Content-Type"}, methods = {RequestMethod.GET, RequestMethod.POST, RequestMethod.PUT, RequestMethod.PATCH, RequestMethod.DELETE, RequestMethod.OPTIONS}, allowCredentials = "true")
public class PostController {
    private final PostRepository postRepository;
    private final PostService postService;
    private final Logger logger = LoggerFactory.getLogger(PostController.class);

    public PostController(PostService postService, PostRepository postRepository) {
        this.postService = postService;
        this.postRepository = postRepository;
    }

    @PostMapping("/create")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<Post> createPost(@RequestBody Post post) {
        logger.debug("Creating a new post: {}", post.getTitre());
        logger.trace("Post details: {}", post);
        Post createdPost = postService.createPost(post);
        return ResponseEntity.status(HttpStatus.CREATED).body(createdPost);
    }

    @GetMapping("/search")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<Page<Post>> searchPosts(Pageable pageable) {
        logger.debug("Searching for all posts");
        Page<Post> posts = postService.search(new Post(), pageable);
        return ResponseEntity.ok(posts);
    }

    @GetMapping("/{id}")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<Post> getPostById(@PathVariable String id) {
        logger.debug("Fetching post with ID: {}", id);
        return postService.getPostById(id)
                .map(post -> ResponseEntity.ok(post))
                .orElseThrow(() -> {
                    logger.warn("Post with ID {} not found", id);
                    return new PostNotFoundException("Post avec ID " + id + " non trouvé.");
                });
    }

    @PutMapping("/{id}")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<Post> updatePost(@PathVariable String id, @RequestBody Post postDetails) {
        logger.debug("Updating post with ID: {}", id);
        logger.trace("Updated details: {}", postDetails);
        try {
            Post updatedPost = postService.updatePost(id, postDetails);
            logger.debug("Post updated successfully: {}", updatedPost);
            return ResponseEntity.ok(updatedPost);
        } catch (Exception e) {
            logger.error("Error updating post with ID {}: {}", id, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<Map<String, Boolean>> deletePost(@PathVariable String id) {
        logger.debug("Deleting post with ID: {}", id);
        postService.deletePost(id);
        return ResponseEntity.ok(Map.of("deleted", true));
    }

    @GetMapping("/archived")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<List<Post>> getArchivedPosts(@RequestParam String userId) {
        logger.debug("Fetching archived posts for user: {}", userId);
        try {
            List<Post> archivedPosts = postRepository.findByUserIdAndArchivedTrue(userId);
            logger.debug("Found {} archived posts", archivedPosts.size());
            return ResponseEntity.ok(archivedPosts);
        } catch (Exception e) {
            logger.error("Error fetching archived posts: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new ArrayList<>());
        }
    }


    @PostMapping("/{id}/archive")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<Post> archivePost(@PathVariable String id) {
        logger.debug("Archiving post with ID: {}", id);
        return postRepository.findById(id)
                .map(post -> {
                    post.setArchived(true);
                    Post updatedPost = postRepository.save(post);
                    logger.debug("Post archived successfully: {}", updatedPost);
                    return ResponseEntity.ok(updatedPost);
                })
                .orElseThrow(() -> {
                    logger.warn("Post with ID {} not found", id);
                    return new PostNotFoundException("Post avec ID " + id + " non trouvé.");
                });
    }
    @GetMapping("/{id}/candidates")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<List<UserDto>> getCandidatesForPost(@PathVariable String id) {
        logger.debug("Fetching candidates for post with ID: {}", id);
        List<UserDto> candidates = postService.getCandidatesByPostId(id);
        return ResponseEntity.ok(candidates);
    }

    @GetMapping("/my-posts")
    public ResponseEntity<Page<Post>> getMyPosts(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            Authentication authentication) {
        try {
            String userId = authentication.getName();
            logger.debug("Fetching posts for user: {}", userId);

            PageRequest pageRequest = PageRequest.of(page, size, Sort.by("datePublication").descending());
            Page<Post> posts = postService.getPostsByUserId(userId, pageRequest);

            return ResponseEntity.ok(posts);
        } catch (Exception e) {
            logger.error("Error fetching posts: ", e);
            return ResponseEntity.internalServerError().build();
        }
    }
}
