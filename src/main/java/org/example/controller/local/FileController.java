package org.example.controller.local;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/files")
public class FileController {

    private static final Logger logger = LoggerFactory.getLogger(FileController.class);
    private static final String UPLOAD_DIR = "uploads";
    private static final String WORKSPACES_DIR = UPLOAD_DIR + "/workspaces";

    /**
     * S'assure que le dossier d'upload existe
     */
    @PostMapping("/ensure-upload-directory")
    public ResponseEntity<Map<String, Object>> ensureUploadDirectoryExists() {
        logger.info("Ensuring upload directory exists");
        Map<String, Object> response = new HashMap<>();
        
        try {
            // Créer le dossier uploads s'il n'existe pas
            Path uploadsPath = Paths.get(UPLOAD_DIR);
            if (!Files.exists(uploadsPath)) {
                Files.createDirectories(uploadsPath);
                logger.info("Created uploads directory: {}", uploadsPath.toAbsolutePath());
            }
            
            // Créer le dossier workspaces s'il n'existe pas
            Path workspacesPath = Paths.get(WORKSPACES_DIR);
            if (!Files.exists(workspacesPath)) {
                Files.createDirectories(workspacesPath);
                logger.info("Created workspaces directory: {}", workspacesPath.toAbsolutePath());
            }
            
            response.put("success", true);
            response.put("message", "Upload directories created successfully");
            response.put("uploadsPath", uploadsPath.toAbsolutePath().toString());
            response.put("workspacesPath", workspacesPath.toAbsolutePath().toString());
            
            return ResponseEntity.ok(response);
        } catch (IOException e) {
            logger.error("Error creating upload directories", e);
            response.put("success", false);
            response.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * Télécharge un fichier
     */
    @PostMapping("/upload")
    public ResponseEntity<Map<String, Object>> uploadFile(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "path", defaultValue = "") String path) {
        
        logger.info("Uploading file: {}", file.getOriginalFilename());
        Map<String, Object> response = new HashMap<>();
        
        if (file.isEmpty()) {
            response.put("success", false);
            response.put("error", "File is empty");
            return ResponseEntity.badRequest().body(response);
        }
        
        try {
            // Déterminer le chemin de destination
            String destinationPath = UPLOAD_DIR;
            if (!path.isEmpty()) {
                destinationPath = destinationPath + "/" + path;
            }
            
            // Créer le dossier de destination s'il n'existe pas
            Path dirPath = Paths.get(destinationPath);
            if (!Files.exists(dirPath)) {
                Files.createDirectories(dirPath);
                logger.info("Created directory: {}", dirPath.toAbsolutePath());
            }
            
            // Sauvegarder le fichier
            String fileName = file.getOriginalFilename();
            Path filePath = Paths.get(destinationPath, fileName);
            Files.copy(file.getInputStream(), filePath);
            
            response.put("success", true);
            response.put("message", "File uploaded successfully");
            response.put("path", filePath.toAbsolutePath().toString());
            
            return ResponseEntity.ok(response);
        } catch (IOException e) {
            logger.error("Error uploading file", e);
            response.put("success", false);
            response.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
}
