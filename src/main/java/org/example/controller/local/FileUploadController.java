package org.example.controller.local;

import org.example.model.Workspace;
import org.example.service.workspace.local.WorkspaceRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

@RestController
@RequestMapping("/api/files")
public class FileUploadController {

    private static final Logger logger = LoggerFactory.getLogger(FileUploadController.class);

    @Autowired
    private WorkspaceRepository workspaceRepository;

    @Value("${uploadFolder}")
    private String uploadFolder;

    /**
     * Endpoint pour tester si le contrôleur est accessible
     */
    @GetMapping("/test")
    public ResponseEntity<String> testEndpoint() {
        logger.info("FileUploadController test endpoint called");
        return ResponseEntity.ok("FileUploadController is working!");
    }

    /**
     * Endpoint pour l'upload du logo d'un workspace
     */
    @PostMapping(value = "/workspace/{id}/logo", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<?> uploadWorkspaceLogo(@PathVariable String id, @RequestParam("logo") MultipartFile logo) {
        logger.info("Uploading logo for workspace with ID: {}", id);
        logger.info("Logo file received: name={}, contentType={}, size={}",
                logo.getOriginalFilename(), logo.getContentType(), logo.getSize());

        // Vérifier si le workspace existe
        Optional<Workspace> workspaceOptional = workspaceRepository.findById(id);
        if (workspaceOptional.isEmpty()) {
            logger.warn("Workspace with ID {} not found", id);
            Map<String, String> response = new HashMap<>();
            response.put("error", "Workspace not found");
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        }

        Workspace workspace = workspaceOptional.get();
        logger.info("Workspace found: {}", workspace.getName());

        // Traitement du logo
        if (logo != null && !logo.isEmpty()) {
            try {
                logger.info("Processing logo file...");

                // Créer un chemin de stockage dans le dossier configuré
                String workspacesDir = "workspaces";
                String fileName = UUID.randomUUID() + "_" + logo.getOriginalFilename();
                logger.info("Generated filename: {}", fileName);

                // Créer le chemin complet vers le dossier d'upload
                Path rootPath = Paths.get(uploadFolder);
                if (!Files.exists(rootPath)) {
                    logger.info("Creating root upload directory: {}", rootPath.toAbsolutePath());
                    Files.createDirectories(rootPath);
                    logger.info("Root directory created: {}", Files.exists(rootPath));
                } else {
                    logger.info("Root directory already exists: {}", rootPath.toAbsolutePath());
                }

                // S'assurer que le dossier de workspaces existe
                Path workspacesPath = rootPath.resolve(workspacesDir);
                if (!Files.exists(workspacesPath)) {
                    logger.info("Creating workspaces directory: {}", workspacesPath.toAbsolutePath());
                    Files.createDirectories(workspacesPath);
                    logger.info("Workspaces directory created: {}", Files.exists(workspacesPath));
                } else {
                    logger.info("Workspaces directory already exists: {}", workspacesPath.toAbsolutePath());
                }

                // Vérifier que le dossier a été créé
                if (!Files.exists(workspacesPath)) {
                    logger.error("Failed to create directory: {}", workspacesPath.toAbsolutePath());
                    Map<String, String> response = new HashMap<>();
                    response.put("error", "Failed to create upload directory");
                    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
                }

                // Vérifier les permissions du dossier
                if (!Files.isWritable(workspacesPath)) {
                    logger.error("Directory is not writable: {}", workspacesPath.toAbsolutePath());
                    Map<String, String> response = new HashMap<>();
                    response.put("error", "Upload directory is not writable");
                    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
                } else {
                    logger.info("Directory is writable: {}", workspacesPath.toAbsolutePath());
                }

                // Sauvegarde physique du fichier
                Path filePath = workspacesPath.resolve(fileName);
                logger.info("Saving logo to: {}", filePath.toAbsolutePath());

                try {
                    Files.copy(logo.getInputStream(), filePath, StandardCopyOption.REPLACE_EXISTING);
                    logger.info("File saved successfully: {}", Files.exists(filePath));
                } catch (Exception e) {
                    logger.error("Error saving file: {}", e.getMessage(), e);
                    Map<String, String> response = new HashMap<>();
                    response.put("error", "Failed to save file: " + e.getMessage());
                    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
                }

                // Enregistre l'URL (ou chemin) dans l'entité
                String logoUrl = "/uploads/" + workspacesDir + "/" + fileName;
                workspace.setLogoUrl(logoUrl);
                logger.info("Logo URL set to: {}", workspace.getLogoUrl());

                // Sauvegarder le workspace
                try {
                    Workspace updatedWorkspace = workspaceRepository.save(workspace);
                    logger.info("Workspace saved successfully: id={}, name={}, logoUrl={}",
                            updatedWorkspace.getId(), updatedWorkspace.getName(), updatedWorkspace.getLogoUrl());

                    Map<String, Object> response = new HashMap<>();
                    response.put("id", updatedWorkspace.getId());
                    response.put("name", updatedWorkspace.getName());
                    response.put("logoUrl", updatedWorkspace.getLogoUrl());

                    return ResponseEntity.ok(response);
                } catch (Exception e) {
                    logger.error("Error saving workspace: {}", e.getMessage(), e);
                    Map<String, String> response = new HashMap<>();
                    response.put("error", "Failed to save workspace: " + e.getMessage());
                    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
                }
            } catch (IOException e) {
                logger.error("Error processing logo: {}", e.getMessage(), e);
                Map<String, String> response = new HashMap<>();
                response.put("error", "Failed to process logo: " + e.getMessage());
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
            }
        } else {
            logger.warn("No logo file received or empty file");
            Map<String, String> response = new HashMap<>();
            response.put("error", "No logo file received or empty file");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }
    }
}
