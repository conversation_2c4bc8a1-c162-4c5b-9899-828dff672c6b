package org.example.controller.local;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.example.dto.TestDTO;
import org.example.model.Test;
import org.example.service.modelIA.LlamaService;
import org.example.service.pdf.PdfSummaryService;
import org.example.service.test.TestService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import reactor.core.publisher.Mono;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@RestController
@RequestMapping("/api/llama")
@CrossOrigin(origins = "*")
public class LlamaController {

    private static final Logger logger = LoggerFactory.getLogger(LlamaController.class);

    @Autowired
    private LlamaService llamaService;

    @Autowired
    private TestService testService;

    @Autowired
    private PdfSummaryService pdfSummaryService;

    @Autowired
    private ObjectMapper objectMapper;

    // ----------- 1. ASK LLAMA FREELY -----------
    @PostMapping("/ask")
    public Mono<String> askLlama(@RequestBody Map<String, String> request) {
        logger.trace("Received request: {}", request);
        String question = request.get("question");

        if (question == null || question.trim().isEmpty()) {
            return Mono.error(new IllegalArgumentException("❌ Question is empty!"));
        }

        return llamaService.askQuestion(question)
                .doOnError(e -> logger.error("Error processing question: {}", e.getMessage(), e));
    }

    // ----------- 2. GENERATE TEST JSON -----------
    @PostMapping("/generate-test")
    public Mono<String> generateTest(@RequestBody Map<String, String> request) {
        String subject = request.get("subject");
        int count = Integer.parseInt(request.getOrDefault("count", "3"));

        String prompt = """
            Génère un JSON de test conforme à ce format :
            {
              "id": null,
              "title": "Titre du test",
              "questions": [
                { "content": "..." }
              ]
            }
            Sujet : "%s"
            Nombre de questions : %d
            Réponds uniquement avec un JSON valide.
            """.formatted(subject, count);

        return llamaService.askQuestion(prompt);
    }

    // ----------- 3. GENERATE AND SAVE TEST -----------
    @PostMapping("/generate-test-and-save")
    public Mono<Test> generateAndSaveTest(@RequestBody Map<String, String> request) {
        String subject = request.get("subject");
        int count = Integer.parseInt(request.getOrDefault("count", "3"));

        String prompt = """
            Génère un JSON de test conforme à ce format :
            {
              "id": null,
              "title": "Titre du test",
              "questions": [
                { "content": "..." }
              ]
            }
            Sujet : "%s"
            Nombre de questions : %d
            Réponds uniquement avec un JSON valide.
            """.formatted(subject, count);

        return llamaService.askQuestion(prompt)
                .flatMap(response -> {
                    try {
                        TestDTO testDTO = objectMapper.readValue(response, TestDTO.class);
                        return Mono.just(testService.createTest(testDTO));
                    } catch (Exception e) {
                        logger.error("Erreur de parsing JSON: {}", e.getMessage(), e);
                        return Mono.error(new RuntimeException("Erreur parsing JSON: " + e.getMessage(), e));
                    }
                });
    }

    // ----------- 4. SUMMARIZE PDF AND EXTRACT CV DATA (MultipartFile) -----------
    @PostMapping("/summarize-pdf")
    public Mono<ResponseEntity<Map<String, Object>>> summarizePdf(@RequestParam("file") MultipartFile file) {
        logger.debug("Received file: {}", file != null ? file.getOriginalFilename() : "null");

        if (file == null || file.isEmpty()) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", "Aucun fichier fourni.");
            return Mono.just(ResponseEntity.badRequest().body(errorResponse));
        }

        String contentType = file.getContentType();
        if (contentType == null || !contentType.equals("application/pdf")) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", "Type de fichier invalide. Attendu : application/pdf, reçu : " + contentType);
            return Mono.just(ResponseEntity.badRequest().body(errorResponse));
        }

        try {
            Map<String, Object> cvData = pdfSummaryService.getPdfSummary(file);
            if (cvData == null || !cvData.containsKey("text")) {
                logger.error("Failed to extract text from CV: cvData is null or missing 'text' field");
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("error", "Échec de l'extraction du texte du CV.");
                return Mono.just(ResponseEntity.internalServerError().body(errorResponse));
            }

            String textContent = (String) cvData.get("text");
            logger.debug("Extracted text from CV: {}", textContent);

            String prompt = """
                Résume le contenu suivant extrait d’un CV dans ce format JSON :
                {
                  "name": "Nom Prénom",
                  "summary": "Résumé professionnel",
                  "skills": ["Skill1", "Skill2"],
                  "experience": [
                    {
                      "title": "Titre",
                      "company": "Entreprise",
                      "period": "Date début - Date fin",
                      "description": "Description"
                    }
                  ],
                  "education": [
                    {
                      "degree": "Diplôme",
                      "school": "Établissement",
                      "year": "Année"
                    }
                  ]
                }

                Texte :
                %s
                """.formatted(textContent);

            return llamaService.askQuestion(prompt)
                    .map(json -> {
                        try {
                            logger.debug("Llama response: {}", json);
                            Pattern jsonPattern = Pattern.compile("```json\\s*(.*?)```", Pattern.DOTALL);
                            Matcher matcher = jsonPattern.matcher(json);
                            String cleanedJson = matcher.find() ? matcher.group(1).trim() : json;

                            Map<String, Object> structuredSummary = objectMapper.readValue(cleanedJson, Map.class);
                            return ResponseEntity.ok(structuredSummary);
                        } catch (Exception e) {
                            logger.error("Erreur lors de l’analyse JSON: {}", e.getMessage(), e);
                            Map<String, Object> error = new HashMap<>();
                            error.put("error", "Erreur lors de l’analyse JSON : " + e.getMessage());
                            return ResponseEntity.internalServerError().body(error);
                        }
                    })
                    .onErrorResume(e -> {
                        logger.error("Erreur lors de l’appel à Llama: {}", e.getMessage(), e);
                        Map<String, Object> error = new HashMap<>();
                        error.put("error", "Erreur lors de l’appel à Llama : " + e.getMessage());
                        return Mono.just(ResponseEntity.internalServerError().body(error));
                    });

        } catch (Exception e) {
            logger.error("Erreur lors du traitement du PDF: {}", e.getMessage(), e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", "Erreur lors du traitement du PDF : " + e.getMessage());
            return Mono.just(ResponseEntity.internalServerError().body(errorResponse));
        }
    }

    // ----------- 5. SUMMARIZE PDF (Base64 JSON) -----------
    @PostMapping("/summarize-pdf-base64")
    public Mono<ResponseEntity<Map<String, Object>>> summarizePdfBase64(@RequestBody Map<String, String> request) {
        logger.debug("Received base64 request: {}", request);

        String base64Pdf = request.get("pdfContent");
        if (base64Pdf == null || base64Pdf.trim().isEmpty()) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", "Aucun contenu PDF fourni.");
            return Mono.just(ResponseEntity.badRequest().body(errorResponse));
        }

        try {
            // Decode base64 to bytes
            byte[] pdfBytes = Base64.getDecoder().decode(base64Pdf);
            logger.debug("Decoded base64 PDF, size: {} bytes", pdfBytes.length);

            // Create a MultipartFile from bytes using the helper class
            MultipartFile multipartFile = new MultipartFileAdapter("cv.pdf", pdfBytes);
            return summarizePdf(multipartFile);

        } catch (IllegalArgumentException e) {
            logger.error("Erreur de décodage base64: {}", e.getMessage(), e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", "Contenu PDF invalide (base64 mal formé) : " + e.getMessage());
            return Mono.just(ResponseEntity.badRequest().body(errorResponse));
        } catch (Exception e) {
            logger.error("Erreur lors du traitement du PDF (base64): {}", e.getMessage(), e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", "Erreur lors du traitement du PDF (base64) : " + e.getMessage());
            return Mono.just(ResponseEntity.internalServerError().body(errorResponse));
        }
    }

    // ----------- Helper class to convert byte array to MultipartFile -----------
    private static class MultipartFileAdapter implements MultipartFile {
        private final String name;
        private final byte[] content;

        public MultipartFileAdapter(String name, byte[] content) {
            this.name = name;
            this.content = content;
        }

        @Override
        public String getName() {
            return "file";
        }

        @Override
        public String getOriginalFilename() {
            return name;
        }

        @Override
        public String getContentType() {
            return "application/pdf";
        }

        @Override
        public boolean isEmpty() {
            return content == null || content.length == 0;
        }

        @Override
        public long getSize() {
            return content.length;
        }

        @Override
        public byte[] getBytes() {
            return content;
        }

        @Override
        public InputStream getInputStream() {
            return new ByteArrayInputStream(content);
        }

        @Override
        public Resource getResource() {
            return new ByteArrayResource(content);
        }

        @Override
        public void transferTo(File dest) throws IOException {
            try (FileOutputStream fos = new FileOutputStream(dest)) {
                fos.write(content);
            }
        }

        @Override
        public void transferTo(Path dest) throws IOException {
            Files.write(dest, content);
        }
    }
}