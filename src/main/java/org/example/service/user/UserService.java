package org.example.service.user;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import org.example.exceptions.login.AuthenticationException;
import org.example.model.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface UserService {
    User me();
    boolean existsByEmail(@NotBlank(message = "Email is mandatory") String email);
    User findById(String id);
    User findById(Long senderId);
    User findByEmail(String email);
    List<User> searchForInvitation(String search, String workspaceId);

    void saveUser(User user);

    record SignUpRequest(
            @NotBlank(message = "Email is mandatory") String email,
            @NotBlank(message = "Password is mandatory") String password,
            @NotBlank(message = "First name is mandatory") String firstName,
            @NotBlank(message = "Last name is mandatory") String lastName,
            String address,
            Date dateOfBirth,
            boolean enabled,
            @NotBlank(message = "Phone number is required")
            @Pattern(regexp = "\\d+", message = "Phone number must contain only digits") String phoneNumber,
            boolean agreeToTerms,
            String role
    ) {
        public User toUser(org.springframework.security.crypto.password.PasswordEncoder passwordEncoder) {
            User user = new User();
            user.setEmail(email);
            user.setPassword(passwordEncoder.encode(password));
            user.setFirstName(firstName);
            user.setLastName(lastName);
            user.setAddress(address);
            user.setDateOfBirth(dateOfBirth);
            user.setPhoneNumber(phoneNumber);
            user.setAgreeToTerms(agreeToTerms);
            return user;
        }
    }

    boolean signUp(SignUpRequest signUpRequest);
    boolean checkEmail(String email);
    User createUser(User user);
    Page<User> getAllUsers(Pageable pageable);
    Optional<User> getUserById(String id);
    Optional<User> getUserByEmail(String email);
    User updateUser(String id, User userDetails);
    void deleteUser(String id);
    Page<User> getUsersByWorkspaceId(String workspaceId, Pageable pageable);
    void assignUserToWorkspace(String userId, String workspaceId);
    Page<User> search(User example, Pageable pageable);
    void changePassword(String email, String oldPassword, String newPassword) throws AuthenticationException;
}