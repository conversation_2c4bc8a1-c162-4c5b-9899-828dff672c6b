package org.example.service.user.local;

import jakarta.transaction.Transactional;
import org.example.exceptions.login.AuthenticationException;
import org.example.exceptions.user.UserAlreadyExistsException;
import org.example.exceptions.user.UserNotFoundException;
import org.example.model.Invitation;
import org.example.model.User;
import org.example.service.invitation.InvitationService;
import org.example.service.user.UserService;
import org.example.service.workspace.WorkspaceService;
import org.example.service.workspace.local.MembershipRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Lookup;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.support.PageableExecutionUtils;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public abstract class DefaultUserService implements UserService, UserDetailsService {

    private static final Logger logger = LoggerFactory.getLogger(DefaultUserService.class);

    protected final UserRepository userRepository;
    protected final PasswordEncoder passwordEncoder;

    public DefaultUserService(UserRepository userRepository, PasswordEncoder passwordEncoder) {
        this.userRepository = userRepository;
        this.passwordEncoder = passwordEncoder;
    }

    @Override
    public User me() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || authentication.getPrincipal() == null) {
            throw new AuthenticationException("Utilisateur non authentifié !");
        }

        Object principal = authentication.getPrincipal();
        if (principal instanceof Jwt jwt) {
            String email = jwt.getSubject();
            return getUserByEmail(email)
                    .orElseThrow(() -> new UserNotFoundException("Utilisateur avec email " + email + " non trouvé."));
        }
        throw new AuthenticationException("Erreur d'authentification !");
    }

    @Override
    public boolean existsByEmail(String email) {
        return userRepository.existsByEmail(email);
    }

    @Override
    public User findById(String id) {
        return userRepository.findById(id).orElseThrow(() -> new UserNotFoundException("User not found with ID: " + id));
    }

    @Override
    public User findById(Long id) {
        return userRepository.findById(String.valueOf(id)).orElseThrow(() -> new UserNotFoundException("User not found with ID: " + id));
    }

    @Override
    public User findByEmail(String email) {
        return userRepository.findByEmail(email).orElseThrow(() -> new UserNotFoundException("User not found with email: " + email));
    }

    @Override
    public List<User> searchForInvitation(String search, String workspaceId) {
        Set<MembershipRepository.MembershipEmail> members = getWorkspaceService().findMembers(workspaceId);
        Set<Invitation> invitedUsers = getInvitationService().findInvitedUsers(workspaceId);
        Set<String> excludedIds = Stream.concat(
                        members.stream().map(MembershipRepository.MembershipEmail::receiverId),
                        invitedUsers.stream().map(Invitation::getReceiver).map(User::getId))
                .collect(Collectors.toSet());

        if (search == null || search.isBlank()) {
            return userRepository.searchForInvitation(excludedIds);
        }
        return userRepository.searchForInvitation(search, excludedIds);
    }

    @Override
    public boolean signUp(SignUpRequest signUpRequest) {
        User user = signUpRequest.toUser(passwordEncoder);
        userRepository.save(user);
        return true;
    }

    @Override
    public boolean checkEmail(String email) {
        return userRepository.existsByEmail(email);
    }

    @Override
    public User createUser(User user) {
        if (userRepository.existsByEmail(user.getEmail())) {
            throw new UserAlreadyExistsException("Email already exists");
        }
        user.setPassword(passwordEncoder.encode(user.getPassword()));
        return userRepository.save(user);
    }

    @Override
    public Page<User> getAllUsers(Pageable pageable) {
        return userRepository.findAll(pageable);
    }

    @Override
    public Optional<User> getUserById(String id) {
        return userRepository.findById(id);
    }

    @Override
    public Optional<User> getUserByEmail(String email) {
        return userRepository.findByEmail(email);
    }

    @Override
    @Transactional
    public User updateUser(String id, User userDetails) {
        return userRepository.findById(id)
                .map(user -> {
                    user.setFirstName(userDetails.getFirstName());
                    user.setLastName(userDetails.getLastName());
                    user.setEmail(userDetails.getEmail());
                    if (userDetails.getPassword() != null && !userDetails.getPassword().isEmpty()) {
                        user.setPassword(passwordEncoder.encode(userDetails.getPassword()));
                    }
                    user.setPhoneNumber(userDetails.getPhoneNumber());
                    user.setAddress(userDetails.getAddress());
                    return userRepository.save(user);
                })
                .orElseThrow(() -> new UserNotFoundException("User not found with ID: " + id));
    }

    @Transactional
    public User updateProfile(String userId, User updatedUser) {
        User existingUser = userRepository.findById(userId)
                .orElseThrow(() -> new UserNotFoundException("User not found with ID: " + userId));

        // Validation de l'email
        if (updatedUser.getEmail() != null && !updatedUser.getEmail().isEmpty() &&
                !updatedUser.getEmail().equals(existingUser.getEmail())) {
            if (userRepository.existsByEmail(updatedUser.getEmail())) {
                throw new UserAlreadyExistsException("Email is already in use: " + updatedUser.getEmail());
            }
            existingUser.setEmail(updatedUser.getEmail());
        }

        // Mise à jour des champs uniquement si les nouvelles valeurs ne sont pas null ou vides
        if (updatedUser.getFirstName() != null && !updatedUser.getFirstName().isEmpty()) {
            existingUser.setFirstName(updatedUser.getFirstName());
        }
        if (updatedUser.getLastName() != null && !updatedUser.getLastName().isEmpty()) {
            existingUser.setLastName(updatedUser.getLastName());
        }
        if (updatedUser.getPhoneNumber() != null && !updatedUser.getPhoneNumber().isEmpty()) {
            existingUser.setPhoneNumber(updatedUser.getPhoneNumber());
        }
        if (updatedUser.getAddress() != null && !updatedUser.getAddress().isEmpty()) {
            existingUser.setAddress(updatedUser.getAddress());
        }

        try {
            return userRepository.save(existingUser);
        } catch (Exception e) {
            throw new RuntimeException("Failed to update user profile: " + e.getMessage(), e);
        }
    }

    @Override
    public void deleteUser(String id) {
        if (!userRepository.existsById(id)) {
            throw new UserNotFoundException("User not found with ID: " + id);
        }
        userRepository.deleteById(id);
    }

    @Override
    public Page<User> getUsersByWorkspaceId(String workspaceId, Pageable pageable) {
        List<User> users = userRepository.findByWorkspaceId(workspaceId);
        return PageableExecutionUtils.getPage(users, pageable, users::size);
    }

    @Override
    public void assignUserToWorkspace(String userId, String workspaceId) {
        userRepository.findById(userId).ifPresent(user -> {
            user.setWorkspaceId(workspaceId);
            userRepository.save(user);
        });
    }

    @Override
    public Page<User> search(User example, Pageable pageable) {
        return userRepository.findAll(pageable);
    }

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        return userRepository.findByEmail(username)
                .orElseThrow(() -> new UserNotFoundException("User not found with email: " + username));
    }

    @Lookup
    protected abstract WorkspaceService getWorkspaceService();

    @Lookup
    protected abstract InvitationService getInvitationService();

    @Override
    public void changePassword(String email, String oldPassword, String newPassword) {
        logger.debug("Attempting to change password for email: {}", email);

        if (email == null || email.trim().isEmpty()) {
            logger.warn("Email is empty");
            throw new IllegalArgumentException("Email cannot be empty");
        }

        User user = getUserByEmail(email)
                .orElseThrow(() -> {
                    logger.warn("User with email {} not found", email);
                    return new UserNotFoundException("User with email " + email + " not found");
                });

        if (oldPassword == null || !passwordEncoder.matches(oldPassword, user.getPassword())) {
            logger.warn("Incorrect old password for user: {}", email);
            throw new AuthenticationException("Incorrect old password");
        }

        if (newPassword == null || newPassword.trim().isEmpty()) {
            logger.warn("New password is invalid for user: {}", email);
            throw new IllegalArgumentException("New password cannot be empty");
        }

        user.setPassword(passwordEncoder.encode(newPassword));
        userRepository.save(user);
        logger.info("Password updated successfully for user: {}", email);
    }

    @Override
    public void saveUser(User user) {
        if (user == null) {
            logger.warn("Attempted to save null user");
            throw new IllegalArgumentException("User cannot be null");
        }
        if (user.getEmail() == null || user.getEmail().trim().isEmpty()) {
            logger.warn("Attempted to save user with invalid email");
            throw new IllegalArgumentException("Email is required");
        }

        userRepository.save(user);
        logger.info("User saved successfully: {}", user.getEmail());
    }
}