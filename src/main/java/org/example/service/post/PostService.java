package org.example.service.post;

import org.example.dto.UserDto;
import org.example.model.Post;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;

public interface PostService {
    Post createPost(Post post);
    Optional<Post> getPostById(String id);
    Post updatePost(String id, Post postDetails);
    void deletePost(String id);
    Page<Post> search(Post example, Pageable pageable);

    List<UserDto> getCandidatesByPostId(String postId);

    Page<Post> getPostsByUserId(String userId, Pageable pageable);
    // autres méthodes existantes...
}
