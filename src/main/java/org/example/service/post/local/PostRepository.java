package org.example.service.post.local;

import org.example.model.Post;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PostRepository extends MongoRepository<Post, String> {

    // ✅ Méthode correctement typée pour la pagination
    Page<Post> findByUserId(String userId, Pageable pageable);

    @Query("{ 'recruteurId': ?0 }")
    List<Post> findByRecruteurId(String recruteurId);

    @Query("{ 'entreprise': ?0 }")
    List<Post> findByEntreprise(String entreprise);

    @Query("{ 'titre': ?0 }")
    List<Post> findByTitre(String titre);

    @Query("{ 'archived': true }")
    List<Post> findByArchivedTrue();

    List<Post> findByUserIdAndArchivedTrue(String userId);
}
