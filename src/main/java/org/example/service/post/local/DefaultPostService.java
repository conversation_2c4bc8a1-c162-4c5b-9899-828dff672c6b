package org.example.service.post.local;

import org.example.dto.UserDto;
import org.example.exceptions.post.PostNotFoundException;
import org.example.model.Post;
import org.example.model.User;
import org.example.service.post.PostService;
import org.example.service.user.local.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class DefaultPostService implements PostService {

    private static final Logger logger = LoggerFactory.getLogger(DefaultPostService.class);

    private final PostRepository postRepository;
    private final UserRepository userRepository;

    public DefaultPostService(PostRepository postRepository, UserRepository userRepository) {
        this.postRepository = postRepository;
        this.userRepository = userRepository;
    }

    @Override
    public Post createPost(Post post) {
        post.setDatePublication(LocalDateTime.now());

        // Récupérer l'utilisateur à partir du userId
        Optional<User> user = userRepository.findById(post.getUserId());
        user.ifPresent(u -> {
            post.setUserFullName(u.getFullName()); // ou u.getFirstName() + " " + u.getLastName()
        });

        return postRepository.save(post);
    }

    @Override
    public Optional<Post> getPostById(String id) {
        logger.debug("Looking up post with ID: {}", id);
        return postRepository.findById(id);
    }

    @Override
    public Post updatePost(String id, Post postDetails) {
        return postRepository.findById(id)
                .map(post -> {
                    post.setTitre(postDetails.getTitre());
                    post.setDescription(postDetails.getDescription());
                    post.setEntreprise(postDetails.getEntreprise());
                    post.setProfileRequest(postDetails.getProfileRequest());
                    post.setContractType(postDetails.getContractType());
                    return postRepository.save(post);
                })
                .orElseThrow(() -> new PostNotFoundException("Post non trouvé avec l'ID : " + id));
    }

    @Override
    public void deletePost(String id) {
        if (!postRepository.existsById(id)) {
            throw new PostNotFoundException("Post non trouvé avec l'ID : " + id);
        }
        postRepository.deleteById(id);
    }

    @Override
    public Page<Post> search(Post example, Pageable pageable) {
        example.setDatePublication(null);
        Page<Post> posts = postRepository.findAll(pageable);

        // Enrichir chaque post avec les informations de l'utilisateur si nécessaire
        posts.getContent().forEach(post -> {
            if (post.getUserId() != null) {
                userRepository.findById(post.getUserId())
                        .ifPresent(user -> post.setUserFullName(user.getFullName()));
            }
        });

        return posts;
    }

    @Override
    public List<UserDto> getCandidatesByPostId(String postId) {
        List<User> candidates = userRepository.findCandidatesByPostId(postId);
        return candidates.stream()
                .map(UserDto::new)
                .collect(Collectors.toList());
    }

    @Override
    public Page<Post> getPostsByUserId(String userId, Pageable pageable) {
        Page<Post> userPosts = postRepository.findByUserId(userId, pageable);

        userPosts.getContent().forEach(post -> {
            userRepository.findById(post.getUserId())
                    .ifPresent(user -> post.setUserFullName(user.getFirstName() + " " + user.getLastName()));
        });

        return userPosts;
    }
}
