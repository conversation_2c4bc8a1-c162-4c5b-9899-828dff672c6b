package org.example.service.pdf.local;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.example.service.pdf.PdfSummaryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Service
public class DefaultPdfSummaryService implements PdfSummaryService {

    private static final Logger logger = LoggerFactory.getLogger(DefaultPdfSummaryService.class);

    @Value("${gradio.api.file.upload}")
    private String fileUploadUrl;

    @Value("${gradio.api.queue.join}")
    private String queueJoinUrl;

    @Value("${gradio.api.queue.data}")
    private String queueDataUrl;

    @Value("${gradio.session.hash:default_hash}")
    private String sessionHash;

    @Value("${groq.api.key:}")
    private String groqApiKey;

    @Value("${uploadFolder}")
    private String uploadFolder;

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;

    public DefaultPdfSummaryService() {
        this.restTemplate = new RestTemplate();
        this.objectMapper = new ObjectMapper();
    }

    @Override
    public Map<String, Object> getPdfSummary(MultipartFile file) {
        try {
            // Validate file
            if (file == null || file.isEmpty()) {
                logger.error("File is null or empty");
                throw new IllegalArgumentException("File is null or empty");
            }

            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null || (!originalFilename.endsWith(".pdf") &&
                    !originalFilename.endsWith(".doc") &&
                    !originalFilename.endsWith(".docx"))) {
                logger.error("Invalid file type: {}", originalFilename);
                throw new IllegalArgumentException("Only PDF, DOC, or DOCX files are supported");
            }

            String contentType = file.getContentType();
            if (contentType == null || !contentType.startsWith("application/")) {
                logger.error("Invalid content type: {}", contentType);
                throw new IllegalArgumentException("Invalid content type: " + contentType);
            }

            // Use file bytes directly
            byte[] fileBytes = file.getBytes();
            logger.debug("Read file bytes, size: {} bytes", fileBytes.length);

            // Check if Gradio API is available
            if (!isGradioApiAvailable()) {
                logger.warn("Gradio API is not available at {}. Falling back to local PDF processing.", fileUploadUrl);
                return processPdfLocally(file, fileBytes);
            }

            // 1. Upload file to /api/upload_file
            ByteArrayResource fileAsResource = new ByteArrayResource(fileBytes) {
                @Override
                public String getFilename() {
                    return originalFilename;
                }
            };

            MultiValueMap<String, Object> uploadBody = new LinkedMultiValueMap<>();
            uploadBody.add("file", fileAsResource);

            HttpHeaders uploadHeaders = new HttpHeaders();
            uploadHeaders.setContentType(MediaType.MULTIPART_FORM_DATA);

            HttpEntity<MultiValueMap<String, Object>> uploadRequestEntity =
                    new HttpEntity<>(uploadBody, uploadHeaders);

            ResponseEntity<String> uploadResponse;
            try {
                uploadResponse = restTemplate.postForEntity(fileUploadUrl, uploadRequestEntity, String.class);
                logger.debug("File upload response: {}", uploadResponse.getBody());
            } catch (RestClientException e) {
                logger.error("Failed to upload file to Gradio API: {}", e.getMessage(), e);
                logger.warn("Falling back to local PDF processing due to Gradio API failure.");
                return processPdfLocally(file, fileBytes);
            }

            // Validate upload response
            if (uploadResponse.getStatusCode().isError() || uploadResponse.getBody() == null) {
                logger.error("File upload failed with status: {}", uploadResponse.getStatusCode());
                throw new RuntimeException("File upload failed with status: " + uploadResponse.getStatusCode());
            }

            // Parse file metadata
            Map<String, Object> fileData;
            try {
                fileData = objectMapper.readValue(uploadResponse.getBody(), HashMap.class);
            } catch (Exception e) {
                logger.error("Failed to parse file upload response: {}", uploadResponse.getBody(), e);
                throw new RuntimeException("Failed to parse file upload response: " + e.getMessage(), e);
            }

            if (fileData.get("path") == null) {
                logger.error("File upload failed: No path returned in response: {}", fileData);
                throw new RuntimeException("File upload failed: No path returned");
            }

            // 2. Submit text extraction job (fn_index=5)
            Map<String, Object> textRequestBody = new HashMap<>();
            textRequestBody.put("data", List.of(fileData));
            textRequestBody.put("fn_index", 5);
            textRequestBody.put("session_hash", sessionHash);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Map<String, Object>> textJoinRequestEntity = new HttpEntity<>(textRequestBody, headers);

            ResponseEntity<String> textJoinResponse;
            try {
                textJoinResponse = restTemplate.postForEntity(queueJoinUrl, textJoinRequestEntity, String.class);
                logger.debug("Text extraction job response: {}", textJoinResponse.getBody());
            } catch (RestClientException e) {
                logger.error("Failed to submit text extraction job to Gradio API: {}", e.getMessage(), e);
                throw new RuntimeException("Failed to submit text extraction job to Gradio API: " + e.getMessage(), e);
            }

            // Validate text job response
            if (textJoinResponse.getStatusCode().isError() || textJoinResponse.getBody() == null) {
                logger.error("Text extraction job submission failed with status: {}", textJoinResponse.getStatusCode());
                throw new RuntimeException("Text extraction job submission failed with status: " + textJoinResponse.getStatusCode());
            }

            // Parse text job ID
            Map<String, Object> textJoinResult;
            try {
                textJoinResult = objectMapper.readValue(textJoinResponse.getBody(), HashMap.class);
            } catch (Exception e) {
                logger.error("Failed to parse text extraction job response: {}", textJoinResponse.getBody(), e);
                throw new RuntimeException("Failed to parse text extraction job response: " + e.getMessage(), e);
            }

            String textQueueId = (String) textJoinResult.get("queue_id");
            if (textQueueId == null) {
                logger.error("No queue_id returned for text extraction job: {}", textJoinResult);
                throw new RuntimeException("No queue_id returned for text extraction job");
            }

            // Poll for text extraction
            String dataUrlWithHash = queueDataUrl + "?session_hash=" + sessionHash;
            Map<String, Object> dataResult;
            int maxAttempts = 30;
            int attempt = 0;

            String extractedText = null;
            while (attempt < maxAttempts) {
                ResponseEntity<String> dataResponse;
                try {
                    dataResponse = restTemplate.getForEntity(dataUrlWithHash, String.class);
                    logger.debug("Queue data response: {}", dataResponse.getBody());
                } catch (RestClientException e) {
                    logger.error("Failed to poll Gradio queue data: {}", e.getMessage(), e);
                    throw new RuntimeException("Failed to poll Gradio queue data: " + e.getMessage(), e);
                }

                if (dataResponse.getBody() == null) {
                    logger.error("Queue data response is null");
                    throw new RuntimeException("Queue data response is null");
                }

                try {
                    dataResult = objectMapper.readValue(dataResponse.getBody(), HashMap.class);
                } catch (Exception e) {
                    logger.error("Failed to parse queue data response: {}", dataResponse.getBody(), e);
                    throw new RuntimeException("Failed to parse queue data response: " + e.getMessage(), e);
                }

                String jobStatus = (String) dataResult.get("status");
                if (jobStatus == null) {
                    logger.warn("Status field missing in response: {}", dataResponse.getBody());
                    Map<String, Object> dataWrapper = (Map<String, Object>) dataResult.get("data");
                    if (dataWrapper != null) {
                        jobStatus = (String) dataWrapper.get("status");
                    }
                    if (jobStatus == null) {
                        logger.error("Status field not found in Gradio response: {}", dataResult);
                        throw new RuntimeException("Status field not found in Gradio response");
                    }
                }

                if ("COMPLETE".equals(jobStatus)) {
                    Map<String, Object> dataWrapper = (Map<String, Object>) dataResult.get("data");
                    if (dataWrapper == null) {
                        logger.error("Data wrapper missing in Gradio response: {}", dataResult);
                        throw new RuntimeException("Data wrapper missing in Gradio response");
                    }
                    List<Object> output = (List<Object>) dataWrapper.get("data");
                    if (output != null && output.size() >= 2) {
                        extractedText = (String) output.get(1);
                        logger.debug("Extracted text: {}", extractedText);
                        break;
                    } else {
                        logger.error("Invalid output format from Gradio API: {}", output);
                        throw new RuntimeException("Invalid output format from Gradio API");
                    }
                } else if ("FAILED".equals(jobStatus)) {
                    String error = (String) dataResult.get("error");
                    logger.error("Gradio text extraction job failed: {}", error != null ? error : "Unknown error");
                    throw new RuntimeException("Gradio text extraction job failed: " + (error != null ? error : "Unknown error"));
                }

                Thread.sleep(2000);
                attempt++;
            }

            if (extractedText == null) {
                logger.error("Gradio text extraction job timed out after {} attempts", maxAttempts);
                throw new RuntimeException("Gradio text extraction job timed out");
            }

            // 3. Submit CV data extraction job (fn_index=6)
            Map<String, Object> cvRequestBody = new HashMap<>();
            cvRequestBody.put("data", List.of(
                    extractedText,
                    "Extract CV data",
                    0,
                    List.of(extractedText),
                    "Groq API",
                    "llama-3.1-8b-instant",
                    groqApiKey
            ));
            cvRequestBody.put("fn_index", 6);
            cvRequestBody.put("session_hash", sessionHash);

            HttpEntity<Map<String, Object>> cvJoinRequestEntity = new HttpEntity<>(cvRequestBody, headers);

            ResponseEntity<String> cvJoinResponse;
            try {
                cvJoinResponse = restTemplate.postForEntity(queueJoinUrl, cvJoinRequestEntity, String.class);
                logger.debug("CV data extraction job response: {}", cvJoinResponse.getBody());
            } catch (RestClientException e) {
                logger.error("Failed to submit CV data extraction job to Gradio API: {}", e.getMessage(), e);
                throw new RuntimeException("Failed to submit CV data extraction job to Gradio API: " + e.getMessage(), e);
            }

            // Validate CV job response
            if (cvJoinResponse.getStatusCode().isError() || cvJoinResponse.getBody() == null) {
                logger.error("CV data extraction job submission failed with status: {}", cvJoinResponse.getStatusCode());
                throw new RuntimeException("CV data extraction job submission failed with status: " + cvJoinResponse.getStatusCode());
            }

            // Parse CV job ID
            Map<String, Object> cvJoinResult;
            try {
                cvJoinResult = objectMapper.readValue(cvJoinResponse.getBody(), HashMap.class);
            } catch (Exception e) {
                logger.error("Failed to parse CV data extraction job response: {}", cvJoinResponse.getBody(), e);
                throw new RuntimeException("Failed to parse CV data extraction job response: " + e.getMessage(), e);
            }

            String cvQueueId = (String) cvJoinResult.get("queue_id");
            if (cvQueueId == null) {
                logger.error("No queue_id returned for CV data extraction job: {}", cvJoinResult);
                throw new RuntimeException("No queue_id returned for CV data extraction job");
            }

            // Poll for CV data
            attempt = 0;
            while (attempt < maxAttempts) {
                ResponseEntity<String> cvDataResponse;
                try {
                    cvDataResponse = restTemplate.getForEntity(dataUrlWithHash, String.class);
                    logger.debug("CV queue data response: {}", cvDataResponse.getBody());
                } catch (RestClientException e) {
                    logger.error("Failed to poll CV queue data: {}", e.getMessage(), e);
                    throw new RuntimeException("Failed to poll CV queue data: " + e.getMessage(), e);
                }

                if (cvDataResponse.getBody() == null) {
                    logger.error("CV queue data response is null");
                    throw new RuntimeException("CV queue data response is null");
                }

                try {
                    dataResult = objectMapper.readValue(cvDataResponse.getBody(), HashMap.class);
                } catch (Exception e) {
                    logger.error("Failed to parse CV queue data response: {}", cvDataResponse.getBody(), e);
                    throw new RuntimeException("Failed to parse CV queue data response: " + e.getMessage(), e);
                }

                String jobStatus = (String) dataResult.get("status");
                if (jobStatus == null) {
                    logger.warn("Status field missing in CV response: {}", cvDataResponse.getBody());
                    Map<String, Object> dataWrapper = (Map<String, Object>) dataResult.get("data");
                    if (dataWrapper != null) {
                        jobStatus = (String) dataWrapper.get("status");
                    }
                    if (jobStatus == null) {
                        logger.error("Status field not found in Gradio CV response: {}", dataResult);
                        throw new RuntimeException("Status field not found in Gradio CV response");
                    }
                }

                if ("COMPLETE".equals(jobStatus)) {
                    Map<String, Object> dataWrapper = (Map<String, Object>) dataResult.get("data");
                    if (dataWrapper == null) {
                        logger.error("Data wrapper missing in Gradio CV response: {}", dataResult);
                        throw new RuntimeException("Data wrapper missing in Gradio CV response");
                    }
                    List<Object> cvOutput = (List<Object>) dataWrapper.get("data");
                    if (cvOutput != null && cvOutput.size() >= 2) {
                        Map<String, Object> cvData = objectMapper.convertValue(cvOutput.get(1), HashMap.class);
                        cvData.put("cvPath", uploadFolder + "/" + originalFilename);
                        cvData.put("originalName", originalFilename);
                        cvData.put("uploadDate", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
                        cvData.put("text", extractedText); // Ensure text is included
                        logger.debug("Extracted CV data: {}", cvData);
                        return cvData;
                    } else {
                        logger.error("Invalid CV data output format: {}", cvOutput);
                        throw new RuntimeException("Invalid CV data output format");
                    }
                } else if ("FAILED".equals(jobStatus)) {
                    String error = (String) dataResult.get("error");
                    logger.error("Gradio CV data extraction job failed: {}", error != null ? error : "Unknown error");
                    throw new RuntimeException("Gradio CV data extraction job failed: " + (error != null ? error : "Unknown error"));
                }

                Thread.sleep(2000);
                attempt++;
            }

            logger.error("Gradio CV data extraction job timed out after {} attempts", maxAttempts);
            throw new RuntimeException("Gradio CV data extraction job timed out");

        } catch (IOException e) {
            logger.error("Error reading file bytes: {}", e.getMessage(), e);
            throw new RuntimeException("Error reading file bytes: " + e.getMessage(), e);
        } catch (InterruptedException e) {
            logger.error("Polling interrupted: {}", e.getMessage(), e);
            Thread.currentThread().interrupt();
            throw new RuntimeException("Polling interrupted: " + e.getMessage(), e);
        } catch (Exception e) {
            logger.error("Error calling Gradio API: {}", e.getMessage(), e);
            throw new RuntimeException("Error calling Gradio API: " + e.getMessage(), e);
        }
    }

    // Check if Gradio API is available
    private boolean isGradioApiAvailable() {
        try {
            ResponseEntity<String> response = restTemplate.getForEntity(fileUploadUrl, String.class);
            return response.getStatusCode() == HttpStatus.OK;
        } catch (RestClientException e) {
            logger.error("Gradio API health check failed: {}", e.getMessage());
            return false;
        }
    }

    // Fallback method to process PDF locally using Apache PDFBox
    private Map<String, Object> processPdfLocally(MultipartFile file, byte[] fileBytes) {
        try {
            logger.debug("Processing PDF locally with Apache PDFBox");

            // Process PDF with Apache PDFBox
            Map<String, Object> result = new HashMap<>();
            try (PDDocument document = PDDocument.load(fileBytes)) {
                PDFTextStripper textStripper = new PDFTextStripper();
                String text = textStripper.getText(document);
                result.put("text", text);
            }

            // Add metadata
            result.put("originalName", file.getOriginalFilename());
            result.put("uploadDate", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            result.put("cvPath", uploadFolder + "/" + file.getOriginalFilename());

            logger.debug("Locally extracted CV data: {}", result);
            return result;

        } catch (IOException e) {
            logger.error("Error processing PDF locally: {}", e.getMessage(), e);
            throw new RuntimeException("Error processing PDF locally: " + e.getMessage(), e);
        }
    }
}