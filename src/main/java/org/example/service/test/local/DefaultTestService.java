package org.example.service.test.local;

import org.example.dto.QuestionDTO;
import org.example.dto.TestDTO;
import org.example.model.Question;
import org.example.model.Test;
import org.example.service.test.TestService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class DefaultTestService implements TestService {

    private final TestRepository testRepository;

    public DefaultTestService(TestRepository testRepository) {
        this.testRepository = testRepository;
    }

    @Override
    public Test createTest(TestDTO testDTO) {
        Test test = new Test();
        test.setTitle(testDTO.getTitle());

        List<Question> questionList = new ArrayList<>();
        for (QuestionDTO questionDTO : testDTO.getQuestions()) {
            Question question = new Question();
            question.setContent(questionDTO.getContent());
            question.setType(questionDTO.getType());
            question.setCorrectOptions(questionDTO.getCorrectOptions());
            question.setOptions(questionDTO.getOptions());
            questionList.add(question);
        }

        test.setQuestions(questionList);
        return testRepository.save(test);
    }

    @Override
    public List<Test> getAllTests() {
        return testRepository.findAll();
    }

    @Override
    public Optional<Test> getTestById(String id) {
        return testRepository.findById(String.valueOf(id));
    }

    @Override
    public Test updateTest(String id, TestDTO testDTO) {
        return testRepository.findById(String.valueOf(id)).map(existingTest -> {
            existingTest.setTitle(testDTO.getTitle());

            List<Question> updatedQuestions = new ArrayList<>();
            for (QuestionDTO questionDTO : testDTO.getQuestions()) {
                Question question = new Question();
                question.setContent(questionDTO.getContent());
                updatedQuestions.add(question);
            }

            existingTest.setQuestions(updatedQuestions);
            return testRepository.save(existingTest);
        }).orElseThrow(() -> new RuntimeException("Test not found with id: " + id));
    }

    @Override
    public void deleteTest(String id) {
        testRepository.deleteById(String.valueOf(id));
    }
}