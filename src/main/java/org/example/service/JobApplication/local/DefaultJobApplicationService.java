package org.example.service.JobApplication.local;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.example.dto.TestDTO;
import org.example.model.JobApplication;
import org.example.model.Post;
import org.example.model.Test;
import org.example.model.User;
import org.example.service.JobApplication.JobApplicationRepository;
import org.example.service.mail.MailService;
import org.example.service.modelIA.LlamaService;
import org.example.service.post.PostService;
import org.example.service.test.TestService;
import org.example.service.user.UserService;
import org.example.service.user.local.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service // Register as a Spring bean
public class DefaultJobApplicationService {
    private final Logger logger = LoggerFactory.getLogger(DefaultJobApplicationService.class);
    private final Pattern CLEANING_PATTERN = Pattern.compile("```json([?:.|\\n\\s\\S]*)```");

    private final JobApplicationRepository repository;
    private final TestService testService;
    private final LlamaService llamaService;
    private final ObjectMapper objectMapper;
    private final PostService postService;
    private final MailService mailService;

    // Constructor injection (preferred over field injection)
    public DefaultJobApplicationService(JobApplicationRepository repository, TestService testService, LlamaService llamaService, ObjectMapper objectMapper, PostService postService, MailService mailService) {
        this.repository = repository;
        this.testService = testService;
        this.llamaService = llamaService;
        this.objectMapper = objectMapper;
        this.postService = postService;
        this.mailService = mailService;
    }


    @Autowired
    private JobApplicationRepository jobApplicationRepository;

    @Autowired
    private UserRepository userRepository;

    public List<User> getCandidatesByPostId(String postId) {
        // Récupérer toutes les candidatures pour ce post
        List<JobApplication> applications = jobApplicationRepository.findByPostId(postId);

        // Extraire les IDs des utilisateurs
        List<String> userIds = applications.stream()
                .map(JobApplication::getUserId)
                .collect(Collectors.toList());

        // Récupérer les informations des utilisateurs
        return userRepository.findAllById(userIds);
    }

    public boolean hasUserApplied(String postId, String userId) {
        return jobApplicationRepository.existsByPostIdAndUserId(postId, userId);
    }

    public JobApplication saveApplication(JobApplication application) {
        return jobApplicationRepository.save(application);
    }

    public List<JobApplication> getApplicationsByPostId(String postId) {
        return jobApplicationRepository.findByPostId(postId);
    }

    public JobApplication acceptApplication(String applicationId) {
        JobApplication application = jobApplicationRepository.findById(applicationId)
                .orElseThrow(() -> new RuntimeException("Application not found"));
        application.setStatus(JobApplication.ApplicationStatus.ACCEPTED);
        Optional<Post> postById = postService.getPostById(application.getPostId());
        postById.ifPresent(post -> new Thread(() -> {
//            Test test = generateTest(post.getProfileRequest());

            mailService.sendMail(application.getEmail(), "Test pour l'entretien", "voici la page de test <a href=\"http://localhost:4200/welcome/681f3d78250d3775fe88c26b\">cliquer ici</a>");
        }).start());
        return jobApplicationRepository.save(application);
    }

    private Test generateTest(String skill) {

        String prompt = """
                Génère un test technique sur le thème: %s
                contenant 10 questions à selection simple ou multiple.
                Je veux le résultat en json sous la forme:
                 {"title": "Titre du test", "questions":
                 [{
                 "content": "THE QUESTION",
                 "type": "RADIO|SELECT",
                 options: ["Option 1", "Option 2"],
                 correctOptions: [0]}]
                 }
                 type: SELECT pour selection multiple RADIO pour selection simple
                 options: les choix minimum 4
                 correctOptions: les indices des choix correctes
                """.formatted(skill);

        return llamaService.askQuestion(prompt)
                .map(json -> {
                    try {
                        // Log raw JSON for debugging
                        System.out.println("Raw JSON from LlamaService: " + json);
                        Matcher matcher = CLEANING_PATTERN.matcher(json);
                        if (matcher.find()) {
                            String cleanedJson = matcher.group(1).trim();
                            return objectMapper.readValue(cleanedJson, TestDTO.class);
                        }
                        throw new JsonParseException(json);
                    } catch (JsonParseException e) {
                        throw new RuntimeException("Invalid JSON format received from LlamaService: " + e.getMessage(), e);
                    } catch (Exception e) {
                        throw new RuntimeException("Error during JSON conversion to TestDTO: " + e.getMessage(), e);
                    }
                })
                .map(testService::createTest)
                .onErrorResume(e -> {
                    if (e.getCause() instanceof org.apache.catalina.connector.ClientAbortException) {
                        System.out.println("Client disconnected: " + e.getMessage());
                        return Mono.empty();
                    }
                    return Mono.error(e);
                }).block();
    }

    public JobApplication rejectApplication(String applicationId) {
        JobApplication application = jobApplicationRepository.findById(applicationId)
                .orElseThrow(() -> new RuntimeException("Application not found"));
        application.setStatus(JobApplication.ApplicationStatus.REJECTED);
        return jobApplicationRepository.save(application);
    }
}