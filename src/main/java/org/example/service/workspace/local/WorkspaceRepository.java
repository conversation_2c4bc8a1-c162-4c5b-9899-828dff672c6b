package org.example.service.workspace.local;

import org.example.model.Workspace;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;
import java.util.Optional;

public interface WorkspaceRepository extends MongoRepository<Workspace, String> {

    @Query("{name: ?0}")
    Optional<Workspace> findByName(String name);

    @Query(value = "{name: ?0}", exists = true)
    boolean existsByName(String name);

    @Query("{'userId': ?0}")
    List<Workspace> findByUserId(String userId);

}
