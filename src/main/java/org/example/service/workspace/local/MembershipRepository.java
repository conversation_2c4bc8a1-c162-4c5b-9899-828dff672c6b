package org.example.service.workspace.local;

import org.example.model.Membership;
import org.example.model.Workspace;
import org.example.service.invitation.local.InvitationRepository;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface MembershipRepository extends MongoRepository<Membership, String> {
    @Query("{userId: ?0}")
    List<Membership> findByUserId(String userId);



    @Query("{workspaceId: ?0, userId: ?1}")
    Optional<Membership> findMembership(String workspaceId, String userId);

    record MembershipEmail(String receiverId) {
    }
    @Query(value = "{ 'workspaceId': ?0 }", fields = "{ 'receiverId': '$userId' }")
    Set<MembershipEmail> findMembers(String workspaceId);
}

