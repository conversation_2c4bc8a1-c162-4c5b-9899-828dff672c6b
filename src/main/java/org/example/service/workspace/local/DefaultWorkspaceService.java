package org.example.service.workspace.local;

import org.example.model.Membership;
import org.example.model.User;
import org.example.model.Workspace;
import org.example.service.user.UserService;
import org.example.service.workspace.WorkspaceService;
import org.example.exceptions.workspace.WorkspaceAlreadyExistsException;
import org.example.exceptions.workspace.WorkspaceNotFoundException;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.Set;

@Service
public class DefaultWorkspaceService implements WorkspaceService {

    private final WorkspaceRepository workspaceRepository;
    private final MembershipRepository membershipRepository;
    private final UserService userService;

    DefaultWorkspaceService(WorkspaceRepository workspaceRepository, MembershipRepository membershipRepository, UserService userService) {
        this.workspaceRepository = workspaceRepository;
        this.membershipRepository = membershipRepository;
        this.userService = userService;
    }

    @Override
    public Workspace createWorkspace(Workspace workspace) {
        if (workspaceRepository.existsByName(workspace.getName())) {
            throw new WorkspaceAlreadyExistsException(workspace.getName());
        }

        User user = userService.me();

        workspace = workspaceRepository.save(workspace);
        Membership membership = new Membership();
        membership.setWorkspaceId(workspace.getId());
        membership.setUserId(user.getId());
        membership.setOwner(true);
        membershipRepository.save(membership);

        return workspace;
    }

    @Override
    public Page<Workspace> getAllWorkspaces(Pageable pageable) {
        return workspaceRepository.findAll(pageable);
    }

    @Override
    public Optional<Workspace> getWorkspaceById(String id) {
        return workspaceRepository.findById(id);
    }

    @Override
    public Workspace updateWorkspace(String id, Workspace workspaceDetails) {
        return workspaceRepository.findById(id)
                .map(workspace -> {
                    workspace.setName(workspaceDetails.getName());
                    workspace.setDescription(workspaceDetails.getDescription());
                    return workspaceRepository.save(workspace);
                })
                .orElseThrow(() -> new WorkspaceNotFoundException(id));
    }

    @Override
    public void deleteWorkspace(String id) {
        if (!workspaceRepository.existsById(id)) {
            throw new WorkspaceNotFoundException(id);
        }
        workspaceRepository.deleteById(id);
    }

    @Override
    public Page<Workspace> search(Workspace example, Pageable pageable) {
        return workspaceRepository.findAll(Example.of(example, ExampleMatcher.matching()), pageable);
    }

    @Override
    public Optional<Membership> getMembership(String workspaceId, String userId) {
        return membershipRepository.findMembership(workspaceId, userId);
    }

    @Override
    public Set<MembershipRepository.MembershipEmail> findMembers(String workspaceId) {
        return membershipRepository.findMembers(workspaceId);
    }

    @Override
    public Optional<Workspace> getWorkspaceForUser(String userEmail) {
        return Optional.empty();
    }

    @Override
    public List<Workspace> getWorkspacesForUser(String userEmail) {
        return List.of();
    }

    @Override
    public Workspace updateWorkspacePaymentStatus(String workspaceId, boolean paymentDone) {
        return workspaceRepository.findById(workspaceId)
                .map(workspace -> {
                    workspace.setPaymentDone(paymentDone);
                    // Si le paiement est effectué, marquer également le workspace comme finalisé
                    if (paymentDone) {
                        workspace.setFinalized(true);
                    }
                    return workspaceRepository.save(workspace);
                })
                .orElseThrow(() -> new WorkspaceNotFoundException(workspaceId));
    }

}
