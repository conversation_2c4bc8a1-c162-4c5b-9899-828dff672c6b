package org.example.service.workspace;

import org.example.model.Membership;
import org.example.model.Workspace;
import org.example.service.workspace.local.MembershipRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface WorkspaceService {
    Workspace createWorkspace(Workspace workspace);
    Page<Workspace> getAllWorkspaces(Pageable pageable);
    Optional<Workspace> getWorkspaceById(String id);
    Workspace updateWorkspace(String id, Workspace workspaceDetails);
    void deleteWorkspace(String id);
    Page<Workspace> search(Workspace example, Pageable pageable);
    Optional<Membership> getMembership(String workspaceId, String userId);

    Set<MembershipRepository.MembershipEmail> findMembers(String workspaceId);

    Optional<Workspace> getWorkspaceForUser(String userEmail);

    List<Workspace> getWorkspacesForUser(String userEmail);

    /**
     * Met à jour le statut de paiement d'un workspace
     * @param workspaceId ID du workspace
     * @param paymentDone true si le paiement est effectué, false sinon
     * @return le workspace mis à jour
     */
    Workspace updateWorkspacePaymentStatus(String workspaceId, boolean paymentDone);
}
