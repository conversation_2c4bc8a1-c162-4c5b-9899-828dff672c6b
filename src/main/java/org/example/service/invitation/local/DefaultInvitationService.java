package org.example.service.invitation.local;

import org.example.model.*;
import org.example.model.Invitation.InvitationStatus;
import org.example.service.invitation.InvitationService;
import org.example.service.user.local.UserRepository;
import org.example.service.workspace.local.MembershipRepository;
import org.example.service.workspace.local.WorkspaceRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import java.util.Optional;
import java.util.Set;

@Service
public class DefaultInvitationService implements InvitationService {

    private final InvitationRepository invitationRepository;
    private final UserRepository userRepository;
    private final WorkspaceRepository workspaceRepository;
    private final MembershipRepository membershipRepository;

    public DefaultInvitationService(InvitationRepository invitationRepository,
                                    UserRepository userRepository,
                                    WorkspaceRepository workspaceRepository, MembershipRepository membershipRepository) {
        this.invitationRepository = invitationRepository;
        this.userRepository = userRepository;
        this.workspaceRepository = workspaceRepository;
        this.membershipRepository = membershipRepository;
    }
    @Override
    public Invitation sendInvitation(String senderId, String receiverId, String workspaceId) {
        return sendInvitation(senderId, receiverId, workspaceId, " ");
    }

    @Override
    public Invitation sendInvitation(String senderId, String receiverId, String workspaceId, String message) {
        try {
            Optional<User> senderOpt = userRepository.findById(senderId);
            Optional<User> receiverOpt = userRepository.findById(receiverId);
            Optional<Workspace> workspaceOpt = workspaceRepository.findById(workspaceId);

            if (senderOpt.isEmpty() || receiverOpt.isEmpty() || workspaceOpt.isEmpty()) {
                String errorMessage = "Utilisateur ou workspace introuvable !";
                System.out.println(errorMessage); // Log pour identifier la cause
                throw new IllegalArgumentException(errorMessage);
            }

            Invitation invitation = new Invitation();
            invitation.setSender(senderOpt.get());
            invitation.setReceiver(receiverOpt.get());
            invitation.setWorkspace(workspaceOpt.get());
            invitation.setStatus(InvitationStatus.PENDING);
            invitation.setMessage(message);

            return invitationRepository.save(invitation);
        } catch (Exception e) {
            System.out.println("Erreur lors de l'envoi de l'invitation : " + e.getMessage());
            e.printStackTrace();
            throw e;  // Propager l'exception après log
        }
    }

    @Override
    public Invitation acceptInvitation(String invitationId, String id) {
        Optional<Invitation> invitationOpt = invitationRepository.findById(invitationId);
        if (invitationOpt.isEmpty()) {
            throw new IllegalArgumentException("Invitation introuvable !");
        }

        Invitation invitation = invitationOpt.get();
        invitation.setStatus(InvitationStatus.ACCEPTED);
        invitation.setUpdatedAt(java.time.LocalDateTime.now());

        // Add user to membership
        User user = invitation.getReceiver(); // The user accepting the invitation
        Workspace workspace = invitation.getWorkspace(); // The workspace associated with the invitation

        // Create the membership
        Membership membership = new Membership();
        membership.setUserId(user.getId());
        membership.setWorkspaceId(workspace.getId());
        membership.setOwner(false); // The user is not the owner by default
        membership.setAuthorities(Set.of(Authority.MANAGE_AUTHORITIES)); // Add default authority or customize as needed

        // Save the membership to the database
        membershipRepository.save(membership);

        // Save the updated invitation
        return invitationRepository.save(invitation);
    }


    @Override
    public void rejectInvitation(String invitationId) {
        Optional<Invitation> invitationOpt = invitationRepository.findById(invitationId);
        if (invitationOpt.isPresent()) {
            Invitation invitation = invitationOpt.get();
            invitation.setStatus(InvitationStatus.REJECTED);
            invitation.setUpdatedAt(java.time.LocalDateTime.now());
            invitationRepository.save(invitation);
        }
    }

    @Override
    public Page<Invitation> getUserInvitations(String userId, int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        return invitationRepository.findByReceiverIdAndStatus(userId, Invitation.InvitationStatus.PENDING, pageable);
    }

    @Override
    public Page<Invitation> getSentInvitations(String senderId, int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        return invitationRepository.findBySenderId(senderId, pageable);
    }

    @Override
    public Set<Invitation> findInvitedUsers(String workspaceId) {
        return Set.of();
    }

    @Override
    public Invitation resendInvitation(String invitationId) {
        Optional<Invitation> invitationOpt = invitationRepository.findById(invitationId);
        if (invitationOpt.isEmpty()) {
            throw new IllegalArgumentException("Invitation introuvable !");
        }

        Invitation invitation = invitationOpt.get();

        // Vérifier que l'invitation est en statut PENDING ou REJECTED
        if (invitation.getStatus() != InvitationStatus.PENDING && invitation.getStatus() != InvitationStatus.REJECTED) {
            throw new IllegalStateException("Impossible de renvoyer une invitation qui n'est pas en attente ou refusée");
        }

        // Mettre à jour le statut à PENDING
        invitation.setStatus(InvitationStatus.PENDING);

        // Mettre à jour les dates
        java.time.LocalDateTime now = java.time.LocalDateTime.now();
        invitation.setUpdatedAt(now);
        invitation.setCreatedAt(now);

        return invitationRepository.save(invitation);
    }

    @Override
    public void cancelInvitation(String invitationId) {
        Optional<Invitation> invitationOpt = invitationRepository.findById(invitationId);
        if (invitationOpt.isEmpty()) {
            throw new IllegalArgumentException("Invitation introuvable !");
        }

        Invitation invitation = invitationOpt.get();

        // Vérifier que l'invitation est en statut PENDING
        if (invitation.getStatus() != InvitationStatus.PENDING) {
            throw new IllegalStateException("Impossible d'annuler une invitation qui n'est pas en attente");
        }

        // Supprimer l'invitation
        invitationRepository.delete(invitation);
    }

    @Override
    public void deleteInvitation(String id) {
        Optional<Invitation> invitationOpt = invitationRepository.findById(id);
        if (invitationOpt.isPresent()) {
            invitationRepository.delete(invitationOpt.get());
        }
    }

    @Override
    public Invitation updateStatus(String id, InvitationStatus invitationStatus) {
        Optional<Invitation> invitationOpt = invitationRepository.findById(id);
        if (invitationOpt.isEmpty()) {
            throw new IllegalArgumentException("Invitation introuvable !");
        }

        Invitation invitation = invitationOpt.get();
        invitation.setStatus(invitationStatus);
        invitation.setUpdatedAt(java.time.LocalDateTime.now());
        return invitationRepository.save(invitation);
    }

}
