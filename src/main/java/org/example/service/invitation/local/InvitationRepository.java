package org.example.service.invitation.local;

import org.example.model.Invitation;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

@Repository
public interface InvitationRepository extends MongoRepository<Invitation, String> {

    @Query("{ 'receiverId': ?0, 'status': ?1 }")
    Page<Invitation> findByReceiverIdAndStatus(String userId, Invitation.InvitationStatus invitationStatus, Pageable pageable);

    @Query("{ 'sender.id': ?0 }")
    Page<Invitation> findBySenderId(String senderId, Pageable pageable);

    @Query("{ 'workspace.id': ?0 }")
    Page<Invitation> findByWorkspaceId(String workspaceId, Pageable pageable);

    @Query(value = "{ 'workspace.id': ?0 }")
    Set<Invitation> findInvitedUsers(String workspaceId);

    @Query("{ 'status': ?0 }")
    Page<Invitation> findByStatus(Invitation.InvitationStatus status, Pageable pageable);
}
