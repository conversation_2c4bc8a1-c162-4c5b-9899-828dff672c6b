package org.example.service.invitation;

import org.example.model.Invitation;
import org.springframework.data.domain.Page;

import java.util.Set;

public interface InvitationService {
    Invitation sendInvitation(String senderId, String receiverId, String workspaceId);

    Invitation sendInvitation(String senderId, String receiverId, String workspaceId, String message);

    Invitation acceptInvitation(String invitationId, String id);

    void rejectInvitation(String invitationId);

    Page<Invitation> getUserInvitations(String userId, int page, int size);

    Page<Invitation> getSentInvitations(String senderId, int page, int size);

    Invitation resendInvitation(String invitationId);

    void cancelInvitation(String invitationId);

    Set<Invitation> findInvitedUsers(String workspaceId);

    void deleteInvitation(String id);

    Invitation updateStatus(String id, Invitation.InvitationStatus invitationStatus);
}
