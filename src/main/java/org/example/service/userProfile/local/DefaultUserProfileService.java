package org.example.service.userProfile.local;

import org.example.model.*;
import org.example.service.user.UserService;
import org.example.service.userProfile.UserProfileService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
public class DefaultUserProfileService implements UserProfileService {

    private final UserProfileRepository repository;
    private final UserService userService;
    private final String uploadFolderResource;

    public DefaultUserProfileService(UserProfileRepository repository, UserService userService, @Value("${uploadFolder}") String uploadFolderResource) {
        this.repository = repository;
        this.userService = userService;
        this.uploadFolderResource = uploadFolderResource;
    }

    @Override
    public UserProfile getProfileByUserId(String userId) {
        UserProfile profile = repository.findByUserId(userId);
        if (profile == null) {
            profile = new UserProfile();
            profile.setUserId(userId);
            profile = repository.save(profile);
        }
        return profile;
    }

    @Override
    public List<UserProfile> getAllProfiles() {
        return repository.findAll();
    }

    @Override
    public UserProfile createOrUpdateProfile(UserProfile profile) {
        return repository.save(profile);
    }

    @Override
    public void deleteProfileByUserId(String userId) {
        UserProfile profile = repository.findByUserId(userId);
        if (profile != null) {
            repository.delete(profile);
        }
    }

    @Override
    public UserProfile save(UserProfile profile) {
        return repository.save(profile);
    }

    @Override
    public void delete(String id) {
        repository.deleteById(id);
    }

    @Override
    public UserProfile getByUserId(String userId) {
        return getProfileByUserId(userId);
    }

    @Override
    public UserProfile addEducation(String userId, Education education) {
        User user = userService.getUserByEmail(userId)
                .orElseThrow(() -> new RuntimeException("User not found"));
        UserProfile profile = getProfileByUserId(user.getId());
        if (profile != null) {
            if (profile.getEducations() == null) {
                profile.setEducations(new java.util.ArrayList<>());
            }
            profile.getEducations().add(education);
            return repository.save(profile);
        }
        return null;
    }

    @Override
    public UserProfile editEducation(String userId, Education education, int index) {
        User user = userService.getUserByEmail(userId)
                .orElseThrow(() -> new RuntimeException("User not found"));
        UserProfile profile = getProfileByUserId(user.getId());
        if (profile != null && profile.getEducations() != null && index >= 0 && index < profile.getEducations().size()) {
            profile.getEducations().set(index, education);
            return repository.save(profile);
        }
        throw new RuntimeException("Education not found at index " + index + " for user: " + userId);
    }

    @Override
    public void deleteEducation(String userId, int index) {
        User user = userService.getUserByEmail(userId)
                .orElseThrow(() -> new RuntimeException("User not found"));
        UserProfile profile = getProfileByUserId(user.getId());
        if (profile != null && profile.getEducations() != null && index >= 0 && index < profile.getEducations().size()) {
            profile.getEducations().remove(index);
            repository.save(profile);
        } else {
            throw new RuntimeException("Education not found at index " + index + " for user: " + userId);
        }
    }

    @Override
    public UserProfile addCertification(String userId, Certificate certification) {
        User user = userService.getUserByEmail(userId)
                .orElseThrow(() -> new RuntimeException("User not found"));
        UserProfile profile = getProfileByUserId(user.getId());
        if (profile != null) {
            if (profile.getCertifications() == null) {
                profile.setCertifications(new java.util.ArrayList<>());
            }
            profile.addCertification(certification);
            return repository.save(profile);
        }
        return null;
    }

    @Override
    public UserProfile editCertification(String userId, Certificate certification, int index) {
        User user = userService.getUserByEmail(userId)
                .orElseThrow(() -> new RuntimeException("User not found"));
        UserProfile profile = getProfileByUserId(user.getId());
        if (profile != null && profile.getCertifications() != null && index >= 0 && index < profile.getCertifications().size()) {
            profile.getCertifications().set(index, certification);
            return repository.save(profile);
        }
        throw new RuntimeException("Certification not found at index " + index + " for user: " + userId);
    }

    @Override
    public void deleteCertification(String userId, int index) {
        User user = userService.getUserByEmail(userId)
                .orElseThrow(() -> new RuntimeException("User not found"));
        UserProfile profile = getProfileByUserId(user.getId());
        if (profile != null && profile.getCertifications() != null && index >= 0 && index < profile.getCertifications().size()) {
            profile.getCertifications().remove(index);
            repository.save(profile);
        } else {
            throw new RuntimeException("Certification not found at index " + index + " for user: " + userId);
        }
    }

    @Override
    public UserProfile addExperience(String userId, Experience experience) {
        User user = userService.getUserByEmail(userId)
                .orElseThrow(() -> new RuntimeException("User not found"));
        UserProfile profile = getProfileByUserId(user.getId());
        if (profile != null) {
            if (profile.getExperiences() == null) {
                profile.setExperiences(new java.util.ArrayList<>());
            }
            profile.addExperience(experience);
            return repository.save(profile);
        }
        return null;
    }

    @Override
    public UserProfile editExperience(String userId, Experience experience, int index) {
        User user = userService.getUserByEmail(userId)
                .orElseThrow(() -> new RuntimeException("User not found"));
        UserProfile profile = getProfileByUserId(user.getId());
        if (profile != null && profile.getExperiences() != null && index >= 0 && index < profile.getExperiences().size()) {
            profile.getExperiences().set(index, experience);
            return repository.save(profile);
        }
        throw new RuntimeException("Experience not found at index " + index + " for user: " + userId);
    }

    @Override
    public void deleteExperience(String userId, int index) {
        User user = userService.getUserByEmail(userId)
                .orElseThrow(() -> new RuntimeException("User not found"));
        UserProfile profile = getProfileByUserId(user.getId());
        if (profile != null && profile.getExperiences() != null && index >= 0 && index < profile.getExperiences().size()) {
            profile.getExperiences().remove(index);
            repository.save(profile);
        } else {
            throw new RuntimeException("Experience not found at index " + index + " for user: " + userId);
        }
    }

    @Override
    public UserProfile addSkill(String userId, Skill skill) {
        User user = userService.getUserByEmail(userId)
                .orElseThrow(() -> new RuntimeException("User not found"));
        UserProfile profile = getProfileByUserId(user.getId());
        if (profile != null) {
            if (profile.getSkills() == null) {
                profile.setSkills(new java.util.ArrayList<>());
            }
            profile.getSkills().add(skill);
            return repository.save(profile);
        }
        return null;
    }

    @Override
    public UserProfile editSkill(String userId, Skill skill, int index) {
        User user = userService.getUserByEmail(userId)
                .orElseThrow(() -> new RuntimeException("User not found"));
        UserProfile profile = getProfileByUserId(user.getId());
        if (profile != null && profile.getSkills() != null && index >= 0 && index < profile.getSkills().size()) {
            profile.getSkills().set(index, skill);
            return repository.save(profile);
        }
        throw new RuntimeException("Skill not found at index " + index + " for user: " + userId);
    }

    @Override
    public void deleteSkill(String userId, int index) {
        User user = userService.getUserByEmail(userId)
                .orElseThrow(() -> new RuntimeException("User not found"));
        UserProfile profile = getProfileByUserId(user.getId());
        if (profile != null && profile.getSkills() != null && index >= 0 && index < profile.getSkills().size()) {
            profile.getSkills().remove(index);
            repository.save(profile);
        } else {
            throw new RuntimeException("Skill not found at index " + index + " for user: " + userId);
        }
    }

    @Override
    public UserProfile addLink(String userId, Link link) {
        User user = userService.getUserByEmail(userId)
                .orElseThrow(() -> new RuntimeException("User not found"));
        UserProfile profile = getProfileByUserId(user.getId());
        if (profile != null) {
            if (profile.getLinks() == null) {
                profile.setLinks(new java.util.ArrayList<>());
            }
            profile.getLinks().add(link);
            return repository.save(profile);
        }
        return null;
    }

    @Override
    public UserProfile editLink(String userId, Link link, int index) {
        User user = userService.getUserByEmail(userId)
                .orElseThrow(() -> new RuntimeException("User not found"));
        UserProfile profile = getProfileByUserId(user.getId());
        if (profile != null && profile.getLinks() != null && index >= 0 && index < profile.getLinks().size()) {
            profile.getLinks().set(index, link);
            return repository.save(profile);
        }
        throw new RuntimeException("Link not found at index " + index + " for user: " + userId);
    }

    @Override
    public void deleteLink(String userId, int index) {
        User user = userService.getUserByEmail(userId)
                .orElseThrow(() -> new RuntimeException("User not found"));
        UserProfile profile = getProfileByUserId(user.getId());
        if (profile != null && profile.getLinks() != null && index >= 0 && index < profile.getLinks().size()) {
            profile.getLinks().remove(index);
            repository.save(profile);
        } else {
            throw new RuntimeException("Link not found at index " + index + " for user: " + userId);
        }
    }

    @Override
    public UserProfile uploadCV(String userId, MultipartFile file) throws IOException {
        User user = userService.getUserByEmail(userId)
                .orElseThrow(() -> new RuntimeException("User not found"));

        UserProfile profile = getProfileByUserId(user.getId());
        if (profile == null) {
            throw new RuntimeException("User profile not found for user: " + userId);
        }

        try {
            // Créer le répertoire de destination s'il n'existe pas
            Path uploadPath = Paths.get(uploadFolderResource).resolve("uploads").resolve("cv");
            Files.createDirectories(uploadPath);

            // Générer un nom de fichier unique
            String originalFileName = file.getOriginalFilename();
            String fileName = UUID.randomUUID() + "_" + (originalFileName != null ? originalFileName : "cv.pdf");

            // Chemin complet du fichier
            Path filePath = uploadPath.resolve(fileName);

            // Enregistrer le fichier
            Files.copy(file.getInputStream(), filePath, StandardCopyOption.REPLACE_EXISTING);

            // Mettre à jour les informations du CV dans le profil
            Cv cv = new Cv();
            cv.setFileName(fileName);
            cv.setOriginalName(originalFileName);
            cv.setUploadDate(LocalDate.now().toString());
            cv.setFileUrl("/content/uploads/cv/" + fileName);

            profile.setCv(cv);

            // Sauvegarder le profil mis à jour
            return repository.save(profile);
        } catch (IOException e) {
            throw new IOException("Erreur lors de l'enregistrement du fichier CV: " + e.getMessage(), e);
        }
    }

    private UserProfile enrichProfileFromCv(UserProfile profile, Cv extractedData) {
        if (extractedData == null) return profile;

        if (extractedData.getExperiences() != null && !extractedData.getExperiences().isEmpty()) {
            profile.setExperiences(extractedData.getExperiences());
        }

        return profile;
    }

    private static final org.slf4j.Logger logger = org.slf4j.LoggerFactory.getLogger(DefaultUserProfileService.class);

    @Override
    public UserProfile uploadProfilePhoto(String userId, MultipartFile file) throws IOException {
        try {
            logger.info("Début de l'upload de photo pour l'utilisateur: {}", userId);
            logger.info("Taille du fichier: {}, Type de contenu: {}, Nom original: {}",
                    file.getSize(), file.getContentType(), file.getOriginalFilename());

            // Vérifier si l'utilisateur existe
            logger.info("Recherche de l'utilisateur avec l'email: {}", userId);
            User user = userService.getUserByEmail(userId)
                    .orElseThrow(() -> {
                        logger.error("Utilisateur non trouvé avec l'email: {}", userId);
                        return new RuntimeException("User not found with email: " + userId);
                    });
            logger.info("Utilisateur trouvé avec l'ID: {}", user.getId());

            // Récupérer ou créer le profil utilisateur
            logger.info("Récupération du profil pour l'utilisateur: {}", user.getId());
            UserProfile profile = getProfileByUserId(user.getId());
            if (profile != null) {
                logger.info("Profil trouvé pour l'utilisateur: {}", user.getId());

                // Générer un nom de fichier unique pour éviter les collisions
                String originalFileName = file.getOriginalFilename();
                logger.info("Nom original du fichier: {}", originalFileName);

                String extension = ".jpg";
                if (originalFileName != null && originalFileName.contains(".")) {
                    extension = originalFileName.substring(originalFileName.lastIndexOf("."));
                }
                logger.info("Extension du fichier: {}", extension);

                String fileName = user.getId() + "_photo_" + System.currentTimeMillis() + extension;
                logger.info("Nom de fichier généré: {}", fileName);

                // Créer le dossier s'il n'existe pas
                logger.info("Dossier d'upload configuré: {}", uploadFolderResource);
                Path uploadDir = Paths.get(uploadFolderResource).resolve("uploads").resolve("photos");
                logger.info("Chemin complet du dossier d'upload: {}", uploadDir.toAbsolutePath());

                if (!Files.exists(uploadDir)) {
                    logger.info("Le dossier d'upload n'existe pas, création en cours...");
                    Files.createDirectories(uploadDir);
                    logger.info("Dossier d'upload créé avec succès");
                } else {
                    logger.info("Le dossier d'upload existe déjà");
                }

                // Chemin complet du fichier
                Path path = uploadDir.resolve(fileName);
                logger.info("Chemin complet du fichier: {}", path.toAbsolutePath());

                // Écrire le fichier avec l'option de remplacement si le fichier existe déjà
                logger.info("Copie du fichier en cours...");
                Files.copy(file.getInputStream(), path, StandardCopyOption.REPLACE_EXISTING);
                logger.info("Fichier copié avec succès");

                // Mettre à jour l'URL de la photo dans le profil
                String photoUrl = "/uploads/photos/" + fileName;
                logger.info("URL de la photo mise à jour: {}", photoUrl);
                profile.setPhotoUrl(photoUrl);

                // Sauvegarder et retourner le profil mis à jour
                logger.info("Sauvegarde du profil en cours...");
                UserProfile savedProfile = repository.save(profile);
                logger.info("Profil sauvegardé avec succès");

                return savedProfile;
            } else {
                logger.error("Profil non trouvé pour l'utilisateur: {}", user.getId());
                return null;
            }
        } catch (IOException e) {
            logger.error("Erreur IO lors de l'enregistrement de la photo de profil: {}", e.getMessage(), e);
            throw new IOException("Erreur lors de l'enregistrement de la photo de profil: " + e.getMessage(), e);
        } catch (Exception e) {
            logger.error("Erreur inattendue lors de l'upload de la photo: {}", e.getMessage(), e);
            throw new IOException("Erreur inattendue lors de l'upload de la photo: " + e.getMessage(), e);
        }
    }

    @Override
    public UserProfile updateProfile(String userId, UserProfile updatedProfile) {
        UserProfile existingProfile = repository.findByUserId(userId);
        if (existingProfile == null) {
            throw new RuntimeException("Profile not found for user: " + userId);
        }

        // Validation de l'email
        if (updatedProfile.getEmail() != null && !updatedProfile.getEmail().isEmpty() &&
                !updatedProfile.getEmail().equals(existingProfile.getEmail())) {
            Optional<UserProfile> profileWithEmail = repository.findByEmail(updatedProfile.getEmail());
            if (profileWithEmail.isPresent() && !profileWithEmail.get().getUserId().equals(userId)) {
                throw new RuntimeException("Email is already in use: " + updatedProfile.getEmail());
            }
            existingProfile.setEmail(updatedProfile.getEmail());
        }

        // Mise à jour des champs uniquement si les nouvelles valeurs ne sont pas null ou vides
        if (updatedProfile.getFirstName() != null && !updatedProfile.getFirstName().isEmpty()) {
            existingProfile.setFirstName(updatedProfile.getFirstName());
        }
        if (updatedProfile.getLastName() != null && !updatedProfile.getLastName().isEmpty()) {
            existingProfile.setLastName(updatedProfile.getLastName());
        }
        if (updatedProfile.getPhoneNumber() != null && !updatedProfile.getPhoneNumber().isEmpty()) {
            existingProfile.setPhoneNumber(updatedProfile.getPhoneNumber());
        }
        if (updatedProfile.getAddress() != null && !updatedProfile.getAddress().isEmpty()) {
            existingProfile.setAddress(updatedProfile.getAddress());
        }
        if (updatedProfile.getBio() != null && !updatedProfile.getBio().isEmpty()) {
            existingProfile.setBio(updatedProfile.getBio());
        }
        if (updatedProfile.getDateOfBirth() != null) {
            existingProfile.setDateOfBirth(updatedProfile.getDateOfBirth());
        }
        if (updatedProfile.getResumeUrl() != null && !updatedProfile.getResumeUrl().isEmpty()) {
            existingProfile.setResumeUrl(updatedProfile.getResumeUrl());
        }
        try {
            return repository.save(existingProfile);
        } catch (Exception e) {
            throw new RuntimeException("Failed to update profile: " + e.getMessage(), e);
        }
    }
}