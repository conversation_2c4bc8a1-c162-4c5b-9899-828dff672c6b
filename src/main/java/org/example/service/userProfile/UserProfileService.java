package org.example.service.userProfile;

import org.example.model.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

public interface UserProfileService {

    UserProfile getProfileByUserId(String userId);
    List<UserProfile> getAllProfiles();
    UserProfile createOrUpdateProfile(UserProfile profile);
    void deleteProfileByUserId(String userId);
    UserProfile save(UserProfile profile);
    void delete(String id);
    UserProfile getByUserId(String userId);
    UserProfile addEducation(String userId, Education education);
    UserProfile editEducation(String userId, Education education, int index);
    void deleteEducation(String userId, int index);
    UserProfile addCertification(String userId, Certificate certification);
    UserProfile editCertification(String userId, Certificate certification, int index);
    void deleteCertification(String userId, int index);
    UserProfile addExperience(String userId, Experience experience);
    UserProfile editExperience(String userId, Experience experience, int index);
    void deleteExperience(String userId, int index);
    UserProfile addSkill(String userId, Skill skill);
    UserProfile editSkill(String userId, Skill skill, int index);
    void deleteSkill(String userId, int index);
    UserProfile addLink(String userId, Link link);
    UserProfile editLink(String userId, Link link, int index);
    void deleteLink(String userId, int index);
    UserProfile uploadCV(String userId, MultipartFile file) throws IOException;
    UserProfile uploadProfilePhoto(String userId, MultipartFile file) throws IOException;
    UserProfile updateProfile(String userId, UserProfile updatedProfile);
}