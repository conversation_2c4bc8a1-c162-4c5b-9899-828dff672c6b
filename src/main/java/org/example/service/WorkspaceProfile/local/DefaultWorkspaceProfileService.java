package org.example.service.WorkspaceProfile.local;

import org.example.model.WorkspaceProfile;
import org.example.service.WorkspaceProfile.WorkspaceProfileService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DefaultWorkspaceProfileService implements WorkspaceProfileService {

    private final WorkspaceProfileRepository repository;

    public DefaultWorkspaceProfileService(WorkspaceProfileRepository repository) {
        this.repository = repository;
    }

    @Override
    public WorkspaceProfile getProfileByWorkspaceId(String workspaceId) {
        return repository.findByWorkspaceId(workspaceId);
    }

    @Override
    public List<WorkspaceProfile> getAllProfiles() {
        return repository.findAll();
    }

    @Override
    public WorkspaceProfile createOrUpdateProfile(WorkspaceProfile profile) {
        if (profile.getWorkspaceId() == null || profile.getWorkspaceId().isEmpty()) {
            throw new IllegalArgumentException("Workspace ID cannot be null or empty");
        }
        return repository.save(profile);
    }

    @Override
    public void deleteProfileByWorkspaceId(String workspaceId) {
        WorkspaceProfile profile = getProfileByWorkspaceId(workspaceId);
        if (profile != null) {
            repository.delete(profile);
        } else {
            throw new RuntimeException("Workspace profile not found");
        }
    }
}
