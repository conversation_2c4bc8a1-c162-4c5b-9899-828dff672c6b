package org.example.service.cantact.local;

import org.example.dto.ContactFormDTO;
import org.example.service.cantact.ContactService;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Service;

@Service
public class DefaultContactService implements ContactService {

    private final JavaMailSender mailSender;

    // Injection par constructeur
    public DefaultContactService(JavaMailSender mailSender) {
        this.mailSender = mailSender;
    }

    @Override
    public void sendContactMessage(ContactFormDTO contactForm) {
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setTo("<EMAIL>"); // Remplacer par ton mail
            message.setSubject("New Contact Form Submission from " + contactForm.getName());
            message.setText(
                    "Name: " + contactForm.getName() + "\n" +
                            "Email: " + contactForm.getEmail() + "\n" +
                            "Message: \n" + contactForm.getMessage()
            );

            mailSender.send(message);
        } catch (Exception e) {
            // Log l'erreur côté serveur
            System.err.println("Erreur lors de l'envoi de l'email: " + e.getMessage());
            throw new RuntimeException("Échec de l'envoi du message.");
        }
    }

}
