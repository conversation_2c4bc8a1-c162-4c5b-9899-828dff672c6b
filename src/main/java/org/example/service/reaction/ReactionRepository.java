package org.example.service.reaction;

import org.example.model.Reaction;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;

public interface ReactionRepository extends MongoRepository<Reaction, String> {
    void deleteByPostIdAndUserId(String postId, String userId);
    long countByPostId(String postId); // Total reactions
    long countByPostIdAndReactionType(String postId, String reactionType); // Reactions by type
    List<Reaction> findByPostId(String postId); // Reactions by type
}