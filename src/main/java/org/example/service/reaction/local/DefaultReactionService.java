package org.example.service.reaction.local;

import org.example.model.Reaction;
import org.example.service.reaction.ReactionRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
public class DefaultReactionService {
    @Autowired
    private ReactionRepository reactionRepository;

    public Reaction saveReaction(String postId, String userId, String reactionType) {
        Reaction reaction = new Reaction(postId, userId, reactionType);
        reactionRepository.deleteByPostIdAndUserId(postId, userId);
        return reactionRepository.save(reaction);
    }

    public Map<String, Object> getReactionCounts(String postId, String userId) { // New method
        List<Reaction> reactions = reactionRepository.findByPostId(postId);
        Map<String, Object> counts = new HashMap<>();
        reactions.forEach(reaction -> {
            int old = (int) counts.getOrDefault(reaction.getReactionType(), 0);
            counts.put(reaction.getReactionType(), old + 1);
            if (Objects.equals(reaction.getUserId(), userId)) {
                counts.put("me", reaction.getReactionType());
            }
        });
        counts.put("total", reactions.size());
        return counts;
    }
}