package org.example.service.modelIA;

import com.fasterxml.jackson.databind.JsonNode;
import org.example.model.LlamaRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.net.URI;
import java.time.Duration;
@Service
public class LlamaService {
    private static final Logger logger = LoggerFactory.getLogger(LlamaService.class);
    private final WebClient webClient;

    public LlamaService(@Value("${llama.api.url}") String llamaApiUrl) {
        if (llamaApiUrl == null || llamaApiUrl.trim().isEmpty()) {
            throw new IllegalArgumentException("LLaMA API URL is not configured");
        }
        this.webClient = WebClient.builder()
                .baseUrl(llamaApiUrl)
                .build();
    }

    public Mono<String> askQuestion(String prompt) {
        LlamaRequest request = new LlamaRequest(prompt);
        logger.trace("Sending request to LLaMA API: {}", request);

        return webClient.post()
                .uri("/api/generate")
                .header("Content-Type", "application/json")
                .bodyValue(request)
                .retrieve()
                .bodyToFlux(JsonNode.class)
                .takeUntil(json -> json.has("done") && json.get("done").asBoolean())
                .filter(json -> json.has("response"))
                .map(json -> json.get("response").asText())
                .collectList()
                .map(fragments -> String.join("", fragments))
                .timeout(Duration.ofHours(10))
                .doOnSuccess(response -> logger.trace("Response received: {}", response))
                .onErrorResume(e -> {
                    logger.error("LLaMA API Error: {}", e.getMessage(), e);
                    return Mono.just("⏱️ Timeout or error: " + e.getMessage());
                });
    }

}
