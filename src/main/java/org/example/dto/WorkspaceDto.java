package org.example.dto;

import org.example.model.Workspace;
import org.example.model.WorkspaceProfile;

public class WorkspaceDto {
    private final String id;
    private final String name;
    private final String description;
    private final String logoUrl;  // corrigé ici : String au lieu de byte[]
    private final String location;
    private final String phoneNumber;
    private final String email;

    public WorkspaceDto(Workspace workspace, WorkspaceProfile workspaceProfile) {
        if (workspace != null) {
            this.id = workspace.getId();
            this.name = workspace.getName();
            this.description = workspace.getDescription();
            this.logoUrl = workspace.getLogoUrl();
        } else {
            this.id = null;
            this.name = null;
            this.description = null;
            this.logoUrl = null;
        }
        if (workspace != null) {
            this.email = workspace.getEmail();
            this.location = workspace.getLocation();
            this.phoneNumber = workspace.getPhoneNumber();
        } else {
            this.email = null;
            this.location = null;
            this.phoneNumber = null;
        }
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }

    public String getLogoUrl() {  // corrigé ici aussi
        return logoUrl;
    }

    public String getLocation() {
        return location;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public String getEmail() {
        return email;
    }

    public String getId() {
        return id;
    }
}
