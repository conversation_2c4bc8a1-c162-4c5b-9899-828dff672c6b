package org.example.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import org.example.model.QuestionType;

import java.util.List;

public class TestDTO {

    @JsonProperty("title")
    @NotBlank(message = "Title cannot be blank")
    private String title;

    @JsonProperty("questions")
    @NotEmpty(message = "Questions list cannot be empty")
    private List<QuestionDTO> questions;

    // Constructeurs

    public TestDTO() {
        // Constructeur par défaut
    }

    public TestDTO(String title, List<QuestionDTO> questions) {
        this.title = title;
        this.questions = questions;
    }

    // Getters et Setters

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public List<QuestionDTO> getQuestions() {
        return questions;
    }

    public void setQuestions(List<QuestionDTO> questions) {
        this.questions = questions;
    }
}