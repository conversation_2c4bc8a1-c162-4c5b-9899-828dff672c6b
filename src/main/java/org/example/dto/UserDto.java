package org.example.dto;

import org.example.model.User;

public class UserDto {
    private final String email;
    private final String firstName;
    private final String lastName;
    private final String address;
    private final String phoneNumber;

    public UserDto(User user) {
        this.email = user.getEmail();
        this.firstName = user.getFirstName();
        this.lastName = user.getLastName();
        this.address = user.getAddress();
        this.phoneNumber = user.getPhoneNumber();
    }

    public String getEmail() {
        return email;
    }

    public String getFirstName() {
        return firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public String getAddress() {
        return address;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }
}