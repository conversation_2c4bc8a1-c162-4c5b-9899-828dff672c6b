package org.example.dto;

import java.time.LocalDateTime;

public class PostDto {
    private String id;
    private String titre;
    private String description;
    private String entreprise;
    private String userId;
    private String recruiterName; // New field for recruiter's name
    private WorkspaceDto workspace; // New field for workspace details (including logo)
    private String profileRequest;
    private String contractType;
    private LocalDateTime datePublication;
    private String jobTitle;
    private String location;
    private boolean archived;
    private String userFullName;


    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTitre() {
        return titre;
    }

    public void setTitre(String titre) {
        this.titre = titre;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getEntreprise() {
        return entreprise;
    }

    public void setEntreprise(String entreprise) {
        this.entreprise = entreprise;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getRecruiterName() {
        return recruiterName;
    }

    public void setRecruiterName(String recruiterName) {
        this.recruiterName = recruiterName;
    }

    public WorkspaceDto getWorkspace() {
        return workspace;
    }

    public void setWorkspace(WorkspaceDto workspace) {
        this.workspace = workspace;
    }

    public String getProfileRequest() {
        return profileRequest;
    }

    public void setProfileRequest(String profileRequest) {
        this.profileRequest = profileRequest;
    }

    public String getContractType() {
        return contractType;
    }

    public void setContractType(String contractType) {
        this.contractType = contractType;
    }

    public LocalDateTime getDatePublication() {
        return datePublication;
    }

    public void setDatePublication(LocalDateTime datePublication) {
        this.datePublication = datePublication;
    }

    public String getJobTitle() {
        return jobTitle;
    }

    public void setJobTitle(String jobTitle) {
        this.jobTitle = jobTitle;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public boolean isArchived() {
        return archived;
    }

    public void setArchived(boolean archived) {
        this.archived = archived;
    }

    public String getUserFullName() {
        return userFullName;
    }

    public void setUserFullName(String userFullName) {
        this.userFullName = userFullName;
    }
}