package org.example.config.security;

import jakarta.servlet.http.HttpServletRequest;
import org.aopalliance.intercept.MethodInvocation;
import org.example.model.Membership;
import org.example.service.workspace.WorkspaceService;
import org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler;
import org.springframework.security.access.expression.method.MethodSecurityExpressionOperations;
import org.springframework.security.core.Authentication;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

public class MembershipMethodSecurityExpressionHandler extends DefaultMethodSecurityExpressionHandler {

    private final WorkspaceService workspaceService;

    public MembershipMethodSecurityExpressionHandler(WorkspaceService workspaceService) {
        this.workspaceService = workspaceService;
    }

    @Override
    protected MethodSecurityExpressionOperations createSecurityExpressionRoot(
            Authentication authentication, MethodInvocation invocation) {
        MembershipMethodSecurityExpressionRoot root = new MembershipMethodSecurityExpressionRoot(authentication);
        root.setTrustResolver(getTrustResolver());
        root.setRoleHierarchy(getRoleHierarchy());

        String workspaceId = getWorkspaceId();
        if (workspaceId != null) {
            workspaceService.getMembership(workspaceId, authentication.getName())
                    .ifPresent(root::setMembership);
        }

        return root;
    }

    private String getWorkspaceId() {
        ServletRequestAttributes attributes =
                (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();

        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            return request.getHeader("WWW-Workspace-Id");
        }
        return null;
    }
}
