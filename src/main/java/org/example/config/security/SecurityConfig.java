package org.example.config.security;

import org.example.service.workspace.WorkspaceService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.security.access.expression.method.MethodSecurityExpressionHandler;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;

import java.util.List;

import static org.springframework.security.config.Customizer.withDefaults;

@Configuration
@EnableMethodSecurity
public class SecurityConfig {

    @Bean
    public MethodSecurityExpressionHandler customExpressionHandler(WorkspaceService workspaceService) {
        return new MembershipMethodSecurityExpressionHandler(workspaceService);
    }

    @Bean
    public BCryptPasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http.cors(cors -> cors.configurationSource(request -> {
                    var config = new org.springframework.web.cors.CorsConfiguration();
                    config.setAllowedOrigins(List.of("http://localhost:4200"));
                    config.setAllowedMethods(List.of("*"));
                    config.setAllowedHeaders(List.of("*"));
                    config.setAllowCredentials(true);
                    return config;
                }))
                .csrf(AbstractHttpConfigurer::disable)
                .authorizeHttpRequests(auth -> auth
                        .requestMatchers(HttpMethod.OPTIONS).permitAll()
                        .requestMatchers("/api/users/change-password/**","/api/user/check-email", "/api/user/signup", "/api/workspaces/create", "/.well-known/openid-configuration", "/api/invitations/**","/api/posts/**","/api/tests/**").permitAll()
                        .requestMatchers("/api/payment/create-checkout-session").permitAll()
                        .requestMatchers("/api/contact").permitAll()
                        .requestMatchers("/uploads/**").permitAll()
                        .requestMatchers("/content/**").permitAll()
                        .requestMatchers("/api/content/**").permitAll()

                        .requestMatchers("/api/user-profiles/**","/api/user/**", "/api/user/check-email", "/api/user/signup", "/api/workspaces/create", "/.well-known/openid-configuration", "/api/invitations/**","/api/posts/**").permitAll()
                        .requestMatchers(HttpMethod.GET, "/api/workspaces/**").permitAll()
                        .requestMatchers(HttpMethod.GET, "/api/workspaces/me").authenticated()
                        .anyRequest().authenticated()
                )
                .oauth2ResourceServer(oauth2 -> oauth2.jwt(withDefaults()))
                .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS));

        return http.build();
    }

}