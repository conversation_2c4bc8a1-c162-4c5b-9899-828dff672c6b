package org.example.config.security;

import org.example.model.Membership;
import org.springframework.security.access.expression.SecurityExpressionRoot;
import org.springframework.security.access.expression.method.MethodSecurityExpressionOperations;
import org.springframework.security.core.Authentication;

import java.util.function.Supplier;

public class MembershipMethodSecurityExpressionRoot extends SecurityExpressionRoot implements MethodSecurityExpressionOperations {


    private Object filterObject;

    private Object returnObject;

    private Object target;

    private Membership membership;

    MembershipMethodSecurityExpressionRoot(Authentication a) {
        super(a);
        Membership anonymousMembership = new Membership();
        anonymousMembership.setAnonymous(true);
        this.membership = anonymousMembership;
    }

    MembershipMethodSecurityExpressionRoot(Supplier<Authentication> authentication) {
        super(authentication);
        Membership anonymousMembership = new Membership();
        anonymousMembership.setAnonymous(true);
        this.membership = anonymousMembership;
    }

    @Override
    public void setFilterObject(Object filterObject) {
        this.filterObject = filterObject;
    }

    @Override
    public Object getFilterObject() {
        return this.filterObject;
    }

    @Override
    public void setReturnObject(Object returnObject) {
        this.returnObject = returnObject;
    }

    @Override
    public Object getReturnObject() {
        return this.returnObject;
    }

    /**
     * Sets the "this" property for use in expressions. Typically this will be the "this"
     * property of the {@code JoinPoint} representing the method invocation which is being
     * protected.
     * @param target the target object on which the method in is being invoked.
     */
    void setThis(Object target) {
        this.target = target;
    }

    @Override
    public Object getThis() {
        return this.target;
    }

    public void setMembership(Membership membership) {
        this.membership = membership;
    }

    public Membership getMembership() {
        return membership;
    }

    public boolean hasMembershipAuthority(String authority) {
        return membership.hasAuthority(authority);
    }
}
