package org.example.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.nio.file.Path;
import java.nio.file.Paths;

@Configuration
public class StaticResourceConfig implements WebMvcConfigurer {

    @Value("${uploadFolder}")
    private String uploadFolder;

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        String uploadPath = uploadFolder;
        System.out.println("Chemin d'upload configuré: " + uploadPath);

        // Configuration pour accéder aux fichiers via /uploads/**
        registry.addResourceHandler("/uploads/**")
                .addResourceLocations("file:" + uploadPath + "/")
                .setCachePeriod(3600); // Cache d'une heure

        // Configuration pour accéder aux fichiers via /content/uploads/**
        registry.addResourceHandler("/content/uploads/**")
                .addResourceLocations("file:" + uploadPath + "/uploads/")
                .setCachePeriod(3600);

        // Configuration pour accéder aux fichiers via /api/content/uploads/**
        registry.addResourceHandler("/api/content/uploads/**")
                .addResourceLocations("file:" + uploadPath + "/uploads/")
                .setCachePeriod(3600);

        // Configuration spécifique pour les workspaces
        registry.addResourceHandler("/uploads/workspaces/**")
                .addResourceLocations("file:" + uploadPath + "/workspaces/")
                .setCachePeriod(3600);
    }
}
