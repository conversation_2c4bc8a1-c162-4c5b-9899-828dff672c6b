package org.example.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

@Configuration
public class FileStorageConfig {

    private static final Logger logger = LoggerFactory.getLogger(FileStorageConfig.class);

    @Value("${uploadFolder}")
    private String uploadFolderPath;

    @Bean
    CommandLineRunner createUploadDirectories() {
        return args -> {
            try {
                // Créer le dossier principal d'upload s'il n'existe pas
                Path uploadFolder = Paths.get(uploadFolderPath);
                if (!Files.exists(uploadFolder)) {
                    logger.info("Création du dossier d'upload principal: {}", uploadFolder.toAbsolutePath());
                    Files.createDirectories(uploadFolder);
                }

                // Créer les sous-dossiers nécessaires
                Path photosFolder = uploadFolder.resolve("uploads").resolve("photos");
                if (!Files.exists(photosFolder)) {
                    logger.info("Création du dossier pour les photos: {}", photosFolder.toAbsolutePath());
                    Files.createDirectories(photosFolder);
                }

                Path cvFolder = uploadFolder.resolve("uploads").resolve("cv");
                if (!Files.exists(cvFolder)) {
                    logger.info("Création du dossier pour les CV: {}", cvFolder.toAbsolutePath());
                    Files.createDirectories(cvFolder);
                }

                logger.info("Tous les dossiers d'upload ont été créés avec succès");
            } catch (Exception e) {
                logger.error("Erreur lors de la création des dossiers d'upload: {}", e.getMessage(), e);
                throw new RuntimeException("Impossible de créer les dossiers d'upload", e);
            }
        };
    }
}
