package org.example.config.web;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.List;

@Configuration
public class WebConfig implements WebMvcConfigurer {

    private final MembershipArgumentResolver membershipArgumentResolver;

    public WebConfig(MembershipArgumentResolver membershipArgumentResolver) {
        this.membershipArgumentResolver = membershipArgumentResolver;
    }

    @Override
    public void addArgumentResolvers(List<HandlerMethodArgumentResolver> resolvers) {
        resolvers.add(membershipArgumentResolver);
    }
    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }
}
