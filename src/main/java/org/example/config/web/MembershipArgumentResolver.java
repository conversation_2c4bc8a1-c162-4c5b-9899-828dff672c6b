package org.example.config.web;

import org.example.model.Membership;
import org.example.model.User;
import org.example.service.user.UserService;
import org.example.service.workspace.WorkspaceService;
import org.springframework.core.MethodParameter;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

@Component
public class MembershipArgumentResolver implements HandlerMethodArgumentResolver {

    private final WorkspaceService workspaceService;
    private final UserService userService;

    public MembershipArgumentResolver(WorkspaceService workspaceService, UserService userService) {
        this.workspaceService = workspaceService;
        this.userService = userService;
    }

    @Override
    public boolean supportsParameter(MethodParameter parameter) {
        return parameter.getParameterType().equals(Membership.class);
    }

    @Override
    public Object resolveArgument(MethodParameter parameter, ModelAndViewContainer mavContainer,
                                  NativeWebRequest webRequest, WebDataBinderFactory binderFactory) {
        String workspaceId = webRequest.getHeader("WWW-Workspace-Id");
        User me = userService.me();
        if (workspaceId == null || workspaceId.isEmpty()) {
            return null;
        }
        return workspaceService.getMembership(workspaceId, me.getId())
                .orElse(null);

    }
}
